# Fulfillmen Support Common

通用支持模块，为各个API支持模块（如Alibaba、WMS等）提供标准化的WebClient配置和请求执行模板。

## 设计目标

1. **统一WebClient配置管理** - 提供标准化的WebClient配置，减少各个支持模块的重复配置
2. **通用请求执行模板** - 提供可扩展的请求执行代码模板，支持不同API的自定义扩展需求

## 核心组件

### WebClient配置管理

#### WebClientBuilder
提供创建和配置WebClient的工具方法：

```java
// 使用默认配置
WebClient webClient = WebClientBuilder.createWebClient("https://api.example.com");

// 使用自定义配置
WebClientConfig config = new WebClientConfig();
config.setMaxConnections(100);
config.setConnectTimeoutMillis(3000);
WebClient webClient = WebClientBuilder.getWebClientBuilder(config)
    .baseUrl("https://api.example.com")
    .build();

// 创建带认证头的WebClient
HttpHeaders headers = WebClientBuilder.createJsonHeaders();
headers.set("X-API-Key", "your-api-key");
WebClient webClient = WebClientBuilder.createWebClient("https://api.example.com", headers);
```

#### WebClientConfig
可配置的WebClient参数类，支持自定义：
- 连接池配置（最大连接数、空闲时间）
- 超时设置（连接、响应、读写超时）
- 内存限制
- 压缩和重定向设置

### 请求执行模板

#### ApiRequestExecutor
通用的API请求执行器，提供：

```java
// 简单的API请求执行
Mono<String> result = ApiRequestExecutor.execute(
    formParams -> webClient.post().uri("/api/endpoint").bodyValue(formParams).retrieve().bodyToMono(String.class),
    params,
    "服务名称",
    "操作名称"
);

// 带参数验证的API请求执行
Mono<String> result = ApiRequestExecutor.executeWithValidation(
    () -> validateParams(params), // 验证逻辑
    formParams -> webClient.post().uri("/api/endpoint").bodyValue(formParams).retrieve().bodyToMono(String.class),
    params,
    "服务名称",
    "操作名称"
);
```

#### ApiResponseHandler
通用的API响应处理器，提供统一的日志记录和异常处理。

#### ApiCallException
统一的API调用异常，包含服务名称和操作名称信息。

## 使用场景

### 1. Alibaba模块集成

```java
public class AlibabaApiClient {
    private final WebClient webClient;
    
    public AlibabaApiClient(String baseUrl) {
        this.webClient = WebClientBuilder.createWebClient(baseUrl);
    }
    
    public Mono<String> searchGoods(String keyword) {
        MultiValueMap<String, String> params = buildAlibabaParams();
        params.add("keyword", keyword);
        
        return ApiRequestExecutor.executeWithRequest(
            formParams -> webClient.post().uri("/api/goods/search").bodyValue(formParams).retrieve().bodyToMono(String.class),
            params,
            Map.of("keyword", keyword),
            "阿里巴巴商品服务",
            "商品搜索"
        );
    }
    
    private MultiValueMap<String, String> buildAlibabaParams() {
        // 阿里巴巴特定的参数构建和签名逻辑
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("appKey", appKey);
        params.add("timestamp", String.valueOf(System.currentTimeMillis()));
        params.add("signature", generateSignature());
        return params;
    }
}
```

### 2. WMS模块集成

```java
public class WmsApiClient {
    private final WebClient webClient;
    
    public WmsApiClient(String baseUrl, String apiKey) {
        HttpHeaders headers = WebClientBuilder.createJsonHeaders();
        headers.set("X-API-Key", apiKey);
        this.webClient = WebClientBuilder.createWebClient(baseUrl, headers);
    }
    
    public Mono<String> createOrder(Map<String, Object> orderData) {
        return ApiRequestExecutor.execute(
            params -> webClient.post().uri("/api/orders").bodyValue(orderData).retrieve().bodyToMono(String.class),
            new LinkedMultiValueMap<>(),
            "WMS服务",
            "创建订单"
        );
    }
}
```

## 特性

### 默认配置
- **连接池**：500最大连接，20秒空闲时间
- **超时设置**：连接5秒，响应20秒，读写30秒
- **内存限制**：16MB
- **其他**：启用压缩和重定向

### 日志记录
- 自动记录请求和响应信息（DEBUG级别）
- 统一的错误日志格式
- 包含服务名称和操作名称的上下文信息

### 异常处理
- 统一的异常类型 `ApiCallException`
- 包含服务名称和操作名称的异常信息
- 区分验证异常和服务异常

## 与现有模块的兼容性

这个重新设计的模块：
1. **保持简单** - 不强制使用复杂的认证或签名接口
2. **高度可扩展** - 各个API模块可以保持自己的特定实现逻辑
3. **向后兼容** - 现有的Alibaba和WMS模块可以逐步迁移到新的通用组件
4. **无侵入性** - 不改变现有模块的核心业务逻辑

## 测试

运行测试：
```bash
mvn test -Ptest -Dspotless.skip=true
```

所有测试用例都已通过，确保了组件的稳定性和可靠性。
