/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.http;

import com.fulfillmen.support.common.exception.ApiCallException;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * API响应处理器
 * 
 * <p>提供通用的API响应处理功能，包括日志记录和错误处理。
 * 支持泛型响应类型，可以根据不同的API响应格式进行定制。</p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@Slf4j
public final class ApiResponseHandler {

    private ApiResponseHandler() {
        // 私有构造函数，防止实例化
    }

    /**
     * 处理API响应
     * 
     * <p>记录请求和响应日志，处理异常情况。
     * 这是一个通用的响应处理方法，适用于大多数API调用场景。</p>
     *
     * @param mono          API响应Mono
     * @param serviceName   服务名称（用于日志）
     * @param operationName 操作名称（用于日志）
     * @param request       请求对象（用于日志）
     * @param <T>           响应类型
     * @return 处理后的响应Mono
     */
    public static <T> Mono<T> handleResponse(Mono<T> mono, String serviceName, String operationName, Object request) {
        return mono
            .doOnSubscribe(subscription -> log
                .debug("[{}] {} 请求开始: request={}", serviceName, operationName, request))
            .doOnSuccess(response -> log
                .info("[{}] {} 请求成功: request={}, response={}", serviceName, operationName, request, response))
            .doOnError(error -> log
                .error("[{}] {} 请求失败: request={}, error={}", serviceName, operationName, request, error.getMessage()))
            .onErrorMap(e -> new ApiCallException(serviceName, operationName, e.getMessage(), e));
    }

    /**
     * 处理API响应（带验证异常处理）
     * 
     * <p>记录请求和响应日志，区分处理验证异常和其他异常。
     * 适用于需要特殊处理验证异常的API调用场景。</p>
     *
     * @param mono                     API响应Mono
     * @param serviceName              服务名称（用于日志）
     * @param operationName            操作名称（用于日志）
     * @param request                  请求对象（用于日志）
     * @param validationExceptionClass 验证异常类
     * @param <T>                      响应类型
     * @param <E>                      验证异常类型
     * @return 处理后的响应Mono
     */
    public static <T, E extends RuntimeException> Mono<T> handleResponse(
        Mono<T> mono,
        String serviceName,
        String operationName,
        Object request,
        Class<E> validationExceptionClass
    ) {
        return mono
            .doOnSubscribe(subscription -> log
                .debug("[{}] {} 请求开始: request={}", serviceName, operationName, request))
            .doOnSuccess(response -> log
                .info("[{}] {} 请求成功: request={}, response={}", serviceName, operationName, request, response))
            .doOnError(error -> {
                if (validationExceptionClass.isInstance(error)) {
                    log.warn("[{}] {} 请求失败(验证错误): request={}, error={}",
                        serviceName, operationName, request, error.getMessage());
                } else {
                    log.error("[{}] {} 请求失败: request={}, error={}",
                        serviceName, operationName, request, error.getMessage());
                }
            })
            .onErrorMap(e -> {
                if (validationExceptionClass.isInstance(e)) {
                    return e;
                }
                return new ApiCallException(serviceName, operationName, e.getMessage(), e);
            });
    }
}
