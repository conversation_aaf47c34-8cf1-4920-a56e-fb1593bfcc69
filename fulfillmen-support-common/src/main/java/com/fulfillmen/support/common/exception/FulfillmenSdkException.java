/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.exception;

import java.io.Serializable;

/**
 * Fulfillmen SDK 异常
 * <pre>
 * 通用的 sdk 异常信息
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/21 14:50
 * @description: todo
 * @since 1.0.0
 */
public class FulfillmenSdkException extends RuntimeException implements Serializable {

    public FulfillmenSdkException(String message) {
        super(message);
    }

    public FulfillmenSdkException(String message, Throwable cause) {
        super(message, cause);
    }

}
