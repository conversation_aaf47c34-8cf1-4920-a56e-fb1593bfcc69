/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.http;

import com.fulfillmen.support.common.webclient.WebClientBuilder;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

/**
 * 基于 WebClient 的通用请求执行器
 * 
 * <p>提供简单易用的 HTTP 请求工具，支持表单和 JSON 格式，支持可选的认证。</p>
 *
 * <AUTHOR>
 * @created 2025-07-29
 */
@Slf4j
public class WebClientRequestExecutor {

    private final WebClient webClient;
    private final String serviceName;
    private final AuthProvider authProvider;

    /**
     * 构造函数
     */
    public WebClientRequestExecutor(String baseUrl, String serviceName) {
        this(baseUrl, serviceName, null);
    }

    /**
     * 构造函数（带认证）
     */
    public WebClientRequestExecutor(String baseUrl, String serviceName, AuthProvider authProvider) {
        this.webClient = WebClientBuilder.createWebClient(baseUrl);
        this.serviceName = serviceName;
        this.authProvider = authProvider;
    }

    /**
     * POST 表单请求
     */
    public <T> Mono<T> postForm(String path, Map<String, String> params, Class<T> responseType) {
        return postForm(path, params, responseType, null);
    }

    /**
     * POST 表单请求（带操作名称）
     */
    public <T> Mono<T> postForm(String path, Map<String, String> params, Class<T> responseType, String operation) {
        MultiValueMap<String, String> formData = buildFormData(params, path);

        return webClient.post()
            .uri(path)
            .contentType(MediaType.APPLICATION_FORM_URLENCODED)
            .body(BodyInserters.fromFormData(formData))
            .retrieve()
            .bodyToMono(responseType)
            .doOnSubscribe(s -> logRequest("POST", path, operation, params))
            .doOnSuccess(response -> logSuccess("POST", path, operation, response))
            .doOnError(error -> logError("POST", path, operation, error));
    }

    /**
     * POST JSON 请求
     */
    public <T> Mono<T> postJson(String path, Object requestBody, Class<T> responseType) {
        return postJson(path, requestBody, responseType, null);
    }

    /**
     * POST JSON 请求（带操作名称）
     */
    public <T> Mono<T> postJson(String path, Object requestBody, Class<T> responseType, String operation) {
        Map<String, String> authParams = addAuthParams(new HashMap<>(), path);

        return webClient.post()
            .uri(uriBuilder -> {
                var builder = uriBuilder.path(path);
                authParams.forEach(builder::queryParam);
                return builder.build();
            })
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(requestBody)
            .retrieve()
            .bodyToMono(responseType)
            .doOnSubscribe(s -> logRequest("POST", path, operation, requestBody))
            .doOnSuccess(response -> logSuccess("POST", path, operation, response))
            .doOnError(error -> logError("POST", path, operation, error));
    }

    /**
     * GET 请求
     */
    public <T> Mono<T> get(String path, Map<String, String> params, Class<T> responseType) {
        return get(path, params, responseType, null);
    }

    /**
     * GET 请求（带操作名称）
     */
    public <T> Mono<T> get(String path, Map<String, String> params, Class<T> responseType, String operation) {
        Map<String, String> allParams = new HashMap<>();
        if (params != null) {
            allParams.putAll(params);
        }
        addAuthParams(allParams, path);

        return webClient.get()
            .uri(uriBuilder -> {
                var builder = uriBuilder.path(path);
                allParams.forEach(builder::queryParam);
                return builder.build();
            })
            .retrieve()
            .bodyToMono(responseType)
            .doOnSubscribe(s -> logRequest("GET", path, operation, allParams))
            .doOnSuccess(response -> logSuccess("GET", path, operation, response))
            .doOnError(error -> logError("GET", path, operation, error));
    }

    // 如果需要更复杂的自定义请求，可以直接使用 webClient 实例
    public WebClient getWebClient() {
        return webClient;
    }

    /**
     * 构建表单数据
     */
    private MultiValueMap<String, String> buildFormData(Map<String, String> params, String path) {
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();

        // 添加业务参数
        if (params != null) {
            params.forEach(formData::add);
        }

        // 添加认证参数
        addAuthParams(formData, path);

        return formData;
    }

    /**
     * 添加认证参数
     */
    private Map<String, String> addAuthParams(Map<String, String> params, String path) {
        if (authProvider != null) {
            authProvider.addAuthParams(params, path);
        }
        return params;
    }

    /**
     * 添加认证参数到 MultiValueMap
     */
    private void addAuthParams(MultiValueMap<String, String> params, String path) {
        if (authProvider != null) {
            Map<String, String> authParams = new HashMap<>();
            authProvider.addAuthParams(authParams, path);
            authParams.forEach(params::add);
        }
    }

    /**
     * 记录请求日志
     */
    private void logRequest(String method, String path, String operation, Object params) {
        String op = operation != null ? operation : "API调用";
        log.debug("[{}] {} {} 请求开始: path={}, params={}", serviceName, method, op, path, params);
    }

    /**
     * 记录成功日志
     */
    private void logSuccess(String method, String path, String operation, Object response) {
        String op = operation != null ? operation : "API调用";
        log.debug("[{}] {} {} 请求成功: path={}", serviceName, method, op, path);
    }

    /**
     * 记录错误日志
     */
    private void logError(String method, String path, String operation, Throwable error) {
        String op = operation != null ? operation : "API调用";
        log.error("[{}] {} {} 请求失败: path={}, error={}", serviceName, method, op, path, error.getMessage());
    }

    /**
     * 认证提供者接口
     */
    public interface AuthProvider {

        /**
         * 添加认证参数
         */
        void addAuthParams(Map<String, String> params, String path);
    }

    /**
     * API Key 认证提供者
     */
    public static class ApiKeyAuthProvider implements AuthProvider {

        private final String apiKey;
        private final String keyName;

        public ApiKeyAuthProvider(String apiKey) {
            this(apiKey, "apikey");
        }

        public ApiKeyAuthProvider(String apiKey, String keyName) {
            this.apiKey = apiKey;
            this.keyName = keyName;
        }

        @Override
        public void addAuthParams(Map<String, String> params, String path) {
            params.put(keyName, apiKey);
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));
        }
    }

    /**
     * 签名认证提供者
     */
    public static class SignatureAuthProvider implements AuthProvider {

        private final String appKey;
        private final String secretKey;
        private final String keyName;

        public SignatureAuthProvider(String appKey, String secretKey) {
            this(appKey, secretKey, "appkey");
        }

        public SignatureAuthProvider(String appKey, String secretKey, String keyName) {
            this.appKey = appKey;
            this.secretKey = secretKey;
            this.keyName = keyName;
        }

        @Override
        public void addAuthParams(Map<String, String> params, String path) {
            params.put(keyName, appKey);
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // 生成签名
            String signature = generateSignature(params, path);
            params.put("signature", signature);
        }

        private String generateSignature(Map<String, String> params, String path) {
            StringBuilder signString = new StringBuilder();
            signString.append(path);

            // 按键名排序拼接参数
            params.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .filter(entry -> !"signature".equals(entry.getKey()))
                .forEach(entry -> signString.append(entry.getKey()).append(entry.getValue()));

            signString.append(secretKey);

            // 简化的签名实现
            return Integer.toHexString(signString.toString().hashCode());
        }
    }

    /**
     * 创建实例的工厂方法
     */
    public static WebClientRequestExecutor create(String baseUrl, String serviceName) {
        return new WebClientRequestExecutor(baseUrl, serviceName);
    }

    /**
     * 创建带 API Key 认证的实例
     */
    public static WebClientRequestExecutor createWithApiKey(String baseUrl, String serviceName, String apiKey) {
        return new WebClientRequestExecutor(baseUrl, serviceName, new ApiKeyAuthProvider(apiKey));
    }

    /**
     * 创建带签名认证的实例
     */
    public static WebClientRequestExecutor createWithSignature(String baseUrl, String serviceName, String appKey, String secretKey) {
        return new WebClientRequestExecutor(baseUrl, serviceName, new SignatureAuthProvider(appKey, secretKey));
    }

}