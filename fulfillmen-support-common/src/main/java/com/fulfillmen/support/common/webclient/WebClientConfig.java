/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.webclient;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fulfillmen.starter.core.util.JacksonUtil;
import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import java.time.Duration;
import java.util.concurrent.TimeUnit;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.http.codec.json.Jackson2JsonDecoder;
import org.springframework.http.codec.json.Jackson2JsonEncoder;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;

/**
 * WebClient配置类
 *
 * <p>提供WebClient的配置选项，支持自定义连接池、超时、内存限制等参数。
 * 这个类可以作为各个API模块WebClient配置的基础。</p>
 *
 * <p>特别针对时间格式兼容性进行了优化，支持多种LocalDateTime格式：</p>
 * <ul>
 * <li>ISO 8601 带毫秒格式：yyyy-MM-dd'T'HH:mm:ss.SSS</li>
 * <li>ISO 8601 标准格式：yyyy-MM-dd'T'HH:mm:ss</li>
 * <li>中国标准格式：yyyy-MM-dd HH:mm:ss</li>
 * <li>带毫秒的中国格式：yyyy-MM-dd HH:mm:ss.SSS</li>
 * </ul>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@Slf4j
@Data
public class WebClientConfig {

    /**
     * 连接池名称
     */
    private String connectionProviderName = "default";

    /**
     * 最大连接数
     */
    private int maxConnections = 500;

    /**
     * 最大空闲时间（秒）
     */
    private int maxIdleTimeSeconds = 20;

    /**
     * 连接超时（毫秒）
     */
    private int connectTimeoutMillis = 5000;

    /**
     * 响应超时（秒）
     */
    private int responseTimeoutSeconds = 20;

    /**
     * 读取超时（秒）
     */
    private int readTimeoutSeconds = 30;

    /**
     * 写入超时（秒）
     */
    private int writeTimeoutSeconds = 30;

    /**
     * 最大内存大小（字节）
     */
    private int maxInMemorySize = 16 * 1024 * 1024; // 16MB

    /**
     * 是否启用压缩
     */
    private boolean compression = true;

    /**
     * 是否跟随重定向
     */
    private boolean followRedirect = true;

    /**
     * 创建WebClient.Builder
     *
     * <p>使用当前配置创建WebClient.Builder，可以进一步自定义。</p>
     *
     * @return WebClient.Builder
     */
    public WebClient.Builder createBuilder() {
        // 创建HTTP客户端
        HttpClient httpClient = createHttpClient();

        // 创建交换策略
        ExchangeStrategies strategies = createExchangeStrategies();

        // 创建WebClient.Builder
        return WebClient.builder()
            .clientConnector(new ReactorClientHttpConnector(httpClient))
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
            .exchangeStrategies(strategies)
            .filter(WebClientBuilder.logRequest())
            .filter(WebClientBuilder.logResponse());
    }

    /**
     * 创建HTTP客户端
     *
     * @return HttpClient
     */
    private HttpClient createHttpClient() {
        // 配置HTTP连接池
        ConnectionProvider provider = ConnectionProvider.builder(connectionProviderName)
            .maxConnections(maxConnections)
            .maxIdleTime(Duration.ofSeconds(maxIdleTimeSeconds))
            .build();

        // 配置HTTP客户端
        return HttpClient.create(provider)
            .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, connectTimeoutMillis)
            .responseTimeout(Duration.ofSeconds(responseTimeoutSeconds))
            .doOnConnected(conn -> conn
                .addHandlerLast(new ReadTimeoutHandler(readTimeoutSeconds, TimeUnit.SECONDS))
                .addHandlerLast(new WriteTimeoutHandler(writeTimeoutSeconds, TimeUnit.SECONDS)))
            .compress(compression)
            .followRedirect(followRedirect);
    }

    /**
     * 创建交换策略
     *
     * @return ExchangeStrategies
     */
    private ExchangeStrategies createExchangeStrategies() {
        // 基于全局ObjectMapper创建副本，避免影响其他模块
        ObjectMapper objectMapper = JacksonUtil.getObjectMapper();
        return ExchangeStrategies.builder()
            .codecs(configurer -> {
                configurer.defaultCodecs().jackson2JsonDecoder(new Jackson2JsonDecoder(objectMapper));
                configurer.defaultCodecs().jackson2JsonEncoder(new Jackson2JsonEncoder(objectMapper));
                configurer.defaultCodecs().maxInMemorySize(maxInMemorySize);
            })
            .build();
    }
}
