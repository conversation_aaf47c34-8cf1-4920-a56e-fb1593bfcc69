/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.base;

import com.fulfillmen.starter.core.util.JacksonUtil;
import java.util.Map;

/**
 * 请求基接口 - 简化版本
 * 
 * <p>支持 record 类和 builder 构建的对象。参数校验为可选项。</p>
 *
 * <AUTHOR>
 * @created 2025-07-29
 */
public interface BaseRequestRecord {

    /**
     * 转换为请求参数
     *
     * @return 请求参数Map
     */
    Map<String, String> toParams();

    /**
     * 参数校验（可选实现）
     * 
     * <p>如果不需要校验，可以留空实现</p>
     */
    default void requireParams() {
        // 默认空实现，用户可选择是否覆盖
    }

    /**
     * 将对象转换为JSON字符串
     *
     * @return JSON字符串
     */
    default String toJsonString() {
        return JacksonUtil.toJsonString(this);
    }

}