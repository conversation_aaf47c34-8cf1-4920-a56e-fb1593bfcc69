/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.http;

import com.fulfillmen.support.common.exception.ApiCallException;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.MultiValueMap;
import reactor.core.publisher.Mono;

/**
 * API请求执行器
 * 
 * <p>提供通用的API请求执行模板，包含参数验证、请求执行、日志记录和异常处理。
 * 这是一个简化的请求执行器，不包含复杂的认证和签名逻辑，
 * 让各个具体的API模块保持自己的特定实现。</p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@Slf4j
public final class ApiRequestExecutor {

    private ApiRequestExecutor() {
        // 私有构造函数，防止实例化
    }

    /**
     * 执行API请求
     * 
     * <p>这是最基础的请求执行方法，只提供日志记录和异常处理。
     * 适用于已经完成参数构建的API调用场景。</p>
     *
     * @param apiCall       API调用函数
     * @param params        请求参数
     * @param serviceName   服务名称（用于日志）
     * @param operationName 操作名称（用于日志）
     * @param <T>           响应类型
     * @return API响应
     */
    public static <T> Mono<T> execute(
        Function<MultiValueMap<String, String>, Mono<T>> apiCall,
        MultiValueMap<String, String> params,
        String serviceName,
        String operationName
    ) {
        try {
            log.debug("[{}] {} 请求参数: {}", serviceName, operationName, params);

            return apiCall.apply(params)
                .doOnSubscribe(subscription -> log
                    .debug("[{}] {} 请求开始", serviceName, operationName))
                .doOnSuccess(response -> log
                    .info("[{}] {} 请求成功: response={}", serviceName, operationName, response))
                .doOnError(error -> log
                    .error("[{}] {} 请求失败: error={}", serviceName, operationName, error.getMessage()))
                .onErrorMap(e -> new ApiCallException(serviceName, operationName, e.getMessage(), e));
        } catch (Exception e) {
            log.error("[{}] {} 请求异常: error={}", serviceName, operationName, e.getMessage(), e);
            return Mono.error(new ApiCallException(serviceName, operationName, e.getMessage(), e));
        }
    }

    /**
     * 执行带参数验证的API请求
     * 
     * <p>在执行API调用前先进行参数验证，适用于需要验证请求参数的场景。
     * 验证失败时会抛出相应的异常。</p>
     *
     * @param validator     参数验证器
     * @param apiCall       API调用函数
     * @param params        请求参数
     * @param serviceName   服务名称（用于日志）
     * @param operationName 操作名称（用于日志）
     * @param <T>           响应类型
     * @return API响应
     */
    public static <T> Mono<T> executeWithValidation(
        Runnable validator,
        Function<MultiValueMap<String, String>, Mono<T>> apiCall,
        MultiValueMap<String, String> params,
        String serviceName,
        String operationName
    ) {
        try {
            // 执行参数验证
            validator.run();

            // 执行API调用
            return execute(apiCall, params, serviceName, operationName);
        } catch (Exception e) {
            log.warn("[{}] {} 参数验证失败: error={}", serviceName, operationName, e.getMessage());
            return Mono.error(e);
        }
    }

    /**
     * 执行带请求对象的API请求
     * 
     * <p>适用于有请求对象的API调用场景，会在日志中记录请求对象信息。
     * 这个方法提供了更详细的日志记录。</p>
     *
     * @param apiCall       API调用函数
     * @param params        请求参数
     * @param request       请求对象（用于日志）
     * @param serviceName   服务名称（用于日志）
     * @param operationName 操作名称（用于日志）
     * @param <T>           响应类型
     * @return API响应
     */
    public static <T> Mono<T> executeWithRequest(
        Function<MultiValueMap<String, String>, Mono<T>> apiCall,
        MultiValueMap<String, String> params,
        Object request,
        String serviceName,
        String operationName
    ) {
        try {
            log.debug("[{}] {} 请求参数: {}", serviceName, operationName, params);

            return apiCall.apply(params)
                .doOnSubscribe(subscription -> log
                    .debug("[{}] {} 请求开始: request={}", serviceName, operationName, request))
                .doOnSuccess(response -> log
                    .info("[{}] {} 请求成功: request={}, response={}", serviceName, operationName, request, response))
                .doOnError(error -> log
                    .error("[{}] {} 请求失败: request={}, error={}", serviceName, operationName, request, error.getMessage()))
                .onErrorMap(e -> new ApiCallException(serviceName, operationName, e.getMessage(), e));
        } catch (Exception e) {
            log.error("[{}] {} 请求异常: request={}, error={}", serviceName, operationName, request, e.getMessage(), e);
            return Mono.error(new ApiCallException(serviceName, operationName, e.getMessage(), e));
        }
    }
}
