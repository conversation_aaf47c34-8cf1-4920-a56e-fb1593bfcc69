/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.example;

import com.fulfillmen.support.common.http.WebClientRequestExecutor;
import java.util.Map;
import lombok.Data;
import reactor.core.publisher.Mono;

/**
 * 简单的 WebClient 请求执行器使用示例
 * 
 * <p>展示如何使用基于 WebClient 的通用请求工具。</p>
 *
 * <AUTHOR>
 * @created 2025-07-29
 */
public class SimpleWebClientExample {

    /**
     * 简单的响应对象
     */
    @Data
    public static class ApiResponse {

        private String result;
        private int code;
        private String message;
    }

    /**
     * 商品对象
     */
    @Data
    public static class Product {

        private String id;
        private String name;
        private String category;
        private double price;
    }

    /**
     * 基本使用示例 - 无认证
     */
    public void basicExample() {
        // 创建执行器
        WebClientRequestExecutor executor = WebClientRequestExecutor.create(
            "https://api.example.com",
            "商品服务"
        );

        // GET 请求
        Map<String, String> params = Map.of(
            "keyword", "手机",
            "category", "电子产品"
        );

        Mono<ApiResponse> getResult = executor.get("/api/search", params, ApiResponse.class, "商品搜索");

        // POST 表单请求
        Map<String, String> formData = Map.of(
            "name", "iPhone 15",
            "price", "5999"
        );

        Mono<ApiResponse> postResult = executor.postForm("/api/product", formData, ApiResponse.class, "创建商品");

        // POST JSON 请求
        Product product = new Product();
        product.setName("iPhone 15");
        product.setCategory("手机");
        product.setPrice(5999.0);

        Mono<ApiResponse> jsonResult = executor.postJson("/api/product/json", product, ApiResponse.class, "JSON创建商品");

        // 处理结果
        getResult.subscribe(
            response -> System.out.println("搜索成功: " + response.getResult()),
            error -> System.err.println("搜索失败: " + error.getMessage())
        );
    }

    /**
     * API Key 认证示例
     */
    public void apiKeyExample() {
        // 创建带 API Key 认证的执行器
        WebClientRequestExecutor executor = WebClientRequestExecutor.createWithApiKey(
            "https://api.external.com",
            "外部服务",
            "your-api-key-here"
        );

        Map<String, String> params = Map.of("query", "test");

        Mono<ApiResponse> result = executor.get("/api/data", params, ApiResponse.class, "获取数据");

        result.subscribe(
            response -> System.out.println("API Key 请求成功: " + response.getResult()),
            error -> System.err.println("API Key 请求失败: " + error.getMessage())
        );
    }

    /**
     * 签名认证示例
     */
    public void signatureExample() {
        // 创建带签名认证的执行器
        WebClientRequestExecutor executor = WebClientRequestExecutor.createWithSignature(
            "https://api.secure.com",
            "安全服务",
            "app-key",
            "secret-key"
        );

        Map<String, String> params = Map.of(
            "userId", "12345",
            "action", "profile"
        );

        Mono<ApiResponse> result = executor.get("/api/user/profile", params, ApiResponse.class, "获取用户信息");

        result.subscribe(
            response -> System.out.println("签名请求成功: " + response.getResult()),
            error -> System.err.println("签名请求失败: " + error.getMessage())
        );
    }

    /**
     * Alibaba API 示例
     */
    public void alibabaExample() {
        // 创建 Alibaba 专用的签名认证
        WebClientRequestExecutor executor = new WebClientRequestExecutor(
            "https://gw.open.1688.com",
            "阿里巴巴API",
            new AlibabaAuthProvider("your-app-key", "your-secret-key", "your-access-token")
        );

        Map<String, String> params = Map.of(
            "keywords", "手机壳",
            "pageSize", "20"
        );

        Mono<ApiResponse> result = executor.postForm("/openapi/param2/1/com.alibaba.product/alibaba.cross.search",
            params, ApiResponse.class, "商品搜索");

        result.subscribe(
            response -> System.out.println("阿里巴巴搜索成功: " + response.getResult()),
            error -> System.err.println("阿里巴巴搜索失败: " + error.getMessage())
        );
    }

    /**
     * 高级自定义请求示例
     */
    public void customRequestExample() {
        WebClientRequestExecutor executor = WebClientRequestExecutor.create(
            "https://api.custom.com",
            "自定义服务"
        );

        // 方式1: 使用 JSON 请求
        Map<String, Object> customData = Map.of(
            "key", "value",
            "action", "custom"
        );
        Mono<ApiResponse> result1 = executor.postJson("/api/custom", customData, ApiResponse.class, "自定义JSON请求");

        // 方式2: 直接使用 WebClient 进行复杂请求
        Mono<ApiResponse> result2 = executor.getWebClient()
            .post()
            .uri("/api/advanced")
            .header("Custom-Header", "custom-value")
            .bodyValue(customData)
            .retrieve()
            .bodyToMono(ApiResponse.class);

        result1.subscribe(
            response -> System.out.println("自定义请求成功: " + response.getResult()),
            error -> System.err.println("自定义请求失败: " + error.getMessage())
        );
    }

    /**
     * Alibaba 专用认证提供者
     */
    public static class AlibabaAuthProvider implements WebClientRequestExecutor.AuthProvider {

        private final String appKey;
        private final String secretKey;
        private final String accessToken;

        public AlibabaAuthProvider(String appKey, String secretKey, String accessToken) {
            this.appKey = appKey;
            this.secretKey = secretKey;
            this.accessToken = accessToken;
        }

        @Override
        public void addAuthParams(Map<String, String> params, String path) {
            params.put("appkey", appKey);
            params.put("_aop_timestamp", String.valueOf(System.currentTimeMillis()));
            if (accessToken != null) {
                params.put("access_token", accessToken);
            }

            // 生成 Alibaba 签名
            String signature = generateAlibabaSignature(params, path);
            params.put("_aop_signature", signature);
        }

        private String generateAlibabaSignature(Map<String, String> params, String path) {
            StringBuilder signString = new StringBuilder();
            signString.append(path);

            // 按参数名排序后拼接（排除签名字段）
            params.entrySet().stream()
                .filter(entry -> !entry.getKey().startsWith("_aop_signature"))
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> signString.append(entry.getKey()).append(entry.getValue()));

            signString.append(secretKey);

            // 实际应该使用 MD5 等加密算法
            return "ALIBABA_" + Integer.toHexString(signString.toString().hashCode()).toUpperCase();
        }
    }

}