/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.webclient;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

/**
 * WebClient构建工具类
 *
 * <p>提供创建和配置WebClient的工具方法。
 * 这个类是WebClient配置的入口点，提供了默认配置和自定义选项。</p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@Slf4j
public final class WebClientBuilder {

    private WebClientBuilder() {
        // 私有构造函数，防止实例化
    }

    /**
     * 获取默认配置的WebClient.Builder
     *
     * <p>使用默认配置创建WebClient.Builder，包括：
     * <ul>
     * <li>HTTP连接池配置</li>
     * <li>超时设置</li>
     * <li>默认请求头</li>
     * <li>日志记录过滤器</li>
     * </ul>
     * </p>
     *
     * @return WebClient.Builder
     */
    public static WebClient.Builder getWebClientBuilder() {
        return new WebClientConfig().createBuilder();
    }

    /**
     * 使用自定义配置创建WebClient.Builder
     *
     * <p>使用提供的配置创建WebClient.Builder，允许自定义连接池、超时等参数。</p>
     *
     * @param config WebClient配置
     * @return WebClient.Builder
     */
    public static WebClient.Builder getWebClientBuilder(WebClientConfig config) {
        return config.createBuilder();
    }

    /**
     * 创建带基础URL的WebClient
     *
     * <p>使用默认配置和提供的基础URL创建WebClient。</p>
     *
     * @param baseUrl 基础URL
     * @return WebClient
     */
    public static WebClient createWebClient(String baseUrl) {
        return getWebClientBuilder()
            .baseUrl(baseUrl)
            .build();
    }

    /**
     * 创建带基础URL和默认请求头的WebClient
     *
     * <p>使用默认配置、提供的基础URL和默认请求头创建WebClient。</p>
     *
     * @param baseUrl 基础URL
     * @param headers 默认请求头
     * @return WebClient
     */
    public static WebClient createWebClient(String baseUrl, HttpHeaders headers) {
        WebClient.Builder builder = getWebClientBuilder()
            .baseUrl(baseUrl);

        headers.forEach((name, values) -> values.forEach(value -> builder.defaultHeader(name, value)));

        return builder.build();
    }

    /**
     * 请求日志记录过滤器
     *
     * <p>记录HTTP请求的详细信息，包括URL、方法和请求头。</p>
     *
     * @return 请求日志过滤器
     */
    public static ExchangeFilterFunction logRequest() {
        return ExchangeFilterFunction.ofRequestProcessor(clientRequest -> {
            if (log.isDebugEnabled()) {
                String sb = """
                    [请求信息]
                    URL: %s
                    Method: %s
                    Headers: %s
                    """.formatted(clientRequest.url(), clientRequest.method(), clientRequest.headers());
                log.debug(sb);
            }
            return Mono.just(clientRequest);
        });
    }

    /**
     * 响应日志记录过滤器
     *
     * <p>记录HTTP响应的详细信息，包括状态码、响应头和响应体。</p>
     *
     * @return 响应日志过滤器
     */
    public static ExchangeFilterFunction logResponse() {
        return ExchangeFilterFunction.ofResponseProcessor(clientResponse -> {
            if (log.isDebugEnabled()) {
                HttpStatusCode httpStatusCode = clientResponse.statusCode();
                HttpHeaders httpHeaders = clientResponse.headers().asHttpHeaders();
                return clientResponse.bodyToMono(String.class).map(body -> {
                    String responseBody = """
                        [响应信息]
                        Status: %s
                        Headers: %s
                        Body: %s
                        """.formatted(httpStatusCode, httpHeaders, body);
                    log.debug(responseBody);
                    return clientResponse.mutate().body(body).build();
                });
            }
            return Mono.just(clientResponse);
        });
    }

    /**
     * 创建JSON内容类型的默认请求头
     *
     * <p>创建包含JSON内容类型的默认请求头。</p>
     *
     * @return 默认请求头
     */
    public static HttpHeaders createJsonHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(java.util.Collections.singletonList(MediaType.APPLICATION_JSON));
        return headers;
    }
}
