/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.webclient;

import org.junit.jupiter.api.Test;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;

import static org.junit.jupiter.api.Assertions.*;

/**
 * WebClientBuilder 测试类
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
class WebClientBuilderTest {

    @Test
    void testGetWebClientBuilder() {
        // 测试获取默认配置的WebClient.Builder
        WebClient.Builder builder = WebClientBuilder.getWebClientBuilder();
        assertNotNull(builder);

        // 构建WebClient并验证
        WebClient webClient = builder.build();
        assertNotNull(webClient);
    }

    @Test
    void testGetWebClientBuilderWithConfig() {
        // 测试使用自定义配置创建WebClient.Builder
        WebClientConfig config = new WebClientConfig();
        config.setMaxConnections(100);
        config.setConnectTimeoutMillis(3000);

        WebClient.Builder builder = WebClientBuilder.getWebClientBuilder(config);
        assertNotNull(builder);

        // 构建WebClient并验证
        WebClient webClient = builder.build();
        assertNotNull(webClient);
    }

    @Test
    void testCreateWebClientWithBaseUrl() {
        // 测试创建带基础URL的WebClient
        String baseUrl = "https://api.example.com";
        WebClient webClient = WebClientBuilder.createWebClient(baseUrl);

        assertNotNull(webClient);
    }

    @Test
    void testCreateWebClientWithBaseUrlAndHeaders() {
        // 测试创建带基础URL和默认请求头的WebClient
        String baseUrl = "https://api.example.com";
        HttpHeaders headers = new HttpHeaders();
        headers.set("X-API-Key", "test-key");
        headers.set("X-Client-Version", "1.0.0");

        WebClient webClient = WebClientBuilder.createWebClient(baseUrl, headers);

        assertNotNull(webClient);
    }

    @Test
    void testCreateJsonHeaders() {
        // 测试创建JSON内容类型的默认请求头
        HttpHeaders headers = WebClientBuilder.createJsonHeaders();

        assertNotNull(headers);
        assertEquals(MediaType.APPLICATION_JSON, headers.getContentType());
        assertTrue(headers.getAccept().contains(MediaType.APPLICATION_JSON));
    }

    @Test
    void testLogRequestFilter() {
        // 测试请求日志过滤器不为null
        assertNotNull(WebClientBuilder.logRequest());
    }

    @Test
    void testLogResponseFilter() {
        // 测试响应日志过滤器不为null
        assertNotNull(WebClientBuilder.logResponse());
    }
}
