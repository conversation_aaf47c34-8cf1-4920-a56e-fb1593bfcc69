/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.webclient;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fulfillmen.starter.core.util.JacksonUtil;
import java.time.LocalDateTime;
import lombok.Data;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * WebClientConfig 测试类
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
class WebClientConfigTest {

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = JacksonUtil.getObjectMapper();
    }

    @Test
    @DisplayName("测试多种时间格式的反序列化")
    void testMultipleTimeFormats() throws Exception {
        // 测试数据：JSON时间值 -> 期望的LocalDateTime
        Object[][] testCases = {
            {"\"2025-07-24T16:37:32.957\"", LocalDateTime.of(2025, 7, 24, 16, 37, 32, 957000000)}, // ISO 8601 带3位毫秒
            {"\"2025-07-24T18:51:25.94\"", LocalDateTime.of(2025, 7, 24, 18, 51, 25, 940000000)},  // ISO 8601 带2位毫秒
            {"\"2025-07-24T16:37:32.5\"", LocalDateTime.of(2025, 7, 24, 16, 37, 32, 500000000)},   // ISO 8601 带1位毫秒
            {"\"2025-07-24T16:37:32\"", LocalDateTime.of(2025, 7, 24, 16, 37, 32)},                // ISO 8601 标准格式
            {"\"2025-07-24 16:37:32.957\"", LocalDateTime.of(2025, 7, 24, 16, 37, 32, 957000000)}, // 中国格式带毫秒
            {"\"2025-07-24 16:37:32\"", LocalDateTime.of(2025, 7, 24, 16, 37, 32)},                // 中国标准格式
            {"\"2025/07/24 16:37:32\"", LocalDateTime.of(2025, 7, 24, 16, 37, 32)},                // 斜杠分隔格式
            {"\"20250724163732\"", LocalDateTime.of(2025, 7, 24, 16, 37, 32)}                      // 紧凑格式
        };

        for (Object[] testCase : testCases) {
            String jsonTimeValue = (String) testCase[0];
            LocalDateTime expectedTime = (LocalDateTime) testCase[1];

            // 构造JSON字符串
            String json = String.format("{\"createTime\":%s}", jsonTimeValue);

            // 反序列化
            TestTimeObject result = objectMapper.readValue(json, TestTimeObject.class);

            // 验证结果
            assertNotNull(result, "反序列化结果不应为null，JSON: " + json);
            assertNotNull(result.getCreateTime(), "时间字段不应为null，JSON: " + json);
            assertEquals(expectedTime, result.getCreateTime(),
                "时间解析结果不匹配，JSON: " + json + ", 期望: " + expectedTime + ", 实际: " + result.getCreateTime());
        }
    }

    @Test
    @DisplayName("测试原始WMS API响应格式")
    void testWmsApiResponseFormat() throws Exception {
        String wmsApiJson = """
            {
                "createTime": "2025-07-24T16:37:32.957",
                "paymentTime": "2025-07-24T18:51:25.94",
                "shippingTime": "1900-01-01T00:00:00",
                "completeTime": "1900-01-01T00:00:00"
            }
            """;

        TestWmsOrder result = objectMapper.readValue(wmsApiJson, TestWmsOrder.class);

        assertNotNull(result);
        assertNotNull(result.getCreateTime());
        assertNotNull(result.getPaymentTime());
        assertNotNull(result.getShippingTime());
        assertNotNull(result.getCompleteTime());

        // 验证具体时间值
        assertEquals(LocalDateTime.of(2025, 7, 24, 16, 37, 32, 957000000), result.getCreateTime());
        assertEquals(LocalDateTime.of(2025, 7, 24, 18, 51, 25, 940000000), result.getPaymentTime());
    }

    @Test
    @DisplayName("测试无效时间格式处理")
    void testInvalidTimeFormat() throws Exception {
        String json = "{\"createTime\":\"invalid-time-format\"}";

        TestTimeObject result = objectMapper.readValue(json, TestTimeObject.class);

        assertNotNull(result);
        assertNull(result.getCreateTime()); // 无效格式应该返回null
    }

    @Test
    @DisplayName("测试空时间值处理")
    void testNullAndEmptyTimeValues() throws Exception {
        // 测试null值
        String nullJson = "{\"createTime\":null}";
        TestTimeObject nullResult = objectMapper.readValue(nullJson, TestTimeObject.class);
        assertNull(nullResult.getCreateTime());

        // 测试空字符串
        String emptyJson = "{\"createTime\":\"\"}";
        TestTimeObject emptyResult = objectMapper.readValue(emptyJson, TestTimeObject.class);
        assertNull(emptyResult.getCreateTime());
    }

    @Test
    @DisplayName("测试WebClientConfig的创建")
    void testWebClientConfigCreation() {
        WebClientConfig config = new WebClientConfig();

        assertNotNull(config);
        assertEquals("default", config.getConnectionProviderName());
        assertEquals(500, config.getMaxConnections());
        assertTrue(config.isCompression());
        assertTrue(config.isFollowRedirect());
    }

    @Test
    @DisplayName("测试WebClient Builder创建")
    void testWebClientBuilderCreation() {
        WebClientConfig config = new WebClientConfig();

        assertDoesNotThrow(() -> {
            var builder = config.createBuilder();
            assertNotNull(builder);
        });
    }

    /**
     * 测试用的时间对象
     */
    @Data
    static class TestTimeObject {

        private LocalDateTime createTime;
    }

    /**
     * 测试用的WMS订单对象
     */
    @Data
    static class TestWmsOrder {

        private LocalDateTime createTime;
        private LocalDateTime paymentTime;
        private LocalDateTime shippingTime;
        private LocalDateTime completeTime;
    }
}