/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.example;

import com.fulfillmen.support.common.http.ApiRequestExecutor;
import com.fulfillmen.support.common.webclient.WebClientBuilder;
import com.fulfillmen.support.common.webclient.WebClientConfig;
import java.util.Map;
import org.springframework.http.HttpHeaders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

/**
 * 使用示例：展示如何在具体的API模块中使用通用组件
 * 
 * <p>这个示例展示了如何使用 fulfillmen-support-common 模块提供的通用组件
 * 来构建具体的API客户端，如Alibaba或WMS模块。</p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
public class ExampleApiUsage {

    /**
     * 示例1：使用默认配置创建WebClient
     */
    public void example1_DefaultWebClient() {
        // 使用默认配置创建WebClient
        WebClient webClient = WebClientBuilder.createWebClient("https://api.example.com");

        // 现在可以使用webClient进行API调用
        // webClient.get().uri("/users").retrieve().bodyToMono(String.class);
    }

    /**
     * 示例2：使用自定义配置创建WebClient
     */
    public void example2_CustomWebClient() {
        // 创建自定义配置
        WebClientConfig config = new WebClientConfig();
        config.setMaxConnections(100);
        config.setConnectTimeoutMillis(3000);
        config.setResponseTimeoutSeconds(10);

        // 使用自定义配置创建WebClient
        WebClient webClient = WebClientBuilder.getWebClientBuilder(config)
            .baseUrl("https://api.example.com")
            .build();
    }

    /**
     * 示例3：创建带认证头的WebClient（适用于WMS等需要API Key的场景）
     */
    public void example3_WebClientWithAuth() {
        // 创建包含认证信息的请求头
        HttpHeaders headers = WebClientBuilder.createJsonHeaders();
        headers.set("X-API-Key", "your-api-key");
        headers.set("X-Timestamp", String.valueOf(System.currentTimeMillis()));

        // 创建WebClient
        WebClient webClient = WebClientBuilder.createWebClient("https://wms.example.com", headers);
    }

    /**
     * 示例4：使用ApiRequestExecutor执行简单的API请求
     */
    public Mono<String> example4_SimpleApiCall() {
        WebClient webClient = WebClientBuilder.createWebClient("https://api.example.com");

        // 准备请求参数
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("param1", "value1");
        params.add("param2", "value2");

        // 使用ApiRequestExecutor执行请求
        return ApiRequestExecutor.execute(
            formParams -> webClient.post()
                .uri("/api/endpoint")
                .bodyValue(formParams)
                .retrieve()
                .bodyToMono(String.class),
            params,
            "示例服务",
            "示例操作"
        );
    }

    /**
     * 示例5：使用ApiRequestExecutor执行带参数验证的API请求
     */
    public Mono<String> example5_ApiCallWithValidation() {
        WebClient webClient = WebClientBuilder.createWebClient("https://api.example.com");

        // 准备请求参数
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("userId", "12345");

        // 定义参数验证逻辑
        Runnable validator = () -> {
            if (!params.containsKey("userId") || params.getFirst("userId") == null) {
                throw new IllegalArgumentException("用户ID不能为空");
            }
        };

        // 使用ApiRequestExecutor执行带验证的请求
        return ApiRequestExecutor.executeWithValidation(
            validator,
            formParams -> webClient.get()
                .uri(uriBuilder -> uriBuilder.path("/users/{userId}")
                    .build(formParams.getFirst("userId")))
                .retrieve()
                .bodyToMono(String.class),
            params,
            "用户服务",
            "获取用户信息"
        );
    }

    /**
     * 示例6：模拟Alibaba模块的使用方式
     */
    public static class AlibabaApiExample {

        private final WebClient webClient;
        private final String appKey;
        private final String secretKey;
        private final String accessToken;

        public AlibabaApiExample(String baseUrl, String appKey, String secretKey, String accessToken) {
            this.webClient = WebClientBuilder.createWebClient(baseUrl);
            this.appKey = appKey;
            this.secretKey = secretKey;
            this.accessToken = accessToken;
        }

        public Mono<String> searchGoods(String keyword) {
            // 构建阿里巴巴特定的请求参数
            MultiValueMap<String, String> params = buildAlibabaParams();
            params.add("keyword", keyword);

            // 使用通用的请求执行器
            return ApiRequestExecutor.executeWithRequest(
                formParams -> webClient.post()
                    .uri("/api/goods/search")
                    .bodyValue(formParams)
                    .retrieve()
                    .bodyToMono(String.class),
                params,
                Map.of("keyword", keyword), // 请求对象用于日志
                "阿里巴巴商品服务",
                "商品搜索"
            );
        }

        private MultiValueMap<String, String> buildAlibabaParams() {
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("appKey", appKey);
            params.add("timestamp", String.valueOf(System.currentTimeMillis()));
            params.add("accessToken", accessToken);
            // 这里可以添加阿里巴巴特定的签名逻辑
            params.add("signature", generateSignature());
            return params;
        }

        private String generateSignature() {
            // 阿里巴巴特定的签名生成逻辑
            return "generated_signature";
        }
    }

    /**
     * 示例7：模拟WMS模块的使用方式
     */
    public static class WmsApiExample {

        private final WebClient webClient;

        public WmsApiExample(String baseUrl, String apiKey) {
            // 创建包含WMS认证信息的请求头
            HttpHeaders headers = WebClientBuilder.createJsonHeaders();
            headers.set("X-API-Key", apiKey);

            this.webClient = WebClientBuilder.createWebClient(baseUrl, headers);
        }

        public Mono<String> createOrder(Map<String, Object> orderData) {
            // WMS不需要复杂的签名，直接使用JSON请求
            return ApiRequestExecutor.execute(
                params -> webClient.post()
                    .uri("/api/orders")
                    .bodyValue(orderData)
                    .retrieve()
                    .bodyToMono(String.class),
                new LinkedMultiValueMap<>(), // WMS使用JSON body，不需要form参数
                "WMS服务",
                "创建订单"
            );
        }
    }
}
