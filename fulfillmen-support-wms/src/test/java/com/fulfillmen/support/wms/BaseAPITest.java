/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms;

import com.fulfillmen.support.wms.autoconfigure.WmsAutoConfiguration;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import java.util.concurrent.atomic.AtomicLong;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;

/**
 * API测试基类 提供通用的测试配置和指标记录功能
 *
 * <AUTHOR>
 * @created 2025-01-08
 * @since 1.0.0
 */
@Slf4j
@SpringBootTest(classes = BaseAPITest.Config.class)
public abstract class BaseAPITest {

    @ComponentScan(basePackages = "com.fulfillmen.support.wms")
    @Import(WmsAutoConfiguration.class)
    static class Config {

        @Bean
        @ConditionalOnMissingBean
        public MeterRegistry meterRegistry() {
            return new SimpleMeterRegistry();
        }

    }

    @SuppressWarnings("unused")
    private static final String METRIC_PREFIX = "wms.api.";
    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    @Autowired
    protected MeterRegistry meterRegistry;

    // 性能指标统计
    private final AtomicLong totalRequests = new AtomicLong(0);
    private final AtomicLong successRequests = new AtomicLong(0);
    private final AtomicLong responseTime = new AtomicLong(0);

    /**
     * 记录API调用指标
     *
     * @param apiName         API名称
     * @param startTimeMillis 开始时间（毫秒）
     * @param success         是否调用成功
     */
    protected void recordMetrics(String apiName, long startTimeMillis, boolean success) {
        long duration = System.currentTimeMillis() - startTimeMillis;
        totalRequests.incrementAndGet();
        if (success) {
            successRequests.incrementAndGet();
        }
        responseTime.set(duration);
    }

    /**
     * 打印API调用指标
     *
     * @param apiName API名称
     */
    protected void logMetrics(String apiName) {
        log.info("[{}] {}{}{}", apiName, LOG_SEPARATOR, "调用结果", LOG_SEPARATOR);
        log.info("[{}] {}调用状态: {}", apiName, LOG_ITEM, successRequests.get() > 0 ? "成功" : "失败");
        log.info("[{}] {}响应时间: {} ms", apiName, LOG_ITEM, responseTime.get());
        log.info("[{}] {}", apiName, LOG_SEPARATOR);
    }

    /**
     * 重置指标计数器
     */
    protected void resetMetrics() {
        totalRequests.set(0);
        successRequests.set(0);
        responseTime.set(0);
    }
}