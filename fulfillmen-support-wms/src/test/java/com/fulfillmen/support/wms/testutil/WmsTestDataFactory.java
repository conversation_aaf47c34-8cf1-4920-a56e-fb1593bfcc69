/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.testutil;

import com.fulfillmen.support.wms.dto.common.WmsPageDTO;
import com.fulfillmen.support.wms.dto.enums.WmsAsnShipmentTypeEnums;
import com.fulfillmen.support.wms.dto.enums.WmsOperationTypeEnum;
import com.fulfillmen.support.wms.dto.request.WmsAccountChargeV2Req;
import com.fulfillmen.support.wms.dto.request.WmsCreateInboundOrderReq;
import com.fulfillmen.support.wms.dto.request.WmsInboundOrderUpdateReq;
import com.fulfillmen.support.wms.dto.request.WmsInboundQueryOrderPageReq;
import com.fulfillmen.support.wms.dto.response.WmsAccountChargeInfoRes;
import com.fulfillmen.support.wms.dto.response.WmsCreateInboundOrderRes;
import com.fulfillmen.support.wms.dto.response.WmsInboundInfoRes;
import com.fulfillmen.support.wms.dto.response.WmsInboundQueryPageRes;
import com.fulfillmen.support.wms.dto.response.WmsInboundUpdateRes;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * WMS测试数据工厂类 用于创建各种测试场景下的请求和响应数据
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class WmsTestDataFactory {

    // ==================== 账户费用管理测试数据 ====================

    /**
     * 创建账户费用退款请求
     */
    public static WmsAccountChargeV2Req createRefundRequest(String cusCode, String orderNo, Double refundAmount) {
        return WmsAccountChargeV2Req.builder()
            .cusCode(cusCode)
            .orderNo(orderNo)
            .operationType(WmsOperationTypeEnum.REFUND)
            .refundAmount(BigDecimal.valueOf(refundAmount))
            .refundReason("测试退款")
            .createUser("testUser")
            .remark("单元测试退款")
            .build();
    }

    /**
     * 创建账户费用扣减请求
     */
    public static WmsAccountChargeV2Req createChargeRequest(String cusCode, String orderNo, Double totalAmount) {
        return WmsAccountChargeV2Req.builder()
            .cusCode(cusCode)
            .orderNo(orderNo)
            .operationType(WmsOperationTypeEnum.CHARGE)
            .productAmount(BigDecimal.valueOf(totalAmount * 0.7))
            .serviceFee(BigDecimal.valueOf(totalAmount * 0.2))
            .shippingFee(BigDecimal.valueOf(totalAmount * 0.1))
            .originalTotalPrice(BigDecimal.valueOf(totalAmount))
            .createUser("testUser")
            .remark("单元测试扣减")
            .build();
    }

    /**
     * 创建账户费用操作响应
     */
    public static WmsAccountChargeInfoRes createAccountChargeResponse(String cusCode, String orderNo,
        String paymentNumber, BigDecimal balanceAfter,
        Integer paymentDetailId) {
        WmsAccountChargeInfoRes response = new WmsAccountChargeInfoRes();
        response.setCusCode(cusCode);
        response.setOrderNo(orderNo);
        response.setPaymentNumber(paymentNumber);
        response.setBalanceAfterCharge(balanceAfter);
        response.setPaymentDetailId(paymentDetailId);
        return response;
    }

    // ==================== 入库管理测试数据 ====================

    /**
     * 创建入库单创建请求
     */
    public static WmsCreateInboundOrderReq createInboundOrderRequest(String cusCode, String purchaseNo) {
        return WmsCreateInboundOrderReq.builder()
            .cusCode(cusCode)
            .purchaseNo(purchaseNo)
            .trackingNo("SF" + System.currentTimeMillis())
            .expressCompany("顺丰速运")
            .warehouseAddress("深圳仓库")
            .enchaseNum(10)
            .shipmentType(WmsAsnShipmentTypeEnums.ONE_PIECE_SALE)
            .remark("测试创建入库单")
            .build();
    }

    /**
     * 创建入库单创建响应
     */
    public static WmsCreateInboundOrderRes createInboundOrderResponse(String asnNumber, String purchaseNo) {
        WmsCreateInboundOrderRes response = new WmsCreateInboundOrderRes();
        response.setAsnNumber(asnNumber);
        response.setPurchaseNo(purchaseNo);
        response.setCusCode("TEST001");
        response.setTrackingNo("SF1234567890");
        return response;
    }

    /**
     * 创建入库单更新请求
     */
    public static WmsInboundOrderUpdateReq createInboundUpdateRequest(String cusCode, String asnNumber) {
        return WmsInboundOrderUpdateReq.builder()
            .cusCode(cusCode)
            .asnNumber(asnNumber)
            .trackingNo("DP" + System.currentTimeMillis())
            .expressCompany("德邦物流")
            .warehouseAddress("深圳仓库")
            .enchaseNum(15)
            .remark("测试更新入库单")
            .build();
    }

    /**
     * 创建入库单更新响应
     */
    public static WmsInboundUpdateRes createInboundUpdateResponse(String asnNumber, String purchaseNo) {
        WmsInboundUpdateRes response = new WmsInboundUpdateRes();
        response.setAsnNumber(asnNumber);
        response.setPurchaseNo(purchaseNo);
        response.setCusCode("TEST001");
        response.setTrackingNo("DP1234567890");
        return response;
    }

    /**
     * 创建入库单分页查询请求
     */
    public static WmsInboundQueryOrderPageReq createInboundQueryPageRequest(String cusCode, Long page, Long pageSize) {
        return WmsInboundQueryOrderPageReq.builder()
            .cusCode(cusCode)
            .page(page)
            .pageSize(pageSize)
            .queryConditions(WmsInboundQueryOrderPageReq.ASNListQueryConditions.builder()
                .build())
            .build();
    }

    /**
     * 创建入库单分页查询响应
     */
    public static WmsPageDTO<WmsInboundQueryPageRes> createInboundQueryPageResponse(Integer total, Integer pageIndex, Integer pageSize) {
        // 创建示例记录
        List<WmsInboundQueryPageRes> records = Arrays.asList(
            createInboundQueryPageRes("ASN001", "PO001"),
            createInboundQueryPageRes("ASN002", "PO002")
        );

        return WmsPageDTO.<WmsInboundQueryPageRes>builder()
            .total(total)
            .pageIndex(pageIndex)
            .pageSize(pageSize)
            .totalPages((total + pageSize - 1) / pageSize)
            .records(records.subList(0, Math.min(total, records.size())))
            .build();
    }

    /**
     * 创建入库单查询页面响应项
     */
    public static WmsInboundQueryPageRes createInboundQueryPageRes(String asnNumber, String purchaseNo) {
        WmsInboundQueryPageRes res = new WmsInboundQueryPageRes();
        res.setAsnId(Long.valueOf(Math.abs(asnNumber.hashCode())));
        res.setAsnNumber(asnNumber);
        res.setPurchaseNo(purchaseNo);
        res.setCreateTime(LocalDateTime.now().toString());
        res.setCreateUser("testUser");
        return res;
    }

    /**
     * 创建入库单详情响应
     */
    public static WmsInboundInfoRes createInboundInfoResponse(Long asnId, String asnNumber, String purchaseNo, String cusCode) {
        WmsInboundInfoRes response = new WmsInboundInfoRes();
        response.setAsnId(asnId);
        response.setAsnNumber(asnNumber);
        response.setPurchaseNo(purchaseNo);
        response.setAsnType("PURCHASE");
        response.setChargePerson(cusCode);
        response.setCreateTime(LocalDateTime.now());
        response.setCreateUser("testUser");
        response.setWarehouseAddress("深圳仓库");
        response.setTransportWay("EXPRESS");
        response.setExpressCompany("顺丰速运");
        response.setExpressNumber("SF1234567890");
        response.setRemark("测试入库单");
        response.setVoidFlag(false);

        // 创建入库单明细
        List<WmsInboundInfoRes.ASNDetail> details = Arrays.asList(
            createASNDetail(1L, "商品1", "Product1", 10L, 8L),
            createASNDetail(2L, "商品2", "Product2", 20L, 18L)
        );
        response.setAsnDetails(details);

        return response;
    }

    /**
     * 创建入库单明细
     */
    public static WmsInboundInfoRes.ASNDetail createASNDetail(Long goodsId, String cnName, String enName,
        Long shouldQuantity, Long factQuantity) {
        WmsInboundInfoRes.ASNDetail detail = new WmsInboundInfoRes.ASNDetail();
        detail.setAsnDetailId(goodsId * 100);
        detail.setAsnMainId(12345L);
        detail.setGoodsId(goodsId);
        detail.setCnName(cnName);
        detail.setEnName(enName);
        detail.setShouldQuantity(shouldQuantity);
        detail.setFactQuantity(factQuantity);
        detail.setOkQuantity(factQuantity);
        detail.setPrice(BigDecimal.valueOf(99.99));
        detail.setRefWeight(BigDecimal.valueOf(1.5));
        detail.setFactWeight(1.4);
        detail.setGoodsStatus("NORMAL");
        detail.setCreateTime(LocalDateTime.now());
        detail.setVoidFlag(false);
        return detail;
    }

    // ==================== 通用测试数据 ====================

    /**
     * 创建测试用的客户代码
     */
    public static String createTestCustomerCode(String prefix) {
        return prefix + System.currentTimeMillis() % 10000;
    }

    /**
     * 创建测试用的订单号
     */
    public static String createTestOrderNumber(String prefix) {
        return prefix + System.currentTimeMillis();
    }

    /**
     * 创建测试用的采购单号
     */
    public static String createTestPurchaseNumber(String prefix) {
        return prefix + System.currentTimeMillis();
    }

    /**
     * 创建测试用的入库单号
     */
    public static String createTestASNNumber(String prefix) {
        return prefix + System.currentTimeMillis();
    }

    // ==================== 边界条件测试数据 ====================

    /**
     * 创建无效的账户费用请求（用于参数验证测试）
     */
    public static WmsAccountChargeV2Req createInvalidAccountChargeRequest() {
        return WmsAccountChargeV2Req.builder()
            .cusCode(null) // 无效的客户代码
            .orderNo("")   // 空的订单号
            .operationType(null) // 无效的操作类型
            .build();
    }

    /**
     * 创建无效的入库单创建请求（用于参数验证测试）
     */
    public static WmsCreateInboundOrderReq createInvalidInboundOrderRequest() {
        return WmsCreateInboundOrderReq.builder()
            .cusCode(null) // 无效的客户代码
            .purchaseNo("") // 空的采购单号
            .build();
    }

    /**
     * 创建空的分页查询响应
     */
    public static WmsPageDTO<WmsInboundQueryPageRes> createEmptyPageResponse() {
        return WmsPageDTO.<WmsInboundQueryPageRes>builder()
            .total(0)
            .pageIndex(1)
            .pageSize(10)
            .totalPages(0)
            .records(List.of())
            .build();
    }
}
