/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.autoconfigure;

import com.fulfillmen.support.wms.api.WmsFulfillmenAPI;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.web.reactive.function.client.WebClient;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * WmsAutoConfiguration 测试类
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
class WmsAutoConfigurationTest {

    private final ApplicationContextRunner contextRunner = new ApplicationContextRunner()
        .withConfiguration(AutoConfigurations.of(WmsAutoConfiguration.class));

    @Test
    void testAutoConfigurationEnabled() {
        // 测试自动配置启用时的情况
        contextRunner
            .withPropertyValues(
                "fulfillmen.wms.enabled=true",
                "fulfillmen.wms.base-url=https://test.wms.com",
                "fulfillmen.wms.api-key=test-key"
            )
            .run(context -> {
                // 验证Bean是否被创建
                assertThat(context).hasSingleBean(WmsProperties.class);
                assertThat(context).hasSingleBean(WebClient.class);
                assertThat(context).hasSingleBean(WmsFulfillmenAPI.class);

                // 验证配置属性
                WmsProperties properties = context.getBean(WmsProperties.class);
                assertThat(properties.getBaseUrl()).isEqualTo("https://test.wms.com");
                assertThat(properties.getApiKey()).isEqualTo("test-key");
            });
    }

    @Test
    void testAutoConfigurationDisabled() {
        // 测试自动配置禁用时的情况
        contextRunner
            .withPropertyValues("fulfillmen.wms.enabled=false")
            .run(context -> {
                // 验证Bean没有被创建
                assertThat(context).doesNotHaveBean(WmsProperties.class);
                assertThat(context).doesNotHaveBean(WebClient.class);
                assertThat(context).doesNotHaveBean(WmsFulfillmenAPI.class);
            });
    }

    @Test
    void testDefaultConfiguration() {
        // 测试默认配置
        contextRunner
            .run(context -> {
                // 验证Bean被创建（默认启用）
                assertThat(context).hasSingleBean(WmsProperties.class);
                assertThat(context).hasSingleBean(WebClient.class);
                assertThat(context).hasSingleBean(WmsFulfillmenAPI.class);

                // 验证默认配置值
                WmsProperties properties = context.getBean(WmsProperties.class);
                assertThat(properties.getBaseUrl()).isEqualTo("https://wms.fulfillmen.com");
                assertThat(properties.getApiKey()).isEqualTo("e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855");
            });
    }

    @Test
    void testWebClientConfiguration() {
        // 测试WebClient配置
        contextRunner
            .withPropertyValues(
                "fulfillmen.wms.base-url=https://test.wms.com",
                "fulfillmen.wms.api-key=test-api-key"
            )
            .run(context -> {
                WebClient webClient = context.getBean("wmsWebClient", WebClient.class);
                assertThat(webClient).isNotNull();

                // 验证WebClient的基础配置
                // 注意：由于WebClient的内部结构复杂，这里主要验证Bean的存在性
                assertThat(webClient).isInstanceOf(WebClient.class);
            });
    }

    @Test
    void testWmsFulfillmenAPIConfiguration() {
        // 测试WmsFulfillmenAPI配置
        contextRunner
            .run(context -> {
                WmsFulfillmenAPI api = context.getBean(WmsFulfillmenAPI.class);
                assertThat(api).isNotNull();
                assertThat(api).isInstanceOf(WmsFulfillmenAPI.class);
            });
    }
}
