/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fulfillmen.support.wms.api.WmsFulfillmenAPI;
import com.fulfillmen.support.wms.dto.common.WmsApiResponse;
import java.time.Duration;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import okhttp3.mockwebserver.RecordedRequest;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

/**
 * WMS API集成测试基础类
 * 
 * <p>提供WMS API集成测试的通用配置和工具方法，包括MockWebServer的设置、
 * 通用的响应构建方法、请求验证方法等。</p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@Slf4j
public abstract class BaseIntegrationTest {

    protected static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(BaseIntegrationTest.class);

    protected MockWebServer mockWebServer;
    protected ObjectMapper objectMapper;
    protected String baseUrl;

    @BeforeEach
    void setUpBaseTest() throws Exception {
        // 初始化MockWebServer
        mockWebServer = new MockWebServer();
        mockWebServer.start();
        baseUrl = mockWebServer.url("/").toString();

        // 初始化ObjectMapper
        objectMapper = new ObjectMapper();

        log.info("MockWebServer started at: {}", baseUrl);
    }

    @AfterEach
    void tearDownBaseTest() throws Exception {
        if (mockWebServer != null) {
            mockWebServer.shutdown();
            log.info("MockWebServer shutdown");
        }
    }

    /**
     * 创建成功的API响应
     *
     * @param data 响应数据
     * @param <T>  数据类型
     * @return WmsApiResponse
     */
    protected <T> WmsApiResponse<T> createSuccessResponse(T data) {
        WmsApiResponse<T> response = new WmsApiResponse<>();
        response.setSuccess("success");
        response.setCode(0);
        response.setMessage("操作成功");
        response.setData(data);
        return response;
    }

    /**
     * 创建失败的API响应
     *
     * @param code    错误码
     * @param message 错误消息
     * @param <T>     数据类型
     * @return WmsApiResponse
     */
    protected <T> WmsApiResponse<T> createFailureResponse(int code, String message) {
        WmsApiResponse<T> response = new WmsApiResponse<>();
        response.setSuccess("false");
        response.setCode(code);
        response.setMessage(message);
        response.setData(null);
        return response;
    }

    /**
     * 创建MockResponse
     *
     * @param responseBody 响应体
     * @return MockResponse
     */
    protected MockResponse createMockResponse(Object responseBody) {
        try {
            String json = objectMapper.writeValueAsString(responseBody);
            return new MockResponse()
                .setResponseCode(200)
                .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .setBody(json);
        } catch (Exception e) {
            throw new RuntimeException("Failed to create mock response", e);
        }
    }

    /**
     * 创建错误的MockResponse
     *
     * @param statusCode   HTTP状态码
     * @param errorMessage 错误消息
     * @return MockResponse
     */
    protected MockResponse createErrorMockResponse(int statusCode, String errorMessage) {
        return new MockResponse()
            .setResponseCode(statusCode)
            .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .setBody("{\"error\":\"" + errorMessage + "\"}");
    }

    /**
     * 验证请求头
     *
     * @param request       录制的请求
     * @param headerName    请求头名称
     * @param expectedValue 期望值
     */
    protected void assertRequestHeader(RecordedRequest request, String headerName, String expectedValue) {
        String actualValue = request.getHeader(headerName);
        if (expectedValue == null) {
            if (actualValue != null) {
                throw new AssertionError("Expected header '" + headerName + "' to be null, but was: " + actualValue);
            }
        } else {
            if (!expectedValue.equals(actualValue)) {
                throw new AssertionError("Expected header '" + headerName + "' to be '" + expectedValue + "', but was: " + actualValue);
            }
        }
    }

    /**
     * 验证请求头存在
     *
     * @param request    录制的请求
     * @param headerName 请求头名称
     */
    protected void assertRequestHeaderExists(RecordedRequest request, String headerName) {
        String actualValue = request.getHeader(headerName);
        if (actualValue == null || actualValue.trim().isEmpty()) {
            throw new AssertionError("Expected header '" + headerName + "' to exist, but was null or empty");
        }
    }

    /**
     * 验证nonce格式（12位随机字符串）
     *
     * @param nonce 随机数
     */
    protected void assertNonceFormat(String nonce) {
        if (nonce == null || nonce.length() != 12) {
            throw new AssertionError("Expected nonce to be 12 characters long, but was: " +
                (nonce == null ? "null" : nonce.length() + " characters"));
        }

        // 验证是否包含字母和数字
        if (!nonce.matches("[a-zA-Z0-9]+")) {
            throw new AssertionError("Expected nonce to contain only alphanumeric characters, but was: " + nonce);
        }
    }

    /**
     * 验证时间戳格式
     *
     * @param timestamp 时间戳字符串
     */
    protected void assertTimestampFormat(String timestamp) {
        if (timestamp == null || timestamp.trim().isEmpty()) {
            throw new AssertionError("Expected timestamp to exist, but was null or empty");
        }

        try {
            long timestampValue = Long.parseLong(timestamp);
            long currentTime = System.currentTimeMillis();

            // 验证时间戳在合理范围内（当前时间前后5分钟）
            long timeDiff = Math.abs(currentTime - timestampValue);
            if (timeDiff > Duration.ofMinutes(5).toMillis()) {
                throw new AssertionError("Expected timestamp to be within 5 minutes of current time, but difference was: " +
                    timeDiff + "ms");
            }
        } catch (NumberFormatException e) {
            throw new AssertionError("Expected timestamp to be a valid number, but was: " + timestamp);
        }
    }

    /**
     * 等待并获取请求
     *
     * @return RecordedRequest
     * @throws InterruptedException 中断异常
     */
    protected RecordedRequest takeRequest() throws InterruptedException {
        return mockWebServer.takeRequest(5, TimeUnit.SECONDS);
    }

    /**
     * 验证WMS特定的请求头
     *
     * @param request 录制的请求
     */
    protected void assertWmsRequestHeaders(RecordedRequest request) {
        // 验证基础请求头
        assertRequestHeaderExists(request, "X-API-Key");
        assertRequestHeaderExists(request, "User-Agent");
        assertRequestHeader(request, "User-Agent", "Fulfillmen-WMS-Client/1.0");

        // 验证动态请求头
        assertRequestHeaderExists(request, WmsFulfillmenAPI.X_NONCE);
        assertRequestHeaderExists(request, WmsFulfillmenAPI.X_TIMESTAMP);

        // 验证nonce和timestamp格式
        String nonce = request.getHeader(WmsFulfillmenAPI.X_NONCE);
        String timestamp = request.getHeader(WmsFulfillmenAPI.X_TIMESTAMP);

        assertNonceFormat(nonce);
        assertTimestampFormat(timestamp);

        log.debug("验证WMS请求头成功: nonce={}, timestamp={}", nonce, timestamp);
    }

    /**
     * 验证两个nonce不相同（确保随机性）
     *
     * @param nonce1 第一个nonce
     * @param nonce2 第二个nonce
     */
    protected void assertNonceRandomness(String nonce1, String nonce2) {
        if (nonce1.equals(nonce2)) {
            throw new AssertionError("Expected nonces to be different for randomness, but both were: " + nonce1);
        }
    }
}
