/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto;

import com.fulfillmen.support.wms.dto.common.WmsApiResponse;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * WmsApiResponse 测试类
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
class WmsApiResponseTest {

    @Test
    void testIsSuccess_WithSuccessString() {
        // 测试success字符串
        WmsApiResponse<String> response = new WmsApiResponse<>();
        response.setSuccess("success");

        assertTrue(response.isSuccess());
    }

    @Test
    void testIsSuccess_WithTrueString() {
        // 测试true字符串
        WmsApiResponse<String> response = new WmsApiResponse<>();
        response.setSuccess("true");

        assertTrue(response.isSuccess());
    }

    @Test
    void testIsSuccess_WithFalseString() {
        // 测试false字符串
        WmsApiResponse<String> response = new WmsApiResponse<>();
        response.setSuccess("false");

        assertFalse(response.isSuccess());
    }

    @Test
    void testIsSuccess_WithNullString() {
        // 测试null值
        WmsApiResponse<String> response = new WmsApiResponse<>();
        response.setSuccess(null);

        assertFalse(response.isSuccess());
    }

    @Test
    void testIsSuccess_WithEmptyString() {
        // 测试空字符串
        WmsApiResponse<String> response = new WmsApiResponse<>();
        response.setSuccess("");

        assertFalse(response.isSuccess());
    }

    @Test
    void testIsSuccess_CaseInsensitive() {
        // 测试大小写不敏感
        WmsApiResponse<String> response1 = new WmsApiResponse<>();
        response1.setSuccess("SUCCESS");
        assertTrue(response1.isSuccess());

        WmsApiResponse<String> response2 = new WmsApiResponse<>();
        response2.setSuccess("True");
        assertTrue(response2.isSuccess());

        WmsApiResponse<String> response3 = new WmsApiResponse<>();
        response3.setSuccess("FALSE");
        assertFalse(response3.isSuccess());
    }

    @Test
    void testCompleteResponse() {
        // 测试完整响应
        WmsApiResponse<String> response = new WmsApiResponse<>();
        response.setSuccess("success");
        response.setCode(0);
        response.setMessage("操作成功");
        response.setData("测试数据");

        assertTrue(response.isSuccess());
        assertEquals(0, response.getCode());
        assertEquals("操作成功", response.getMessage());
        assertEquals("测试数据", response.getData());
    }

    @Test
    void testGenericType() {
        // 测试泛型类型
        WmsApiResponse<Integer> intResponse = new WmsApiResponse<>();
        intResponse.setData(123);
        assertEquals(Integer.valueOf(123), intResponse.getData());

        WmsApiResponse<Boolean> boolResponse = new WmsApiResponse<>();
        boolResponse.setData(true);
        assertEquals(Boolean.TRUE, boolResponse.getData());
    }
}
