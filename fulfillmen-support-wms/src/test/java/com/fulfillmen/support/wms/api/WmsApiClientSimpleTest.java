/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.api;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fulfillmen.support.wms.dto.common.WmsApiResponse;
import com.fulfillmen.support.wms.dto.enums.WmsOperationTypeEnum;
import com.fulfillmen.support.wms.dto.request.WmsAccountChargeV2Req;
import com.fulfillmen.support.wms.dto.response.WmsAccountChargeInfoRes;
import com.fulfillmen.support.wms.exception.WmsApiException;
import java.math.BigDecimal;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;

/**
 * WmsApiClient 简化测试类 只测试现有的账户费用相关API，确保能够编译通过
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("WmsApiClient 简化测试")
class WmsApiClientSimpleTest {

    @Mock
    private WmsFulfillmenAPI wmsFulfillmenAPI;

    private WmsApiClient wmsApiClient;

    @BeforeEach
    void setUp() {
        wmsApiClient = new WmsApiClient(wmsFulfillmenAPI);
    }

    // ==================== 账户费用管理测试 ====================

    @Test
    @DisplayName("账户费用扣减 - 成功场景")
    void testDeductAccountInfoByPurchase_Success() {
        // 准备测试数据
        WmsAccountChargeV2Req request = WmsAccountChargeV2Req.builder()
            .cusCode("TEST001")
            .orderNo("ORDER20250131001")
            .operationType(WmsOperationTypeEnum.CHARGE)
            .productAmount(BigDecimal.valueOf(120.00))
            .serviceFee(BigDecimal.valueOf(30.00))
            .shippingFee(BigDecimal.valueOf(15.00))
            .originalTotalPrice(BigDecimal.valueOf(165.00))
            .createUser("testUser")
            .remark("单元测试扣减")
            .build();

        WmsAccountChargeInfoRes expectedData = new WmsAccountChargeInfoRes();
        expectedData.setCusCode("TEST001");
        expectedData.setOrderNo("ORDER20250131001");
        expectedData.setPaymentNumber("PAY20250131001");
        expectedData.setBalanceAfterCharge(BigDecimal.valueOf(835.00));
        expectedData.setPaymentDetailId(12345);

        when(wmsFulfillmenAPI.accountChargeByPurchase(request))
            .thenReturn(Mono.just(createSuccessResponse(expectedData)));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenReturn(expectedData);

            // 执行测试
            WmsAccountChargeInfoRes result = wmsApiClient.deductAccountInfoByPurchase(request);

            // 验证结果
            assertNotNull(result);
            assertEquals("TEST001", result.getCusCode());
            assertEquals("ORDER20250131001", result.getOrderNo());
            assertEquals("PAY20250131001", result.getPaymentNumber());
            assertEquals(BigDecimal.valueOf(835.00), result.getBalanceAfterCharge());
            assertEquals(Integer.valueOf(12345), result.getPaymentDetailId());

            verify(wmsFulfillmenAPI).accountChargeByPurchase(request);
            mockedHandler.verify(() -> WmsResponseHandler.handleWmsResponse(any(), eq("WMS扣减账户余额API"), eq(request)));
        }
    }

    @Test
    @DisplayName("账户费用退款 - 成功场景")
    void testRefundAccountInfoByPurchase_Success() {
        // 准备测试数据
        WmsAccountChargeV2Req request = WmsAccountChargeV2Req.builder()
            .cusCode("TEST001")
            .orderNo("ORDER20250131001")
            .operationType(WmsOperationTypeEnum.REFUND)
            .refundAmount(BigDecimal.valueOf(165.00))
            .refundReason("测试退款")
            .createUser("testUser")
            .remark("单元测试退款")
            .build();

        WmsAccountChargeInfoRes expectedData = new WmsAccountChargeInfoRes();
        expectedData.setCusCode("TEST001");
        expectedData.setOrderNo("ORDER20250131001");
        expectedData.setPaymentNumber("REF20250131001");
        expectedData.setBalanceAfterCharge(BigDecimal.valueOf(1165.00));
        expectedData.setPaymentDetailId(12346);

        when(wmsFulfillmenAPI.accountChargeByPurchase(request))
            .thenReturn(Mono.just(createSuccessResponse(expectedData)));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenReturn(expectedData);

            // 执行测试
            WmsAccountChargeInfoRes result = wmsApiClient.refundAccountInfoByPurchase(request);

            // 验证结果
            assertNotNull(result);
            assertEquals("TEST001", result.getCusCode());
            assertEquals("ORDER20250131001", result.getOrderNo());
            assertEquals("REF20250131001", result.getPaymentNumber());
            assertEquals(BigDecimal.valueOf(1165.00), result.getBalanceAfterCharge());
            assertEquals(Integer.valueOf(12346), result.getPaymentDetailId());

            verify(wmsFulfillmenAPI).accountChargeByPurchase(request);
            mockedHandler.verify(() -> WmsResponseHandler.handleWmsResponse(any(), eq("WMS返还账户余额API"), eq(request)));
        }
    }

    @Test
    @DisplayName("账户费用退款 - WMS API异常")
    void testRefundAccountInfoByPurchase_WmsApiException() {
        WmsAccountChargeV2Req request = WmsAccountChargeV2Req.builder()
            .cusCode("INVALID001")
            .orderNo("INVALID_ORDER")
            .operationType(WmsOperationTypeEnum.REFUND)
            .refundAmount(BigDecimal.valueOf(100.00))
            .build();

        WmsApiException expectedException = new WmsApiException("WMS返还账户余额API调用失败");

        when(wmsFulfillmenAPI.accountChargeByPurchase(request))
            .thenReturn(Mono.just(createFailureResponse()));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenThrow(expectedException);

            // 执行测试并验证异常
            WmsApiException exception = assertThrows(WmsApiException.class,
                () -> wmsApiClient.refundAccountInfoByPurchase(request));

            assertEquals(expectedException.getMessage(), exception.getMessage());
            verify(wmsFulfillmenAPI).accountChargeByPurchase(request);
        }
    }

    @Test
    @DisplayName("账户费用扣减 - WMS API异常")
    void testDeductAccountInfoByPurchase_WmsApiException() {
        WmsAccountChargeV2Req request = WmsAccountChargeV2Req.builder()
            .cusCode("INVALID001")
            .orderNo("INVALID_ORDER")
            .operationType(WmsOperationTypeEnum.CHARGE)
            .productAmount(BigDecimal.valueOf(100.00))
            .build();

        WmsApiException expectedException = new WmsApiException("WMS扣减账户余额API调用失败");

        when(wmsFulfillmenAPI.accountChargeByPurchase(request))
            .thenReturn(Mono.just(createFailureResponse()));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenThrow(expectedException);

            // 执行测试并验证异常
            WmsApiException exception = assertThrows(WmsApiException.class,
                () -> wmsApiClient.deductAccountInfoByPurchase(request));

            assertEquals(expectedException.getMessage(), exception.getMessage());
            verify(wmsFulfillmenAPI).accountChargeByPurchase(request);
        }
    }

    // ==================== 辅助方法 ====================

    private <T> WmsApiResponse<T> createSuccessResponse(T data) {
        WmsApiResponse<T> response = new WmsApiResponse<>();
        response.setSuccess("success");
        response.setCode(0);
        response.setMessage("成功");
        response.setData(data);
        return response;
    }

    private <T> WmsApiResponse<T> createFailureResponse() {
        WmsApiResponse<T> response = new WmsApiResponse<>();
        response.setSuccess("false");
        response.setCode(1001);
        response.setMessage("API调用失败");
        return response;
    }
}
