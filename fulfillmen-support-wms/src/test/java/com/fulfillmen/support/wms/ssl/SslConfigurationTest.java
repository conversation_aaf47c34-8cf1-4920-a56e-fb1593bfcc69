/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.ssl;

import static org.assertj.core.api.Assertions.assertThat;

import com.fulfillmen.support.wms.autoconfigure.WmsAutoConfiguration;
import com.fulfillmen.support.wms.autoconfigure.WmsProperties;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * SSL配置测试类
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
class SslConfigurationTest {

    private final ApplicationContextRunner contextRunner = new ApplicationContextRunner()
        .withConfiguration(AutoConfigurations.of(WmsAutoConfiguration.class));

    @Test
    void testSslSkipVerificationEnabled() {
        // 测试跳过SSL验证的配置
        contextRunner
            .withPropertyValues(
                "fulfillmen.wms.base-url=https://test.wms.com",
                "fulfillmen.wms.api-key=test-api-key",
                "fulfillmen.wms.ssl.skip-verification=true",
                "fulfillmen.wms.ssl.handshake-timeout-millis=5000",
                "fulfillmen.wms.ssl.debug-enabled=false"
            )
            .run(context -> {
                // 验证配置属性
                WmsProperties properties = context.getBean(WmsProperties.class);
                assertThat(properties.getSsl().isSkipVerification()).isTrue();
                assertThat(properties.getSsl().getHandshakeTimeoutMillis()).isEqualTo(5000);
                assertThat(properties.getSsl().isDebugEnabled()).isFalse();

                // 验证WebClient能够正常创建
                WebClient webClient = context.getBean("wmsWebClient", WebClient.class);
                assertThat(webClient).isNotNull();
            });
    }

    @Test
    void testSslSkipVerificationDisabled() {
        // 测试启用SSL验证的配置
        contextRunner
            .withPropertyValues(
                "fulfillmen.wms.base-url=https://test.wms.com",
                "fulfillmen.wms.api-key=test-api-key",
                "fulfillmen.wms.ssl.skip-verification=false",
                "fulfillmen.wms.ssl.handshake-timeout-millis=10000"
            )
            .run(context -> {
                // 验证配置属性
                WmsProperties properties = context.getBean(WmsProperties.class);
                assertThat(properties.getSsl().isSkipVerification()).isFalse();
                assertThat(properties.getSsl().getHandshakeTimeoutMillis()).isEqualTo(10000);

                // 验证WebClient能够正常创建
                WebClient webClient = context.getBean("wmsWebClient", WebClient.class);
                assertThat(webClient).isNotNull();
            });
    }

    @Test
    void testHttpUrlNoSslConfiguration() {
        // 测试HTTP URL不需要SSL配置
        contextRunner
            .withPropertyValues(
                "fulfillmen.wms.base-url=http://test.wms.com",
                "fulfillmen.wms.api-key=test-api-key"
            )
            .run(context -> {
                // 验证配置属性
                WmsProperties properties = context.getBean(WmsProperties.class);
                assertThat(properties.getBaseUrl()).isEqualTo("http://test.wms.com");

                // 验证WebClient能够正常创建
                WebClient webClient = context.getBean("wmsWebClient", WebClient.class);
                assertThat(webClient).isNotNull();
            });
    }

    @Test
    void testDefaultSslConfiguration() {
        // 测试默认SSL配置
        contextRunner
            .withPropertyValues(
                "fulfillmen.wms.base-url=https://wms.fulfillmen.com",
                "fulfillmen.wms.api-key=test-api-key"
            )
            .run(context -> {
                // 验证默认配置
                WmsProperties properties = context.getBean(WmsProperties.class);
                assertThat(properties.getSsl().isSkipVerification()).isFalse();
                assertThat(properties.getSsl().getHandshakeTimeoutMillis()).isEqualTo(10000);
                assertThat(properties.getSsl().isDebugEnabled()).isFalse();

                // 验证连接配置默认值
                assertThat(properties.getConnection().getConnectTimeoutMillis()).isEqualTo(10000);
                assertThat(properties.getConnection().getResponseTimeoutSeconds()).isEqualTo(30);

                // 验证WebClient能够正常创建
                WebClient webClient = context.getBean("wmsWebClient", WebClient.class);
                assertThat(webClient).isNotNull();
            });
    }

    @Test
    void testConnectionConfiguration() {
        // 测试连接配置
        contextRunner
            .withPropertyValues(
                "fulfillmen.wms.base-url=https://test.wms.com",
                "fulfillmen.wms.api-key=test-api-key",
                "fulfillmen.wms.connection.connect-timeout-millis=15000",
                "fulfillmen.wms.connection.response-timeout-seconds=60",
                "fulfillmen.wms.connection.read-timeout-seconds=120",
                "fulfillmen.wms.connection.write-timeout-seconds=120",
                "fulfillmen.wms.connection.max-connections=50",
                "fulfillmen.wms.connection.max-idle-time-seconds=60"
            )
            .run(context -> {
                // 验证连接配置
                WmsProperties properties = context.getBean(WmsProperties.class);
                WmsProperties.ConnectionConfig connConfig = properties.getConnection();

                assertThat(connConfig.getConnectTimeoutMillis()).isEqualTo(15000);
                assertThat(connConfig.getResponseTimeoutSeconds()).isEqualTo(60);
                assertThat(connConfig.getReadTimeoutSeconds()).isEqualTo(120);
                assertThat(connConfig.getWriteTimeoutSeconds()).isEqualTo(120);
                assertThat(connConfig.getMaxConnections()).isEqualTo(50);
                assertThat(connConfig.getMaxIdleTimeSeconds()).isEqualTo(60);

                // 验证WebClient能够正常创建
                WebClient webClient = context.getBean("wmsWebClient", WebClient.class);
                assertThat(webClient).isNotNull();
            });
    }
}
