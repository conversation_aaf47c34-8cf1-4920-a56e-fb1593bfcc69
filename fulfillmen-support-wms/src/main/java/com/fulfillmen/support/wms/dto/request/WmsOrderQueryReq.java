/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.request;

import com.fulfillmen.support.wms.dto.enums.WmsOrderStatusEnum;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * WMS 订单查询请求
 *
 * <p>定义WMS订单查询的基础请求参数，包含分页信息和查询条件。
 * 使用SuperBuilder支持继承链中的构建器模式。</p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class WmsOrderQueryReq {

    /**
     * 页码
     */
    private Integer page;

    /**
     * 页大小
     */
    private Integer pageSize;

    /**
     * 是否包含详情
     */
    private boolean includeDetails;

    /**
     * 查询条件
     */
    private WmsOrderQueryConditions queryConditions;

    /**
     * WMS 订单查询条件
     *
     * <p>定义WMS订单查询的条件参数，包含各种筛选选项。</p>
     *
     * <AUTHOR>
     * @created 2025-07-29
     */
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WmsOrderQueryConditions {

        /**
         * 客户码
         */
        private String customerCode;

        /**
         * 采购单号
         */
        private String purchaseNo;

        /**
         * wms 采购单号
         */
        private String nayaPurchaseNo;

        /**
         * 订单状态
         */
        private WmsOrderStatusEnum status;

        /**
         * 1688订单id
         */
        private Long orderId;

        /**
         * 开始时间
         */
        private LocalDateTime startTime;

        /**
         * 结束时间
         */
        private LocalDateTime endTime;

        /**
         * 平台名称
         */
        private String platform;

        /**
         * 店铺id
         */
        private String storeId;

        /**
         * 最小金额
         */
        private BigDecimal minTotalAmount;

        /**
         * 最大金额
         */
        private BigDecimal maxTotalAmount;

        /**
         * 是否询价
         */
        private Boolean isRequestQuote;

        /**
         * 支付类型
         */
        private String payType;

        /**
         * 创建用户
         */
        private String createUser;

        /**
         * 外部交易号
         */
        private String outTradeNo;

        /**
         * 物流单号
         */
        private String trackingNo;

        /**
         * 平台状态
         */
        private String platformStatus;

        /**
         * 是否包含详情
         */
        @lombok.Builder.Default
        private Boolean includeDetails = true;
    }
}
