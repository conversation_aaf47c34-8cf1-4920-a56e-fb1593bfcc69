/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.autoconfigure;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * WMS配置属性类
 *
 * <p>定义WMS API相关的配置属性，包括基础URL、API密钥和RSA密钥等。
 * 使用Spring Boot的配置属性绑定机制，支持从配置文件中读取配置。</p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@Data
@ConfigurationProperties(prefix = "fulfillmen.wms")
public class WmsProperties {

    /**
     * WMS API基础URL
     */
    private String baseUrl = "https://wms.fulfillmen.com";

    /**
     * WMS API密钥
     * 平台系统之间的认证密钥
     */
    private String apiKey = "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855";

    /**
     * SSL配置
     */
    private SslConfig ssl = new SslConfig();

    /**
     * 连接配置
     */
    private ConnectionConfig connection = new ConnectionConfig();

    /**
     * SSL配置类
     */
    @Data
    public static class SslConfig {

        /**
         * 是否跳过SSL证书验证（仅用于开发/测试环境）
         */
        private boolean skipVerification = false;

        /**
         * SSL握手超时时间（毫秒）
         */
        private int handshakeTimeoutMillis = 10000;

        /**
         * 是否启用SSL调试日志
         */
        private boolean debugEnabled = false;
    }

    /**
     * 连接配置类
     */
    @Data
    public static class ConnectionConfig {

        /**
         * 连接超时时间（毫秒）
         */
        private int connectTimeoutMillis = 10000;

        /**
         * 响应超时时间（秒）
         */
        private int responseTimeoutSeconds = 30;

        /**
         * 读取超时时间（秒）
         */
        private int readTimeoutSeconds = 60;

        /**
         * 写入超时时间（秒）
         */
        private int writeTimeoutSeconds = 60;

        /**
         * 最大连接数
         */
        private int maxConnections = 100;

        /**
         * 最大空闲时间（秒）
         */
        private int maxIdleTimeSeconds = 30;
    }

    /**
     * RSA私钥，用于解密WMS返回的数据
     */
    public static final String RSA_SECRET_KEY = """
          MIIG/AIBADANBgkqhkiG9w0BAQEFAASCBuYwggbiAgEAAoIBgQCzNlfIt6taJtrB
          /Kd2+On0WDVBVN3WN6eUPk04/BuGNFZUECXpIGpjSNFPJTO2gC5mPEeIGY/Ww7Wa
          HeMiSokgEkJ3I0ApzD5NhH9Oy05OgGZ7UR70xhIq3+jmKfxMuULfgYwkuiHoz8mI
          v5epcb9E/D9wofk9faKUpgnxOhrMzYI8qutTaKzVUwU7QueZ42MQrGoE9RF5epvY
          jw8TjHu08QxUd0quualsO8E6iEAcKwBH/lJImBQhg7jbNLNQOtENvYkZf87iCyh5
          a8T+SerrlwdSaR2tUjkaI3p0Z/OfZGAgwt1OyAtsgAZaEneWYHrpt026dsStZTi2
          hb0n3w3NgoVtRSWryllU6uNuFZ+3Oshbwirmubbn4hHNbnv8eWqAQtzIKGx7iW93
          tp9tTvmB2kUzY4IuDoZq4NUVcRYKGo485G0pcxbfd1+Mn0l8cV5W0tkQI0o3LOzx
          uxElIaZFBUXTRgBRVhPiwZT8Z9L9Ygd9QFfA1U6SB7LfZKC3E98CAwEAAQKCAYBs
          CF9k1SvLnc6T0E3Xj3fhX2dDKfjM0SIJYIFGz9t7zIMaC93+qhbYISZkUqXY526R
          HNd2OQqXycTo0gmBO3NsTPWaQjrEt6X7Lttb0X00H30n66AAqIJ9LBuG3FqGmcux
          VrP30+jrIo94Cx9O+w31dSZ9LHdGVT0z5N/rdj5/pYdUMSjJUDmYI4Ls1YkuIIAw
          pe8HbACLvZjDNII/I5nTWtg29u6jY7K5V1BFi6syD84MmEhcEKlaBsDiC+PogntO
          IxZhsH0ySwokHPjUbtO/3iEH3cT2G4qcwt/1ziFUO1HdX7ufP7lnIDBSaMFZA2HU
          t/mktLICEq5Y5NDw1jiu3+M+OSWdkMrOvwmZ5vQpyRJOECMAGHj+ohaulUtTh1mc
          W9UycuhoiV3bx3aZMLZPBCZt28NrNczmywCQLQRu/Od+MpCK2D297CvvkPux3IGl
          aq9Ljpgb882cTnsC27Dw2ZmcyOkWQGp7PflWakVBq/7aHtM4oh7vZUqibq3LnOEC
          gcEA67QEFTvns0w6EDq5bVwXCOQj9kp0kj1LxRhmpL8UGI3w3y2rq6L1B20jpqNj
          ai1oMwEHQDhe01+2f0OHkkWalfDrWq9bwyYlwaapKmFd9LkfdzQ5618Xlckf4BVW
          k+JcRgkGOcPgxurhhRsgkbuKmxesRZoBdtR37mgahuLyDubJlrAoDMXEDhocjx2j
          87mi62Aol/W5OqWmWRg3+rvvPiO8xIYukyUN/TG5TX1HQdBwCPeR2D0hRuYgw0xQ
        1d+xAoHBAMKlAc6td57EqBwgNfsO9ursKg/Jy37HFhatiteE5w3Qw5tHmPjHz+UW
        DSI4OnNh773BwwJ2FMiXuwVmyGGn4JpgYvKavYi5TCgIQRNAJnY5WAeZ5xrjcAer
        T2a8VYDVVSht6cYPgup86JFRDxNpMA+6twjEnXyLMLPZYU1TPbnJKjAkA4AwkcF0
        8KVV35njVptCwngMkrM3PuxhnQsRsR0dy40uVzh56rCjUDvrq0HyToJ4hV9k4O5x
        Em6qdRkgjwKBwGsNmgUi7jurVXvlkC6xcuzg4cyNSKDbIjzktkoBV1lHrq1PCver
        zeqPkW+wKgkhkoAH6YkrkTYj9LmcrEitQGb1w4+usdjSzLdFwPO0wX3TTw1PNTdI
        O2gWr1mRl38Xgp10n9VAuCjgqYlRJtvJ+Ew01lPOfPfBzK8FrXayQQqvm8PLQoV6
        lW9qQKpn5f1exTZuuEE8o+KVNhalP1esYB8zEBuabjD61X24RdeCHQkxaJFKEH3M
        Vf7jquHJGbXqIQKBwGgvOEtvMFMMQktjtmEefm36YP04bzL5z0nuNB3t5lrPvpOz
        /B5VnWKqrfb+D9NIvbFdMEt19uPzy0g13tXerOsU4oGm8AJp5GidGm/6a1vmj8bp
        yvjqekGwoXyFHGMsTTupsXKxRMrMdxOpRp+GMxKIR4Zq7z0Fp9jsZxejkauLEIja
          DgGkfbAGLvTghSHyvJ24Wy001aUV0hlmmk+ddab3FsgH4Om64DMSB1U7bNyMnRda
          +VC5R2h5n3saxDjh4wKBwFDBfvvFmQpKi2h3EakCJwiKYSktBL0Ji3x7EmbD16Ch
          TZHlJHZKzJIb/c6wCNgBaZi0wGkBy5FYeo29dxg/dJ0OEpYEJN8HiZ9jLk/+3Kvw
          GPULY3zU0HgC81VhC2NHCOvx5r8ldLnrj1hz2r7JrMWz+oqlr3/aXA4ChZ91iOVy
          tkVvcakGfPRcZjPGm37kmoSc19aud+eVILQ4sxW/Cexf8k0L0tt6ycylOiQDrlQP
          hevrnL528IH1AFqj07OGtg==
        """;
}
