/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.response;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

/**
 * WMS账户数据响应
 * 
 * <p>定义WMS API响应中的data部分，包含加密的用户信息。
 * 通常需要使用RSA私钥进行解密处理。</p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@Data
public class WmsAccountDataRes {

    /**
     * 加密的用户信息
     * 需要使用RSA私钥进行解密
     */
    @JsonAlias(value = {"encryptedData"})
    private String encryptedUserInfo;
}
