/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.fulfillmen.starter.core.util.JacksonUtil;
import lombok.Getter;

/**
 * ASN 发货类型枚举
 *
 * <AUTHOR>
 * @date 2025/7/31 16:38
 * @description: todo
 * @since 1.0.0
 */
@Getter
public enum WmsAsnShipmentTypeEnums {

    //    0 一件代发 2 集货运输
    ONE_PIECE_SALE(0, "一件代发"),
    COLLECTION(2, "集货运输");

    @JsonValue
    private final int code;
    private final String desc;

    WmsAsnShipmentTypeEnums(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static void main(String[] args) {
        System.out.println(JacksonUtil.toJsonString(WmsAsnShipmentTypeEnums.ONE_PIECE_SALE));
    }

}
