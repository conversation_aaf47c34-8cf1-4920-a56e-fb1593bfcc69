/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/30 10:39
 * @description: todo
 * @since 1.0.0
 */
@Data
public class WmsPurchaseOrderDetailsItemRes {

    /**
     * 1688 订单 id
     */
    private String orderId;

    /**
     * 商品SKU ID
     */
    @JsonProperty("sku")
    private String skuId;

    /**
     * 英文名称
     */
    private String enName;

    /**
     * 中文名称
     */
    private String cnName;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 规格ID
     */
    private String variantId;

    /**
     * 产品ID
     */
    private String productId;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 原始单价
     */
    private BigDecimal originUnitPrice;

    /**
     * 最终单价
     */
    private BigDecimal finalUnitPrice;

    /**
     * 小计
     */
    private BigDecimal subTotal;

    /**
     * 原始小计金额
     */
    private BigDecimal originSubTotalAmount;

    /**
     * 最终小计金额
     */
    private BigDecimal finalSubTotalAmount;

    /**
     * 商品图片URL
     */
    private String imageUrl;

    /**
     * 商品链接
     */
    private String productUrl;

    /**
     * 商品属性
     */
    private String attributes;

    /**
     * SKU属性（中文）
     */
    private String skuAttrib;

    /**
     * SKU属性（英文）
     */
    private String skuAttribEn;
}
