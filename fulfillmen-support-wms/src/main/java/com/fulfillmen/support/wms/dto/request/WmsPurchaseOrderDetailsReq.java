/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * WMS 订单详情请求
 * 
 * <p>定义WMS订单详情的信息，包含商品SKU、价格、数量等详细信息。
 * 用于订单创建时的商品明细数据。</p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WmsPurchaseOrderDetailsReq {

    /**
     * 商品SKU ID
     */
    @JsonProperty("sku")
    private String skuId;

    /**
     * 英文名称
     */
    private String enName;

    /**
     * 中文名称
     */
    private String cnName;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 规格ID
     */
    private String variantId;

    /**
     * 产品ID
     */
    private String productId;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 原始单价
     */
    private BigDecimal originUnitPrice;

    /**
     * 最终单价
     */
    private BigDecimal finalUnitPrice;

    /**
     * 小计
     */
    private BigDecimal subTotal;

    /**
     * 原始小计金额
     */
    private BigDecimal originSubTotalAmount;

    /**
     * 最终小计金额
     */
    private BigDecimal finalSubTotalAmount;

    /**
     * 商品图片URL
     */
    private String imageUrl;

    /**
     * 商品链接
     */
    private String productUrl;

    /**
     * 商品属性
     */
    private String attributes;

    /**
     * 备注
     */
    private String remark;

    /**
     * SKU属性（中文）
     */
    private String skuAttrib;

    /**
     * SKU属性（英文）
     */
    private String skuAttribEn;
}
