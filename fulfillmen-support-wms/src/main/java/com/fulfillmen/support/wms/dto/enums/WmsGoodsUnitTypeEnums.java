/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 产品单位枚举
 * <pre>
 * ## 产品单位枚举
 * | 值 | 中文名称 | 英文名称 | 说明 |
 * |----|----------|----------|------|
 * | 0 | 个/件 | Piece | 单个商品 |
 * | 1 | 箱 | Box | 整箱商品 |
 * | 3 | 包 | Package | 包装商品 |
 * | 4 | 套 | Set | 套装商品 |
 * | 5 | 对 | Pair | 成对商品 |
 * | 6 | 打 | Dozen | 十二个一组 |
 * | 7 | 千克 | Kilogram | 重量单位 |
 * | 8 | 克 | Gram | 重量单位 |
 * | 9 | 米 | Meter | 长度单位 |
 * | 10 | 厘米 | Centimeter | 长度单位 |
 * | 11 | 未设置 | Not Set | 默认值 |
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/28 22:35
 * @description: todo
 * @since 1.0.0
 */
@Getter
public enum WmsGoodsUnitTypeEnums {

    PIECE(0, "个/件"),
    BOX(1, "箱"),
    PACKAGE(3, "包"),
    SET(4, "套"),
    PAIR(5, "对"),
    DOZEN(6, "打"),
    KILOGRAM(7, "千克"),
    GRAM(8, "克"),
    METER(9, "米"),
    CENTIMETER(10, "厘米"),
    NOT_SET(11, "未设置");

    @JsonValue
    private final int code;
    private final String desc;

    WmsGoodsUnitTypeEnums(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
