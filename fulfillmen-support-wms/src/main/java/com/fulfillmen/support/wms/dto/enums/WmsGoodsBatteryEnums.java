/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.enums;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 产品是否带电
 *
 * <AUTHOR>
 * @date 2025/7/28 22:37
 * @description: todo
 * @since 1.0.0
 */
public enum WmsGoodsBatteryEnums {

    /**
     * 0-不带电
     */
    NOT_BATTERY(0, "不带电"),
    /**
     * 1-带电
     */
    BATTERY(1, "带电");

    @JsonValue
    private final int code;
    private final String desc;

    WmsGoodsBatteryEnums(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
