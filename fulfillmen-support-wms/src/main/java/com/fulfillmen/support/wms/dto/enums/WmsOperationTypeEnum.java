/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/7/29 15:55
 * @description: todo
 * @since 1.0.0
 */
@Getter
public enum WmsOperationTypeEnum {

    CHARGE("charge", "费用"),
//    CONSUMPTION("consumption", "消费"),
    REFUND("refund", "退款");

    @JsonValue
    private final String code;
    private final String desc;

    WmsOperationTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
