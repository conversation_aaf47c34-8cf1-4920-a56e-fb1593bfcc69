/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询 wms 产品列表 请求
 *
 * <AUTHOR>
 * @date 2025/7/26 13:35
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WmsProductQueryReq {

    /**
     * 客户编码
     */
    @JsonProperty("cuscode")
    private String cusCode;
    /**
     * 页码
     */
    @JsonProperty("page")
    private Integer page;
    /**
     * 页大小
     */
    @JsonProperty("pageSize")
    private Integer pageSize;
    /**
     * sku
     */
    @JsonProperty("sku")
    private String sku;
    /**
     * 条码
     */
    @JsonProperty("barcode")
    private String barcode;
}
