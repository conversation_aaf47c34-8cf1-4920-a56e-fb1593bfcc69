/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.request;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * WMS 创建采购订单请求
 * 
 * <p>定义WMS创建采购订单的请求参数，包含客户编码和订单信息列表。
 * 支持批量创建多个订单。</p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WmsCreateOrderReq {

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 订单信息列表
     */
    private List<WmsPurchaseOrderReq> orders;
}
