/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 更新 wms 商品信息 请求 最多 50个更新商品
 * <pre>
 * ## 必填字段
 * - cusCode: 客户编码
 * - offerID: 1688 offerID
 * - platformSku: 平台SKU
 *
 * ## 数值字段更新规则
 * - 使用-1表示不修改该字段
 * - 使用>=0的值表示更新该字段
 *
 * ## 字符串字段更新规则
 * - 使用空值或null表示不修改该字段
 * - 使用非空值表示更新该字段
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/29 17:35
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WmsProductInfoUpdate {

    /**
     * 品牌
     */
    private String brand;
    /**
     * 中文名称
     */
    private String cnName;
    /**
     * 客户编码（必填）
     */
    private String cusCode;
    /**
     * 海关编码
     */
    private String customcCode;
    /**
     * 英文名称
     */
    private String enName;
    /**
     * 预期数量（-1表示不修改，>=0表示更新）
     */
    private Long expectNum;
    /**
     * 商品条码
     */
    private String goodsCode;
    /**
     * 商品状态
     */
    private String goodsStatus;
    /**
     * 商品分类
     */
    private String goodsStyle;
    /**
     * 高度（-1表示不修改，>=0表示更新）
     */
    private Double high;
    /**
     * 图片URL
     */
    private String image;
    /**
     * 长度（-1表示不修改，>=0表示更新）
     */
    private Double length;
    /**
     * 1688 offerID（必填）
     */
    private String offerId;
    /**
     * 包装类型
     */
    private String packStyle;
    /**
     * 平台SKU（必填）
     */
    private String platformSku;
    /**
     * 申报价值（-1表示不修改，>=0表示更新）
     */
    private Double price;
    /**
     * 原产地
     */
    private String producingArea;
    /**
     * 产品单位（-1表示不修改，>=0表示更新）
     */
    private Long proUnit;
    /**
     * 采购成本（-1表示不修改，>=0表示更新）
     */
    private Double purchaseCost;
    /**
     * 采购价格（-1表示不修改，>=0表示更新）
     */
    private Double purchasePrice;
    /**
     * 备注
     */
    private String remark;
    /**
     * SKU
     */
    private String sku;
    /**
     * 重量（-1表示不修改，>=0表示更新）
     */
    private Double weight;
    /**
     * 宽度（-1表示不修改，>=0表示更新）
     */
    private Double width;
}
