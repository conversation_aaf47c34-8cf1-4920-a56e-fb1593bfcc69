/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.exception;

import com.fulfillmen.support.common.exception.FulfillmenSdkException;
import java.io.Serial;
import java.io.Serializable;

/**
 * WMS API调用异常
 * 
 * <p>用于封装WMS API调用过程中发生的网络异常、HTTP状态码异常等技术性异常。
 * 这是一个纯技术异常，不包含业务逻辑相关的异常信息。</p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
public class WmsApiException extends FulfillmenSdkException implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 构造函数
     *
     * @param message 异常信息
     */
    public WmsApiException(String message) {
        super(message);
    }

    /**
     * 构造函数
     *
     * @param message 异常信息
     * @param cause   原始异常
     */
    public WmsApiException(String message, Throwable cause) {
        super(message, cause);
    }
}
