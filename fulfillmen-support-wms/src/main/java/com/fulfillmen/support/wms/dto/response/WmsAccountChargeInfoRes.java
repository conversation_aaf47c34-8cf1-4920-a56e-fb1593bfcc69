/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.wms.dto.enums.WmsOperationTypeEnum;
import java.math.BigDecimal;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 账户消费或退款成功
 * <pre>
 * <code>
 * {
 * "customerCode": "CUST001",
 * "orderNo": "ORD20231201001",
 * "operationType": "charge",
 * "paymentNumber": "WCUST0012312011430221234",
 * "balanceAfterOperation": 884.25
 * }
 * </code>
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/21 17:46
 * @description: todo
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
public class WmsAccountChargeInfoRes {

    /**
     * 客户编码
     */
    @JsonProperty("customerCode")
    private String cusCode;

    /**
     * 店铺订单号 - 对应 供应商订单号
     */
    @JsonProperty("orderNo")
    private String orderNo;

    /**
     * 操作类型 消费、退款
     */
    @JsonProperty("operationType")
    private WmsOperationTypeEnum operationType;

    /**
     * 流水号
     */
    @JsonProperty("paymentNumber")
    private String paymentNumber;

    /**
     * 剩余余额
     */
    @JsonProperty("balanceAfterOperation")
    private BigDecimal balanceAfterCharge;

    /**
     * 流水记录ID
     */
    @JsonProperty("paymentDetailId")
    private Integer paymentDetailId;

}
