/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.response;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * WMS产品信息
 * 
 * <p>定义WMS产品的基本信息，包含SKU、条码、名称、价格等。
 * 用于产品创建和查询功能。</p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WmsProductRes {

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 总数量
     */
    private Integer count;

    /**
     * sku
     */
    private String sku;

    /**
     * 产品条码（不可重复）
     */
    private String barcode;

    /**
     * 英文名称
     */
    private String enName;

    /**
     * 中文名称
     */
    private String cnName;

    /**
     * 描述
     */
    private String remark;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 长度
     */
    private BigDecimal length;

    /**
     * 宽度
     */
    private BigDecimal width;

    /**
     * 高度
     */
    private BigDecimal height;

    /**
     * 图片URL
     */
    private String imageUrl;

    /**
     * 产品链接
     */
    private String productUrl;

    /**
     * 分类
     */
    private String category;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}
