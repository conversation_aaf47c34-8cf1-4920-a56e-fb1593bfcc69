/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.wms.dto.enums.WmsAsnShipmentTypeEnums;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 创建入库单请求
 *
 * <AUTHOR>
 * @date 2025/7/26 13:39
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WmsCreateInboundOrderReq {

    /**
     * 客户编码
     */
    @JsonProperty("cusCode")
    private String cusCode;
    /**
     * 采购单号
     */
    @JsonProperty("purchaseNo")
    private String purchaseNo;
    /**
     * 物流单号
     */
    @JsonProperty("trackingNo")
    private String trackingNo;
    /**
     * 物流公司
     */
    @JsonProperty("expressCompany")
    private String expressCompany;
    /**
     * 仓库地址
     */
    @JsonProperty("warehouseAddress")
    private String warehouseAddress;
    /**
     * 发货时间
     */
    private LocalDateTime deliveryTime;
    /**
     * 采购数量
     */
    @JsonProperty("enchaseNum")
    private Integer enchaseNum;
    /**
     * 发货类型 0 一件代发 2 集货运输
     */
    @JsonProperty("shipmentType")
    private WmsAsnShipmentTypeEnums shipmentType;
    /**
     * 备注
     */
    @JsonProperty("remark")
    private String remark;
    /**
     * 采购详情
     */
    @JsonProperty("purchaseDetails")
    private List<PurchaseDetailsDTO> purchaseDetails;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PurchaseDetailsDTO {

        /**
         * 商品编码
         */
        @JsonProperty("sku")
        private String sku;
        /**
         * 商品ID
         */
        private Long productId;
        /**
         * 采购数量
         */
        private Integer quantity;
        /**
         * 单价
         */
        private BigDecimal unitPrice;
        /**
         * 重量
         */
        private BigDecimal weight;
        /**
         * 中文名称
         */
        private String cnName;
        /**
         * 英文名称
         */
        private String enName;
        /**
         * SKU属性
         */
        private String skuAttributes;
    }
}
