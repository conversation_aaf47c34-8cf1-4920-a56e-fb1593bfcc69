/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.common;

import lombok.Data;

/**
 * WMS API 通用响应结构
 *
 * <AUTHOR>
 * @date 2025/7/21 17:03
 * @description: todo
 * @since 1.0.0
 */
@Data
public class WmsApiResponse<T> {

    /**
     * WMS接口返回的成功标识，可能是 "success" 字符串或布尔值。
     * 为了兼容性，使用String接收。
     */
    private String success;
    private int code;
    private String message;
    private T data;

    /**
     * 自定义getter，将 "success" 字符串转换为布尔值true。
     *
     * @return 如果WMS返回表示成功的字符串，则为true；否则为false。
     */
    public boolean isSuccess() {
        return "success".equalsIgnoreCase(this.success) || "true".equalsIgnoreCase(this.success);
    }
}
