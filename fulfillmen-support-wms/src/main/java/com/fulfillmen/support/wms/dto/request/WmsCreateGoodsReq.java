/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.request;

import com.fulfillmen.support.wms.dto.enums.WmsGoodsAuditStatusEnums;
import com.fulfillmen.support.wms.dto.enums.WmsGoodsBatteryEnums;
import com.fulfillmen.support.wms.dto.enums.WmsGoodsStatusEnums;
import com.fulfillmen.support.wms.dto.enums.WmsGoodsUnitTypeEnums;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 创建 wms 商品
 *
 * <AUTHOR>
 * @date 2025/7/28 22:26
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WmsCreateGoodsReq {

    /**
     * 商品用途
     */
    private String application;
    /**
     * 商品条码
     */
    private String barcode;
    /**
     * 是否带电 | 值 | 状态 | 说明 | |----|------|------| | 0 | 否 | 普通商品 | | 1 | 是 | 含电池商品 |
     */
    private WmsGoodsBatteryEnums battery;
    /**
     * 品牌
     */
    private String brand;
    /**
     * 商品中文名称（必填）
     */
    private String cnName;
    /**
     * 货币单位
     */
    private String currency;
    /**
     * 客户编码（必填）
     */
    private String cusCode;
    /**
     * 商品英文名称（必填）
     */
    private String enName;
    /**
     * 预警数量
     */
    private Long expectNum;
    /**
     * 商品状态 | 值 | 状态 | 说明 | |----|------|------| | 0 | 禁用 | 商品不可用 | | 1 | 启用 | 商品可用 |
     */
    private WmsGoodsStatusEnums goodsStatus;
    /**
     * 商品高度（厘米）
     */
    private Double height;
    /**
     * 最高库存
     */
    private Long highStock;
    /**
     * 海关编码
     */
    private String hsCode;
    /**
     * 商品图片URL（支持自动下载）
     */
    private String imageUrl;
    /**
     * 是否审核 | 值 | 状态 | 说明 | |----|------|------| | 0 | 未审核 | 需要审核 | | 1 | 已审核 | 审核通过 |
     */
    private WmsGoodsAuditStatusEnums isApproval;
    /**
     * 是否批量 | 值 | 状态 | 说明 | |----|------|------| | 0 | 否 | 非批量商品 | | 1 | 是 | 批量商品 |
     */
    private Integer isBatch;
    /**
     * 是否到期 | 值 | 状态 | 说明 | |----|------|------| | 0 | 否 | 无到期时间 | | 1 | 是 | 有到期时间 |
     */
    private Integer isDueTo;
    /**
     * 是否生产 | 值 | 状态 | 说明 | |----|------|------| | 0 | 否 | 非生产商品 | | 1 | 是 | 生产商品 |
     */
    private Integer isProduction;
    /**
     * 商品长度（厘米）
     */
    private Double length;
    /**
     * 最低库存
     */
    private Long lowStock;
    /**
     * 商品材质
     */
    private String material;
    /**
     * 产品ID（对应采购单的Product_id）
     */
    private Long offerId;
    /**
     * 原产地
     */
    private String origin;
    /**
     * 平台sku（必填，唯一标识）
     */
    private String platformSku;
    /**
     * 平台SKU ID
     */
    private String platformSkuId;
    /**
     * 商品价格
     */
    private Double price;
    /**
     * 产品链接
     */
    private String productLink;
    /**
     * 产品单位（必填）
     * <p>
     * | 值 | 中文名称 | 英文名称 | 说明 | |----|----------|----------|------| | 0 | 个/件 | Piece | 单个商品 | | 1 | 箱 | Box | 整箱商品 | | 3 | 包 | Package | 包装商品 | | 4 | 套 | Set | 套装商品 | | 5 | 对 |
     * Pair | 成对商品 | | 6 | 打 | Dozen | 十二个一组 | | 7 | 千克 | Kilogram | 重量单位 | | 8 | 克 | Gram | 重量单位 | | 9 | 米 | Meter | 长度单位 | | 10 | 厘米 | Centimeter | 长度单位 | | 11 | 未设置 | Not Set |
     * 默认值 |
     */
    private WmsGoodsUnitTypeEnums proUnit;
    /**
     * 采购成本
     */
    private Double purchaseCost;
    /**
     * 采购价格
     */
    private Double purchasePrice;
    /**
     * 商品描述
     */
    private String remark;
    /**
     * 销售地址
     */
    private String salesAddress;
    /**
     * wms商品SKU
     */
    private String sku;
    /**
     * 商品规格属性（如：Size:38/85 - Color:171 shrimp powder）
     */
    private String skuAttributes;
    /**
     * 商品规格属性英文
     */
    private String skuAttributesEn;
    /**
     * 规格ID
     */
    private String specId;
    /**
     * 商品规格
     */
    private String specification;
    /**
     * 店铺名称
     */
    private String storeName;
    /**
     * 税则号
     */
    private String tariffNo;
    /**
     * 计量单位
     */
    private String unit;
    /**
     * 商品重量（千克）
     */
    private Double weight;
    /**
     * 商品宽度（厘米）
     */
    private Double width;
}
