/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.wms.dto.enums.WmsAsnShipmentTypeEnums;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 创建入库单请求
 *
 * <AUTHOR>
 * @date 2025/7/26 13:39
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WmsInboundOrderUpdateReq {

    /**
     * 客户编码
     */
    private String cusCode;
    /**
     * 采购单号
     */
    private String purchaseNo;
    /**
     * 物流单号
     */
    private String trackingNo;
    /**
     * 入库单号
     */
    private String asnNumber;
    /**
     * 物流公司
     */
    private String expressCompany;
    /**
     * 仓库地址
     */
    private String warehouseAddress;
    /**
     * 采购数量
     */
    private Integer enchaseNum;
    /**
     * 发货类型 0 一件代发 2 集货运输
     */
    private WmsAsnShipmentTypeEnums shipmentType;
    /**
     * 备注
     */
    private String remark;
    /**
     * 采购详情
     */
    @JsonProperty("asnDetails")
    private List<AsnDetailDTO> asnDetails;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AsnDetailDTO {

        /**
         * 商品编码
         */
        @JsonProperty("sku")
        private String sku;
        /**
         * 商品ID
         */
        private String productId;
        /**
         * 采购数量
         */
        @JsonProperty("quantity")
        private Integer quantity;
        /**
         * 单价
         */
        @JsonProperty("unitPrice")
        private BigDecimal unitPrice;
        /**
         * 重量
         */
        @JsonProperty("weight")
        private BigDecimal weight;
        /**
         * 中文名称
         */
        @JsonProperty("cnName")
        private String cnName;
        /**
         * 英文名称
         */
        @JsonProperty("enName")
        private String enName;

        /**
         * SKU属性
         */
        private String skuAttributes;
    }
}
