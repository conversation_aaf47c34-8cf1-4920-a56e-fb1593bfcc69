/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;

/**
 * 加密/解密工具类
 *
 * <AUTHOR>
 * @since 2025/4/12 13:00
 */
public final class SecureUtils {

    private SecureUtils() {
    }

    /**
     * 公钥加密
     *
     * @param data      要加密的内容
     * @param publicKey 公钥
     * @return 加密后的内容
     */
    public static String encryptByRsaPublicKey(String data, String publicKey) {
        byte[] encryptedBytes = SecureUtil.rsa(null, publicKey).encrypt(data, KeyType.PublicKey);
        return Base64.encode(encryptedBytes);
    }

    /**
     * 私钥解密
     *
     * @param data       要解密的内容（Base64 加密过）
     * @param privateKey 私钥
     * @return 解密后的内容
     */
    public static String decryptByRsaPrivateKey(String data, String privateKey) {
        return new String(SecureUtil.rsa(privateKey, null).decrypt(Base64.decode(data), KeyType.PrivateKey));
    }

}
