/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.autoconfigure;

import cn.hutool.core.util.RandomUtil;
import com.fulfillmen.support.common.webclient.WebClientBuilder;
import com.fulfillmen.support.wms.api.WmsFulfillmenAPI;
import com.fulfillmen.support.wms.exception.WmsApiException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;
import reactor.core.publisher.Mono;

/**
 * WMS自动配置类
 * 
 * <p>
 * 使用Spring Boot的自动配置机制，配置WMS相关的Bean。
 * 使用通用的WebClientBuilder创建WebClient，并通过HttpServiceProxyFactory创建声明式HTTP接口代理。
 * </p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@Slf4j
@AutoConfiguration
@RequiredArgsConstructor
@EnableConfigurationProperties(WmsProperties.class)
@ComponentScan(basePackages = "com.fulfillmen.support.wms")
@ConditionalOnProperty(prefix = "fulfillmen.wms", name = "enabled", havingValue = "true", matchIfMissing = true)
public class WmsAutoConfiguration {

    private final WmsProperties wmsProperties;

    /**
     * 配置WMS WebClient
     *
     * <p>
     * 使用自定义的HttpClient创建WebClient，包含SSL配置和超时设置。
     * </p>
     *
     * @return WebClient实例
     */
    @Bean("wmsWebClient")
    @ConditionalOnMissingBean(name = "wmsWebClient")
    public WebClient wmsWebClient() {
        log.info("初始化WMS WebClient: baseUrl=[{}], skipSslVerification=[{}]",
            wmsProperties.getBaseUrl(), wmsProperties.getSsl().isSkipVerification());

        try {

            return WebClientBuilder.getWebClientBuilder()
                .baseUrl(wmsProperties.getBaseUrl())
                .defaultHeader(HttpHeaders.CONTENT_TYPE, "application/json")
                .defaultHeader(HttpHeaders.ACCEPT, "application/json")
                .defaultHeader("X-API-Key", wmsProperties.getApiKey())
                .defaultHeader("User-Agent", "Fulfillmen-WMS-Client/1.0")
                .filter(wmsRequestHeaderFilter())
                // 4xx 客户端错误
                .defaultStatusHandler(HttpStatusCode::is4xxClientError,
                    (response) -> {
                        log.error("[is4xxClientError] WMS API 请求失败: {}", response.statusCode());
                        return response.bodyToMono(String.class).map(WmsApiException::new);
                    })
                // 5xx 服务端错误
                .defaultStatusHandler(HttpStatusCode::is5xxServerError,
                    (response) -> {
                        log.error("[is5xxServerError] WMS API 请求失败: {}", response.statusCode());
                        return response.bodyToMono(String.class).map(WmsApiException::new);
                    })
                .build();
        } catch (Exception e) {
            log.error("创建WMS WebClient失败", e);
            throw new RuntimeException("创建WMS WebClient失败", e);
        }
    }

    /**
     * WMS请求头过滤器
     *
     * <p>
     * 为每个WMS API请求动态添加随机数、时间戳等请求头。
     * </p>
     *
     * @return ExchangeFilterFunction
     */
    private ExchangeFilterFunction wmsRequestHeaderFilter() {
        return ExchangeFilterFunction.ofRequestProcessor(request -> {
            String nonce = RandomUtil.randomString(12);
            String timestamp = System.currentTimeMillis() + "";

            ClientRequest modifiedRequest = ClientRequest.from(request)
                .header(WmsFulfillmenAPI.X_NONCE, nonce)
                .header(WmsFulfillmenAPI.X_TIMESTAMP, timestamp)
                // 预留签名头，暂时不实现
                // .header(WmsFulfillmenAPI.X_SIGNATURE, generateSignature(request, nonce,
                // timestamp))
                .build();

            log.debug("设置 WMS API 请求头信息, timestamp: {} ,nonce: {} ", timestamp, nonce);
            return Mono.just(modifiedRequest);
        });
    }

    /**
     * 配置WMS声明式HTTP接口
     * 
     * <p>
     * 使用Spring 6.x的HttpServiceProxyFactory创建声明式HTTP接口代理。
     * </p>
     *
     * @param wmsWebClient WMS WebClient
     * @return WmsFulfillmenAPI接口代理
     */
    @Bean
    @ConditionalOnMissingBean
    public WmsFulfillmenAPI wmsFulfillmenAPI(WebClient wmsWebClient) {
        log.info("初始化WMS声明式HTTP接口");

        WebClientAdapter adapter = WebClientAdapter.create(wmsWebClient);
        HttpServiceProxyFactory factory = HttpServiceProxyFactory.builderFor(adapter).build();

        return factory.createClient(WmsFulfillmenAPI.class);
    }
}
