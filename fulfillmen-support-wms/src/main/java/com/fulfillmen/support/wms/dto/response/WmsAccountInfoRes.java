/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * WMS账户信息响应
 *
 * <p>定义WMS账户信息的响应结构，包含客户基本信息、账户余额、联系方式等。
 * 使用JsonAlias注解支持多种字段名格式的兼容性。</p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WmsAccountInfoRes {

    /**
     * 客户 id
     */
    private Integer customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 仓库 id
     */
    private String warehouseId;

    /**
     * API 密钥
     */
    private String apiKey;

    /**
     * 账户余额
     */
    private BigDecimal accountMoney;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 地址
     */
    private String address;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 电话
     */
    private String phone;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 状态
     */
    private String status;

    /**
     * 服务费率
     */
    private BigDecimal serviceFeeRate;

    /**
     * 信用额度
     */
    private BigDecimal creditLimit;
}
