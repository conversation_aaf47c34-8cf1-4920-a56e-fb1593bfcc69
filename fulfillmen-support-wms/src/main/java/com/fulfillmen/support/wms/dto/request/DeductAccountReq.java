/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.wms.dto.enums.WmsPayTypeEnum;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/7/21 17:10
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Deprecated
public class DeductAccountReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户编码
     */
    @JsonProperty("customerCode")
    private String cusCode;
    /**
     * 店铺订单号 - 对应 供应商订单号
     */
    @JsonProperty("orderNo")
    private String orderNo;
    /**
     * 商品金额
     */
    @JsonProperty("productAmount")
    private Double productAmount;
    /**
     * 运费
     */
    @JsonProperty("shippingFee")
    private Integer shippingFee;
    /**
     * 服务费
     */
    @JsonProperty("serviceFee")
    private Double serviceFee;
    /**
     * 总金额
     */
    @JsonProperty("totalAmount")
    private Double totalAmount;

    /**
     * 创建用户
     */
    @JsonProperty("createUser")
    private String createUser;

    /**
     * 备注
     */
    @JsonProperty("remark")
    private String remark;

    /**
     * 支付类型
     */
    @JsonProperty("payType")
    private WmsPayTypeEnum payType;

}
