/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * Wms 订单支付类型枚举
 *
 * <AUTHOR>
 * @date 2025/6/30
 * @description: todo
 * @since 1.0.0
 */
@Getter
public enum WmsPayTypeEnum {

    BALANCE(0, "余额支付"),
    ALIPAY(1, "支付宝支付"),
    CREDIT_CARD(2, "信用卡支付");

    @JsonValue
    private final int code;
    private final String desc;

    WmsPayTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static WmsPayTypeEnum fromValue(int code) {
        for (WmsPayTypeEnum type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown PayTypeEnum code: " + code);
    }
}
