/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * WMS分页DTO
 * 
 * <p>定义WMS API的分页响应结构，包含分页信息和数据列表。
 * 支持泛型数据类型，适用于不同的分页查询结果。</p>
 *
 * @param <T> 分页数据类型
 * <AUTHOR>
 * @created 2025-01-21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WmsPageDTO<T> {

    /**
     * 总记录数
     */
    @JsonProperty("totalCount")
    private Integer total;

    /**
     * 每页大小
     */
    @JsonProperty("pageSize")
    private Integer pageSize;

    /**
     * 当前页码
     */
    @JsonProperty("page")
    private Integer pageIndex;

    /**
     * 总页数
     */
    @JsonProperty("totalPages")
    private Integer totalPages;

    /**
     * 数据列表
     */
    @JsonProperty("record")
    private List<T> records;
}
