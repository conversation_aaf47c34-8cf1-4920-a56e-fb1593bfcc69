/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.wms.dto.enums.WmsOperationTypeEnum;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 账户余额操作
 * <pre>
 * 账户扣费请求示例：
 * <code>
 * {
 * "customerCode": "CUST001",
 * "orderNo": "ORD20231201001",
 * "operationType": "charge",
 * "productAmount": 100.5,
 * "shippingFee": 15,
 * "serviceFee": 5.25,
 * "storeId": "536",
 * "originalTotalPrice": 115.5,
 * "createUser": "SYSTEM",
 * "remark": "[Naya Shop]"
 * }
 * </code>
 * 退款请求示例：
 * <code>
 * {
 * "customerCode": "CUST001",
 * "orderNo": "ORD20231201001",
 * "operationType": "refund",
 * "refundAmount": 120.75,
 * "refundReason": "Order cancelled by customer",
 * "createUser": "SYSTEM",
 * "remark": "[Refund] Order ORD20231201001 Cancelled Refund"
 * }
 * </code>
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/21 17:10
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WmsAccountChargeV2Req implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 客户编码
     */
    @JsonProperty("customerCode")
    private String cusCode;

    /**
     * 操作类型
     */
    private WmsOperationTypeEnum operationType;
    /**
     * 订单号/业务单号
     */
    private String orderNo;
    /**
     * 原始总价
     */
    private BigDecimal originalTotalPrice;
    /**
     * 产品费用（扣费时使用）
     */
    private BigDecimal productAmount;
    /**
     * 退费金额（退费时使用）
     */
    private BigDecimal refundAmount;
    /**
     * 退费原因（退费时使用）
     */
    private String refundReason;
    /**
     * 备注
     */
    private String remark;
    /**
     * 服务费（扣费时使用）
     */
    private BigDecimal serviceFee;
    /**
     * 运费（扣费时使用）
     */
    private BigDecimal shippingFee;
    /**
     * 店铺ID
     */
    private String storeId;

}
