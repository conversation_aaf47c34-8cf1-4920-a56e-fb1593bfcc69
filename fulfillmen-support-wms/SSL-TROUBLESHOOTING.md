# WMS SSL 问题排查指南

## 问题描述

当调用WMS API时出现SSL握手失败错误：

```
io.netty.handler.ssl.StacklessSSLHandshakeException: Connection closed while SSL/TLS handshake was in progress
```

## 解决方案

### 1. 快速解决（开发/测试环境）

在配置文件中添加以下配置来跳过SSL验证：

```yaml
fulfillmen:
  wms:
    ssl:
      skip-verification: true  # 跳过SSL证书验证
      debug-enabled: true      # 开启SSL调试日志
    connection:
      connect-timeout-millis: 15000    # 增加连接超时
      handshake-timeout-millis: 15000  # 增加SSL握手超时
```

### 2. 生产环境解决方案

生产环境不建议跳过SSL验证，应该使用以下配置：

```yaml
fulfillmen:
  wms:
    ssl:
      skip-verification: false  # 保持SSL证书验证
      handshake-timeout-millis: 20000  # 增加SSL握手超时
      debug-enabled: false      # 关闭调试日志
    connection:
      connect-timeout-millis: 20000
      response-timeout-seconds: 90
      read-timeout-seconds: 180
      write-timeout-seconds: 180
```

### 3. 环境变量配置

可以通过环境变量来配置：

```bash
# 跳过SSL验证（仅用于开发/测试）
export WMS_SSL_SKIP_VERIFICATION=true

# 增加超时时间
export WMS_CONNECT_TIMEOUT=20000
export WMS_SSL_HANDSHAKE_TIMEOUT=20000
```

对应的配置文件：

```yaml
fulfillmen:
  wms:
    ssl:
      skip-verification: ${WMS_SSL_SKIP_VERIFICATION:false}
      handshake-timeout-millis: ${WMS_SSL_HANDSHAKE_TIMEOUT:10000}
    connection:
      connect-timeout-millis: ${WMS_CONNECT_TIMEOUT:10000}
```

## 问题排查步骤

### 1. 检查网络连接

```bash
# 测试基本连接
curl -I https://wms.fulfillmen.com

# 测试SSL连接
openssl s_client -connect wms.fulfillmen.com:443 -servername wms.fulfillmen.com
```

### 2. 启用SSL调试日志

在配置中启用SSL调试：

```yaml
fulfillmen:
  wms:
    ssl:
      debug-enabled: true
```

或者通过JVM参数：

```bash
-Djavax.net.debug=ssl,handshake
-Djavax.net.ssl.handshakeTimeout=15000
```

### 3. 检查证书信息

```bash
# 查看证书详情
echo | openssl s_client -connect wms.fulfillmen.com:443 2>/dev/null | openssl x509 -noout -text
```

### 4. 验证配置

检查应用启动日志中的WMS配置信息：

```
初始化WMS WebClient: baseUrl=[https://wms.fulfillmen.com], skipSslVerification=[true]
```

## 常见问题

### Q1: 为什么会出现SSL握手失败？

**可能原因：**

- 目标服务器SSL证书有问题（过期、自签名、域名不匹配）
- 网络环境问题（防火墙、代理）
- SSL协议版本不兼容
- 超时时间设置过短

### Q2: 跳过SSL验证安全吗？

**答案：**

- 开发/测试环境：可以临时使用
- 生产环境：**不建议**，存在安全风险

### Q3: 如何在不跳过SSL验证的情况下解决问题？

**建议：**

1. 联系WMS服务提供方检查SSL证书
2. 增加超时时间配置
3. 检查网络环境和防火墙设置
4. 使用自定义信任库（如果有内部CA证书）

## 配置参考

### 完整配置示例

```yaml
fulfillmen:
  wms:
    enabled: true
    base-url: https://wms.fulfillmen.com
    api-key: ${WMS_API_KEY}
    
    # SSL配置
    ssl:
      skip-verification: ${WMS_SSL_SKIP_VERIFICATION:false}
      handshake-timeout-millis: ${WMS_SSL_HANDSHAKE_TIMEOUT:15000}
      debug-enabled: ${WMS_SSL_DEBUG:false}
    
    # 连接配置
    connection:
      connect-timeout-millis: ${WMS_CONNECT_TIMEOUT:15000}
      response-timeout-seconds: ${WMS_RESPONSE_TIMEOUT:60}
      read-timeout-seconds: ${WMS_READ_TIMEOUT:120}
      write-timeout-seconds: ${WMS_WRITE_TIMEOUT:120}
      max-connections: ${WMS_MAX_CONNECTIONS:50}
      max-idle-time-seconds: ${WMS_MAX_IDLE_TIME:60}
```

### 不同环境的推荐配置

| 环境 | skip-verification | handshake-timeout | connect-timeout |
|----|-------------------|-------------------|-----------------|
| 开发 | true              | 10000ms           | 10000ms         |
| 测试 | true              | 15000ms           | 15000ms         |
| 生产 | false             | 20000ms           | 20000ms         |

## 联系支持

如果问题仍然存在，请联系技术支持并提供：

1. 完整的错误堆栈信息
2. 当前的配置文件
3. SSL调试日志（如果可能）
4. 网络环境信息
