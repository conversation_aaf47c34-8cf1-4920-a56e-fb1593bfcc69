# Fulfillmen Support WMS

WMS API支持模块，提供WMS系统的API调用功能，包括账户管理、订单管理、产品管理等功能。

## 重构成果

本模块已成功从 `@/fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/wms` 重构并迁移到独立的 `@/fulfillmen-support-wms` 模块中。

### 重构特点

1. **使用通用组件** - 集成了 `@/fulfillmen-support-common` 模块中的 `WebClientBuilder` 和通用配置
2. **声明式HTTP接口** - 保持Spring 6.x的声明式HTTP接口模式（@HttpExchange、@GetExchange、@PostExchange）
3. **自动配置** - 使用Spring Boot自动配置机制，支持配置属性绑定
4. **完整的API覆盖** - 保持现有WMS API的功能完整性
5. **全面的测试覆盖** - 包含单元测试和集成测试

## 模块结构

```
fulfillmen-support-wms/
├── src/main/java/com/fulfillmen/support/wms/
│   ├── api/                    # API接口和客户端
│   │   ├── ApiPaths.java       # API路径常量
│   │   ├── WmsFulfillmenAPI.java # 声明式HTTP接口
│   │   └── WmsApiClient.java   # API客户端封装
│   ├── autoconfigure/          # 自动配置
│   │   ├── WmsAutoConfiguration.java # 自动配置类
│   │   └── WmsProperties.java  # 配置属性
│   ├── dto/                    # 数据传输对象
│   │   ├── common/            # 通用DTO
│   │   ├── request/           # 请求DTO
│   │   └── response/          # 响应DTO
│   └── exception/             # 异常类
│       └── WmsApiException.java
└── src/test/java/             # 测试代码
    ├── api/                   # API测试
    ├── autoconfigure/         # 自动配置测试
    └── dto/                   # DTO测试
```

## 核心功能

### 1. 账户管理
- 通过授权码获取账户信息
- 通过客户码获取账户信息
- 采购商品扣减账户余额

### 2. 采购订单管理
- 创建采购订单
- 查询采购订单（分页）
- 查询采购订单详情

### 3. 产品管理
- 创建产品
- 获取产品列表

## 配置说明

### 配置属性

```yaml
fulfillmen:
  wms:
    enabled: true  # 是否启用WMS模块，默认true
    base-url: https://wms.fulfillmen.com  # WMS API基础URL
    api-key: your-api-key  # WMS API密钥

    # SSL配置
    ssl:
      skip-verification: false  # 是否跳过SSL证书验证（仅用于开发/测试环境）
      handshake-timeout-millis: 10000  # SSL握手超时时间（毫秒）
      debug-enabled: false  # 是否启用SSL调试日志

    # 连接配置
    connection:
      connect-timeout-millis: 10000  # 连接超时时间（毫秒）
      response-timeout-seconds: 30   # 响应超时时间（秒）
      read-timeout-seconds: 60       # 读取超时时间（秒）
      write-timeout-seconds: 60      # 写入超时时间（秒）
      max-connections: 100           # 最大连接数
      max-idle-time-seconds: 30      # 最大空闲时间（秒）
```

### 自动配置

模块使用Spring Boot自动配置机制，会自动创建以下Bean：
- `WmsProperties` - 配置属性
- `WebClient` - WMS专用的WebClient实例
- `WmsFulfillmenAPI` - 声明式HTTP接口代理
- `WmsApiClient` - API客户端

## 使用示例

### 1. 基本使用

```java
@Autowired
private WmsApiClient wmsApiClient;

// 获取账户信息
WmsAccountInfoRes accountInfo = wmsApiClient.getAccountInfoByCustomerCode("CUSTOMER001");

// 创建采购订单
WmsCreateOrderReq request = WmsCreateOrderReq.builder()
    .customerCode("CUSTOMER001")
    .orders(orderList)
    .build();
WmsPurchaseDataRes result = wmsApiClient.createPurchaseOrder(request);
```

### 2. 声明式HTTP接口使用

```java
@Autowired
private WmsFulfillmenAPI wmsApi;

// 直接使用声明式接口
Mono<WmsApiResponse<WmsAccountInfoRes>> response = 
    wmsApi.getAccountInfoByCustomerCode("CUSTOMER001");
```

## 技术特性

### 1. 使用通用WebClient配置
- 集成 `@/fulfillmen-support-common` 中的 `WebClientBuilder`
- 统一的连接池配置和超时设置
- 标准化的请求/响应日志记录

### 2. 声明式HTTP接口
- 使用Spring 6.x的 `@HttpExchange` 注解
- 通过 `HttpServiceProxyFactory` 创建接口代理
- 类型安全的API调用

### 3. RSA加密支持
- 内置RSA私钥用于解密WMS返回的加密数据
- 自动处理账户信息的解密逻辑

### 4. 动态请求头注入
- 自动为每个API请求添加动态请求头
- `X-Nonce`: 12位随机字符串，确保请求唯一性
- `X-Timestamp`: 当前时间戳，用于请求时效性验证
- `X-Signature`: 预留签名字段，支持未来签名功能扩展
- 使用WebClient的ExchangeFilterFunction实现请求拦截

### 5. 异常处理
- 统一的 `WmsApiException` 异常类
- 详细的错误信息和日志记录
- 支持异常链追踪

## 测试覆盖

### 单元测试
- `WmsApiClientTest` - API客户端单元测试（8个测试用例）
- `WmsAutoConfigurationTest` - 自动配置测试（5个测试用例）
- `WmsApiResponseTest` - 响应DTO测试（8个测试用例）

### 集成测试
- `WmsApiClientIntegrationTest` - API客户端集成测试（4个测试用例）
  - 使用MockWebServer进行真实HTTP请求测试
  - 验证请求头注入功能（X-Nonce、X-Timestamp）
  - 验证nonce随机性和时间戳准确性
  - 测试所有主要API调用功能
- `BaseIntegrationTest` - 集成测试基础类，提供MockWebServer支持

### 测试运行
```bash
# 运行所有测试（25个测试用例）
mvn test -Ptest -Dspotless.skip=true

# 只运行集成测试
mvn test -Ptest -Dspotless.skip=true -Dtest=WmsApiClientIntegrationTest
```

## 兼容性

### 与现有代码的兼容性
- 保持所有现有WMS API调用的功能完整性
- API签名和认证机制保持不变
- 支持现有的请求/响应数据结构

### 依赖要求
- Spring Boot 3.x
- Spring WebFlux
- Java 17+
- Lombok
- Hutool

## 迁移指南

从原有的 `@/fulfillmen-shop-manager` 中的WMS代码迁移到新模块：

1. **添加依赖**
```xml
<dependency>
    <groupId>com.fulfillmen.support</groupId>
    <artifactId>fulfillmen-support-wms</artifactId>
    <version>${project.version}</version>
</dependency>
```

2. **更新导入包名**
```java
// 原来的导入
import com.fulfillmen.shop.manager.support.wms.*;

// 新的导入
import com.fulfillmen.support.wms.api.*;
import com.fulfillmen.support.wms.dto.*;
```

3. **配置属性**
```yaml
fulfillmen:
  wms:
    base-url: ${WMS_BASE_URL:https://wms.fulfillmen.com}
    api-key: ${WMS_API_KEY}
```

## 版本信息

- **当前版本**: 1.1.9-SNAPSHOT
- **最后更新**: 2025-01-21
- **测试状态**: ✅ 所有测试通过 (25个测试用例，包含4个集成测试)
- **编译状态**: ✅ 编译成功
- **新增功能**: ✅ 动态请求头注入机制 (X-Nonce, X-Timestamp)
- **集成测试**: ✅ MockWebServer集成测试覆盖
