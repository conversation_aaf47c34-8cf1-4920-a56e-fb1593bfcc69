# Fulfillmen Alibaba SDK 开发指南

## 一、项目概述

### 1.1 项目说明

基于 Spring WebFlux 的响应式 SDK，用于对接阿里巴巴1688开放平台的 API。

### 1.2 核心依赖

```xml

<dependencies>
    <!-- Spring WebFlux -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-webflux</artifactId>
    </dependency>
    <!-- Lombok -->
    <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
    </dependency>
    <!-- Apache Commons -->
    <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
    </dependency>
</dependencies>
```

## 二、核心类说明

### 2.1 基础类

```java
// 基础请求类
com.fulfillmen.support.alibaba.api.request.BaseAlibabaRequest
// 请求构建器
com.fulfillmen.support.alibaba.common.AlibabaRequestBuilder
// API 路径常量
com.fulfillmen.support.alibaba.api.ApiPaths
// 配置属性类
com.fulfillmen.support.alibaba.autoconfigure.AlibabaProperties
// 异常类
com.fulfillmen.support.alibaba.exception.AlibabaServiceException
com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException
```

### 2.2 职责说明

| 类名                      | 职责                       | 使用注意                              |
|-------------------------|--------------------------|-----------------------------------|
| BaseAlibabaRequest      | 所有请求对象的基类，提供参数验证和转换的基础方法 | 必须实现 requireParams() 和 toParams() |
| AlibabaRequestBuilder   | 统一的请求处理器，处理签名、调用和错误处理    | 所有 API 调用必须通过此类进行                 |
| ApiPaths                | API 路径常量定义               | 按模块分类管理                           |
| AlibabaProperties       | 配置属性管理                   | 包含 appKey、secretKey 等配置           |
| AlibabaServiceException | 业务异常基类                   | 统一的异常处理                           |

## 三、开发规范

### 3.1 包结构

```
com.fulfillmen.support.alibaba
├── api/                    # API 接口定义
│   ├── ApiPaths.java      # API 路径
│   ├── request/           # 请求对象
│   ├── response/          # 响应对象
│   └── *API.java         # API 接口
├── service/               # 服务层
│   ├── impl/             # 实现类
│   └── interfaces/       # 接口定义
├── common/               # 公共工具
├── exception/            # 异常定义
└── autoconfigure/        # 自动配置
```

### 3.2 命名规范

1. **API 接口命名**：

```java
public interface ProductAPI {

}  // 产品相关 API

public interface OrderAPI {

}    // 订单相关 API
```

2. **请求对象命名**：

```java
public class QueryProductRequest extends BaseAlibabaRequest {

}

public class CreateOrderRequest extends BaseAlibabaRequest {

}
```

3. **服务接口命名**：

```java
public interface IProductService {

}

public interface IOrderService {

}
```

### 3.3 代码模板

1. **请求对象模板**：

```java

@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class XxxRequest extends BaseAlibabaRequest {

    @Override
    public void requireParams() {
        assertNotNull(field, "字段不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("field", field);
        return params;
    }
}
```

2. **API 接口模板**：

```java

@HttpExchange("/")
public interface XxxAPI {

    @PostExchange(value = ApiPaths.XxxAPI.XXX_PATH, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<XxxResponse> methodName(@PathVariable("APPKEY") String appKey,
      @RequestBody MultiValueMap<String, String> params
    );
}
```

3. **服务实现模板**：

```java

@Slf4j
@Service
@RequiredArgsConstructor
public class XxxServiceImpl implements IXxxService {

    private static final String SERVICE_NAME = "服务名称";
    private final XxxAPI xxxAPI;
    private final AlibabaProperties alibabaProperties;

    @Override
    public Mono<XxxResponse> method(XxxRequest request) {
        request.requireParams();
        return AlibabaRequestBuilder.executeRequest(
          request,
          ApiPaths.XxxAPI.PATH,
          formParams -> xxxAPI.methodName(
            alibabaProperties.getOpen1688().getAppKey(),
            formParams
          ),
          "操作名称",
          SERVICE_NAME,
          alibabaProperties.getOpen1688().getAppKey(),
          alibabaProperties.getOpen1688().getSecretKey(),
          alibabaProperties.getOpen1688().getAccessToken()
        );
    }
}
```

## 四、开发流程

1. 定义 API 路径常量
2. 创建请求/响应对象
3. 定义 API 接口
4. 实现服务层
5. 编写单元测试

## 五、注意事项

1. **参数验证**
    - 所有请求必须实现参数验证
    - 使用 assertNotNull/assertNotBlank 等方法
    - 验证失败抛出 AlibabaServiceValidationException

2. **错误处理**
    - 统一使用 AlibabaServiceException
    - 适当的日志记录
    - 错误信息要明确具体

3. **响应式编程**
    - 避免阻塞操作
    - 正确使用 Mono/Flux
    - 合理处理背压

4. **配置管理**
    - 不要硬编码配置
    - 使用 AlibabaProperties 管理配置
    - 支持配置热更新

5. **测试要求**
    - 单元测试覆盖率 > 80%
    - 使用 StepVerifier 测试响应式代码
    - 模拟外部依赖

## 六、常见问题

1. 签名失败
    - 检查参数排序
    - 验证签名算法
    - 确认密钥正确

2. 请求超时
    - 检查超时配置
    - 验证网络连接
    - 查看调用链路

3. 参数错误
    - 检查必填参数
    - 验证参数格式
    - 确认参数编码

## 七、参考资料

1. [1688 开放平台文档](https://open.1688.com/api/doc.htm)
2. [Spring WebFlux 文档](https://docs.spring.io/spring-framework/docs/current/reference/html/web-reactive.html)
3. [Project Reactor 文档](https://projectreactor.io/docs/core/release/reference/)