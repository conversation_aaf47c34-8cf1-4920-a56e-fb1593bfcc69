/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.autoconfigure;

import com.fulfillmen.support.alibaba.api.AiCapabilityAPI;
import com.fulfillmen.support.alibaba.api.BusinessAPI;
import com.fulfillmen.support.alibaba.api.CategoryAPI;
import com.fulfillmen.support.alibaba.api.FeedbackAPI;
import com.fulfillmen.support.alibaba.api.GoodsAPI;
import com.fulfillmen.support.alibaba.api.LogisticsAPI;
import com.fulfillmen.support.alibaba.api.MemberAPI;
import com.fulfillmen.support.alibaba.api.MessageAPI;
import com.fulfillmen.support.alibaba.api.OrderAPI;
import com.fulfillmen.support.alibaba.api.PayAPI;
import com.fulfillmen.support.alibaba.api.RefundAPI;
import com.fulfillmen.support.alibaba.api.ToolsAPI;
import com.fulfillmen.support.common.webclient.WebClientBuilder;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

/**
 * 阿里巴巴开放平台自动配置类 提供WebClient配置和API接口代理配置
 *
 * <AUTHOR>
 * @created 2025-01-08
 * @since 1.0.0
 */
@Slf4j
@EnableConfigurationProperties(AlibabaProperties.class)
public class AlibabaAutoConfiguration {

    /**
     * 指标名称前缀
     */
    private static final String METRIC_NAME_PREFIX = "alibaba.api";

    /**
     * 请求总数指标名称
     */
    private static final String METRIC_NAME_REQUEST = METRIC_NAME_PREFIX + ".request";

    /**
     * 成功请求数指标名称
     */
    private static final String METRIC_NAME_SUCCESS = METRIC_NAME_PREFIX + ".success";

    /**
     * 错误请求数指标名称
     */
    private static final String METRIC_NAME_ERROR = METRIC_NAME_PREFIX + ".error";

    /**
     * 响应时间指标名称
     */
    private static final String METRIC_NAME_RESPONSE_TIME = METRIC_NAME_PREFIX + ".response.time";

    /**
     * 并发请求数指标名称
     */
    private static final String METRIC_NAME_CONCURRENT_REQUESTS = METRIC_NAME_PREFIX + ".concurrent.requests";

    /**
     * 错误率指标名称
     */
    private static final String METRIC_NAME_ERROR_RATE = METRIC_NAME_PREFIX + ".error.rate";

    /**
     * 配置阿里巴巴API的WebClient
     */
    @Bean("alibabaWebClient")
    @ConditionalOnMissingBean(name = "alibabaWebClient")
    @ConditionalOnClass(AlibabaProperties.class)
    public WebClient alibabaWebClient(AlibabaProperties properties, MeterRegistry meterRegistry) {
        return WebClientBuilder.getWebClientBuilder()
            .baseUrl(properties.getOpen1688().getGatewayUrl())
            .filter(enhancedMetricsFilter(meterRegistry))
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE)
            .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
            .build();
    }

    /**
     * 增强的指标记录过滤器
     */
    private ExchangeFilterFunction enhancedMetricsFilter(MeterRegistry meterRegistry) {
        return (request, next) -> {
            Timer.Sample sample = Timer.start(meterRegistry);
            String path = request.url().getPath();
            String method = request.method().name();

            // 请求计数
            meterRegistry.counter(METRIC_NAME_REQUEST, "path", path, "method", method).increment();

            // 并发请求数监控
            AtomicInteger concurrentRequests = meterRegistry
                .gauge(METRIC_NAME_CONCURRENT_REQUESTS, new AtomicInteger(0));
            if (concurrentRequests != null) {
                concurrentRequests.incrementAndGet();
            }

            return next.exchange(request).doOnSuccess(response -> {
                // 成功请求统计
                meterRegistry.counter(METRIC_NAME_SUCCESS, "path", path, "method", method, "status", String
                    .valueOf(response.statusCode().value())).increment();

                // 响应时间统计（包含分位数）
                Timer responseTimer = Timer.builder(METRIC_NAME_RESPONSE_TIME)
                    .tags("path", path, "method", method, "status", String.valueOf(response.statusCode().value()))
                    .publishPercentiles(0.5, 0.75, 0.95, 0.99) // 添加分位数统计
                    .register(meterRegistry);
                sample.stop(responseTimer);
            }).doOnError(error -> {
                // 错误统计（按错误类型分类）
                meterRegistry.counter(METRIC_NAME_ERROR, "path", path, "method", method, "error_type", error.getClass()
                    .getSimpleName(), "error_message", error.getMessage()).increment();

                // 错误率统计
                meterRegistry.gauge(METRIC_NAME_ERROR_RATE, meterRegistry.counter(METRIC_NAME_ERROR)
                    .count() / meterRegistry.counter(METRIC_NAME_REQUEST).count());

                sample.stop(meterRegistry
                    .timer(METRIC_NAME_PREFIX + ".error.duration", "path", path, "method", method, "error", error
                        .getClass()
                        .getSimpleName()));

                log.error("[API调用异常] url={}, method={}, error={}", request.url(), request.method(), error
                    .getMessage(), error);
            }).doFinally(signalType -> {
                if (concurrentRequests != null) {
                    concurrentRequests.decrementAndGet();
                }
            });
        };
    }

    /// /////////////////////////////////// API ///////////////////////////////////

    /**
     * 类目管理API接口代理配置
     */
    @Bean
    @ConditionalOnBean(name = "alibabaWebClient")
    CategoryAPI categoryAPI(@Qualifier("alibabaWebClient") WebClient alibabaWebClient) {
        return HttpServiceProxyFactory.builderFor(WebClientAdapter.create(alibabaWebClient))
            .build()
            .createClient(CategoryAPI.class);
    }

    /**
     * 商品管理API接口代理配置
     */
    @Bean
    @ConditionalOnBean(name = "alibabaWebClient")
    GoodsAPI goodsAPI(@Qualifier("alibabaWebClient") WebClient alibabaWebClient) {
        return HttpServiceProxyFactory.builderFor(WebClientAdapter.create(alibabaWebClient))
            .build()
            .createClient(GoodsAPI.class);
    }

    /**
     * 订单管理API接口代理配置
     */
    @Bean
    @ConditionalOnBean(name = "alibabaWebClient")
    OrderAPI orderAPI(@Qualifier("alibabaWebClient") WebClient alibabaWebClient) {
        return HttpServiceProxyFactory.builderFor(WebClientAdapter.create(alibabaWebClient))
            .build()
            .createClient(OrderAPI.class);
    }

    /**
     * 支付管理API接口代理配置
     */
    @Bean
    @ConditionalOnBean(name = "alibabaWebClient")
    PayAPI payAPI(@Qualifier("alibabaWebClient") WebClient alibabaWebClient) {
        return HttpServiceProxyFactory.builderFor(WebClientAdapter.create(alibabaWebClient))
            .build()
            .createClient(PayAPI.class);
    }

    /**
     * 退款管理API接口代理配置
     */
    @Bean
    @ConditionalOnBean(name = "alibabaWebClient")
    RefundAPI refundAPI(@Qualifier("alibabaWebClient") WebClient alibabaWebClient) {
        return HttpServiceProxyFactory.builderFor(WebClientAdapter.create(alibabaWebClient))
            .build()
            .createClient(RefundAPI.class);
    }

    /**
     * 会员管理API接口代理配置
     */
    @Bean
    @ConditionalOnBean(name = "alibabaWebClient")
    MemberAPI memberAPI(@Qualifier("alibabaWebClient") WebClient alibabaWebClient) {
        return HttpServiceProxyFactory.builderFor(WebClientAdapter.create(alibabaWebClient))
            .build()
            .createClient(MemberAPI.class);
    }

    /**
     * 物流管理API接口代理配置
     */
    @Bean
    @ConditionalOnBean(name = "alibabaWebClient")
    LogisticsAPI logisticsAPI(@Qualifier("alibabaWebClient") WebClient alibabaWebClient) {
        return HttpServiceProxyFactory.builderFor(WebClientAdapter.create(alibabaWebClient))
            .build()
            .createClient(LogisticsAPI.class);
    }

    /**
     * 消息管理API接口代理配置
     */
    @Bean
    @ConditionalOnBean(name = "alibabaWebClient")
    MessageAPI messageAPI(@Qualifier("alibabaWebClient") WebClient alibabaWebClient) {
        return HttpServiceProxyFactory.builderFor(WebClientAdapter.create(alibabaWebClient))
            .build()
            .createClient(MessageAPI.class);
    }

    /**
     * 回传数据API
     */
    @Bean
    @ConditionalOnBean(name = "alibabaWebClient")
    public FeedbackAPI feedbackAPI(@Qualifier("alibabaWebClient") WebClient alibabaWebClient) {
        return HttpServiceProxyFactory.builderFor(WebClientAdapter.create(alibabaWebClient))
            .build()
            .createClient(FeedbackAPI.class);
    }

    /**
     * AI能力API接口
     */
    @Bean
    @ConditionalOnBean(name = "alibabaWebClient")
    public AiCapabilityAPI aiCapabilityAPI(@Qualifier("alibabaWebClient") WebClient alibabaWebClient) {
        return HttpServiceProxyFactory.builderFor(WebClientAdapter.create(alibabaWebClient))
            .build()
            .createClient(AiCapabilityAPI.class);
    }

    /**
     * 工具类API接口代理配置
     */
    @Bean
    @ConditionalOnBean(name = "alibabaWebClient")
    public ToolsAPI toolsAPI(@Qualifier("alibabaWebClient") WebClient alibabaWebClient) {
        return HttpServiceProxyFactory.builderFor(WebClientAdapter.create(alibabaWebClient))
            .build()
            .createClient(ToolsAPI.class);
    }

    /**
     * 商机API接口代理配置
     */
    @Bean
    @ConditionalOnBean(name = "alibabaWebClient")
    public BusinessAPI businessAPI(@Qualifier("alibabaWebClient") WebClient alibabaWebClient) {
        return HttpServiceProxyFactory.builderFor(WebClientAdapter.create(alibabaWebClient))
            .build()
            .createClient(BusinessAPI.class);
    }

    /**
     * 指标注册器配置
     */
    @Bean
    @ConditionalOnMissingBean
    public MeterRegistry meterRegistry() {
        return new SimpleMeterRegistry();
    }
}
