/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.message;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 失败消息批量确认响应
 *
 * <AUTHOR>
 * @created 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MessageConfirmResponse extends BaseAlibabaResponse {

    /**
     * 操作是否成功 类型：boolean 示例：true
     */
    private Boolean isSuccess;
}