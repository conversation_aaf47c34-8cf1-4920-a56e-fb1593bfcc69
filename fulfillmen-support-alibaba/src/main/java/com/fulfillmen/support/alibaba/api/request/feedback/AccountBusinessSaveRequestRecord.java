/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.feedback;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 保存账号所属业务线请求
 *
 * <AUTHOR>
 * @created 2025-01-17
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.account.business.save">保存账号所属业务线 API</a>
 */
@Builder
public record AccountBusinessSaveRequestRecord(
    /**
     * 账号名称 必填
     */
    String account,

    /**
     * 所属业务线 必填
     * 可选值：东南亚sea、南亚sa、日韩jk、港澳台hmt、中东me、北美na、拉美la、西欧we、泛俄ru、非洲af
     */
    String business
) implements BaseAlibabaRequestRecord {

    @Override
    public void requireParams() throws AlibabaServiceValidationException {
        assertNotBlank(account, "账号名称不能为空");
        assertNotBlank(business, "所属业务线不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        Map<String, String> accountPerformance = new HashMap<>();
        accountPerformance.put("account", account);
        accountPerformance.put("business", business);
        params.put("accountPerformance", accountPerformance.toString());
        return params;
    }
}