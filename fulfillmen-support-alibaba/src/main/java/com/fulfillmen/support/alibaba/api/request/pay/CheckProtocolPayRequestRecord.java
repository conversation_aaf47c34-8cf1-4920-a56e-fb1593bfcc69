/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.pay;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 查询是否开通免密支付请求
 * <p>
 * 用于查询是否开通代扣协议
 *
 * <AUTHOR>
 * @created 2025-01-10
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.pay.protocolPay.isopen-1">API文档</a>
 */
@Builder
public record CheckProtocolPayRequestRecord() implements BaseAlibabaRequestRecord {

    /**
     * 创建 CheckProtocolPayRequestRecord 实例
     *
     * @return CheckProtocolPayRequestRecord 实例
     */
    public static CheckProtocolPayRequestRecord of() {
        return new CheckProtocolPayRequestRecord();
    }

    @Override
    public void requireParams() {
        // 无需参数校验
    }

    @Override
    public Map<String, String> toParams() {
        return new HashMap<>();
    }
}