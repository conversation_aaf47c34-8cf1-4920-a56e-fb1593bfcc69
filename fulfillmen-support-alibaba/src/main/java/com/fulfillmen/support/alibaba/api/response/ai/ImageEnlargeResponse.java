/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.ai;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 图片高清放大响应
 *
 * <AUTHOR>
 * @created 2025-01-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ImageEnlargeResponse extends BaseAlibabaResponse {

    /**
     * 错误码：200 代表调用成功，其他的错误代码见参考错误码说明
     */
    private String code;

    /**
     * 成功/失败信息
     */
    private String message;

    /**
     * 放大结果
     */
    private EnlargeImageModel result;

    @Data
    public static class EnlargeImageModel {

        /**
         * 放大后的图片URL
         */
        private String enlargedImageUrl;

        /**
         * 图片宽度
         */
        private Integer imageWidth;

        /**
         * 图片高度
         */
        private Integer imageHeight;
    }
}