/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.message;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import com.fulfillmen.support.alibaba.util.DateUtils;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 查询式获取失败的消息列表请求 获取的消息不会自动确认，需要调用方手动调用确认API来确认消息状态。
 * <p>
 * 参数说明： - createStartTime: 消息创建时间查找范围开始，选填 - createEndTime: 消息创建时间查找范围结束，选填 - page: 当前数据页，默认从1开始，选填 - pageSize: 每次分页获取的数据量，范围20-50，默认20，选填 - type: 消息类型，选填 - userInfo: 用户Id，选填
 *
 * <AUTHOR>
 * @created 2025-01-17
 * @since 1.0.0
 */
@Builder
public record MessageQueryFailedListRequestRecord(
    /**
     * 消息创建时间查找范围开始 选填 格式：yyyyMMddHHmmssSSS+0800
     */
    Date createStartTime,

    /**
     * 消息创建时间查找范围结束 选填 格式：yyyyMMddHHmmssSSS+0800
     */
    Date createEndTime,

    /**
     * 当前数据页，默认从1开始 选填
     */
    Integer page,

    /**
     * 每次分页获取的数据量，范围20-50，默认20 选填
     */
    Integer pageSize,

    /**
     * 消息类型 选填
     */
    String type,

    /**
     * 用户Id 选填
     */
    String userInfo
) implements BaseAlibabaRequestRecord {

    /**
     * 默认每页数据量
     */
    private static final int DEFAULT_PAGE_SIZE = 20;

    /**
     * 默认页码
     */
    private static final int DEFAULT_PAGE_NO = 1;

    /**
     * 最大每页数据量
     */
    private static final int MAX_PAGE_SIZE = 50;

    /**
     * 创建查询式获取失败的消息列表请求
     *
     * @param createStartTime 消息创建时间查找范围开始(选填)
     * @param createEndTime   消息创建时间查找范围结束(选填)
     * @param page            当前数据页(选填,默认1)
     * @param pageSize        每次分页获取的数据量(选填,默认20,范围20-50)
     * @param type            消息类型(选填)
     * @param userInfo        用户Id(选填)
     * @return 查询式获取失败的消息列表请求
     */
    public static MessageQueryFailedListRequestRecord of(Date createStartTime,
        Date createEndTime,
        Integer page,
        Integer pageSize,
        String type,
        String userInfo) {
        return new MessageQueryFailedListRequestRecord(createStartTime, createEndTime, page == null
            ? DEFAULT_PAGE_NO
            : page, pageSize == null ? DEFAULT_PAGE_SIZE : Math.min(pageSize, MAX_PAGE_SIZE), type, userInfo);
    }

    public static MessageQueryFailedListRequestRecord of(Date createStartTime, Date createEndTime) {
        return of(createStartTime, createEndTime, null, null);
    }

    /**
     * 创建查询式获取失败的消息列表请求(使用默认分页参数)
     *
     * @param createStartTime 消息创建时间查找范围开始(选填)
     * @param createEndTime   消息创建时间查找范围结束(选填)
     * @param type            消息类型(选填)
     * @param userInfo        用户Id(选填)
     * @return 查询式获取失败的消息列表请求
     */
    public static MessageQueryFailedListRequestRecord of(Date createStartTime,
        Date createEndTime,
        String type,
        String userInfo) {
        return of(createStartTime, createEndTime, DEFAULT_PAGE_NO, DEFAULT_PAGE_SIZE, type, userInfo);
    }

    @Override
    public void requireParams() {
        if (pageSize != null && (pageSize < DEFAULT_PAGE_SIZE || pageSize > MAX_PAGE_SIZE)) {
            throw new AlibabaServiceValidationException(String
                .format("每页数据量必须在%d-%d之间", DEFAULT_PAGE_SIZE, MAX_PAGE_SIZE));
        }
        if (page != null && page < DEFAULT_PAGE_NO) {
            throw new AlibabaServiceValidationException("页码必须大于等于1");
        }
        if (createStartTime != null && createEndTime != null && createStartTime.after(createEndTime)) {
            throw new AlibabaServiceValidationException("开始时间不能晚于结束时间");
        }
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        if (createStartTime != null) {
            params.put("createStartTime", DateUtils.formatDate(createStartTime));
        }
        if (createEndTime != null) {
            params.put("createEndTime", DateUtils.formatDate(createEndTime));
        }
        if (page != null) {
            params.put("page", String.valueOf(page));
        }
        if (pageSize != null) {
            params.put("pageSize", String.valueOf(pageSize));
        }
        if (type != null) {
            params.put("type", type);
        }
        if (userInfo != null) {
            params.put("userInfo", userInfo);
        }
        return params;
    }
}