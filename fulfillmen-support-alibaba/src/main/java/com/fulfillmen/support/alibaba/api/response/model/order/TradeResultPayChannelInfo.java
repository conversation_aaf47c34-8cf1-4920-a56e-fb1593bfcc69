/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 1688交易支付渠道信息
 *
 * <AUTHOR>
 * @created 2025-01-09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradeResultPayChannelInfo {

    /**
     * 支付渠道名称 "alipay"
     */
    private String name;

    /**
     * 支付渠道金额限制 单位: 分
     */
    private String amountLimit;
}
