/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook.data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 订单消息数据模型
 * 
 * 支持所有 OrderMessageTypeEnums 中定义的订单消息类型，包括：
 * <ul>
 * <li>订单创建、支付、发货、确认收货、交易成功等基础流程消息</li>
 * <li>订单关闭、价格修改等状态变更消息</li>
 * <li>售中退款、售后退款等退款相关消息</li>
 * <li>批量支付状态同步消息</li>
 * </ul>
 * 
 * <p>使用说明：</p>
 * <ul>
 * <li>基础字段（orderId, currentStatus, msgSendTime, buyerMemberId, sellerMemberId）适用于所有消息类型</li>
 * <li>退款字段（refundAction, operator, refundId）仅在退款相关消息中使用</li>
 * <li>批量支付字段（batchPay）仅在批量支付消息中使用</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/11
 * @since 1.0.0
 */
@Data
public class OrderMessage implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    // ==================== 基础字段（所有订单消息都包含） ====================

    /**
     * 订单ID
     * 适用于所有订单消息类型
     */
    private Long orderId;

    /**
     * 当前订单状态
     * 
     * 常见状态值：
     * <ul>
     * <li>waitbuyerpay - 等待买家付款</li>
     * <li>waitsellersend - 等待卖家发货</li>
     * <li>waitbuyerreceive - 等待买家收货</li>
     * <li>confirm_goods_and_has_subsidy - 确认收货且有补贴</li>
     * <li>success - 交易成功</li>
     * <li>cancel - 交易取消</li>
     * <li>refundsuccess - 退款成功</li>
     * </ul>
     */
    private String currentStatus;

    /**
     * 消息发送时间
     * 格式：yyyy-MM-dd HH:mm:ss
     */
    private String msgSendTime;

    /**
     * 买家会员ID
     * 格式：b2b-xxxxxxxxx
     */
    private String buyerMemberId;

    /**
     * 卖家会员ID
     * 格式：b2b-xxxxxxxxx
     */
    private String sellerMemberId;

    // ==================== 退款相关字段（仅退款消息使用） ====================

    /**
     * 退款操作类型
     * 
     * 仅在售中退款和售后退款消息中使用，可能的值包括：
     * <ul>
     * <li>BUYER_APPLY_REFUND - 买家申请退款</li>
     * <li>SELLER_AGREE_REFUND - 卖家同意退款</li>
     * <li>SELLER_REJECT_REFUND - 卖家拒绝退款</li>
     * <li>SELLER_AGREE_REFUND_PROTOCOL - 卖家同意退款协议</li>
     * <li>SYSTEM_AGREE_REFUND_PROTOCOL - 系统超时同意退款协议</li>
     * <li>SYSTEM_AGREE_REFUND - 系统超时同意退款</li>
     * <li>BUYER_RECEIVE_CLOSE(买家确认收货关闭)</li>
     * <li>SELLER_SEND_GOODS_CLOSE(卖家发货关闭)</li>
     * <li>BUYER_CANCEL_REFUND_CLOSE(买家撤销退款申请关闭)</li>
     * <li>BUYER_UPLOAD_BILL(买家上传凭证)</li>
     * <li>SELLER_UPLOAD_BILL(卖家上传凭证)</li>
     * <li>SELLER_RECEIVE_GOODS(卖家确认收货)</li>
     * <li>BUYER_SEND_GOODS(买家声明发货)</li>
     * <li>BUYER_MODIFY_REFUND_PROTOCOL(买家修改退款协议)</li>
     * <li>BUYER_APPLY_SUPPORT(买家申请客服介入)</li>
     * <li>SELLER_APPLY_SUPPORT(卖家申请客服介入)</li>
     * <li>等等...</li>
     * </ul>
     */
    private String refundAction;

    /**
     * 操作发起人
     * 
     * 仅在退款消息中使用，可能的值：
     * <ul>
     * <li>buyer - 买家</li>
     * <li>seller - 卖家</li>
     * <li>system - 系统</li>
     * </ul>
     */
    private String operator;

    /**
     * 退款ID
     * 仅在退款消息中使用
     */
    private String refundId;

    // ==================== 批量支付相关字段（仅批量支付消息使用） ====================

    /**
     * 批量支付列表
     * 仅在 ORDER_BATCH_PAY 消息类型中使用
     */
    private List<BatchPayItem> batchPay;

    // ==================== 便利方法 ====================

    /**
     * 判断是否为退款相关消息
     * 
     * @return 如果包含退款相关字段则返回true
     */
    public boolean isRefundMessage() {
        return refundAction != null || refundId != null;
    }

    /**
     * 判断是否为批量支付消息
     * 
     * @return 如果包含批量支付字段则返回true
     */
    public boolean isBatchPayMessage() {
        return batchPay != null && !batchPay.isEmpty();
    }

    /**
     * 判断是否为成功状态
     * 
     * @return 如果订单状态为成功则返回true
     */
    public boolean isSuccessStatus() {
        return "success".equals(currentStatus);
    }

    /**
     * 判断是否为取消状态
     * 
     * @return 如果订单状态为取消则返回true
     */
    public boolean isCancelStatus() {
        return "cancel".equals(currentStatus);
    }

    // ==================== 内部类 ====================

    /**
     * 批量支付项
     * 用于 ORDER_BATCH_PAY 消息类型
     */
    @Data
    public static class BatchPayItem implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 订单ID
         */
        private String orderId;

        /**
         * 支付状态
         * 
         * 可能的值：
         * <ul>
         * <li>successed - 支付成功</li>
         * <li>ACCOUNT_BALANCE_NOT_ENOUGH - 余额不足</li>
         * <li>ACCOUNT_NOT_EXIST - 账户不存在</li>
         * <li>ACCOUNT_FROZEN - 账户冻结</li>
         * <li>PARAM_ILLEGAL - 参数非法</li>
         * </ul>
         */
        private String status;

        /**
         * 判断支付是否成功
         * 
         * @return 如果支付成功则返回true
         */
        public boolean isPaymentSuccess() {
            return "successed".equals(status);
        }
    }
}
