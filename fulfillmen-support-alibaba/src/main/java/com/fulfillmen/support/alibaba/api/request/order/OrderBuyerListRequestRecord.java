/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.order;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;
import lombok.Getter;

/**
 * 获取订单列表(买家视角)请求
 * <p>
 * 用于获取买家视角的订单列表信息
 *
 * <AUTHOR>
 * @created 2025-01-10
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.getBuyerOrderList-1">API文档</a>
 */
@Builder
public record OrderBuyerListRequestRecord(
    /**
     * 业务类型，支持：
     * <ul>
     * <li>"cn"(普通订单类型)</li>
     * <li>"ws"(大额批发订单类型)</li>
     * <li>"yp"(普通拿样订单类型)</li>
     * <li>"yf"(一分钱拿样订单类型)</li>
     * <li>"fs"(倒批(限时折扣)订单类型)</li>
     * <li>"cz"(加工定制订单类型)</li>
     * <li>"ag"(协议采购订单类型)</li>
     * <li>"hp"(伙拼订单类型)</li>
     * <li>"gc"(国采订单类型)</li>
     * <li>"supply"(供销订单类型)</li>
     * <li>"nyg"(nyg订单类型)</li>
     * <li>"factory"(淘工厂订单类型)</li>
     * <li>"quick"(快订下单)</li>
     * <li>"xiangpin"(享拼订单)</li>
     * <li>"nest"(采购商城-鸟巢)</li>
     * <li>"f2f"(当面付)</li>
     * <li>"cyfw"(存样服务)</li>
     * <li>"sp"(代销订单标记)</li>
     * <li>"wg"(微供订单)</li>
     * <li>"factorysamp"(淘工厂打样订单)</li>
     * <li>"factorybig"(淘工厂大货订单)</li>
     * </ul>
     */
    String bizTypes,

    /**
     * 下单开始时间
     * <p>
     * 格式: yyyyMMddHHmmssSSS+0800
     */
    String createStartTime,

    /**
     * 下单结束时间
     * <p>
     * 格式: yyyyMMddHHmmssSSS+0800
     */
    String createEndTime,

    /**
     * 修改时间开始
     * <p>
     * 格式: yyyyMMddHHmmssSSS+0800
     */
    String modifyStartTime,

    /**
     * 修改时间结束
     * <p>
     * 格式: yyyyMMddHHmmssSSS+0800
     */
    String modifyEndTime,

    /**
     * 订单状态，默认为空，表示所有状态
     * <ul>
     * <li>success: 交易成功</li>
     * <li>cancel: 交易取消/交易关闭</li>
     * <li>waitbuyerpay: 等待买家付款</li>
     * <li>waitsellersend: 等待卖家发货</li>
     * <li>waitbuyerreceive: 等待买家收货</li>
     * <li>waitselleragree: 等待卖家同意</li>
     * </ul>
     */
    String orderStatus,

    /**
     * 页码，从1开始
     */
    Integer page,

    /**
     * 每页条数，建议20
     */
    Integer pageSize,

    /**
     * 卖家memberId
     */
    String sellerMemberId,

    /**
     * 卖家loginId
     */
    String sellerLoginId,

    /**
     * 卖家评价状态
     * <ul>
     * <li>4:已评价</li>
     * <li>5:未评价</li>
     * <li>6:不需要评价</li>
     * </ul>
     */
    Integer sellerRateStatus,

    /**
     * 交易类型
     * <ul>
     * <li>担保交易(1)</li>
     * <li>预存款交易(2)</li>
     * <li>ETC预外收款交易(3)</li>
     * <li>即时到帐交易(4)</li>
     * <li>保障金安全交易(5)</li>
     * <li>统一交易流程(6)</li>
     * <li>分阶段交易(7)</li>
     * <li>货到付款交易(8)</li>
     * <li>信用凭证支付交易(9)</li>
     * <li>账期支付交易(10)</li>
     * <li>1688交易4.0</li>
     * <li>新分阶段交易(50060)</li>
     * <li>当面付的交易流程(50070)</li>
     * <li>服务类的交易流程(50080)</li>
     * </ul>
     */
    String tradeType,

    /**
     * 商品名称
     */
    String productName,

    /**
     * 是否需要买家详细地址信息和电话
     */
    Boolean needBuyerAddressAndPhone,

    /**
     * 是否需要备注信息
     */
    Boolean needMemoInfo,

    /**
     * 外部订单号，可用于控制幂等
     */
    String outOrderId,

    /**
     * 是否查询历史订单表数据，默认查询当前订单表
     */
    Boolean isHis
) implements BaseAlibabaRequestRecord {

    /**
     * 默认分页大小
     */
    private static final int DEFAULT_PAGE_SIZE = 20;

    /**
     * 默认页码
     */
    private static final int DEFAULT_PAGE_NO = 1;

    /**
     * 最大分页大小
     */
    private static final int MAX_PAGE_SIZE = 50;

    /**
     * 默认业务类型
     */
    private static final String DEFAULT_BIZ_TYPE = "cn";

    /**
     * 创建 OrderBuyerListRequestRecord 实例
     *
     * @param page     页码
     * @param pageSize 每页条数
     * @return OrderBuyerListRequestRecord 实例
     */
    public static OrderBuyerListRequestRecord of(Integer page, Integer pageSize) {
        return of(BizTypesEnum.CN, null, page, pageSize, null, null, null, null, null, false, false, null, false, null, null, null, null);
    }

    /**
     * 创建 OrderBuyerListRequestRecord 实例
     *
     * @param orderStatus 订单状态
     * @param page        页码
     * @param pageSize    每页条数
     * @return OrderBuyerListRequestRecord 实例
     */
    public static OrderBuyerListRequestRecord of(OrderStatusEnum orderStatus, Integer page, Integer pageSize) {
        return of(BizTypesEnum.CN, orderStatus, page, pageSize, null, null, null, null, null, false, false, null, false, null, null, null, null);
    }

    /**
     * 默认填充 all 参数
     *
     * @param bizTypes
     * @param orderStatus
     * @param page
     * @param pageSize
     * @param sellerMemberId
     * @param sellerLoginId
     * @param sellerRateStatus
     * @param tradeType
     * @param productName
     * @param needBuyerAddressAndPhone
     * @param needMemoInfo
     * @param outOrderId
     * @param isHis
     * @return
     */
    public static OrderBuyerListRequestRecord of(BizTypesEnum bizTypes,
        OrderStatusEnum orderStatus,
        Integer page,
        Integer pageSize,
        String sellerMemberId,
        String sellerLoginId,
        SellerRateStatusEnum sellerRateStatus,
        TradeTypeEnum tradeType,
        String productName,
        Boolean needBuyerAddressAndPhone,
        Boolean needMemoInfo,
        String outOrderId,
        Boolean isHis,
        String createStartTime,
        String createEndTime,
        String modifyStartTime,
        String modifyEndTime) {
        // 处理分页参数
        int finalPageSize = pageSize == null ? DEFAULT_PAGE_SIZE : Math.min(pageSize, MAX_PAGE_SIZE);
        int finalPage = page == null ? DEFAULT_PAGE_NO : page;
        return new OrderBuyerListRequestRecord(bizTypes != null
            ? bizTypes.getValue()
            : null, createStartTime, createEndTime, modifyStartTime, modifyEndTime, orderStatus != null
                ? orderStatus.getValue()
                : null, finalPage, finalPageSize, sellerMemberId, sellerLoginId, sellerRateStatus != null
                    ? sellerRateStatus.getValue()
                    : null, tradeType != null
                        ? tradeType.getValue()
                        : null, productName, needBuyerAddressAndPhone, needMemoInfo, outOrderId, isHis);
    }

    @Override
    public void requireParams() {
        // 校验分页参数
        assertTrue(page == null || page >= 1, "页码必须大于等于1");
        assertTrue(pageSize == null || (pageSize >= 1 && pageSize <= MAX_PAGE_SIZE), "每页条数必须大于等于1且小于等于" + MAX_PAGE_SIZE);
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();

        // 处理分页参数
        params.put("page", String.valueOf(page != null ? page : DEFAULT_PAGE_NO));
        params.put("pageSize", String.valueOf(pageSize != null
            ? Math.min(pageSize, MAX_PAGE_SIZE)
            : DEFAULT_PAGE_SIZE));

        // 添加其他非空参数
        if (bizTypes != null) {
            params.put("bizTypes", bizTypes);
        }
        if (createStartTime != null) {
            params.put("createStartTime", createStartTime);
        }
        if (createEndTime != null) {
            params.put("createEndTime", createEndTime);
        }
        if (modifyStartTime != null) {
            params.put("modifyStartTime", modifyStartTime);
        }
        if (modifyEndTime != null) {
            params.put("modifyEndTime", modifyEndTime);
        }
        if (orderStatus != null) {
            params.put("orderStatus", orderStatus);
        }
        if (sellerMemberId != null) {
            params.put("sellerMemberId", sellerMemberId);
        }
        if (sellerLoginId != null) {
            params.put("sellerLoginId", sellerLoginId);
        }
        if (sellerRateStatus != null) {
            params.put("sellerRateStatus", sellerRateStatus.toString());
        }
        if (tradeType != null) {
            params.put("tradeType", tradeType);
        }
        if (productName != null) {
            params.put("productName", productName);
        }
        if (needBuyerAddressAndPhone != null) {
            params.put("needBuyerAddressAndPhone", needBuyerAddressAndPhone.toString());
        }
        if (needMemoInfo != null) {
            params.put("needMemoInfo", needMemoInfo.toString());
        }
        if (outOrderId != null) {
            params.put("outOrderId", outOrderId);
        }
        if (isHis != null) {
            params.put("isHis", isHis.toString());
        }

        return params;
    }

    /**
     * 业务类型，支持：
     * <ul>
     * <li>"cn"(普通订单类型)</li>
     * <li>"ws"(大额批发订单类型)</li>
     * <li>"yp"(普通拿样订单类型)</li>
     * <li>"yf"(一分钱拿样订单类型)</li>
     * <li>"fs"(倒批(限时折扣)订单类型)</li>
     * <li>"cz"(加工定制订单类型)</li>
     * <li>"ag"(协议采购订单类型)</li>
     * <li>"hp"(伙拼订单类型)</li>
     * <li>"gc"(国采订单类型)</li>
     * <li>"supply"(供销订单类型)</li>
     * <li>"nyg"(nyg订单类型)</li>
     * <li>"factory"(淘工厂订单类型)</li>
     * <li>"quick"(快订下单)</li>
     * <li>"xiangpin"(享拼订单)</li>
     * <li>"nest"(采购商城-鸟巢)</li>
     * <li>"f2f"(当面付)</li>
     * <li>"cyfw"(存样服务)</li>
     * <li>"sp"(代销订单标记)</li>
     * <li>"wg"(微供订单)</li>
     * <li>"factorysamp"(淘工厂打样订单)</li>
     * <li>"factorybig"(淘工厂大货订单)</li>
     * </ul>
     */
    @Getter
    public enum BizTypesEnum {

        /**
         * 普通订单类型
         */
        CN("cn"),

        /**
         * 大额批发订单类型
         */
        WS("ws"),

        /**
         * 普通拿样订单类型
         */
        YP("yp"),

        /**
         * 一分钱拿样订单类型
         */
        YF("yf"),

        /**
         * 倒批(限时折扣)订单类型
         */
        FS("fs"),

        /**
         * 加工定制订单类型
         */
        CZ("cz"),

        /**
         * 协议采购订单类型
         */
        AG("ag"),

        /**
         * 伙拼订单类型
         */
        HP("hp"),

        /**
         * 国采订单类型
         */
        GC("gc"),

        /**
         * 供销订单类型
         */
        SUPPLY("supply"),

        /**
         * nyg订单类型
         */
        NYG("nyg"),

        /**
         * 淘工厂订单类型
         */
        FACTORY("factory"),

        /**
         * 快订下单
         */
        QUICK("quick"),

        /**
         * 享拼订单
         */
        XIANG_PIN("xiangpin"),

        /**
         * 采购商城-鸟巢
         */
        NEST("nest"),

        /**
         * 当面付
         */
        F2F("f2f"),

        /**
         * 存样服务
         */
        CYFW("cyfw"),

        /**
         * 代销订单标记
         */
        SP("sp"),

        /**
         * 微供订单
         */
        WG("wg"),

        /**
         * 淘工厂打样订单
         */
        FACTORY_SAMP("factorysamp"),

        /**
         * 淘工厂大货订单
         */
        FACTORY_BIG("factorybig");

        private final String value;

        BizTypesEnum(String value) {
            this.value = value;
        }
    }

    /**
     * 订单状态，默认为空，表示所有状态
     * <ul>
     * <li>success: 交易成功</li>
     * <li>cancel: 交易取消/交易关闭</li>
     * <li>waitbuyerpay: 等待买家付款</li>
     * <li>waitsellersend: 等待卖家发货</li>
     * <li>waitbuyerreceive: 等待买家收货</li>
     * <li>waitselleragree: 等待卖家同意</li>
     * </ul>
     */
    @Getter
    public enum OrderStatusEnum {

        /**
         * 交易成功
         */
        SUCCESS("success"),

        /**
         * 交易取消/交易关闭
         */
        CANCEL("cancel"),

        /**
         * 等待买家付款
         */
        WAIT_BUYER_PAY("waitbuyerpay"),

        /**
         * 等待卖家发货
         */
        WAIT_SELLER_SEND("waitsellersend"),

        /**
         * 等待买家收货
         */
        WAIT_BUYER_RECEIVE("waitbuyerreceive"),

        /**
         * 等待卖家同意
         */
        WAIT_SELLER_AGREE("waitselleragree");

        private final String value;

        OrderStatusEnum(String value) {
            this.value = value;
        }
    }

    @Getter
    public enum SellerRateStatusEnum {

        /**
         * 已评价
         */
        RATED(4),

        /**
         * 未评价
         */
        UNRATED(5),

        /**
         * 不需要评价
         */
        NEED_NOT_RATE(6);

        private final Integer value;

        SellerRateStatusEnum(Integer value) {
            this.value = value;
        }
    }

    /**
     * 交易类型
     * <ul>
     * <li>担保交易(1)</li>
     * <li>预存款交易(2)</li>
     * <li>ETC预外收款交易(3)</li>
     * <li>即时到帐交易(4)</li>
     * <li>保障金安全交易(5)</li>
     * <li>统一交易流程(6)</li>
     * <li>分阶段交易(7)</li>
     * <li>货到付款交易(8)</li>
     * <li>信用凭证支付交易(9)</li>
     * <li>账期支付交易(10)</li>
     * <li>1688交易4.0</li>
     * <li>新分阶段交易(50060)</li>
     * <li>当面付的交易流程(50070)</li>
     * <li>服务类的交易流程(50080)</li>
     * </ul>
     */
    @Getter
    public enum TradeTypeEnum {

        /**
         * 担保交易
         */
        GUARANTEE("1"),

        /**
         * 预存款交易
         */
        PRE_DEPOSIT("2"),

        /**
         * ETC预外收款交易
         */
        ETC("3"),

        /**
         * 即时到帐交易
         */
        INSTANT("4"),

        /**
         * 保障金安全交易
         */
        SECURITY("5"),

        /**
         * 统一交易流程
         */
        UNIFIED("6"),

        /**
         * 分阶段交易
         */
        PHASE("7"),

        /**
         * 货到付款交易
         */
        COD("8"),

        /**
         * 信用凭证支付交易
         */
        CREDIT("9"),

        /**
         * 账期支付交易
         */
        ACCOUNT("10"),

        /**
         * 1688交易4.0
         */
        ALIBABA_TRADING_40("1688"),

        /**
         * 新分阶段交易
         */
        NEW_PHASE("50060"),

        /**
         * 当面付的交易流程
         */
        F2F("50070"),

        /**
         * 服务类的交易流程
         */
        SERVICE("50080");

        private final String value;

        TradeTypeEnum(String value) {
            this.value = value;
        }
    }
}
