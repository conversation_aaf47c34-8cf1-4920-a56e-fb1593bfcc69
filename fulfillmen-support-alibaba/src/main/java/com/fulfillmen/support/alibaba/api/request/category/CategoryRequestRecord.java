/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.category;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 类目查询请求参数
 *
 * <AUTHOR>
 * @created 2025-01-17
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:category.get-1">类目查询API文档</a>
 */
@Builder
public record CategoryRequestRecord(/**
                                     * 类目ID（必填）
                                     */
@JsonProperty("categoryID") String categoryId) implements BaseAlibabaRequestRecord {

    @Override
    public void requireParams() {
        assertNotBlank(categoryId, "类目ID不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("categoryID", categoryId);
        return params;
    }
}