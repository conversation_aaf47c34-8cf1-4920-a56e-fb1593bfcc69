/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.refund;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import com.fulfillmen.support.alibaba.api.response.model.RefundReasonListResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询退款原因列表响应
 * <p>
 * 查询可用的退款原因列表的响应结果，包含所有可选的退款原因
 *
 * <AUTHOR>
 * @created 2025-01-13
 * @see RefundReasonListResult
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RefundReasonListResponse extends BaseAlibabaResponse {

    /**
     * 退款原因列表查询结果
     */
    private RefundReasonListResult result;

}