/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.goods;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.Map;
import lombok.Builder;

/**
 * 商品图片上传请求参数
 *
 * <AUTHOR>
 * @created 2025-01-17
 */
@Builder
public record GoodsImageUploadRequestRecord(
    @JsonProperty("imageBase64") String imageBase64,
    @JsonProperty("outMemberId") String outMemberId
) implements BaseAlibabaRequestRecord {

    /**
     * 创建商品图片上传请求
     *
     * @param imageBase64 图片base64（必填）
     * @return 商品图片上传请求
     */
    public static GoodsImageUploadRequestRecord of(String imageBase64) {
        return new GoodsImageUploadRequestRecord(imageBase64, null);
    }

    /**
     * 创建商品图片上传请求
     *
     * @param imageBase64 图片base64（必填）
     * @param outMemberId 外部用户ID（选填）
     * @return 商品图片上传请求
     */
    public static GoodsImageUploadRequestRecord of(String imageBase64, String outMemberId) {
        return new GoodsImageUploadRequestRecord(imageBase64, outMemberId);
    }

    @Override
    public void requireParams() {
        assertNotBlank(imageBase64, "图片base64不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.convertValue(this, new TypeReference<>() {
        });
    }
}