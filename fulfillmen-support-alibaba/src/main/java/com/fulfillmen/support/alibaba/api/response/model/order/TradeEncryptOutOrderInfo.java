/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 外部订单信息
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Data
public class TradeEncryptOutOrderInfo {

    /**
     * 外部订单号
     */
    @JsonProperty("outPlatformOrderNo")
    private String outPlatformOrderNo;

    /**
     * 下游平台
     * <pre>
     * 淘宝-thyny，
     * 天猫-tm，
     * 淘特-taote，
     * 阿里巴巴C2M-c2m，
     * 京东-jingdong，
     * 拼多多-pinduoduo，
     * 微信-weixin，
     * 跨境-kuajing，
     * 快手-kuaishou，
     * 有赞-youzan，
     * 抖音-douyin，
     * 寺库-siku，
     * 美团团好货-meituan，
     * 小红书-xiaohongshu，
     * 当当-dangdang，
     * 苏宁-suning，
     * 大V店-davdian，
     * 行云-xingyun，
     * 蜜芽-miya，
     * 菠萝派商城-boluo，
     * 其他-other
     * </pre>
     */
    @JsonProperty("outPlatformCode")
    private String outPlatformCode;

    /**
     * 获取下游订单信息的下游平台的appkey
     */
    @JsonProperty("outPlatformAppkey")
    private String outPlatformAppKey;

    /**
     * 淘宝oaid
     * <pre>
     * xxx-xxxx-xxx
     * </pre>
     */
    @JsonProperty("oaid")
    private String oaid;

    /**
     * 下游加密收货人姓名
     */
    @JsonProperty("encryptReceiverName")
    private String encryptReceiverName;

    /**
     * 下游加密收货人电话
     */
    @JsonProperty("encryptReceiverMobile")
    private String encryptReceiverMobile;

    /**
     * 下游加密收货人地址
     */
    @JsonProperty("encryptReceiverAddress")
    private String encryptReceiverAddress;

    /**
     * 其他扩展信息
     */
    @JsonProperty("outPlatformExtraInfo")
    private String outPlatformExtraInfo;

    /**
     * 下游平台shopId
     */
    @JsonProperty("outShopId")
    private String outShopId;

    /**
     * 外部原始地址信息
     */
    @JsonProperty("outOriginAddress")
    private TradeOutOriginAddress outOriginAddress;

    /**
     * 下游平台店铺名称
     */
    @JsonProperty("outShopName")
    private String outShopName;

    /**
     * 下游渠道子业务编码
     */
    @JsonProperty("outPlatformSubCode")
    private String outPlatformSubCode;

    /**
     * 下游平台供应链采购订单号
     */
    @JsonProperty("outPlatformSupplyOrderNo")
    private String outPlatformSupplyOrderNo;

    /**
     * 下游平台供应链提供商id
     */
    @JsonProperty("outSupplierId")
    private String outSupplierId;
}
