/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.logistics;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 根据运单号或无主件码查询外部订单ID请求
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Builder
public record LogisticsOutOrderIdRequestRecord(
    /**
     * 运单号 选填(与无主件码至少填一个)
     */
    String shipmentId,

    /**
     * 无主件码 选填(与运单号至少填一个)
     */
    String noMainPartCode
) implements BaseAlibabaRequestRecord {

    /**
     * 创建根据运单号查询外部订单ID请求
     *
     * @param shipmentId 运单号(必填)
     * @return 根据运单号查询外部订单ID请求
     */
    public static LogisticsOutOrderIdRequestRecord ofShipmentId(String shipmentId) {
        return new LogisticsOutOrderIdRequestRecord(shipmentId, null);
    }

    /**
     * 创建根据无主件码查询外部订单ID请求
     *
     * @param noMainPartCode 无主件码(必填)
     * @return 根据无主件码查询外部订单ID请求
     */
    public static LogisticsOutOrderIdRequestRecord ofNoMainPartCode(String noMainPartCode) {
        return new LogisticsOutOrderIdRequestRecord(null, noMainPartCode);
    }

    /**
     * 创建根据运单号或无主件码查询外部订单ID请求
     *
     * @param shipmentId     运单号(选填,与无主件码至少填一个)
     * @param noMainPartCode 无主件码(选填,与运单号至少填一个)
     * @return 根据运单号或无主件码查询外部订单ID请求
     */
    public static LogisticsOutOrderIdRequestRecord of(String shipmentId, String noMainPartCode) {
        return new LogisticsOutOrderIdRequestRecord(shipmentId, noMainPartCode);
    }

    @Override
    public void requireParams() {
        // 运单号和无主件码至少需要一个
        if (shipmentId == null && noMainPartCode == null) {
            throw new AlibabaServiceValidationException("运单号和无主件码不能同时为空");
        }
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        if (shipmentId != null) {
            params.put("shipmentId", shipmentId);
        }
        if (noMainPartCode != null) {
            params.put("noMainPartCode", noMainPartCode);
        }
        return params;
    }
}