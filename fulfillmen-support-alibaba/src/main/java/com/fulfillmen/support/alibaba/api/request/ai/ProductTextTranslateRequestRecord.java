/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.ai;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Builder;

/**
 * 商品文本翻译请求对象
 * <p>
 * 支持将商品文本翻译成指定语言，专门为电商场景研发，实现60多种语向间的精确翻译。
 *
 * <AUTHOR>
 * @created 2025-01-16
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:product.text.translate-1">商品文本翻译API文档</a>
 */
@Builder
public record ProductTextTranslateRequestRecord(
    /**
     * 翻译文本列表 必填，单字符长度不超过1000，建议文本不超过50个
     */
    List<String> sourceTextList,

    /**
     * 源语言代码 必填，使用ISO 693-1语言代码标准，例如"zh"代表中文
     */
    String sourceLanguage,

    /**
     * 目标语言代码 必填，使用ISO 693-1语言代码标准，例如"en"代表英文
     */
    String targetLanguage,

    /**
     * 文本类型 选填，目前支持html和text类型，默认为text
     */
    String formatType
) implements BaseAlibabaRequestRecord {

    @Override
    public void requireParams() {
        assertNotEmpty(sourceTextList, "sourceTextList不能为空");
        assertNotBlank(sourceLanguage, "sourceLanguage不能为空");
        assertNotBlank(targetLanguage, "targetLanguage不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("sourceTextList", toJsonString(sourceTextList));
        params.put("sourceLanguage", sourceLanguage);
        params.put("targetLanguage", targetLanguage);
        if (formatType != null) {
            params.put("formatType", formatType);
        }
        return params;
    }
}