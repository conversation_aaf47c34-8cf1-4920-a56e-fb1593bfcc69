/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.goods;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import com.fulfillmen.support.alibaba.enums.LanguageEnum;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 商品关键词导航请求
 *
 * <AUTHOR>
 * @created 2025-01-17
 */
@Builder
public record GoodsKeywordNavigationRequestRecord(
    /**
     * 搜索关键词(必填)
     */
    String keyword,
    /**
     * 语言(必填)，如英语en_US
     */
    LanguageEnum language,
    /**
     * 地区(必填)，如美国US
     */
    String region,
    /**
     * 币种(必填)，如美元USD
     */
    String currency
) implements BaseAlibabaRequestRecord {

    /**
     * 创建商品关键词导航请求
     *
     * @param keyword  搜索关键词(必填)
     * @param language 语言(必填)，如英语en_US
     * @param region   地区(必填)，如美国US
     * @param currency 币种(必填)，如美元USD
     * @return 商品关键词导航请求
     */
    public static GoodsKeywordNavigationRequestRecord of(String keyword,
        LanguageEnum language,
        String region,
        String currency) {
        return new GoodsKeywordNavigationRequestRecord(keyword, language, region, currency);
    }

    @Override
    public void requireParams() {
        assertNotBlank(keyword, "搜索关键词不能为空");
        assertNotNull(language, "语言不能为空");
        assertNotBlank(region, "地区不能为空");
        assertNotBlank(currency, "币种不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("keyword", keyword);
        params.put("language", language.getLanguage());
        params.put("region", region);
        params.put("currency", currency);
        return params;
    }
}