/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.ai;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 图片智能消除响应
 *
 * <AUTHOR>
 * @created 2025-01-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ImageRemoveResponse extends BaseAlibabaResponse {

    @Data
    public static class Result {

        private Boolean success;
        /**
         * 错误码：200 代表调用成功，其他的错误代码见参考错误码说明
         */
        private String code;

        /**
         * 成功/失败信息
         */
        private String message;

        /**
         * 消除后的图片URL
         */
        private String result;
    }

    /**
     * 消除后的图片URL
     */
    private Result result;

}