/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 1688交易快速创建订单加密信息
 *
 * <AUTHOR>
 * @created 2025-01-09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradeFastCreateOrderEncryptOutOrderInfo {

    /**
     * 是否加密订单
     */
    private Boolean encryptOrder;

    /**
     * 下游平台订单号
     */
    private String outPlatformOrderNo;

    /**
     * 下游平台供应链采购订单
     */
    private String outPlatformSupplyOrderNo;

    /**
     * 平台代码
     */
    private String outPlatformCode;

    /**
     * 下游平台获取订单的appkey
     */
    private String outPlatformAppkey;

    /**
     * 下游平台店铺Id
     */
    private String outShopId;

    /**
     * 下游平台店铺名称
     */
    private String outShopName;

    /**
     * 淘宝oaid
     */
    private String oaid;

    /**
     * 下游平台其他扩展信息
     */
    private String outPatformExtraInfo;

    /**
     * 下游加密收货人姓名
     */
    private String encryptReceiverName;

    /**
     * 下游加密收货人电话
     */
    private String encryptReceiverMobile;

    /**
     * 下游加密收货人地址
     */
    private String encryptReceiverAddress;

    /**
     * 下游渠道子业务编码
     */
    private String outPlatformSubCode;

    /**
     * 下游平台店铺Id
     */
    private String outSupplierId;

    /**
     * 下游原始地址信息
     */
    private TradeParamOutAddress outOriginAddress;
}