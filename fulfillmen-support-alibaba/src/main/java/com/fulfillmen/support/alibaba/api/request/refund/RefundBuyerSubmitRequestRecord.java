/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.refund;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Builder;

/**
 * 买家提交退款申请请求
 *
 * <AUTHOR>
 * @created 2025-01-13
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.returnGoods-1">买家提交退款货信息</a>
 */
@Builder
public record RefundBuyerSubmitRequestRecord(
    /**
     * 退款单号，10开头
     * 必填
     */
    String refundId,

    /**
     * 物流公司编码
     * 必填，使用alibaba.logistics.OpQueryLogisticCompanyList.offline接口查询
     */
    String logisticsCompanyNo,

    /**
     * 物流公司运单号
     * 必填，请填真实单号，否则会有相应的处罚措施
     */
    String freightBill,

    /**
     * 凭证图片URLs
     * <p>
     * 凭证图片URLs，必须使用API alibaba.trade.uploadRefundVoucher返回的"图片域名/相对路径"，最多可上传 10
     * 张图片；
     * 单张大小不超过1M；支持jpg、gif、jpeg、png、和bmp格式。 请上传凭证，以便以后续赔所需（不上传将无法理赔）
     */
    List<String> vouchers,

    /**
     * 发货说明
     * 内容在2-200个字之间
     */
    String description
) implements BaseAlibabaRequestRecord {

    /**
     * 根据退款单号创建请求
     *
     * @param refundId           退款单号
     * @param logisticsCompanyNo 物流公司编码
     * @param freightBill        物流运单号
     * @return 请求
     */
    public static RefundBuyerSubmitRequestRecord of(String refundId, String logisticsCompanyNo, String freightBill) {
        return new RefundBuyerSubmitRequestRecord(refundId, logisticsCompanyNo, freightBill, null, null);
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("refundId", refundId);
        params.put("logisticsCompanyNo", logisticsCompanyNo);
        params.put("freightBill", freightBill);
        if (description != null) {
            params.put("description", description);
        }
        if (vouchers != null) {
            params.put("vouchers", toJsonString(vouchers));
        }
        return params;
    }

    @Override
    public void requireParams() {
        assertNotBlank(refundId, "退款单号不能为空");
        assertNotBlank(logisticsCompanyNo, "物流公司编码不能为空");
        assertNotBlank(freightBill, "物流运单号不能为空");
        if (description != null && (description.length() < 2 || description.length() > 200)) {
            throw new IllegalArgumentException("发货说明内容必须在2-200个字之间");
        }
        if (vouchers != null && vouchers.size() > 10) {
            throw new IllegalArgumentException("凭证图片不能超过10张");
        }
    }
}