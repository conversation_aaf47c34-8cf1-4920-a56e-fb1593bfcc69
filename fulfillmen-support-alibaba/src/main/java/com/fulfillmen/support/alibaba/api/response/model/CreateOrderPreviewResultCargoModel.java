/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model;

import com.fulfillmen.support.alibaba.api.response.model.order.TradePromotionModel;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/9 10:43
 * @description: todo
 * @since 1.0.0
 */
@Data
public class CreateOrderPreviewResultCargoModel {

    /**
     * 产品总金额
     */
    private Double amount;
    /**
     * 返回信息
     */
    private String message;
    /**
     * 最终单价
     */
    private Double finalUnitPrice;
    /**
     * 规格ID，offer内唯一
     */
    private String specId;
    /**
     * 规格ID，全局唯一
     */
    private Long skuId;
    /**
     * 返回码
     */
    private String resultCode;
    /**
     * 商品ID
     */
    private Long offerId;
    /**
     * 加密商品ID
     */
    private String openOfferId;
    /**
     * 商品优惠列表
     */
    private List<TradePromotionModel> cargoPromotionList;

}
