/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.goods;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 相关性商品推荐响应
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsRelatedRecommendResponse extends BaseAlibabaResponse {

    private Result result;

    @Data
    public static class Result {

        /**
         * 是否成功
         */
        private Boolean success;

        /**
         * 错误码
         */
        private String code;

        /**
         * 错误信息
         */
        private String message;

        /**
         * 返回结果
         */
        private ProductInfo[] result;
    }

    @Data
    public static class ProductInfo {

        /**
         * 商品ID
         */
        private Long offerId;

        /**
         * 商品标题
         */
        private String subject;

        /**
         * 商品标题译文
         */
        private String subjectTrans;

        /**
         * 商品主图
         */
        private String imageUrl;

        /**
         * 30天销量
         */
        private Integer monthSold;

        /**
         * 复购率
         */
        private String repurchaseRate;

        /**
         * 价格信息
         */
        private PriceInfo priceInfo;

        /**
         * 商品详情页链接
         */
        private String promotionURL;
    }

    @Data
    public static class PriceInfo {

        /**
         * 批发价
         */
        private String price;

        /**
         * 代发价
         */
        private String consignPrice;
    }
}