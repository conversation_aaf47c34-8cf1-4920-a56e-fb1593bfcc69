/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service.impl;

import com.fulfillmen.support.alibaba.api.ApiPaths;
import com.fulfillmen.support.alibaba.api.MessageAPI;
import com.fulfillmen.support.alibaba.api.request.message.MessageConfirmRequestRecord;
import com.fulfillmen.support.alibaba.api.request.message.MessageCursorRequestRecord;
import com.fulfillmen.support.alibaba.api.request.message.MessageQueryFailedListRequestRecord;
import com.fulfillmen.support.alibaba.api.response.message.MessageConfirmResponse;
import com.fulfillmen.support.alibaba.api.response.message.MessageCursorResponse;
import com.fulfillmen.support.alibaba.api.response.message.MessageQueryFailedListResponse;
import com.fulfillmen.support.alibaba.autoconfigure.AlibabaProperties;
import com.fulfillmen.support.alibaba.service.IMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 消息服务实现类
 *
 * <AUTHOR>
 * @created 2025-01-16
 */
@Slf4j
@Service
public class MessageServiceImpl extends BaseAlibabaServiceImpl implements IMessageService {

    private static final String SERVICE_NAME = "消息服务";
    private final MessageAPI messageAPI;

    public MessageServiceImpl(MessageAPI messageAPI, AlibabaProperties alibabaProperties) {
        super(alibabaProperties);
        this.messageAPI = messageAPI;
    }

    @Override
    protected String getServiceName() {
        return SERVICE_NAME;
    }

    @Override
    public Mono<MessageQueryFailedListResponse> queryFailedMessageList(MessageQueryFailedListRequestRecord request) {
        log.debug("开始处理失败消息列表查询请求: {}", request);
        return wrapWithErrorHandler("查询失败消息列表", request, ApiPaths.MessageAPI.QUERY_FAILED_MESSAGE_LIST, formParams -> messageAPI
            .queryFailedMessageList(appKey, formParams));
    }

    @Override
    public Mono<MessageConfirmResponse> confirmFailedMessage(MessageConfirmRequestRecord request) {
        log.debug("开始处理失败消息确认请求: {}", request);
        return wrapWithErrorHandler("确认失败消息", request, ApiPaths.MessageAPI.CONFIRM_FAILED_MESSAGE, formParams -> messageAPI
            .confirmFailedMessage(appKey, formParams));
    }

    @Override
    public Mono<MessageCursorResponse> getMessageCursorList(MessageCursorRequestRecord request) {
        log.debug("开始处理游标式消息列表查询请求: {}", request);
        return wrapWithErrorHandler("游标式获取失败消息列表", request, ApiPaths.MessageAPI.MESSAGE_CURSOR_LIST, formParams -> messageAPI
            .getMessageCursorList(appKey, formParams));
    }
}