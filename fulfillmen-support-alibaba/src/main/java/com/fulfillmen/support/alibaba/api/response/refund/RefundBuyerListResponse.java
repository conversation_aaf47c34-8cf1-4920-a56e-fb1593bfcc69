/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.refund;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import com.fulfillmen.support.alibaba.api.response.model.RefundBuyerListResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询退款单列表响应
 * <p>
 * 查询买家视角的退款单列表响应结果，包含退款单列表和分页信息
 *
 * <AUTHOR>
 * @created 2025-01-13
 * @see RefundBuyerListResult
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RefundBuyerListResponse extends BaseAlibabaResponse {

    /**
     * 退款单列表查询结果
     */
    private RefundBuyerListResult result;
}