/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.sign;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.HMac;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

/**
 * 阿里巴巴开放平台签名工具
 *
 * <AUTHOR>
 * @date 2024-12-31 09:13
 * @description: 提供阿里巴巴开放平台 API 调用所需的签名生成、验证等功能
 * @since 1.0.0
 */
@Slf4j
public class AlibabaSignature {

    public static final String SIGNATURE_PARAM = "_aop_signature";

    /**
     * 生成签名
     *
     * @param urlPath   API路径 (如: http/1/system/currentTime/1688)
     * @param params    请求参数
     * @param secretKey 密钥
     * @return 签名结果
     */
    public static String sign(String urlPath, Map<String, String> params, String secretKey) {
        // 1. 验证参数
        if (StrUtil.isBlank(urlPath) || StrUtil.isBlank(secretKey)) {
            throw new IllegalArgumentException("urlPath and secretKey cannot be empty");
        }

        // 2. 拼装参数
        List<String> paramPairs = new ArrayList<>();
        if (MapUtil.isNotEmpty(params)) {
            params.forEach((key, value) -> {
                if (!SIGNATURE_PARAM.equals(key) && value != null) {
                    paramPairs.add(key + value);
                }
            });
        }

        // 3. 参数排序
        Collections.sort(paramPairs);

        // 4. 构造签名字符串
        StringBuilder signStr = new StringBuilder(urlPath);
        for (String pair : paramPairs) {
            signStr.append(pair);
        }

        // 5. 执行 HMAC-SHA1 签名
        HMac hmac = SecureUtil.hmacSha1(secretKey.getBytes(StandardCharsets.UTF_8));
        byte[] signBytes = hmac.digest(signStr.toString().getBytes(StandardCharsets.UTF_8));
        // 6. 转换为大写的十六进制字符串
        String signature = HexUtil.encodeHexStr(signBytes).toUpperCase();
        log.info("签名因子: {} 签名: {}", signStr, signature);
        return signature;
    }

    /**
     * 验证签名
     *
     * @param urlPath   API路径
     * @param params    请求参数
     * @param secretKey 密钥
     * @param signature 待验证的签名
     * @return 是否验证通过
     */
    public static boolean verify(String urlPath, Map<String, String> params, String secretKey, String signature) {
        String calculatedSignature = sign(urlPath, params, secretKey);
        return StrUtil.equals(calculatedSignature, signature);
    }

    /**
     * 为URL添加签名参数
     *
     * @param url       原始URL
     * @param params    请求参数
     * @param secretKey 密钥
     * @return 带签名的URL
     */
    public static String signUrl(String url, Map<String, String> params, String secretKey) {
        // 1. 提取 urlPath
        String urlPath = extractUrlPath(url);

        // 2. 生成签名
        String signature = sign(urlPath, params, secretKey);

        // 3. 构建完整URL
        StringBuilder finalUrl = new StringBuilder(url);

        // 4. 添加请求参数
        boolean isFirstParam = !url.contains("?");
        if (isFirstParam) {
            finalUrl.append("?");
        }

        if (MapUtil.isNotEmpty(params)) {
            List<String> paramPairs = new ArrayList<>();
            params.forEach((key, value) -> {
                if (!SIGNATURE_PARAM.equals(key) && value != null) {
                    paramPairs.add(key + "=" + value);
                }
            });
            Collections.sort(paramPairs);

            for (String pair : paramPairs) {
                if (!isFirstParam) {
                    finalUrl.append("&");
                }
                finalUrl.append(pair);
                isFirstParam = false;
            }
        }

        // 5. 添加签名参数
        if (!isFirstParam) {
            finalUrl.append("&");
        }
        finalUrl.append(SIGNATURE_PARAM).append("=").append(signature);

        return finalUrl.toString();
    }

    /**
     * 从URL中提取 urlPath 例如: http://gw.open.1688.com/openapi/http/1/system/currentTime/1688 返回: http/1/system/currentTime/1688
     */
    public static String extractUrlPath(String url) {
        if (StrUtil.isBlank(url)) {
            return "";
        }

        // 1. 移除域名部分
        String path = url;
        if (url.contains("://")) {
            path = url.substring(url.indexOf("://") + 3);
            path = path.substring(path.indexOf('/') + 1);
        }

        // 2. 移除 openapi 前缀
        if (path.startsWith("openapi/")) {
            path = path.substring("openapi/".length());
        }

        // 3. 移除查询参数
        int questionIndex = path.indexOf('?');
        if (questionIndex > 0) {
            path = path.substring(0, questionIndex);
        }

        return path;
    }
}