/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.pay;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import com.fulfillmen.support.alibaba.api.response.model.AccountPeriodInfo;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查询买家信用账期信息响应
 * <p>
 * 包含买家的所有账期信息列表
 *
 * <AUTHOR>
 * @created 2025-01-10
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.accountPeriod.list.buyerView-1">API文档</a>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AccountPeriodListResponse extends BaseAlibabaResponse {

    private AccountPeriodInfoResult resultList;

    @Data
    public static class AccountPeriodInfoResult {

        /**
         * 总数据条数
         */
        private String totalCount;

        /**
         * 授信列表
         */
        private List<AccountPeriodInfo> accountPeriodList;
    }

}