/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.refund;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 取消退款申请请求
 *
 * <AUTHOR>
 * @created 2025-01-13
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.cancelRefund-1">取消退款申请</a>
 */
@Builder
public record RefundCancelRequestRecord(/**
                                         * 退款单号
                                         */
String refundId) implements BaseAlibabaRequestRecord {

    @Override
    public void requireParams() {
        assertNotBlank(refundId, "退款单号不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("refundId", refundId);
        return params;
    }
}