/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * 查询退款单详情(根据订单ID)结果
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Data
public class RefundBuyerOrderViewResult {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 退款单列表
     */
    private List<OpOrderRefundModel> opOrderRefundModels;

    @Data
    public static class OpOrderRefundModel {

        /**
         * 支付宝交易号
         */
        private String alipayPaymentId;

        /**
         * 申请退运费金额（单位：分）
         */
        private Long applyCarriage;

        /**
         * 买家期望退款金额（单位：分）
         */
        private Long applyExpect;

        /**
         * 申请退款金额（单位：分）
         */
        private Long applyPayment;

        /**
         * 申请原因
         */
        private String applyReason;

        /**
         * 申请原因ID
         */
        private Integer applyReasonId;

        /**
         * 申请子原因
         */
        private String applySubReason;

        /**
         * 退款申请编码
         */
        private String applyRefCode;

        /**
         * 申请子原因ID
         */
        private Integer applySubReasonId;

        /**
         * 卖家退款编码
         */
        private String applySellerRefCode;

        /**
         * 买家登录ID
         */
        private String buyerLoginId;

        /**
         * 买家会员ID
         */
        private String buyerMemberId;

        /**
         * 买家用户ID
         */
        private Long buyerUserId;

        /**
         * 极速到账打款渠道
         */
        private String disburseChannel;

        /**
         * 退款类型(1:退款 2:退款退货 3:换货)
         */
        private Integer disputeRequest;

        /**
         * 纠纷类型
         */
        private Integer disputeType;

        /**
         * 扩展信息
         */
        @JsonProperty("extInfo")
        private Map<String, Object> extInfo;

        /**
         * 冻结ID
         */
        private String freezeId;

        /**
         * 申请时间
         */
        @JsonFormat(pattern = "yyyyMMddHHmmssSSS+0800")
        private String gmtApply;

        /**
         * 完成时间
         */
        @JsonFormat(pattern = "yyyyMMddHHmmssSSS+0800")
        private String gmtCompleted;

        /**
         * 创建时间
         */
        @JsonFormat(pattern = "yyyyMMddHHmmssSSS+0800")
        private String gmtCreate;

        /**
         * 冻结开始时间
         */
        @JsonFormat(pattern = "yyyyMMddHHmmssSSS+0800")
        private String gmtFreezed;

        /**
         * 修改时间
         */
        @JsonFormat(pattern = "yyyyMMddHHmmssSSS+0800")
        private String gmtModified;

        /**
         * 超时时间
         */
        @JsonFormat(pattern = "yyyyMMddHHmmssSSS+0800")
        private String gmtTimeOut;

        /**
         * 货物状态(1:未收到货 2:已收到货 3:已退货)
         */
        private Integer goodsStatus;

        /**
         * 退款单ID
         */
        private Long id;

        /**
         * 极速到账退款类型
         */
        private String instantRefundType;

        /**
         * 订单条目数量映射
         */
        private Map<String, Integer> orderEntryCountMap;

        /**
         * 订单条目ID列表
         */
        @JsonProperty("orderEntryIdList")
        private List<String> orderEntryIds;

        /**
         * 订单ID
         */
        private Long orderId;

        /**
         * 垫资金额（单位：分）
         */
        private Long prepaidBalance;

        /**
         * 商品名称
         */
        private String productName;

        /**
         * 退运费金额（单位：分）
         */
        private Long refundCarriage;

        /**
         * 退款单号
         */
        private String refundId;

        /**
         * 退款金额（单位：分）
         */
        private Long refundPayment;

        /**
         * 拒绝原因
         */
        private String rejectReason;

        /**
         * 拒绝原因ID
         */
        private Integer rejectReasonId;

        /**
         * 拒绝次数
         */
        private Integer rejectTimes;

        /**
         * 卖家登录ID
         */
        private String sellerLoginId;

        /**
         * 卖家会员ID
         */
        private String sellerMemberId;

        /**
         * 卖家手机号
         */
        private String sellerMobile;

        /**
         * 卖家姓名
         */
        private String sellerRealName;

        /**
         * 卖家收货地址
         */
        private String sellerReceiveAddress;

        /**
         * 卖家电话
         */
        private String sellerTel;

        /**
         * 卖家用户ID
         */
        private Long sellerUserId;

        /**
         * 退款状态
         */
        private String status;

        /**
         * 工单状态
         */
        private String taskStatus;

        /**
         * 超时操作类型
         */
        private String timeOutOperateType;

        /**
         * 交易类型
         */
        private String tradeTypeStr;

        /**
         * 退款操作记录列表
         */
        private List<RefundOperation> refundOperationList;

        /**
         * 是否CRM修改退款
         */
        private Boolean isCrmModifyRefund;

        /**
         * 是否超时冻结
         */
        private Boolean isTimeOutFreeze;

        /**
         * 是否余额不足
         */
        private Boolean isInsufficientAccount;

        /**
         * 是否已收到货物
         */
        private Boolean isGoodsReceived;

        /**
         * 是否仅退款
         */
        private Boolean isOnlyRefund;

        /**
         * 是否退货
         */
        private Boolean isRefundGoods;

        /**
         * 是否卖家延迟支付
         */
        private Boolean isSellerDelayDisburse;

        /**
         * 是否售后自动打款
         */
        private Boolean isAftersaleAutoDisburse;

        /**
         * 是否支持新的分步支付
         */
        private Boolean isSupportNewSteppay;

        /**
         * 是否新退款退货
         */
        private Boolean isNewRefundReturn;

        /**
         * 是否买家已发货
         */
        private Boolean isBuyerSendGoods;

        /**
         * 是否售后超时
         */
        private Boolean isAftersaleAgreeTimeout;

        /**
         * 是否保证金不足
         */
        private Boolean isInsufficientBail;

        /**
         * 冻结资金（单位：分）
         */
        private Long frozenFund;

        /**
         * 可退款金额（单位：分）
         */
        private Long canRefundPayment;
    }

    @Data
    public static class RefundOperation {

        /**
         * 操作后状态
         */
        private String afterOperateStatus;

        /**
         * 操作前状态
         */
        private String beforeOperateStatus;

        /**
         * 关闭退款阶段ID
         */
        private Long closeRefundStepId;

        /**
         * 是否CRM修改退款
         */
        private Boolean crmModifyRefund;

        /**
         * 描述
         */
        private String discription;

        /**
         * 邮箱
         */
        private String email;

        /**
         * 运单号
         */
        private String freightBill;

        /**
         * 创建时间
         */
        private Date gmtCreate;

        /**
         * 修改时间
         */
        private Date gmtModified;

        /**
         * 操作记录ID
         */
        private Long id;

        /**
         * 留言状态(1:正常 2:屏蔽)
         */
        private Integer messageStatus;

        /**
         * 手机号
         */
        private String mobile;

        /**
         * 留言类型(3:小二留言给卖家和买家 4:给买家的留言 5:给卖家的留言 7:cbu的普通留言)
         */
        private Integer msgType;

        /**
         * 操作备注
         */
        private String operateRemark;

        /**
         * 操作类型
         */
        private Integer operateTypeInt;

        /**
         * 操作者会员ID
         */
        private String operatorId;

        /**
         * 操作者登录ID
         */
        private String operatorLoginId;

        /**
         * 操作者角色ID(1:买家 2:卖家 3:系统)
         */
        private Integer operatorRoleId;

        /**
         * 操作者用户ID
         */
        private Long operatorUserId;

        /**
         * 电话
         */
        private String phone;

        /**
         * 退货地址
         */
        private String refundAddress;

        /**
         * 退款单号
         */
        private String refundId;

        /**
         * 拒绝原因
         */
        private String rejectReason;

        /**
         * 凭证列表
         */
        private List<String> vouchers;

        /**
         * 物流公司信息
         */
        private LogisticsCompany logisticsCompany;
    }

    @Data
    public static class LogisticsCompany {

        /**
         * 公司名称
         */
        private String companyName;

        /**
         * 公司编号
         */
        private String companyNo;

        /**
         * 服务电话
         */
        private String companyPhone;

        /**
         * 创建时间
         */
        private Date gmtCreate;

        /**
         * 修改时间
         */
        private Date gmtModified;

        /**
         * ID
         */
        private Long id;

        /**
         * 拼音
         */
        private String spelling;

        /**
         * 是否支持打印
         */
        private Boolean supportPrint;
    }
}