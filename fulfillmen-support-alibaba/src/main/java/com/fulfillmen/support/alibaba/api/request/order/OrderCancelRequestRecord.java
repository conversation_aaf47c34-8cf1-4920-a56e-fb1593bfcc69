/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 取消交易请求
 * <p>
 * 用于取消1688或国际站的交易订单
 *
 * <AUTHOR>
 * @created 2025-01-10
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.cancel-1">API文档</a>
 */
@Builder
public record OrderCancelRequestRecord(
    /**
     * 站点信息，指定调用的API是属于国际站还是1688网站
     * <p>
     * 1688网站: 1688
     * 国际站: alibaba
     */
    String webSite,

    /**
     * 交易ID
     */
    @JsonProperty("tradeID") Long tradeId,

    /**
     * 取消原因
     * <p>
     * buyerCancel: 买家取消订单
     * sellerGoodsLack: 卖家库存不足
     * other: 其它
     */
    String cancelReason,

    /**
     * 备注
     */
    String remark
) implements BaseAlibabaRequestRecord {

    /**
     * 创建 OrderCancelRequestRecord 实例
     *
     * @param webSite      站点信息
     * @param tradeId      交易ID
     * @param cancelReason 取消原因
     * @param remark       备注
     * @return OrderCancelRequestRecord 实例
     * @throws IllegalArgumentException 如果参数无效
     */
    public static OrderCancelRequestRecord of(String webSite, Long tradeId, String cancelReason, String remark) {
        return new OrderCancelRequestRecord(webSite, tradeId, cancelReason, remark);
    }

    /**
     * 创建 OrderCancelRequestRecord 实例
     *
     * @param webSite      站点信息
     * @param tradeId      交易ID
     * @param cancelReason 取消原因
     * @return OrderCancelRequestRecord 实例
     * @throws IllegalArgumentException 如果参数无效
     */
    public static OrderCancelRequestRecord of(String webSite, Long tradeId, String cancelReason) {
        return of(webSite, tradeId, cancelReason, null);
    }

    @Override
    public void requireParams() {
        assertNotBlank(webSite, "站点不能为空");
        assertNotBlank(cancelReason, "取消原因不能为空");
        assertNotNull(tradeId, "交易ID不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("webSite", webSite);
        params.put("tradeID", tradeId.toString());
        params.put("cancelReason", cancelReason);
        if (remark != null) {
            params.put("remark", remark);
        }
        return params;
    }
}