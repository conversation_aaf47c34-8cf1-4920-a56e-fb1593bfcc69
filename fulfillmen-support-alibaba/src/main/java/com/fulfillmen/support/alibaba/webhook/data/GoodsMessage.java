/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook.data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

import lombok.Data;

/**
 * 商品消息数据模型
 * 
 * 支持所有 GoodsMessageTypeEnums 中定义的商品消息类型，包括：
 * <ul>
 * <li>产品状态变更消息（下架、新增/修改、删除、上架、审核）</li>
 * <li>商品库存变更消息</li>
 * <li>精选货源相关消息（下架、价格变动）</li>
 * <li>跨境相关消息（一键铺货、设为货源）</li>
 * </ul>
 * 
 * <p>使用说明：</p>
 * <ul>
 * <li>基础字段（productIds, memberId, status, msgSendTime）适用于大部分消息类型</li>
 * <li>库存变更字段（offerInventoryChangeList）仅在库存变更消息中使用</li>
 * <li>精选货源字段（offerId, type, openUid, minPrice等）用于精选货源相关消息</li>
 * <li>跨境字段（clientId, productId, productUri, action等）用于跨境相关消息</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/26
 * @since 1.0.0
 */
@Data
public class GoodsMessage implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    // ==================== 基础字段（大部分商品消息都包含） ====================

    /**
     * 产品ID列表
     * 适用于产品状态变更消息
     * 格式：逗号分隔的产品ID字符串，如 "44179498967,************"
     */
    private String productIds;

    /**
     * 会员ID
     * 适用于大部分消息类型
     * 格式：b2b-xxxxxxxxx
     */
    private String memberId;

    /**
     * 产品状态
     * 
     * 用于产品状态变更消息，可能的值：
     * <ul>
     * <li>RELATION_VIEW_PRODUCT_EXPIRE - 产品下架</li>
     * <li>RELATION_VIEW_PRODUCT_NEW_OR_MODIFY - 产品新增或修改</li>
     * <li>RELATION_VIEW_PRODUCT_DELETE - 产品删除</li>
     * <li>RELATION_VIEW_PRODUCT_REPOST - 产品上架</li>
     * <li>RELATION_VIEW_PRODUCT_AUDIT - 产品审核</li>
     * </ul>
     */
    private String status;

    /**
     * 消息发送时间
     * 格式：yyyy-MM-dd HH:mm:ss
     */
    private String msgSendTime;

    // ==================== 库存变更相关字段 ====================

    /**
     * 商品库存变更列表
     * 仅在 PRODUCT_PRODUCT_INVENTORY_CHANGE 消息类型中使用
     */
    private List<OfferInventoryChange> offerInventoryChangeList;

    // ==================== 精选货源相关字段 ====================

    /**
     * 商品ID
     * 用于精选货源相关消息
     */
    private Long offerId;

    /**
     * 商品类型
     * 用于精选货源下架消息，如 "single_direct"
     */
    private String type;

    /**
     * 商家openUid
     * 用于精选货源相关消息
     */
    private String openUid;

    /**
     * 变更价格（分）
     * 用于精选货源价格变动消息
     */
    private Long minPrice;

    /**
     * 价格变更生效时间
     * 用于精选货源价格变动消息
     */
    private Long activeTime;

    /**
     * SKU价格变更信息
     * 用于精选货源价格变动消息
     */
    private Map<String, Object> skuUpdateInfos;

    // ==================== 跨境相关字段 ====================

    /**
     * 客户端ID
     * 用于跨境设为货源消息
     */
    private String clientId;

    /**
     * 产品ID
     * 用于跨境设为货源消息
     */
    private String productId;

    /**
     * 产品URI
     * 用于跨境设为货源消息
     */
    private String productUri;

    /**
     * 操作类型
     * 用于一键铺货消息，如 "distribution"
     */
    private String action;

    /**
     * 用户信息
     * 用于一键铺货消息
     */
    private String userInfo;

    // ==================== 便利方法 ====================

    /**
     * 判断是否为产品状态变更消息
     * 
     * @return 如果包含产品状态相关字段则返回true
     */
    public boolean isProductStatusMessage() {
        return productIds != null && status != null;
    }

    /**
     * 判断是否为库存变更消息
     * 
     * @return 如果包含库存变更相关字段则返回true
     */
    public boolean isInventoryChangeMessage() {
        return offerInventoryChangeList != null && !offerInventoryChangeList.isEmpty();
    }

    /**
     * 判断是否为精选货源消息
     * 
     * @return 如果包含精选货源相关字段则返回true
     */
    public boolean isPftOfferMessage() {
        return offerId != null && (type != null || minPrice != null);
    }

    /**
     * 判断是否为跨境相关消息
     * 
     * @return 如果包含跨境相关字段则返回true
     */
    public boolean isCrossboardMessage() {
        return (clientId != null && productId != null) ||
            (action != null && "distribution".equals(action));
    }

    /**
     * 判断产品状态是否为下架
     * 
     * @return 如果状态为下架则返回true
     */
    public boolean isExpireStatus() {
        return "RELATION_VIEW_PRODUCT_EXPIRE".equals(status);
    }

    /**
     * 判断产品状态是否为新增或修改
     * 
     * @return 如果状态为新增或修改则返回true
     */
    public boolean isNewOrModifyStatus() {
        return "RELATION_VIEW_PRODUCT_NEW_OR_MODIFY".equals(status);
    }

    /**
     * 判断产品状态是否为删除
     * 
     * @return 如果状态为删除则返回true
     */
    public boolean isDeleteStatus() {
        return "RELATION_VIEW_PRODUCT_DELETE".equals(status);
    }

    /**
     * 判断产品状态是否为上架
     * 
     * @return 如果状态为上架则返回true
     */
    public boolean isRepostStatus() {
        return "RELATION_VIEW_PRODUCT_REPOST".equals(status);
    }

    /**
     * 判断产品状态是否为审核
     * 
     * @return 如果状态为审核则返回true
     */
    public boolean isAuditStatus() {
        return "RELATION_VIEW_PRODUCT_AUDIT".equals(status);
    }

    // ==================== 内部类 ====================

    /**
     * 商品库存变更项
     * 用于 PRODUCT_PRODUCT_INVENTORY_CHANGE 消息类型
     */
    @Data
    public static class OfferInventoryChange implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 商品ID
         */
        private Long offerId;

        /**
         * 在线可售offer数量
         */
        private Integer offerOnSale;

        /**
         * SKU ID
         */
        private Long skuId;

        /**
         * 在线可售sku数量
         */
        private Integer skuOnSale;

        /**
         * 该offer整体库存变化数
         */
        private Integer quantity;

        /**
         * 库存变更时间
         */
        private String bizTime;

        /**
         * 判断库存是否增加
         * 
         * @return 如果库存变化数大于0则返回true
         */
        public boolean isInventoryIncrease() {
            return quantity != null && quantity > 0;
        }

        /**
         * 判断库存是否减少
         * 
         * @return 如果库存变化数小于0则返回true
         */
        public boolean isInventoryDecrease() {
            return quantity != null && quantity < 0;
        }
    }
}
