/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.common;

import com.fulfillmen.starter.core.util.JacksonUtil;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequest;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceException;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import com.fulfillmen.support.alibaba.sign.AlibabaSignature;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Mono;

/**
 * 阿里巴巴请求构建器
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@Builder
public class AlibabaRequestBuilder {

    /**
     * 签名时间戳参数名
     */
    private static final String PARAM_TIMESTAMP = "_aop_timestamp";

    /**
     * 签名参数名
     */
    private static final String PARAM_SIGNATURE = "_aop_signature";

    /**
     * 访问令牌参数名
     */
    private static final String PARAM_ACCESS_TOKEN = "access_token";

    /**
     * 应用标识
     */
    private String appKey;

    /**
     * 密钥
     */
    private String secretKey;

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * API路径
     */
    private String urlPath;

    /**
     * 请求参数
     */
    private Map<String, String> params;

    /**
     * 是否JSON参数
     */
    private boolean isJson;

    /**
     * JSON对象
     */
    private String jsonParam;

    /**
     * JSON参数名
     */
    private String jsonParamName;

    /**
     * 创建JSON参数构建器
     *
     * @param appKey        应用标识
     * @param secretKey     密钥
     * @param accessToken   访问令牌
     * @param urlPath       API路径
     * @param jsonParam     JSON对象
     * @param jsonParamName JSON参数名
     * @return 构建器
     */
    public static AlibabaRequestBuilder buildJsonRequest(String appKey,
        String secretKey,
        String accessToken,
        String urlPath,
        String jsonParam,
        String jsonParamName) {
        return AlibabaRequestBuilder.builder()
            .appKey(appKey)
            .secretKey(secretKey)
            .accessToken(accessToken)
            .urlPath(urlPath)
            .isJson(true)
            .jsonParam(jsonParam)
            .jsonParamName(jsonParamName)
            .build();
    }

    /**
     * 创建普通参数构建器
     *
     * @param appKey      应用标识
     * @param secretKey   密钥
     * @param accessToken 访问令牌
     * @param urlPath     API路径
     * @param params      请求参数
     * @return 构建器
     */
    public static AlibabaRequestBuilder buildFormRequest(String appKey,
        String secretKey,
        String accessToken,
        String urlPath,
        Map<String, String> params) {
        return AlibabaRequestBuilder.builder()
            .appKey(appKey)
            .secretKey(secretKey)
            .accessToken(accessToken)
            .urlPath(urlPath)
            .params(params)
            .build();
    }

    /**
     * 执行API请求的通用方法
     *
     * @param request       请求对象
     * @param apiPath       API路径
     * @param apiCall       API调用函数
     * @param operationName 操作名称
     * @param serviceName   服务名称
     * @param isJson        是否为JSON请求
     * @param jsonKey       JSON参数名
     * @return API响应
     */
    public static <T, R extends BaseAlibabaRequest> Mono<T> executeRequest(R request, String apiPath, Function<MultiValueMap<String, String>, Mono<T>> apiCall,
        String operationName, String serviceName, String appKey, String secretKey,
        String accessToken, boolean isJson, String jsonKey) {
        try {
            // 校验请求参数
            request.requireParams();
            MultiValueMap<String, String> formParams;
            // 如果是 json 字符串，则将请求参数转换为 json 字符串
            if (isJson) {
                Map<String, String> params = new HashMap<>();
                params.put(jsonKey, JacksonUtil.toJsonString(request));
                formParams = buildFormRequest(appKey, secretKey, accessToken, apiPath, params).build();
            } else {
                formParams = buildFormRequest(appKey, secretKey, accessToken, apiPath, request.toParams()).build();
            }
            log.debug("[{}] {} 请求参数: {}", serviceName, operationName, formParams);

            return apiCall.apply(formParams)
                .doOnSubscribe(subscription -> log
                    .debug("[{}] {} 请求开始: request={}", serviceName, operationName, request))
                .doOnSuccess(response -> log
                    .info("[{}] {} 请求成功: request={}, response={}", serviceName, operationName, request, response))
                .doOnError(error -> {
                    if (error instanceof AlibabaServiceValidationException) {
                        log.warn("[{}] {} 请求失败: request={}, error={}", serviceName, operationName, request, error
                            .getMessage());
                    } else {
                        log.error("[{}] {} 请求失败: request={}, error={}", serviceName, operationName, request, error
                            .getMessage());
                    }
                })
                .onErrorMap(e -> {
                    if (e instanceof AlibabaServiceValidationException) {
                        return e;
                    }
                    return new AlibabaServiceException(String.format("[%s] %s失败: %s", serviceName, operationName, e
                        .getMessage()), e);
                });
        } catch (Exception e) {
            if (e instanceof AlibabaServiceValidationException) {
                log.warn("[{}] {} 请求异常: request={}, error={}", serviceName, operationName, request, e.getMessage());
            } else {
                log.error("[{}] {} 请求异常: request={}, error={}", serviceName, operationName, request, e.getMessage(), e);
            }
            return Mono.error(e instanceof AlibabaServiceValidationException
                ? e
                : new AlibabaServiceException(String.format("[%s] %s异常: %s", serviceName, operationName, e
                    .getMessage()), e));
        }
    }

    /**
     * 执行普通API请求的简化方法
     */
    public static <T, R extends BaseAlibabaRequest> Mono<T> executeRequest(R request,
        String apiPath,
        Function<MultiValueMap<String, String>, Mono<T>> apiCall,
        String operationName,
        String serviceName,
        String appKey,
        String secretKey,
        String accessToken) {
        return executeRequest(request, apiPath, apiCall, operationName, serviceName, appKey, secretKey, accessToken, false, null);
    }

    /**
     * 构建请求参数
     *
     * @return 表单参数
     */
    public MultiValueMap<String, String> build() {
        try {
            // 校验基础参数
            Assert.hasText(appKey, "appKey不能为空");
            Assert.hasText(secretKey, "secretKey不能为空");
            Assert.hasText(urlPath, "urlPath不能为空");

            // 构建签名参数
            Map<String, String> signParams = new HashMap<>();

            // 添加时间戳
            String timestamp = String.valueOf(System.currentTimeMillis());
            signParams.put(PARAM_TIMESTAMP, timestamp);

            // 添加访问令牌
            if (StringUtils.hasText(accessToken)) {
                signParams.put(PARAM_ACCESS_TOKEN, accessToken);
            }

            // 处理业务参数
            if (isJson) {
                // JSON参数校验
                Assert.notNull(jsonParam, "JSON参数对象不能为空");
                Assert.hasText(jsonParamName, "JSON参数名不能为空");

                String jsonValue = JacksonUtil.toJsonString(jsonParam);
                if (StringUtils.hasText(jsonValue)) {
                    signParams.put(jsonParamName, jsonValue);
                }
            } else if (params != null) {
                params.forEach((key, value) -> {
                    if (StringUtils.hasText(value)) {
                        signParams.put(key, value);
                    }
                });
            }

            // 替换URL中的APPKEY占位符
            String resolvedPath = urlPath.replace("{APPKEY}", appKey);

            // 生成签名
            String signature = AlibabaSignature.sign(resolvedPath, signParams, secretKey);

            // 构建最终请求参数
            MultiValueMap<String, String> formParams = new LinkedMultiValueMap<>();
            signParams.forEach(formParams::add);
            formParams.add(PARAM_SIGNATURE, signature);

            return formParams;
        } catch (Exception e) {
            throw new AlibabaServiceException("构建请求参数失败", e);
        }
    }
}