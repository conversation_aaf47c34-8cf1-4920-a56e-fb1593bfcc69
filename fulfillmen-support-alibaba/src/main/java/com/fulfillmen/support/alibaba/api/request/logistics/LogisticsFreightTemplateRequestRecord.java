/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.logistics;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 获取物流模板详情请求
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Builder
public record LogisticsFreightTemplateRequestRecord(
    /**
     * 模板id，用于单条查询的场景 选填
     */
    Long templateId,

    /**
     * 是否查询子模板 选填
     */
    Boolean querySubTemplate,

    /**
     * 是否查询子模板费率 选填
     */
    Boolean queryRate
) implements BaseAlibabaRequestRecord {

    /**
     * 创建获取物流模板详情请求
     *
     * @param templateId       模板id(选填)
     * @param querySubTemplate 是否查询子模板(选填)
     * @param queryRate        是否查询子模板费率(选填)
     * @return 获取物流模板详情请求
     */
    public static LogisticsFreightTemplateRequestRecord of(Long templateId,
        Boolean querySubTemplate,
        Boolean queryRate) {
        return new LogisticsFreightTemplateRequestRecord(templateId, querySubTemplate, queryRate);
    }

    /**
     * 创建获取物流模板详情请求(仅查询模板基本信息)
     *
     * @param templateId 模板id(必填)
     * @return 获取物流模板详情请求
     */
    public static LogisticsFreightTemplateRequestRecord of(Long templateId) {
        return of(templateId, null, null);
    }

    /**
     * 创建获取物流模板详情请求(仅查询模板基本信息)
     *
     * @return 获取物流模板详情请求
     */
    public static LogisticsFreightTemplateRequestRecord of() {
        return of(null, null, null);
    }

    /**
     * 创建获取物流模板详情请求(包含子模板和费率)
     *
     * @param templateId 模板id(必填)
     * @return 获取物流模板详情请求
     */
    public static LogisticsFreightTemplateRequestRecord ofWithDetails(Long templateId) {
        return of(templateId, true, true);
    }

    @Override
    public void requireParams() {
        // 所有参数都是可选的，无需校验
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();

        if (templateId != null) {
            params.put("templateId", String.valueOf(templateId));
        }
        if (querySubTemplate != null) {
            params.put("querySubTemplate", String.valueOf(querySubTemplate));
        }
        if (queryRate != null) {
            params.put("queryRate", String.valueOf(queryRate));
        }

        return params;
    }
}