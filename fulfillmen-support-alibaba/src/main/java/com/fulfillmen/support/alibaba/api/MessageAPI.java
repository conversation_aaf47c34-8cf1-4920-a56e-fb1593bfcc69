/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api;

import com.fulfillmen.support.alibaba.api.response.message.MessageConfirmResponse;
import com.fulfillmen.support.alibaba.api.response.message.MessageCursorResponse;
import com.fulfillmen.support.alibaba.api.response.message.MessageQueryFailedListResponse;
import org.springframework.http.MediaType;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * 1688消息相关API 提供消息查询、确认等功能
 *
 * <AUTHOR>
 * @created 2025-01-15
 * @since 1.0.0
 */
@HttpExchange("/")
public interface MessageAPI {

    /**
     * 查询式获取失败的消息列表 获取的消息不会自动确认，需要调用方手动调用确认API来确认消息状态。需注意，确认后，会标记分页段的所有消息。
     * <p>
     * 请求参数说明： - createStartTime: 消息创建时间查找范围开始，格式：yyyyMMddHHmmssSSS+0800 - createEndTime: 消息创建时间查找范围结束，格式：yyyyMMddHHmmssSSS+0800 - page: 当前数据页，默认从1开始 - pageSize:
     * 每次分页获取的数据量，范围20-50，默认20 - type: 消息类型，选填 - userInfo: 用户Id，选填
     * <p>
     * 响应结果说明： - success: 是否成功 - errorCode: 错误码 - errorMessage: 错误信息 - pushMessagePage: 分页数据 - datas: 消息列表 - totalCount: 总记录数
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 失败消息列表响应
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=cn.alibaba.open:push.query.messageList-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.MessageAPI.QUERY_FAILED_MESSAGE_LIST, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<MessageQueryFailedListResponse> queryFailedMessageList(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 失败消息批量确认 手动调用确认API，确认消息已经被消费成功。仅当使用查询式获取失败消息的时候，才需要使用此接口进行确认。
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 失败消息批量确认响应
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=cn.alibaba.open:push.message.confirm-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.MessageAPI.CONFIRM_FAILED_MESSAGE, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<MessageConfirmResponse> confirmFailedMessage(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 游标式获取失败的消息列表 获取失败的消息列表，支持游标式分页。每次请求返回的消息会自动从消息队列中删除，所以下次请求不会再获取到相同的消息。
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 失败消息列表响应
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=cn.alibaba.open:push.cursor.messageList-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.MessageAPI.MESSAGE_CURSOR_LIST, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<MessageCursorResponse> getMessageCursorList(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);
}