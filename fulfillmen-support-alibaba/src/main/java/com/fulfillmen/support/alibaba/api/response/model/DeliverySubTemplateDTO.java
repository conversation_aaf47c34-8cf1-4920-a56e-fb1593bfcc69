/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model;

import lombok.Data;

/**
 * 物流子模板
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Data
public class DeliverySubTemplateDTO {

    /**
     * 计件类型。0:重量 1:件数 2:体积
     */
    private Integer chargeType;

    /**
     * 是否系统模板
     */
    private Boolean isSysTemplate;

    /**
     * 运费承担类型 卖家承担：0；买家承担：1
     */
    private Integer serviceChargeType;

    /**
     * 服务类型。0:快递 1:货运 2:货到付款
     */
    private Integer serviceType;

    /**
     * 子模板类型 0基准 1增值。默认0
     */
    private Integer type;
}