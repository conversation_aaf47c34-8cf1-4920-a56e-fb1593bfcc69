/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import com.fulfillmen.support.alibaba.api.response.model.OrderPreviewResult;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 预览订单响应
 * <pre>
 * 订单预览接口的响应结果，包含：
 * 1. 订单总费用、运费、优惠等金额信息
 * 2. 可用的交易方式和支付渠道
 * 3. 商品规格和优惠信息
 * 4. 特殊商品标记（代销、跨境等）
 * </pre>
 *
 * <AUTHOR>
 * @created 2025-01-09
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.createOrder.preview-1">API文档</a>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OrderPreviewResponse extends BaseAlibabaResponse {

    /**
     * 订单预览结果
     */
    @JsonProperty("orderPreviewResuslt")
    private List<OrderPreviewResult> orderPreviewResult;

    /**
     * 运费说明的商品列表
     */
    private List<Long> postFeeByDescOfferList;

    /**
     * 代销商品列表
     */
    private List<Long> consignOfferList;

    /**
     * 不支持跨境宝支付的商品列表
     */
    private List<Long> unsupportedCrossBorderPayOfferList;
}