/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.enums;

import lombok.Getter;

/**
 * 交易消息类型
 *
 * <AUTHOR>
 * @date 2025/7/9 18:57
 * @description: todo
 * @since 1.0.0
 */
@Getter
public enum OrderMessageTypeEnums implements CallbackMessageType {

    /**
     * 1688创建订单（买家视角）/order created (buyer view)
     * <pre>
     * 消息格式:
     * {
     * "orderId": 167539019420540000,
     * "currentStatus": "waitbuyerpay",
     * "msgSendTime": "2018-05-30 19: 24: 18",
     * "buyerMemberId": "b2b-*********",
     * "sellerMemberId": "b2b-1676547900b7bb3"
     * }
     * </pre>
     */
    ORDER_BUYER_VIEW_BUYER_MAKE("ORDER_BUYER_VIEW_BUYER_MAKE", "1688创建订单（买家视角）/order created (buyer view)"),
    /**
     * 1688修改订单价格（买家视角）/order price modification (buyer view)
     * <p>
     * 1688修改订单价格（买家视角），包括关闭子订单。订单价格修改成功后会发送该消息，用户可以根据该消息调用API查询最新的订单价格/1688 order price modification (buyer view)
     * </p>
     * <pre>
     * 消息格式:
     * {
     * "orderId": 167539019420540000,
     * "currentStatus": "waitbuyerpay",
     * "msgSendTime": "2018-05-30 19: 34: 27",
     * "buyerMemberId": "b2b-*********",
     * "sellerMemberId": "b2b-1676547900b7bb3"
     * }
     * </pre>
     */
    ORDER_BUYER_VIEW_ORDER_PRICE_MODIFY("ORDER_BUYER_VIEW_ORDER_PRICE_MODIFY",
        "1688修改订单价格（买家视角）/order price modification (buyer view)"),
    /**
     * 1688交易付款（买家视角）/1688 transaction payment (buyer view)
     * <pre>
     * 1688交易付款（买家视角）。分阶段的第一次付款，都会发送这个消息/1688 transaction payment (buyer view)
     * 消息格式:
     * {
     * "orderId": 167539019420540000,
     * "currentStatus": "waitsellersend",
     * "msgSendTime": "2018-05-30 19: 34: 27",
     * "buyerMemberId": "b2b-*********",
     * "sellerMemberId": "b2b-1676547900b7bb3"
     * }
     * </pre>
     */
    ORDER_BUYER_VIEW_ORDER_PAY("ORDER_BUYER_VIEW_ORDER_PAY", "1688交易付款（买家视角）/1688 transaction payment (buyer view)"),
    /**
     * 1688订单发货（买家视角）/1688 order delivery (buyer view)
     * <pre>
     * 1688订单发货（买家视角）。状态是顺序变化的，但消息是异步发送的，会出现后发的消息先到的情况/1688 order delivery (buyer view)
     * 消息格式:
     * {
     * "orderId": 167539019420540000,
     * "currentStatus": "waitbuyerreceive",
     * "msgSendTime": "2018-05-30 19: 34: 27",
     * "buyerMemberId": "b2b-*********",
     * "sellerMemberId": "b2b-1676547900b7bb3"
     * }
     * </pre>
     */
    ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS("ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS",
        "1688订单发货（买家视角）/1688 order delivery (buyer view)"),
    /**
     * 1688订单部分发货（买家视角）/Partial delivery of 1688 order (buyer view)
     * <pre>
     * 1688订单部分发货（买家视角）。状态是顺序变化的，但消息是异步发送的，会出现后发的消息先到的情况/Partial delivery of 1688 order (buyer view)
     * 消息格式:
     * {
     * "orderId": 167539019420540000,
     * "currentStatus": "waitbuyerreceive",
     * "msgSendTime": "2018-05-30 19: 34: 27",
     * "buyerMemberId": "b2b-*********",
     * "sellerMemberId": "b2b-1676547900b7bb3"
     * }
     * </pre>
     */
    ORDER_BUYER_VIEW_PART_PART_SENDGOODS("ORDER_BUYER_VIEW_PART_PART_SENDGOODS",
        "1688订单部分发货（买家视角）/Partial delivery of 1688 order (buyer view)"),
    /**
     * 1688订单确认收货（买家视角）/order receipt confirmation (buyer view)
     * <pre>
     * 1688订单确认收货（买家视角）。每次确认收货都会发送这个消息，部分确认收货也是这个消息。1688 order receipt confirmation (buyer view)
     * 消息格式:
     * {
     * "orderId": 167539019420540000,
     * "currentStatus": "confirm_goods_and_has_subsidy",
     * "msgSendTime": "2018-05-30 19: 34: 27",
     * "buyerMemberId": "b2b-*********",
     * "sellerMemberId": "b2b-1676547900b7bb3"
     * }
     * </pre>
     */
    ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS("ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS",
        "1688订单确认收货（买家视角）/order receipt confirmation (buyer view)"),
    /**
     * 1688交易成功（买家视角）
     * <pre>
     * 1688交易成功（买家视角）。1688 order success (buyer view)
     * 消息格式:
     * {
     * "orderId": 167539019420540000,
     * "currentStatus": "success",
     * "msgSendTime": "2018-05-30 19: 34: 27",
     * "buyerMemberId": "b2b-*********",
     * "sellerMemberId": "b2b-1676547900b7bb3"
     * }
     * </pre>
     */
    ORDER_BUYER_VIEW_ORDER_SUCCESS("ORDER_BUYER_VIEW_ORDER_SUCCESS", "1688交易成功（买家视角）"),
    /**
     * 1688买家关闭订单（买家视角）/buyer closing order (buyer view)
     * <pre>
     * 1688买家关闭订单（买家视角）。买家关闭订单后会发送该消息，用户可以根据该消息调用API查询订单状态/buyer closing order (buyer view)
     * 消息格式:
     * {
     * "orderId": 167539019420540000,
     * "currentStatus": "cancel",
     * "msgSendTime": "2018-05-30 19: 34: 27",
     * "buyerMemberId": "b2b-*********",
     * "sellerMemberId": "b2b-1676547900b7bb3"
     * }
     * </pre>
     */
    ORDER_BUYER_VIEW_ORDER_BUYER_CLOSE("ORDER_BUYER_VIEW_ORDER_BUYER_CLOSE",
        "1688买家关闭订单（买家视角）/buyer closing order (buyer view)"),
    /**
     * 1688运营后台关闭订单（买家视角）
     * <pre>
     * 1688运营后台关闭订单，付款超时关闭，包括买家拍下未付款，系统自动关闭订单情况
     * {
     * "orderId": 167539019420540000,
     * "currentStatus": "cancel",
     * "msgSendTime": "2018-05-30 19: 34: 27",
     * "buyerMemberId": "b2b-*********",
     * "sellerMemberId": "b2b-1676547900b7bb3"
     * }
     * </pre>
     */
    ORDER_BUYER_VIEW_ORDER_BOPS_CLOSE("ORDER_BUYER_VIEW_ORDER_BOPS_CLOSE", "1688运营后台关闭订单（买家视角）"),
    /**
     * 1688订单售中退款（买家视角）
     * <pre>
     * 1688订单售中退款，交易未完结，仅买家能收到退款消息
     * {
     * "orderId": 1479266113491823456,
     * "currentStatus": "refundsuccess",
     * "msgSendTime": "2018-05-30 19: 30: 13",
     * "buyerMemberId": "b2b-9161839253fcc5",
     * "refundAction": "SYSTEM_AGREE_REFUND_PROTOCOL",
     * "operator": "seller", // 操作的发起人，buyer(买家)，seller(卖家)，system(系统)
     * "sellerMemberId": "b2b-346900403",
     * "refundId": "1234556"
     * }
     * refundAction: 退款操作，具体状态为：
     * <p>
     * BUYER_APPLY_REFUND(买家申请退款)、
     * BUYER_RECEIVE_CLOSE(买家确认收货关闭)、
     * SELLER_SEND_GOODS_CLOSE(卖家发货关闭)、
     * BUYER_CANCEL_REFUND_CLOSE(买家撤销退款申请关闭)、
     * BUYER_UPLOAD_BILL(买家上传凭证)、
     * SELLER_UPLOAD_BILL(卖家上传凭证)、
     * SELLER_REJECT_REFUND(卖家拒绝退款)、
     * SELLER_AGREE_REFUND(卖家同意退款)、
     * SELLER_RECEIVE_GOODS(卖家确认收货)、
     * BUYER_SEND_GOODS(买家声明发货)、
     * BUYER_MODIFY_REFUND_PROTOCOL(买家修改退款协议)、
     * BUYER_APPLY_SUPPORT(买家申请客服介入)、
     * SELLER_APPLY_SUPPORT(卖家申请客服介入)、
     * SYSTEM_AGREE_REFUND_PROTOCOL(系统超时同意退款协议)、
     * SYSTEM_AGREE_REFUND(系统超时同意退款，即退款成功)、
     * SYSTEM_SEND_GOODS(系统超时退货，主交易流程的退货)、
     * SYSTEM_MODIFY_REFUND_PROTOCOL(系统超时修改协议)、
     * SYSTEM_NOTIFY_APPLY_SUPPORT(系统通知申请客服介入)、
     * SELLER_AGREE_REFUND_PROCOTOL(卖家同意退款协议)、
     * SELLER_REJECT_REFUND_PROCOTOL(卖家拒绝退款协议)、
     * CRM_APPLY_TIMEOUT_CLOSE(申请客服介入、超时关闭、目前仅售后业务在用)、
     * CRM_APPLY_SUPPORT(CRM申请介入)、
     * CRM_INTERVENE_TASK(CRM介入处理)、
     * CRM_DISMISS_TASK(CRM撤销工单)、
     * CRM_FINISH_TASK(CRM完结工单)、
     * BUYER_STEP_PAY_ORDER_CLOSE(买家支付，退款关闭，分阶段订单情况)、
     * BUYER_STEP_CONFIRM_CLOSE(买家确认，退款关闭，分阶段订单情况)、
     * BUYER_CLOSE_TRADE_CLOSE(买家终止交易，退款关闭，分阶段订单情况)、
     * SELLER_CONFIRM_ORDER_CLOSE(卖家确认，退款关闭，分阶段订单情况)、
     * SELLER_STEP_PUSH_CLOSE(卖家推进，退款关闭，分阶段订单情况)
     * </p>
     * </pre>
     */
    ORDER_BUYER_VIEW_ORDER_BUYER_REFUND_IN_SALES("ORDER_BUYER_VIEW_ORDER_BUYER_REFUND_IN_SALES", "1688订单售中退款（买家视角）"),
    /**
     * 1688订单售后退款（买家视角）
     * <pre>
     * 1688订单售后退款，交易已完成，仅买家能收到退款消息
     * {
     * "orderId": 1479266113491823456,
     * "currentStatus": "refundsuccess",
     * "msgSendTime": "2018-05-30 19: 30: 13",
     * "buyerMemberId": "b2b-9161839253fcc5",
     * "refundAction": "SYSTEM_AGREE_REFUND_PROTOCOL",
     * "operator": "system", // 操作的发起人，buyer(买家)，seller(卖家)，system(系统)
     * "sellerMemberId": "b2b-346900403",
     * "refundId": "1234556"
     * }
     * </pre>
     */
    ORDER_BUYER_VIEW_ORDER_REFUND_AFTER_SALES("ORDER_BUYER_VIEW_ORDER_REFUND_AFTER_SALES", "1688订单售后退款（买家视角）"),
    /**
     * 1688订单阶段付款（买家视角）
     * <pre>
     * 1688订单阶段付款（买家视角）
     * {
     * "orderId": 167539019420540000,
     * "currentStatus": "waitsellersend",
     * "msgSendTime": "2018-05-30 19: 34: 27",
     * "buyerMemberId": "b2b-*********",
     * "sellerMemberId": "b2b-1676547900b7bb3"
     * }
     * </pre>
     */
    ORDER_BUYER_VIEW_ORDER_STEP_PAY("ORDER_BUYER_VIEW_ORDER_STEP_PAY", "1688订单阶段付款（买家视角）"),
    /**
     * 1688订单批量支付状态同步消息
     * <pre>
     * 订单批量支付状态同步消息，能返回批量支付订单各个订单的支付状态。
     * {
     * "batchPay": [{
     * "orderId": "558880835194545941",
     * "status": "successed"
     * }]
     * }
     * status: 订单支付状态
     * <p>
     * successed（支付成功）、
     * ACCOUNT_BALANCE_NOT_ENOUGH（余额不足）、
     * ACCOUNT_NOT_EXIST（跨境宝2.0场景下可能签约但是在ipay没有开户）、
     * ACCOUNT_FROZEN（账户冻结）、
     * PARAM_ILLEGAL（参数非法）
     * </p>
     * </pre>
     */
    ORDER_BATCH_PAY("ORDER_BATCH_PAY", "1688订单批量支付状态同步消息"),
    /**
     * 1688卖家关闭订单（买家视角）/seller closing order (buyer view)
     * <pre>
     * 1688卖家关闭订单（买家视角）。退款导致交易关闭，也会发送此消息/1688 seller closing order (buyer view)
     * {
     * "orderId": 167539019420540000,
     * "currentStatus": "cancel",
     * "msgSendTime": "2018-05-30 19: 34: 27",
     * "buyerMemberId": "b2b-*********",
     * "sellerMemberId": "b2b-1676547900b7bb3"
     * }
     * </pre>
     */
    ORDER_BUYER_VIEW_ORDER_SELLER_CLOSE("ORDER_BUYER_VIEW_ORDER_SELLER_CLOSE", "1688卖家关闭订单（买家视角）"),

    ;

    /**
     * 消息类型
     */
    private final String messageType;
    /**
     * 消息描述
     */
    private final String messageDesc;

    OrderMessageTypeEnums(String messageType, String messageDesc) {
        this.messageType = messageType;
        this.messageDesc = messageDesc;
    }

    /**
     * 根据消息类型获取枚举
     *
     * @param messageType 消息类型
     * @return 枚举
     */
    public static OrderMessageTypeEnums fromMessageType(String messageType) {
        for (OrderMessageTypeEnums type : OrderMessageTypeEnums.values()) {
            if (type.getMessageType().equals(messageType)) {
                return type;
            }
        }
        return null;
    }
}
