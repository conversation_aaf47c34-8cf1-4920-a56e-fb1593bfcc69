/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.tools;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 拉取商品池中商品数据响应
 * <p>
 * 包含商品池中的商品列表数据，每个商品包含商品ID、类目ID和商品池总数信息。 响应结果会根据请求参数中的排序字段和排序规则进行排序。
 *
 * <AUTHOR>
 * @created 2025-01-16
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:pool.product.pull-1">API文档</a>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PoolProductPullResponse extends BaseAlibabaResponse {

    /**
     * 响应结果
     */
    private Result result;

    @Data
    public static class Result {

        /**
         * 是否成功
         */
        private Boolean success;

        /**
         * 错误码，成功时为S0000
         */
        private String code;

        /**
         * 错误描述，成功时为"成功"
         */
        private String message;

        /**
         * 商品池数据列表
         */
        private List<ProductPool> result;
    }

    @Data
    public static class ProductPool {

        /**
         * 商品ID
         */
        private Long offerId;

        /**
         * 机构的类目ID
         */
        private String bizCategoryId;

        /**
         * 商品池总数(每个offer都返回相同的值)
         */
        private Integer offerPoolTotal;
    }
}