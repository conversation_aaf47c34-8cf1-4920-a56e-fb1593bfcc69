/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.goods;

import lombok.Data;

/**
 * 多语言商品店铺响应
 */
@Data
public class GoodsSellerResponse {

    private Result result;

    @Data
    public static class Result {

        private Boolean success;
        private String code;
        private String message;
        private PageInfo result;
    }

    @Data
    public static class PageInfo {

        private Integer totalRecords;
        private Integer totalPage;
        private Integer pageSize;
        private Integer currentPage;
        private ProductInfo[] data;
    }

    @Data
    public static class ProductInfo {

        private String imageUrl;
        private String subject;
        private String subjectTrans;
        private Long offerId;
        private Boolean isJxhy;
        private String repurchaseRate;
        private Integer monthSold;
        private String traceInfo;
        private Boolean isOnePsale;
        private PriceInfo priceInfo;
        private String createDate;
        private String modifyDate;
        private Boolean isPatentProduct;
        private String[] offerIdentities;
        private String isSelect;
        private String token;
        private String promotionURL;
    }

    @Data
    public static class PriceInfo {

        private String price;
        private String jxhyPrice;
        private String pfJxhyPrice;
        private String consignPrice;
    }
}