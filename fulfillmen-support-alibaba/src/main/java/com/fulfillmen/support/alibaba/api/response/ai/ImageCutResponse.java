/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.ai;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 图片裁剪响应
 *
 * <AUTHOR>
 * @created 2025-01-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ImageCutResponse extends BaseAlibabaResponse {

    /**
     * 错误码：200 代表调用成功，其他的错误代码见参考错误码说明
     */
    private String code;

    /**
     * 成功/失败信息
     */
    private String message;

    /**
     * 裁剪后的图片URL
     */
    private String result;
}