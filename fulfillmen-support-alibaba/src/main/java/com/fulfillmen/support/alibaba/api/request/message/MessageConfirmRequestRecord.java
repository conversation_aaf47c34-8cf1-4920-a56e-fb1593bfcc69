/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.message;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Builder;

/**
 * 失败消息批量确认请求 手动调用确认API，确认消息已经被消费成功。仅当使用查询式获取失败消息的时候，才需要使用此接口进行确认。
 *
 * <AUTHOR>
 * @created 2025-01-17
 */
@Builder
public record MessageConfirmRequestRecord(
    /**
     * 待确认的消息id列表 必填
     */
    List<Long> msgIdList
) implements BaseAlibabaRequestRecord {

    /**
     * 创建失败消息批量确认请求
     *
     * @param msgIdList 待确认的消息id列表(必填)
     * @return 失败消息批量确认请求
     */
    public static MessageConfirmRequestRecord of(List<Long> msgIdList) {
        return new MessageConfirmRequestRecord(msgIdList);
    }

    @Override
    public void requireParams() {
        assertNotEmpty(msgIdList, "待确认的消息id列表不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        if (msgIdList != null && !msgIdList.isEmpty()) {
            params.put("msgIdList", toJsonString(msgIdList));
        }
        return params;
    }
}