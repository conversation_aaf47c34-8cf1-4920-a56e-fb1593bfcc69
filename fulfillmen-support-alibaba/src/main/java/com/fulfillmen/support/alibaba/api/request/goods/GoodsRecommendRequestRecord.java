/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.goods;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import com.fulfillmen.support.alibaba.enums.LanguageEnum;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 商品推荐请求参数
 *
 * <AUTHOR>
 * @created 2025-01-17
 */
@Builder
public record GoodsRecommendRequestRecord(
    /**
     * 语言(必填)，如英语en_US
     */
    String country,
    /**
     * 开始页码(选填，默认1)
     */
    Integer beginPage,
    /**
     * 每页大小(选填，默认20，最大50)
     */
    Integer pageSize,
    /**
     * 外部机构用户id(选填)
     */
    String outMemberId
) implements BaseAlibabaRequestRecord {

    /**
     * 默认分页大小
     */
    private static final int DEFAULT_PAGE_SIZE = 20;
    /**
     * 默认页码
     */
    private static final int DEFAULT_PAGE_NO = 1;
    /**
     * 最大分页大小
     */
    private static final int MAX_PAGE_SIZE = 50;

    /**
     * 创建商品推荐请求
     *
     * @param country 语言（必填）
     * @return 商品推荐请求
     */
    public static GoodsRecommendRequestRecord of(LanguageEnum country) {
        return of(country, DEFAULT_PAGE_NO, DEFAULT_PAGE_SIZE, null);
    }

    /**
     * 创建商品推荐请求
     *
     * @param country   语言（必填）
     * @param beginPage 开始页码（选填，默认1）
     * @param pageSize  每页大小（选填，默认20，最大50）
     * @return 商品推荐请求
     */
    public static GoodsRecommendRequestRecord of(LanguageEnum country, Integer beginPage, Integer pageSize) {
        return of(country, beginPage, pageSize, null);
    }

    /**
     * 创建商品推荐请求
     *
     * @param country     语言（必填）
     * @param beginPage   开始页码（选填，默认1）
     * @param pageSize    每页大小（选填，默认20，最大50）
     * @param outMemberId 外部机构用户id（选填）
     * @return 商品推荐请求
     */
    public static GoodsRecommendRequestRecord of(LanguageEnum country,
        Integer beginPage,
        Integer pageSize,
        String outMemberId) {
        // 分页大小最大不超过50，建议20效果最佳
        pageSize = Math.min(pageSize, MAX_PAGE_SIZE);
        return new GoodsRecommendRequestRecord(country.getLanguage(), beginPage, pageSize, outMemberId);
    }

    @Override
    public void requireParams() {
        assertNotNull(country, "语言不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("country", country);
        params.put("beginPage", beginPage == null ? String.valueOf(DEFAULT_PAGE_NO) : beginPage.toString());
        params.put("pageSize", pageSize == null ? String.valueOf(DEFAULT_PAGE_SIZE) : pageSize.toString());
        if (outMemberId != null) {
            params.put("outMemberId", outMemberId);
        }
        return params;
    }
}