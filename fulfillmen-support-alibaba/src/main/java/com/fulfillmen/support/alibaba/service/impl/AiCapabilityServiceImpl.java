/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service.impl;

import com.fulfillmen.support.alibaba.api.AiCapabilityAPI;
import com.fulfillmen.support.alibaba.api.ApiPaths;
import com.fulfillmen.support.alibaba.api.request.ai.ImageCutRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ImageElementsRecognitionRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ImageEnlargeRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ImageMattingRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ImageRemoveRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ImageTranslateRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ProductDescGenerateRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ProductTextTranslateRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ProductTitleGenerateRequestRecord;
import com.fulfillmen.support.alibaba.api.response.ai.ImageCutResponse;
import com.fulfillmen.support.alibaba.api.response.ai.ImageElementsRecognitionResponse;
import com.fulfillmen.support.alibaba.api.response.ai.ImageEnlargeResponse;
import com.fulfillmen.support.alibaba.api.response.ai.ImageMattingResponse;
import com.fulfillmen.support.alibaba.api.response.ai.ImageRemoveResponse;
import com.fulfillmen.support.alibaba.api.response.ai.ImageTranslateResponse;
import com.fulfillmen.support.alibaba.api.response.ai.ProductDescGenerateResponse;
import com.fulfillmen.support.alibaba.api.response.ai.ProductTextTranslateResponse;
import com.fulfillmen.support.alibaba.api.response.ai.ProductTitleGenerateResponse;
import com.fulfillmen.support.alibaba.autoconfigure.AlibabaProperties;
import com.fulfillmen.support.alibaba.service.IAiCapabilityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * AI能力服务实现类
 * <p>
 * 提供商品文本翻译、标题生成、详描生成等AI能力服务的具体实现。 所有方法都遵循响应式编程模式，使用 Project Reactor 进行异步处理。
 *
 * <AUTHOR>
 * @created 2025-01-16
 */
@Slf4j
@Service
public class AiCapabilityServiceImpl extends BaseAlibabaServiceImpl implements IAiCapabilityService {

    private static final String SERVICE_NAME = "AI能力服务";
    private static final String LOG_ITEM = "[AI能力]";
    private final AiCapabilityAPI aiCapabilityAPI;

    public AiCapabilityServiceImpl(AiCapabilityAPI aiCapabilityAPI,
        AlibabaProperties alibabaProperties) {
        super(alibabaProperties);
        this.aiCapabilityAPI = aiCapabilityAPI;
    }

    @Override
    protected String getServiceName() {
        return SERVICE_NAME;
    }

    @Override
    public Mono<ProductTextTranslateResponse> translateProductText(ProductTextTranslateRequestRecord request) {
        log.debug("{} 开始处理商品文本翻译请求: sourceLanguage={}, targetLanguage={}, textCount={}", LOG_ITEM, request
            .sourceLanguage(), request.targetLanguage(), request.sourceTextList() != null
                ? request.sourceTextList().size()
                : 0);

        return wrapWithErrorHandler("商品文本翻译", request, ApiPaths.AiCapabilityAPI.PRODUCT_TEXT_TRANSLATE, formParams -> aiCapabilityAPI
            .translateProductText(appKey, formParams)).doOnNext(response -> log
                .debug("{} 商品文本翻译完成: success={}, code={}, message={}", LOG_ITEM, response.getSuccess(), response
                    .getCode(), response.getMessage()))
            .doOnError(error -> log.error("{} 商品文本翻译失败: {}", LOG_ITEM, error.getMessage()));
    }

    @Override
    public Mono<ProductTitleGenerateResponse> generateProductTitle(ProductTitleGenerateRequestRecord request) {
        log.debug("{} 开始处理商品标题生成请求: productName={}, targetLanguage={}, category={}", LOG_ITEM, request
            .productName(), request.targetLanguage(), request.productCategory());

        return wrapWithErrorHandler("商品标题生成", request, ApiPaths.AiCapabilityAPI.PRODUCT_TITLE_GENERATE, formParams -> aiCapabilityAPI
            .generateProductTitle(appKey, formParams), "generateTitleParam");
    }

    @Override
    public Mono<ProductDescGenerateResponse> generateProductDesc(ProductDescGenerateRequestRecord request) {
        log.debug("{} 开始处理商品详描生成请求: productName={}, targetLanguage={}, category={}", LOG_ITEM, request
            .productName(), request.targetLanguage(), request.productCategory());

        return wrapWithErrorHandler("商品详描生成", request, ApiPaths.AiCapabilityAPI.PRODUCT_DESC_GENERATE, formParams -> aiCapabilityAPI
            .generateProductDesc(appKey, formParams), "generateDescription");
    }

    @Override
    public Mono<ImageTranslateResponse> translateImage(ImageTranslateRequestRecord request) {
        log.debug("{} 开始处理图片翻译请求: imageUrl={}, originalLanguage={}, targetLanguage={}", LOG_ITEM, request
            .imageUrl(), request.originalLanguage(), request.targetLanguage());

        return wrapWithErrorHandler("图片翻译", request, ApiPaths.AiCapabilityAPI.IMAGE_TRANSLATE, formParams -> aiCapabilityAPI
            .translateImage(appKey, formParams));
    }

    @Override
    public Mono<ImageCutResponse> cutImage(ImageCutRequestRecord request) {
        log.debug("{} 开始处理图片裁剪请求: imageUrl={}, width={}, height={}", LOG_ITEM, request.imageUrl(), request
            .width(), request.height());

        return wrapWithErrorHandler("图片裁剪", request, ApiPaths.AiCapabilityAPI.IMAGE_CUT, formParams -> aiCapabilityAPI
            .cutImage(appKey, formParams));
    }

    @Override
    public Mono<ImageEnlargeResponse> enlargeImage(ImageEnlargeRequestRecord request) {
        log.debug("{} 开始处理图片高清放大请求: imageUrl={}, upscaleFactor={}", LOG_ITEM, request.imageUrl(), request
            .upscaleFactor());

        return wrapWithErrorHandler("图片高清放大", request, ApiPaths.AiCapabilityAPI.IMAGE_ENLARGE, formParams -> aiCapabilityAPI
            .enlargeImage(appKey, formParams));
    }

    @Override
    public Mono<ImageMattingResponse> mattingImage(ImageMattingRequestRecord request) {
        log.debug("{} 开始处理图片智能抠图请求: imageUrl={}, backgroundBGR={}, height={}, width={}", LOG_ITEM, request
            .imageUrl(), request.backgroundBGR(), request.height(), request.width());

        return wrapWithErrorHandler("图片智能抠图", request, ApiPaths.AiCapabilityAPI.IMAGE_MATTING, formParams -> aiCapabilityAPI
            .mattingImage(appKey, formParams));
    }

    @Override
    public Mono<ImageRemoveResponse> removeImage(ImageRemoveRequestRecord request) {
        log.debug("{} 开始处理图片智能消除请求: imageUrl={}, noobjRemoveCharacter={}, noobjRemoveLogo={}, noobjRemoveNpx={}, " + "noobjRemoveQrcode={}, noobjRemoveWatermark={}, objRemoveCharacter={}, objRemoveLogo={}, "
            + "objRemoveNpx={}, objRemoveQrcode={}, objRemoveWatermark={}", LOG_ITEM, request
                .imageUrl(), request.noobjRemoveCharacter(), request.noobjRemoveLogo(), request.noobjRemoveNpx(), request
                    .noobjRemoveQrcode(), request.noobjRemoveWatermark(), request.objRemoveCharacter(), request
                        .objRemoveLogo(), request.objRemoveNpx(), request.objRemoveQrcode(), request.objRemoveWatermark());

        return wrapWithErrorHandler("图片智能消除", request, ApiPaths.AiCapabilityAPI.IMAGE_REMOVE, formParams -> aiCapabilityAPI
            .removeImage(appKey, formParams), "param");
    }

    @Override
    public Mono<ImageElementsRecognitionResponse> recognizeImageElements(ImageElementsRecognitionRequestRecord request) {
        log.debug("{} 开始处理图像元素识别请求: imageUrl={}, detectElements=[{}, {}]", LOG_ITEM, request.imageUrl(), request
            .objectDetectElements() != null ? String.join(",", request.objectDetectElements()) : "", request
                .nonObjectDetectElements() != null ? String.join(",", request.nonObjectDetectElements()) : "");

        return wrapWithErrorHandler("图像元素识别", request, ApiPaths.AiCapabilityAPI.IMAGE_ELEMENTS_RECOGNITION, formParams -> aiCapabilityAPI
            .recognizeImageElements(appKey, formParams), "recognitionImageParam");
    }
}
