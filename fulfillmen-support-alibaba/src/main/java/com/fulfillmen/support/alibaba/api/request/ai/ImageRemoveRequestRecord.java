/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.ai;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 图片智能消除请求记录
 * <p>
 * 用于去除图片中的文字、特定标识、遮挡手势和牛皮癣，可提高图像使用内的美观度。
 *
 * @param imageUrl             图片URL，支持JPG、JPEG、PNG、BMP格式，分辨率在512x512至3000x3000像素之间，文件大小不超过10MB
 * @param noobjRemoveCharacter 非主体消除文字
 * @param noobjRemoveLogo      非主体消除Logo
 * @param noobjRemoveNpx       非主体消除牛皮癣
 * @param noobjRemoveQrcode    非主体消除二维码
 * @param noobjRemoveWatermark 非主体消除水印
 * @param objRemoveCharacter   主体消除文字
 * @param objRemoveLogo        主体消除Logo
 * @param objRemoveNpx         主体消除牛皮癣
 * @param objRemoveQrcode      主体消除二维码
 * @param objRemoveWatermark   主体消除水印
 * <AUTHOR>
 * @created 2025-01-22
 * @see com.fulfillmen.support.alibaba.api.AiCapabilityAPI#removeImage
 */
@Builder
public record ImageRemoveRequestRecord(
    @JsonProperty("imageUrl") String imageUrl,
    @JsonProperty("noobjRemoveCharacter") Boolean noobjRemoveCharacter,
    @JsonProperty("noobjRemoveLogo") Boolean noobjRemoveLogo,
    @JsonProperty("noobjRemoveNpx") Boolean noobjRemoveNpx,
    @JsonProperty("noobjRemoveQrcode") Boolean noobjRemoveQrcode,
    @JsonProperty("noobjRemoveWatermark") Boolean noobjRemoveWatermark,
    @JsonProperty("objRemoveCharacter") Boolean objRemoveCharacter,
    @JsonProperty("objRemoveLogo") Boolean objRemoveLogo,
    @JsonProperty("objRemoveNpx") Boolean objRemoveNpx,
    @JsonProperty("objRemoveQrcode") Boolean objRemoveQrcode,
    @JsonProperty("objRemoveWatermark") Boolean objRemoveWatermark
) implements BaseAlibabaRequestRecord {

    @Override
    public void requireParams() {
        assertNotBlank(imageUrl, "imageUrl不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("imageUrl", imageUrl);

        if (noobjRemoveCharacter != null) {
            params.put("noobjRemoveCharacter", noobjRemoveCharacter.toString());
        }
        if (noobjRemoveLogo != null) {
            params.put("noobjRemoveLogo", noobjRemoveLogo.toString());
        }
        if (noobjRemoveNpx != null) {
            params.put("noobjRemoveNpx", noobjRemoveNpx.toString());
        }
        if (noobjRemoveQrcode != null) {
            params.put("noobjRemoveQrcode", noobjRemoveQrcode.toString());
        }
        if (noobjRemoveWatermark != null) {
            params.put("noobjRemoveWatermark", noobjRemoveWatermark.toString());
        }
        if (objRemoveCharacter != null) {
            params.put("objRemoveCharacter", objRemoveCharacter.toString());
        }
        if (objRemoveLogo != null) {
            params.put("objRemoveLogo", objRemoveLogo.toString());
        }
        if (objRemoveNpx != null) {
            params.put("objRemoveNpx", objRemoveNpx.toString());
        }
        if (objRemoveQrcode != null) {
            params.put("objRemoveQrcode", objRemoveQrcode.toString());
        }
        if (objRemoveWatermark != null) {
            params.put("objRemoveWatermark", objRemoveWatermark.toString());
        }
        return params;
    }
}