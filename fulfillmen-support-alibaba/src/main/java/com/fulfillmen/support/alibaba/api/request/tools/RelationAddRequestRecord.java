/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.tools;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 买卖家分销关系添加请求记录
 * <p>
 * 通过商品ID，添加买卖家分销关系。
 *
 * @param offerId 商品ID，用于标识要添加分销关系的商品
 * <AUTHOR>
 * @created 2025-01-16
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao:alibaba.fenxiao.relationadd-1">API文档</a>
 */
@Builder
public record RelationAddRequestRecord(@JsonProperty("offerId") Long offerId) implements BaseAlibabaRequestRecord {

    @Override
    public void requireParams() {
        assertNotNull(offerId, "商品ID不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("offerId", String.valueOf(offerId));
        return params;
    }
}