/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.feedback;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 回传机构真实用户订单和1688订单的映射关系请求
 *
 * <AUTHOR>
 * @created 2025-01-17
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:order.relation.write-1">回传订单映射关系 API</a>
 */
@Builder
public record OrderRelationWriteRequestRecord(/**
                                               * 订单映射关系参数
                                               */
@JsonProperty("orderRelationParam") OrderRelationParam orderRelationParam) implements BaseAlibabaRequestRecord {

    @Override
    public void requireParams() throws AlibabaServiceValidationException {
        assertNotNull(orderRelationParam, "订单映射关系参数不能为空");
        assertNotBlank(orderRelationParam.orderId(), "外部机构子订单ID不能为空");
        assertNotBlank(orderRelationParam.parentOrderId(), "外部机构主订单ID不能为空");
        assertNotNull(orderRelationParam.purchaseOrderId(), "1688采购子订单ID不能为空");
        assertNotNull(orderRelationParam.purchaseParentOrderId(), "1688采购主订单ID不能为空");
        assertTrue(orderRelationParam.purchaseOrderId() > 0, "1688采购子订单ID必须大于0");
        assertTrue(orderRelationParam.purchaseParentOrderId() > 0, "1688采购主订单ID必须大于0");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("orderRelationParam", toJsonString(orderRelationParam));
        return params;
    }

    /**
     * 订单映射关系参数
     */
    public record OrderRelationParam(
        /**
         * 外部机构子订单ID
         */
        @JsonProperty("orderId") String orderId,

        /**
         * 外部机构主订单ID
         */
        @JsonProperty("parentOrderId") String parentOrderId,

        /**
         * 1688采购子订单ID
         */
        @JsonProperty("purchaseOrderId") Long purchaseOrderId,

        /**
         * 1688采购主订单ID
         */
        @JsonProperty("purchaseParentOrderId") Long purchaseParentOrderId
    ) {

    }
}