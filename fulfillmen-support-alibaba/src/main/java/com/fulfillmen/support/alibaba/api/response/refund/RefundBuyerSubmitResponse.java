/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.refund;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import com.fulfillmen.support.alibaba.api.response.model.RefundBuyerSubmitResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 提交退款单物流信息响应
 * <p>
 * 买家提交退款单物流信息的响应结果，包含提交后的退款单状态等信息
 *
 * <AUTHOR>
 * @created 2025-01-13
 * @see RefundBuyerSubmitResult
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RefundBuyerSubmitResponse extends BaseAlibabaResponse {

    /**
     * 退款单物流信息提交结果
     */
    private RefundBuyerSubmitResult result;
}