/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.pay;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 查询订单可用支付方式请求
 * <p>
 * 用于查询某笔未付订单可以使用的支付方式通道
 *
 * <AUTHOR>
 * @created 2025-01-10
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.payWay.query-1">API文档</a>
 */
@Builder
public record PayWayQueryRequestRecord(
    /**
     * 订单ID
     */
    Long orderId
) implements BaseAlibabaRequestRecord {

    /**
     * 创建 PayWayQueryRequestRecord 实例
     *
     * @param orderId 订单ID
     * @return PayWayQueryRequestRecord 实例
     * @throws IllegalArgumentException 如果参数无效
     */
    public static PayWayQueryRequestRecord of(Long orderId) {
        return new PayWayQueryRequestRecord(orderId);
    }

    @Override
    public void requireParams() {
        assertNotNull(orderId, "订单ID不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("orderId", orderId.toString());
        return params;
    }
}