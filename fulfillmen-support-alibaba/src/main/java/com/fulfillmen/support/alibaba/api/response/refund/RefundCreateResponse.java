/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.refund;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import com.fulfillmen.support.alibaba.api.response.model.RefundCreateResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 创建退款申请响应
 * <p>
 * 买家创建退款申请的响应结果，包含新创建的退款单信息
 *
 * <AUTHOR>
 * @created 2025-01-13
 * @see RefundCreateResult
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RefundCreateResponse extends BaseAlibabaResponse {

    /**
     * 退款申请创建结果
     */
    private RefundCreateResult result;
}