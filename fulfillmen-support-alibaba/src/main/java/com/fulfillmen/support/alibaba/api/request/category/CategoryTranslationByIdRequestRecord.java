/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.category;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 类目ID翻译请求
 * <p>
 * 用于根据类目ID获取指定语言的类目名称。
 *
 * <AUTHOR>
 * @created 2025-01-17
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:category.name.translate.byid-1">类目ID翻译API文档</a>
 */
@Builder
public record CategoryTranslationByIdRequestRecord(
    /**
     * 类目ID（必填）
     */
    Long categoryId,

    /**
     * 语言代码（必填）
     */
    String language,

    /**
     * 外部会员ID（选填）
     */
    String outMemberId,

    /**
     * 父类目ID（选填）
     */
    Long parentCateId
) implements BaseAlibabaRequestRecord {

    @Override
    public void requireParams() {
        assertNotNull(categoryId, "类目ID不能为空");
        assertNotBlank(language, "语言代码不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("categoryId", String.valueOf(categoryId));
        params.put("language", language);
        params.put("outMemberId", outMemberId);
        if (parentCateId != null) {
            params.put("parentCateId", String.valueOf(parentCateId));
        }
        return params;
    }
}