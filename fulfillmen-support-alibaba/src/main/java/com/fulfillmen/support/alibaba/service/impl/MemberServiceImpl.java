/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service.impl;

import com.fulfillmen.support.alibaba.api.ApiPaths;
import com.fulfillmen.support.alibaba.api.MemberAPI;
import com.fulfillmen.support.alibaba.api.request.member.MemberRegisterRequestRecord;
import com.fulfillmen.support.alibaba.api.request.member.SubAccountAuthAddRequestRecord;
import com.fulfillmen.support.alibaba.api.request.member.SubAccountAuthCancelRequestRecord;
import com.fulfillmen.support.alibaba.api.request.member.SubAccountAuthListRequestRecord;
import com.fulfillmen.support.alibaba.api.response.member.MemberRegisterResponse;
import com.fulfillmen.support.alibaba.api.response.member.SubAccountAuthAddResponse;
import com.fulfillmen.support.alibaba.api.response.member.SubAccountAuthCancelResponse;
import com.fulfillmen.support.alibaba.api.response.member.SubAccountAuthListResponse;
import com.fulfillmen.support.alibaba.autoconfigure.AlibabaProperties;
import com.fulfillmen.support.alibaba.service.IMemberService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 会员服务实现类
 *
 * <AUTHOR>
 * @created 2025-01-16
 */
@Slf4j
@Service
public class MemberServiceImpl extends BaseAlibabaServiceImpl implements IMemberService {

    private static final String SERVICE_NAME = "会员服务";
    private final MemberAPI memberAPI;

    public MemberServiceImpl(MemberAPI memberAPI, AlibabaProperties alibabaProperties) {
        super(alibabaProperties);
        this.memberAPI = memberAPI;
    }

    @Override
    protected String getServiceName() {
        return SERVICE_NAME;
    }

    @Override
    public Mono<MemberRegisterResponse> register(MemberRegisterRequestRecord request) {
        log.debug("开始处理会员注册请求: {}", request);
        return wrapWithErrorHandler("会员注册", request, ApiPaths.MemberAPI.REGISTER, formParams -> memberAPI
            .register(appKey, formParams));
    }

    @Override
    public Mono<SubAccountAuthAddResponse> addSubAccountAuth(SubAccountAuthAddRequestRecord request) {
        log.debug("开始处理添加子账号授权请求: {}", request);
        return wrapWithErrorHandler("添加子账号授权", request, ApiPaths.MemberAPI.SUB_ACCOUNT_AUTH_ADD, formParams -> memberAPI
            .addSubAccountAuth(appKey, formParams));
    }

    @Override
    public Mono<SubAccountAuthCancelResponse> cancelSubAccountAuth(SubAccountAuthCancelRequestRecord request) {
        log.debug("开始处理取消子账号授权请求: {}", request);
        return wrapWithErrorHandler("取消子账号授权", request, ApiPaths.MemberAPI.SUB_ACCOUNT_AUTH_CANCEL, formParams -> memberAPI
            .cancelSubAccountAuth(appKey, formParams));
    }

    @Override
    public Mono<SubAccountAuthListResponse> listSubAccountAuth(SubAccountAuthListRequestRecord request) {
        log.debug("开始处理查询子账号授权列表请求: {}", request);
        return wrapWithErrorHandler("查询子账号授权列表", request, ApiPaths.MemberAPI.SUB_ACCOUNT_AUTH_LIST, formParams -> memberAPI
            .listSubAccountAuth(appKey, formParams));
    }
}