/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.category;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类目响应
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CategoryResponse extends BaseAlibabaResponse {

    /**
     * 类目列表
     */
    private List<CategoryInfo> categoryInfo;

    @Data
    public static class CategoryInfo {

        /**
         * 类目ID
         */
        @JsonProperty("categoryID")
        private Long categoryId;

        /**
         * 类目名称
         */
        private String name;

        /**
         * 类目层级，1688无此内容
         */
        private Integer level;

        /**
         * 是否叶子类目（只有叶子类目才能发布商品）
         */
        private Boolean isLeaf;

        /**
         * 父类目ID数组，1688只返回一个父id
         */
        @JsonProperty("parentIDs")
        private List<Long> parentIds;

        /**
         * 子类目ID数组，1688无此内容
         */
        @JsonProperty("childIDs")
        private List<Long> childIds;

        /**
         * 子类目信息
         */
        private List<ChildCategory> childCategorys;

        /**
         * 类目的类型：1表示cbu类目，2表示gallop类目
         */
        private String categoryType;

        /**
         * 类目是否支持加工定制
         */
        private Boolean isSupportProcessing;

        /**
         * 最小起订量
         */
        private Long minOrderQuantity;

        /**
         * 类目特征信息
         */
        private List<FeatureInfo> featureInfos;
    }

    @Data
    public static class ChildCategory {

        /**
         * 子类目ID
         */
        private Long id;

        /**
         * 子类目名称
         */
        private String name;

        /**
         * 是否叶子类目（只有叶子类目才能发布商品）
         */
        private Boolean isLeaf;

        /**
         * 类目的类型：1表示cbu类目，2表示gallop类目
         */
        private String categoryType;

        /**
         * 最小起订量
         */
        private Long minOrderQuantity;
    }

    @Data
    public static class FeatureInfo {

        /**
         * 特征名称
         */
        private String key;

        /**
         * 特征值
         */
        private String value;

        /**
         * 状态
         */
        private Integer status;

        /**
         * 是否继承到子元素上
         */
        private Boolean hierarchy;
    }
}