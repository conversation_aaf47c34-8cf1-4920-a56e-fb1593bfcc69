/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 1688交易地址参数信息
 *
 * <AUTHOR>
 * @created 2025-01-09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradeParamOutAddress {

    /**
     * 省份信息
     */
    private BizTradeParamPlace province;

    /**
     * 城市信息
     */
    private BizTradeParamPlace city;

    /**
     * 区域信息
     */
    private BizTradeParamPlace area;

    /**
     * 镇信息
     */
    private BizTradeParamPlace town;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 邮政编码
     */
    private String postCode;

    /**
     * 地址位置信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BizTradeParamPlace {

        /**
         * 地区编码
         */
        private String code;

        /**
         * 地区名称
         */
        private String name;
    }
}
