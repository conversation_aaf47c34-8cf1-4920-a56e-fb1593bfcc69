/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.order;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Builder;

/**
 * 创建跨境订单请求
 *
 * <AUTHOR>
 * @created 2025-01-10
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.createCrossBorder-1">API文档</a>
 */
@Builder
public record OrderCreateRequestRecord(
    /**
     * 流程类型
     *
     * <pre>
     * general（创建大市场订单），
     * fenxiao（创建分销订单）,
     * saleproxy流程将校验分销关系,
     * paired(火拼下单),
     * boutiquefenxiao(精选货源分销价下单，采购量1个使用包邮)，
     * boutiquepifa(精选货源批发价下单，采购量大于2使用)
     * </pre>
     */
    String flow,

    /**
     * 买家留言
     */
    String message,

    /**
     * 开放平台业务码
     * <p>
     * cross(跨境业务),cross_daigou（跨境代购业务）
     */
    String isvBizType,

    /**
     * 收货地址信息
     */
    AddressParamRecord addressParam,

    /**
     * 商品信息列表
     */
    List<CargoParamRecord> cargoParamList,

    /**
     * 发票信息
     */
    InvoiceParamRecord invoiceParam,

    /**
     * 交易方式类型
     *
     * <pre>
     * 由于不同的商品支持的交易方式不同，没有一种交易方式是全局通用的，所以当前下单可使用的交易方式必须通过下单预览接口的tradeModeNameList获取。
     * 交易方式类型说明：
     * - assureTrade（交易4.0通用担保交易）
     * - alipay（大市场通用的支付宝担保交易（目前在做切流，后续会下掉））
     * - period（普通账期交易）
     * - assure（大买家企业采购询报价下单时需要使用的担保交易流程）
     * - creditBuy（诚E赊）
     * - bank（银行转账）
     * - 631staged（631分阶段付款）
     * - 37staged（37分阶段）
     *
     * 此字段不传则系统默认会选取一个可用的交易方式下单，如果开通了诚E赊默认是creditBuy（诚E赊），
     * 未开通诚E赊默认使用的方式是支付宝担宝交易。
     * </pre>
     */
    String tradeType,

    /**
     * 店铺优惠ID
     */
    String shopPromotionId,

    /**
     * 是否匿名下单
     */
    Boolean anonymousBuyer,

    /**
     * 分销渠道
     */
    String fenxiaoChannel,

    /**
     * 库存模式
     * <p>
     * JIT（jit模式）或 NORMAL（仓发模式）,目前只提供给AE使用
     */
    String inventoryMode,

    /**
     * 外部订单号
     */
    String outOrderId,

    /**
     * 上门揽收
     * <p>
     * 目前AE供货可用，其他场景暂不开通 y或n,默认为n
     */
    String pickupService,

    /**
     * 仓库编码
     */
    String warehouseCode,

    /**
     * 预选支付渠道
     */
    String preSelectPayChannel,

    /**
     * 是否小额采购
     */
    String smallProcurement,

    /**
     * 是否使用红包
     */
    String useRedEnvelope,

    /**
     * 是否代发
     */
    String dropshipping,

    /**
     * 增值服务
     */
    String addedService
) implements BaseAlibabaRequestRecord {

    /**
     * 创建 OrderCreateRequestRecord 实例
     *
     * @param flow           流程类型
     * @param isvBizType     开放平台业务码
     * @param addressParam   收货地址信息
     * @param cargoParamList 商品信息列表
     * @return OrderCreateRequestRecord 实例
     */
    public static OrderCreateRequestRecord of(String flow,
        String isvBizType,
        AddressParamRecord addressParam,
        List<CargoParamRecord> cargoParamList) {
        return new OrderCreateRequestRecord(flow, null, isvBizType, addressParam, cargoParamList, null, null, null, null, null, null, null, null, null, null, null, null, null,
            null);
    }

    @Override
    public void requireParams() {
        // 校验必填参数
        assertNotBlank(flow, "流程类型不能为空");
        assertNotNull(addressParam, "收货地址信息不能为空");
        assertNotNull(cargoParamList, "商品信息列表不能为空");
        assertNotBlank(outOrderId, "外部订单号不能为空");

        if (cargoParamList.isEmpty()) {
            throw new AlibabaServiceValidationException("商品信息列表不能为空");
        }

        // 校验收货地址信息
        assertNotBlank(addressParam.fullName, "收货人姓名不能为空");
        assertNotBlank(addressParam.mobile, "手机号码不能为空");
//        assertNotBlank(addressParam.phone, "电话号码不能为空");
        assertNotBlank(addressParam.postCode, "邮编不能为空");
        assertNotBlank(addressParam.cityText, "城市不能为空");
        assertNotBlank(addressParam.provinceText, "省份不能为空");
        assertNotBlank(addressParam.areaText, "区不能为空");
//        assertNotBlank(addressParam.townText, "镇不能为空");
        assertNotBlank(addressParam.address, "街道地址不能为空");
        assertNotBlank(addressParam.districtCode, "地址编码不能为空");

        // 校验商品信息
        for (CargoParamRecord cargo : cargoParamList) {
            assertNotNull(cargo.offerId, "商品ID不能为空");
            // 如果 offerId 是单品，是不需要传 specId 的。
            //  assertNotBlank(cargo.specId, "商品规格ID不能为空");
            assertNotNull(cargo.quantity, "商品数量不能为空");
        }

        // 校验发票信息（如果有）
        if (invoiceParam != null) {
            assertNotNull(invoiceParam.invoiceType, "发票类型不能为空");
            assertNotBlank(invoiceParam.companyName, "购货公司名不能为空");
            assertNotBlank(invoiceParam.taxpayerIdentifier, "纳税识别码不能为空");
            assertNotBlank(invoiceParam.bankAndAccount, "开户行及帐号不能为空");
            assertNotBlank(invoiceParam.localInvoiceId, "增值税本地发票号不能为空");
            assertNotBlank(invoiceParam.provinceText, "省份不能为空");
            assertNotBlank(invoiceParam.cityText, "城市不能为空");
            assertNotBlank(invoiceParam.areaText, "地区不能为空");
            assertNotBlank(invoiceParam.townText, "镇不能为空");
            assertNotBlank(invoiceParam.postCode, "邮编不能为空");
            assertNotBlank(invoiceParam.address, "街道地址不能为空");
            assertNotBlank(invoiceParam.fullName, "收票人姓名不能为空");
            assertNotBlank(invoiceParam.phone, "电话不能为空");
            assertNotBlank(invoiceParam.mobile, "手机不能为空");
        }
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();

        // 添加必填参数
        params.put("flow", flow);
        params.put("isvBizType", isvBizType);

        // 添加收货地址信息
        if (addressParam != null) {
            params.put("addressParam", toJsonString(addressParam));
        }

        // 添加商品信息
        if (cargoParamList != null && !cargoParamList.isEmpty()) {
            params.put("cargoParamList", toJsonString(cargoParamList));
        }

        // 添加其他非空参数
        if (message != null) {
            params.put("message", message);
        }
        if (invoiceParam != null) {
            params.put("invoiceParam", toJsonString(invoiceParam));
        }
        if (tradeType != null) {
            params.put("tradeType", tradeType);
        }
        if (shopPromotionId != null) {
            params.put("shopPromotionId", shopPromotionId);
        }
        if (anonymousBuyer != null) {
            params.put("anonymousBuyer", toJsonString(anonymousBuyer));
        }
        if (fenxiaoChannel != null) {
            params.put("fenxiaoChannel", fenxiaoChannel);
        }
        if (inventoryMode != null) {
            params.put("inventoryMode", inventoryMode);
        }
        if (outOrderId != null) {
            params.put("outOrderId", outOrderId);
        }
        if (pickupService != null) {
            params.put("pickupService", pickupService);
        }
        if (warehouseCode != null) {
            params.put("warehouseCode", warehouseCode);
        }
        if (preSelectPayChannel != null) {
            params.put("preSelectPayChannel", preSelectPayChannel);
        }
        if (smallProcurement != null) {
            params.put("smallProcurement", smallProcurement);
        }
        if (useRedEnvelope != null) {
            params.put("useRedEnvelope", useRedEnvelope);
        }
        if (dropshipping != null) {
            params.put("dropshipping", dropshipping);
        }
        if (addedService != null) {
            params.put("addedService", addedService);
        }

        return params;
    }

    /**
     * 收货地址信息
     */
    @Builder
    public record AddressParamRecord(
        /**
         * 收货地址id
         */
        Long addressId,

        /**
         * 收货人姓名
         */
        String fullName,

        /**
         * 手机号码
         */
        String mobile,

        /**
         * 电话号码
         */
        String phone,

        /**
         * 邮政编码
         */
        String postCode,

        /**
         * 城市名称
         */
        String cityText,

        /**
         * 省份名称
         */
        String provinceText,

        /**
         * 区域名称
         */
        String areaText,

        /**
         * 镇名称
         */
        String townText,

        /**
         * 详细街道地址
         */
        String address,

        /**
         * 地址编码
         */
        String districtCode
    ) {

    }

    /**
     * 商品信息
     */
    @Builder
    public record CargoParamRecord(
        /**
         * 商品ID
         */
        Long offerId,

        /**
         * 商品规格ID
         * <pre>
         * 注意：如果 offerId 是单品，可以忽略。此配置，即offerId 是没有规格的。
         * </pre>
         */
        String specId,

        /**
         * 商品数量
         */
        Double quantity,

        /**
         * 商品明细ID
         */
        String openOfferId,

        /**
         * 外部会员ID
         */
        String outMemberId
    ) {

    }

    /**
     * 发票信息
     */
    @Builder
    public record InvoiceParamRecord(
        /**
         * 发票类型
         */
        Integer invoiceType,

        /**
         * 省份名称
         */
        String provinceText,

        /**
         * 城市名称
         */
        String cityText,

        /**
         * 区域名称
         */
        String areaText,

        /**
         * 镇名称
         */
        String townText,

        /**
         * 邮政编码
         */
        String postCode,

        /**
         * 详细地址
         */
        String address,

        /**
         * 收票人姓名
         */
        String fullName,

        /**
         * 电话号码
         */
        String phone,

        /**
         * 手机号码
         */
        String mobile,

        /**
         * 公司名称
         */
        String companyName,

        /**
         * 纳税人识别号
         */
        String taxpayerIdentifier,

        /**
         * 开户行及账号
         */
        String bankAndAccount,

        /**
         * 本地发票ID
         */
        String localInvoiceId
    ) {

    }
}