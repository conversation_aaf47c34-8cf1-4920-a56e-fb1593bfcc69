/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook.data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 物流消息数据模型
 * 
 * 支持所有 LogisticsMessageTypeEnums 中定义的物流消息类型，包括：
 * <ul>
 * <li>物流单状态变更（买家视角）- LOGISTICS_BUYER_VIEW_TRACE</li>
 * <li>物流单号修改消息 - LOGISTICS_MAIL_NO_CHANGE</li>
 * <li>1688跨境物流包裹消息 - LOGISTICS_GLOBAL_1688_PACKAGE_CHANGE</li>
 * </ul>
 * 
 * <p>使用说明：</p>
 * <ul>
 * <li>基础字段（logisticsId, orderLogsItems）适用于所有消息类型</li>
 * <li>状态变更字段（cpCode, mailNo, statusChanged, changeTime）用于状态变更消息</li>
 * <li>运单号修改字段（oldCpCode, newCpCode, oldMailNo, newMailNo, eventTime）用于运单号修改消息</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/11
 * @since 1.0.0
 */
@Data
public class LogisticsMessage implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    // ==================== 基础字段（所有物流消息都包含） ====================

    /**
     * 物流编号
     * 适用于所有物流消息类型
     */
    private String logisticsId;

    /**
     * 该物流单关联的订单信息
     * 适用于所有物流消息类型
     */
    private List<OrderLogItem> orderLogsItems;

    // ==================== 状态变更字段（状态变更消息使用） ====================

    /**
     * CP Code（快递公司代码）
     * 
     * 用于以下消息类型：
     * <ul>
     * <li>LOGISTICS_BUYER_VIEW_TRACE - 物流单状态变更（买家视角）</li>
     * <li>LOGISTICS_GLOBAL_1688_PACKAGE_CHANGE - 1688跨境物流包裹消息</li>
     * </ul>
     */
    private String cpCode;

    /**
     * 运单号
     * 
     * 用于以下消息类型：
     * <ul>
     * <li>LOGISTICS_BUYER_VIEW_TRACE - 物流单状态变更（买家视角）</li>
     * <li>LOGISTICS_GLOBAL_1688_PACKAGE_CHANGE - 1688跨境物流包裹消息</li>
     * </ul>
     */
    private String mailNo;

    /**
     * 物流单发生变化的状态
     * 
     * 可能的值：
     * <ul>
     * <li>CONSIGN - 发货</li>
     * <li>ACCEPT - 揽收</li>
     * <li>TRANSPORT - 运输</li>
     * <li>DELIVERING - 派送</li>
     * <li>SIGN - 签收</li>
     * </ul>
     * 
     * 用于以下消息类型：
     * <ul>
     * <li>LOGISTICS_BUYER_VIEW_TRACE - 物流单状态变更（买家视角）</li>
     * <li>LOGISTICS_GLOBAL_1688_PACKAGE_CHANGE - 1688跨境物流包裹消息</li>
     * </ul>
     */
    private String statusChanged;

    /**
     * 发生变化的时间
     * 
     * 用于以下消息类型：
     * <ul>
     * <li>LOGISTICS_BUYER_VIEW_TRACE - 物流单状态变更（买家视角）</li>
     * <li>LOGISTICS_GLOBAL_1688_PACKAGE_CHANGE - 1688跨境物流包裹消息</li>
     * </ul>
     */
    private String changeTime;

    // ==================== 运单号修改字段（运单号修改消息使用） ====================

    /**
     * 更改前的CP Code
     * 仅在 LOGISTICS_MAIL_NO_CHANGE 消息类型中使用
     */
    private String oldCpCode;

    /**
     * 更改后的CP Code
     * 仅在 LOGISTICS_MAIL_NO_CHANGE 消息类型中使用
     */
    private String newCpCode;

    /**
     * 更改前的运单号
     * 仅在 LOGISTICS_MAIL_NO_CHANGE 消息类型中使用
     */
    private String oldMailNo;

    /**
     * 更改后的运单号
     * 仅在 LOGISTICS_MAIL_NO_CHANGE 消息类型中使用
     */
    private String newMailNo;

    /**
     * 事件发生时间
     * 仅在 LOGISTICS_MAIL_NO_CHANGE 消息类型中使用
     */
    private String eventTime;

    // ==================== 便利方法 ====================

    /**
     * 判断是否为状态变更消息
     * 
     * @return 如果包含状态变更相关字段则返回true
     */
    public boolean isStatusChangeMessage() {
        return statusChanged != null || changeTime != null;
    }

    /**
     * 判断是否为运单号修改消息
     * 
     * @return 如果包含运单号修改相关字段则返回true
     */
    public boolean isMailNoChangeMessage() {
        return oldMailNo != null || newMailNo != null || eventTime != null;
    }

    /**
     * 判断是否为跨境物流包裹消息
     * 注意：此方法需要结合消息类型来准确判断，这里仅基于数据结构判断
     * 
     * @return 如果是状态变更消息则可能为跨境物流包裹消息
     */
    public boolean isPossibleGlobalPackageMessage() {
        return isStatusChangeMessage();
    }

    /**
     * 判断物流状态是否为已发货
     * 
     * @return 如果状态为发货则返回true
     */
    public boolean isConsignStatus() {
        return "CONSIGN".equals(statusChanged);
    }

    /**
     * 判断物流状态是否为已揽收
     * 
     * @return 如果状态为揽收则返回true
     */
    public boolean isAcceptStatus() {
        return "ACCEPT".equals(statusChanged);
    }

    /**
     * 判断物流状态是否为运输中
     * 
     * @return 如果状态为运输则返回true
     */
    public boolean isTransportStatus() {
        return "TRANSPORT".equals(statusChanged);
    }

    /**
     * 判断物流状态是否为派送中
     * 
     * @return 如果状态为派送则返回true
     */
    public boolean isDeliveringStatus() {
        return "DELIVERING".equals(statusChanged);
    }

    /**
     * 判断物流状态是否为已签收
     * 
     * @return 如果状态为签收则返回true
     */
    public boolean isSignStatus() {
        return "SIGN".equals(statusChanged);
    }

    // ==================== 内部类 ====================

    /**
     * 订单物流项
     * 表示该物流单关联的订单信息
     */
    @Data
    public static class OrderLogItem implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 交易主单ID
         */
        private Long orderId;

        /**
         * 交易子单ID
         */
        private Long orderEntryId;
    }
}
