/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model.order;

import com.fulfillmen.support.alibaba.api.response.model.Contact;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

/**
 * 卖家联系人信息
 *
 * <AUTHOR>
 * @created 2025-01-10
 */
@Data
public class TradeSellContact extends Contact implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 旺铺名称
     */
    private String shopName;

    /**
     * 发件人电话，在微供等分销场景下由分销商设置
     */
    private String wgSenderPhone;

    /**
     * 发件人名称，在微供等分销场景下由分销商设置
     */
    private String wgSenderName;

}
