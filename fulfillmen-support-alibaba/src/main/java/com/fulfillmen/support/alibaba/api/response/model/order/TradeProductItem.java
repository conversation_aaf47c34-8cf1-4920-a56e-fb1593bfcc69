/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fulfillmen.support.alibaba.api.response.util.AlibabaDateDeserializer;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * 商品信息
 *
 * <AUTHOR>
 * @created 2025-01-10
 */
@Data
public class TradeProductItem {

    /**
     * 指定单品货号，国际站无需关注
     *
     * <pre>
     * 该字段不一定有值，仅仅在下单时才会把货号记录(如果卖家设置了单品货号的话)。
     * 别的订单类型的货号只能通过商品接口去获取。
     * 请注意：通过商品接口获取时的货号和下单时的货号可能不一致，因为下单完成后卖家可能修改商品信息，改变了货号。
     * </pre>
     */
    @JsonProperty("cargoNumber")
    private String cargoNumber;

    /**
     * 描述
     *
     * <pre>
     * 1688无此信息
     * </pre>
     */
    @JsonProperty("description")
    private String description;

    /**
     * 实付金额，单位为元
     */
    @JsonProperty("itemAmount")
    private BigDecimal itemAmount;

    /**
     * 商品名称
     */
    @JsonProperty("name")
    private String name;

    /**
     * 原始单价，以元为单位
     */
    @JsonProperty("price")
    private BigDecimal price;

    /**
     * 产品ID（非在线产品为空）
     */
    @JsonProperty("productID")
    private Long productId;

    /**
     * 商品图片url
     */
    @JsonProperty("productImgUrl")
    private String[] productImgUrl;

    /**
     * 产品快照url
     *
     * <pre>
     * 交易订单产生时会自动记录下当时的商品快照，供后续纠纷时参考
     * </pre>
     */
    @JsonProperty("productSnapshotUrl")
    private String productSnapshotUrl;

    /**
     * 以unit为单位的数量
     *
     * <pre>
     * 例如多少个、多少件、多少箱、多少吨
     * </pre>
     */
    @JsonProperty("quantity")
    private BigDecimal quantity;

    /**
     * 退款金额，单位为元
     */
    @JsonProperty("refund")
    private BigDecimal refund;

    /**
     * skuID
     */
    @JsonProperty("skuID")
    private Long skuId;

    /**
     * 排序字段
     *
     * <pre>
     * 商品列表按此字段进行排序，从0开始，1688不提供
     * </pre>
     */
    @JsonProperty("sort")
    private Integer sort;

    /**
     * 子订单状态
     */
    @JsonProperty("status")
    private String status;

    /**
     * 子订单号，或商品明细条目ID
     */
    @JsonProperty("subItemID")
    private Long subItemId;

    /**
     * 类型
     *
     * <pre>
     * 国际站使用，供卖家标注商品所属类型
     * </pre>
     */
    @JsonProperty("type")
    private String type;

    /**
     * 售卖单位
     *
     * <pre>
     * 例如：个、件、箱、吨
     * </pre>
     */
    @JsonProperty("unit")
    private String unit;

    /**
     * 重量
     *
     * <pre>
     * 按重量单位计算的重量，例如：100
     * </pre>
     */
    @JsonProperty("weight")
    private String weight;

    /**
     * 重量单位
     *
     * <pre>
     * 例如：g，kg，t
     * </pre>
     */
    @JsonProperty("weightUnit")
    private String weightUnit;

    /**
     * 商品货号
     *
     * <pre>
     * 指定商品货号，该字段不一定有值，在下单时才会把货号记录。别的订单类型的货号只能通过商品接口去获取。
     * 请注意：通过商品接口获取时的货号和下单时的货号可能不一致，因为下单完成后卖家可能修改商品信息，改变了货号。
     * 该字段和cargoNUmber的区别是：该字段是定义在商品级别上的货号，cargoNUmber是定义在单品级别的货号
     * </pre>
     */
    @JsonProperty("productCargoNumber")
    private String productCargoNumber;

    /**
     * SKU属性信息列表
     */
    @JsonProperty("skuInfos")
    private List<TradeSkuInfo> skuInfos;

    /**
     * 订单明细涨价或降价的金额
     */
    @JsonProperty("entryDiscount")
    private BigDecimal entryDiscount;

    /**
     * 规格ID
     */
    @JsonProperty("specId")
    private String specId;

    /**
     * 数量系数
     *
     * <pre>
     * 以unit为单位的quantity精度系数，值为10的幂次，例如:quantityFactor=1000,unit=吨，那么quantity的最小精度为0.001吨
     * </pre>
     */
    @JsonProperty("quantityFactor")
    private Integer quantityFactor;

    /**
     * 状态描述
     *
     * <pre>
     * 子订单状态描述
     * </pre>
     */
    @JsonProperty("statusStr")
    private String statusStr;

    /**
     * 退款状态
     *
     * <pre>
     * WAIT_SELLER_AGREE 等待卖家同意
     * REFUND_SUCCESS 退款成功
     * REFUND_CLOSED 退款关闭
     * WAIT_BUYER_MODIFY 待买家修改
     * WAIT_BUYER_SEND 等待买家退货
     * WAIT_SELLER_RECEIVE 等待卖家确认收货
     * WAIT_SELLER_CONFIRM 等待卖家确认
     * WAIT_SELLER_SEND 等待卖家发货
     * </pre>
     */
    @JsonProperty("refundStatus")
    private String refundStatus;

    /**
     * 退款ID
     *
     * <pre>
     * 售中退款单号
     * </pre>
     */
    @JsonProperty("refundId")
    private String refundId;

    /**
     * 售后退款单号
     *
     * <pre>
     * 售后退款单号
     * </pre>
     */
    @JsonProperty("refundIdForAs")
    private String refundIdForAs;

    /**
     * 子订单号或商品明细条目ID
     *
     * <pre>
     * (字符串类型，由于Long类型的ID可能在JS和PHP中处理有问题，所以可以用字符串类型来处理)
     * </pre>
     */
    @JsonProperty("subItemIDString")
    private String subItemIdString;

    /**
     * 关闭原因
     */
    @JsonProperty("closeReason")
    private String closeReason;

    /**
     * 商品类目ID
     */
    @JsonProperty("categoryId")
    private Long categoryId;

    /**
     * 商品单价
     */
    @JsonProperty("unitPrice")
    private BigDecimal unitPrice;

    /**
     * 物流状态
     *
     * <pre>
     * 1 未发货 2 已发货 3 已收货 4 已经退货 5 部分发货 8 还未创建物流订单
     * </pre>
     */
    @JsonProperty("logisticsStatus")
    private Integer logisticsStatus;

    /**
     * 创建时间
     */
    @JsonProperty("gmtCreate")
    @JsonDeserialize(using = AlibabaDateDeserializer.class)
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @JsonProperty("gmtModified")
    @JsonDeserialize(using = AlibabaDateDeserializer.class)
    private LocalDateTime gmtModified;

    /**
     * 完成时间
     */
    @JsonProperty("gmtCompleted")
    @JsonDeserialize(using = AlibabaDateDeserializer.class)
    private LocalDateTime gmtCompleted;

    /**
     * 支付过期时间
     *
     * <pre>
     * 库存超时时间，格式为“yyyy-MM-dd HH:mm:ss”
     * </pre>
     */
    @JsonProperty("gmtPayExpireTime")
    @JsonDeserialize(using = AlibabaDateDeserializer.class)
    private LocalDateTime gmtPayExpireTime;

    /**
     * 分担邮费，单位为元
     */
    @JsonProperty("sharePostage")
    private BigDecimal sharePostage;

    /**
     * 保障条款列表
     */
    @JsonProperty("guaranteesTerms")
    private List<TradeGuaranteeTermsInfo> guaranteesTerms;

    @Data
    public static class TradeSkuInfo {

        private String name;
        private String value;
    }
}