/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model;

import java.util.Date;
import lombok.Data;

/**
 * 批量查询子账号授权结果
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Data
public class SubAccountAuthListResult {

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息描述
     */
    private String errorMessage;

    /**
     * 返回结果
     */
    private AuthRelationDTO[] returnValue;

    /**
     * 是否成功
     */
    private Boolean success;

    @Data
    public static class AuthRelationDTO {

        /**
         * 授权凭证
         */
        private String accessToken;

        /**
         * 主账号loginId
         */
        private String adminOwnerId;

        /**
         * 主账号userId
         */
        private Long adminUserId;

        /**
         * appKey
         */
        private String clientId;

        /**
         * appName
         */
        private String clientName;

        /**
         * 授权过期时间
         */
        private Date gmtExpired;

        /**
         * 授权用户memberId
         */
        private String memberId;

        /**
         * 授权用户loginId
         */
        private String ownerId;

        /**
         * 资源域
         */
        private String resourceScopes;

        /**
         * 授权站点
         */
        private String site;

        /**
         * 授权状态
         */
        private String status;

        /**
         * 是否子账号授权
         */
        private Boolean subAuth;

        /**
         * 子账号loginId
         */
        private String subOwnerId;

        /**
         * 子账号userId
         */
        private Long subUserId;

        /**
         * 授权用户userId
         */
        private Long userId;
    }
}