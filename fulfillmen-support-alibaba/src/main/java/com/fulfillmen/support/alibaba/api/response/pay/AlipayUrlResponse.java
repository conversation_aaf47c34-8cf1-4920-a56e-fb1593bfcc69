/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.pay;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 获取支付宝支付链接响应
 * <p>
 * 包含收银台支付链接和支付失败的订单列表信息
 *
 * <AUTHOR>
 * @created 2025-01-10
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.alipay.url.get-1">API文档</a>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AlipayUrlResponse extends BaseAlibabaResponse {

    /**
     * 支付链接
     * <p>
     * 单个订单返回1688收银台地址，多个订单返回支付宝收银台地址
     */
    private String payUrl;

    /**
     * 部分失败订单列表
     */
    private List<Long> payFailureOrderList;
}