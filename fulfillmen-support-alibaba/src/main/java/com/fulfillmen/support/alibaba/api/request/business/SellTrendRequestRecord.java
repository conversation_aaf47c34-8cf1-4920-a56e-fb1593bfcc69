/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.business;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 商品每日销售数量趋势请求记录
 *
 * @param offerId   商品id
 * @param startDate 查询起始时间，格式：yyyyMMdd
 * @param endDate   查询截止时间，格式：yyyyMMdd
 * <AUTHOR>
 * @created 2025-01-23
 */
@Builder
public record SellTrendRequestRecord(
    @JsonProperty("offerId") Long offerId,
    @JsonProperty("startDate") String startDate,
    @JsonProperty("endDate") String endDate
) implements BaseAlibabaRequestRecord {

    @Override
    public void requireParams() {
        assertNotNull(offerId, "商品ID不能为空");
        assertNotBlank(startDate, "查询起始时间不能为空");
        assertNotBlank(endDate, "查询截止时间不能为空");
        assertTrue(startDate.matches("\\d{8}"), "查询起始时间格式错误，应为yyyyMMdd");
        assertTrue(endDate.matches("\\d{8}"), "查询截止时间格式错误，应为yyyyMMdd");
        assertTrue(startDate.compareTo(endDate) <= 0, "查询起始时间不能大于截止时间");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        Map<String, Object> trendQueryParam = new HashMap<>();
        trendQueryParam.put("offerId", offerId);
        trendQueryParam.put("startDate", startDate);
        trendQueryParam.put("endDate", endDate);

        params.put("tendQueryParam", toJsonString(trendQueryParam));
        return params;
    }
}