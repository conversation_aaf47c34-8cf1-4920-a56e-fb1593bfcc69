/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.ai;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 商品标题生成请求对象
 * <p>
 * 基于淘宝电商数据训练，通过简单入参即可快速生成吸引力十足的标题，凸显商品优势。
 *
 * <AUTHOR>
 * @created 2025-01-16
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:text.generate.title-1">商品标题生成API文档</a>
 */
@Builder
public record ProductTitleGenerateRequestRecord(
    /**
     * 商品名称 必填，可以输入商品原商品标题或对商品的简短描述
     */
    String productName,

    /**
     * 目标语言 必填，请输入语言代码
     */
    String targetLanguage,

    /**
     * 商品类目 选填，可参考任何一个平台的类目结构，输入商品类目名称
     */
    String productCategory,

    /**
     * 商品关键词 选填，一般是商品SEO相关关键词或商品核心卖点词
     */
    String productKeyword,

    /**
     * 商品详细描述 选填，阐述说明商品的卖点信息
     */
    String productDescription,

    /**
     * 业务标识code 选填，一般不用写
     */
    String bizCode,

    /**
     * 商品规格信息 选填，商品cpv信息
     */
    String itemSpec
) implements BaseAlibabaRequestRecord {

    @Override
    public void requireParams() {
        assertNotBlank(productName, "productName不能为空");
        assertNotBlank(targetLanguage, "targetLanguage不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("productName", productName);
        params.put("targetLanguage", targetLanguage);
        if (productCategory != null) {
            params.put("productCategory", productCategory);
        }
        if (productKeyword != null) {
            params.put("productKeyword", productKeyword);
        }
        if (productDescription != null) {
            params.put("productDescription", productDescription);
        }
        if (bizCode != null) {
            params.put("bizCode", bizCode);
        }
        if (itemSpec != null) {
            params.put("itemSpec", itemSpec);
        }
        return params;
    }
}