/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 1688交易快速创建订单地址信息
 *
 * <AUTHOR>
 * @created 2025-01-09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradeFastAddress {

    /**
     * 收货地址id
     */
    private Long addressId;

    /**
     * 收货人姓名
     */
    private String fullName;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 电话号码
     */
    private String phone;

    /**
     * 邮政编码
     */
    private String postCode;

    /**
     * 城市名称
     */
    private String cityText;

    /**
     * 省份名称
     */
    private String provinceText;

    /**
     * 区域名称
     */
    private String areaText;

    /**
     * 镇名称
     */
    private String townText;

    /**
     * 详细街道地址
     */
    private String address;

    /**
     * 地址编码
     */
    private String districtCode;
}
