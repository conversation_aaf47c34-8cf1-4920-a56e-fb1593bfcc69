/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.message;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 游标式获取失败消息列表响应
 *
 * <AUTHOR>
 * @created 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MessageCursorResponse extends BaseAlibabaResponse {

    /**
     * 推送消息列表
     */
    private List<PushMessage> pushMessageList;

    /**
     * 推送消息
     */
    @Data
    public static class PushMessage {

        /**
         * 消息唯一id 类型：long
         */
        private Long msgId;

        /**
         * 消息类型 类型：String
         */
        private String type;

        /**
         * 消息关联的用户memberId 类型：String
         */
        private String userInfo;

        /**
         * 消息内容 类型：Map
         */
        private Map<String, Object> data;

        /**
         * 消息创建的时间，单位毫秒 类型：long
         */
        private Long gmtBorn;
    }
}