/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model.order;

import lombok.Data;

/**
 * 当前交易可以支持的交易方式列表。结果可以参照1688下单预览页面的交易方式。
 *
 * <AUTHOR>
 * @date 2025/1/9 10:52
 * @description: todo
 * @since 1.0.0
 */
@Data
public class TradeModelExtension {

    /**
     * 交易方式名称，1688下单预览页面展示的名称
     */
    private String name;
    /**
     * 交易描述
     */
    private String description;
    /**
     * 做为入参传入下单接口的tradeType字段
     */
    private String tradeType;
    /**
     * 开放平台下单是否支持此种交易模式。如果为true,该交易方式可做为下单接口tradeType参数的入参；如果为false,则不可做为下单接口的入参。
     */
    private Boolean opSupport;

}
