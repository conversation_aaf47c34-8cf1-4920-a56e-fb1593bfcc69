/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.refund;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import com.fulfillmen.support.alibaba.api.response.model.RefundBuyerOrderViewResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询退款单详情(根据订单ID)响应
 * <p>
 * 根据订单ID查询退款单详细信息的响应结果，包含订单相关的所有退款单信息
 *
 * <AUTHOR>
 * @created 2025-01-13
 * @see RefundBuyerOrderViewResult
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RefundBuyerOrderViewResponse extends BaseAlibabaResponse {

    /**
     * 订单退款单详情查询结果
     */
    private RefundBuyerOrderViewResult result;
}