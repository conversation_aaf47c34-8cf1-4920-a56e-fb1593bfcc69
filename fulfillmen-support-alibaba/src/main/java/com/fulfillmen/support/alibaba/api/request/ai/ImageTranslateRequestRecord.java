/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.ai;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 图片翻译请求记录
 *
 * @param imageUrl               源图片URL，大小不超过10MB
 * @param originalLanguage       源语言代码
 * @param targetLanguage         目标语言代码
 * @param isIncludingProductArea 是否翻译商品主体上的文字
 * @param useImageEditor         是否进行二次编辑
 * <AUTHOR>
 * @created 2025-01-22
 */
@Builder
public record ImageTranslateRequestRecord(
    @JsonProperty("imageUrl") String imageUrl,
    @JsonProperty("originalLanguage") String originalLanguage,
    @JsonProperty("targetLanguage") String targetLanguage,
    @JsonProperty("isIncludingProductArea") String isIncludingProductArea,
    @JsonProperty("useImageEditor") String useImageEditor
) implements BaseAlibabaRequestRecord {

    @Override
    public void requireParams() {
        assertNotBlank(imageUrl, "imageUrl不能为空");
        assertNotBlank(originalLanguage, "originalLanguage不能为空");
        assertNotBlank(targetLanguage, "targetLanguage不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("imageUrl", imageUrl);
        params.put("originalLanguage", originalLanguage);
        params.put("targetLanguage", targetLanguage);

        if (isIncludingProductArea != null) {
            params.put("isIncludingProductArea", isIncludingProductArea);
        }
        if (useImageEditor != null) {
            params.put("useImageEditor", useImageEditor);
        }

        return params;
    }
}