/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.enums;

import lombok.Getter;

/**
 * 商品消息
 *
 * <AUTHOR>
 * @date 2025/7/9 19:09
 * @description: todo
 * @since 1.0.0
 */
@Getter
public enum GoodsMessageTypeEnums implements CallbackMessageType {

    /**
     * 1688产品下架（关系用户视角）
     * <pre>
     * 1688产品下架，仅关系用户（包含跨境、分销等关系）可见
     * 消息格式:
     * {
     * "productIds": "44179498967",
     * "memberId": "xuxan",
     * "status": "RELATION_VIEW_PRODUCT_EXPIRE",
     * "msgSendTime": "2018-05-30 20: 27: 48"
     * }
     * </pre>
     */
    PRODUCT_RELATION_VIEW_PRODUCT_EXPIRE("PRODUCT_RELATION_VIEW_PRODUCT_EXPIRE", "1688产品下架（关系用户视角）"),
    /**
     * 1688产品新增或修改（关系用户视角）
     * <pre>
     * 1688产品新增或修改，仅关系用户（包含跨境、分销等关系）可见
     * 消息格式:
     * {
     * "productIds": "547096780502",
     * "memberId": "b2b-28308412336ca4e7",
     * "status": "RELATION_VIEW_PRODUCT_NEW_OR_MODIFY",
     * "msgSendTime": "2018-05-30 20: 28: 42"
     * }
     * </pre>
     */
    PRODUCT_RELATION_VIEW_PRODUCT_NEW_OR_MODIFY("PRODUCT_RELATION_VIEW_PRODUCT_NEW_OR_MODIFY", "1688产品新增或修改（关系用户视角）"),
    /**
     * 1688产品删除（关系用户视角）
     * <pre>
     * 1688产品删除，仅关系用户（包含跨境、分销等关系）可见
     * 消息格式:
     * {
     * "productIds": "570872343603",
     * "memberId": "b2b-342071512394d025",
     * "status": "RELATION_VIEW_PRODUCT_DELETE",
     * "msgSendTime": "2018-05-30 20: 26: 41"
     * }
     * status: 产品状态
     * <p>
     * RELATION_VIEW_PRODUCT_EXPIRE、
     * RELATION_VIEW_PRODUCT_NEW_OR_MODIFY、
     * RELATION_VIEW_PRODUCT_DELETE、
     * RELATION_VIEW_PRODUCT_REPOST
     * </p>
     * </pre>
     */
    PRODUCT_RELATION_VIEW_PRODUCT_DELETE("PRODUCT_RELATION_VIEW_PRODUCT_DELETE", "1688产品删除（关系用户视角）"),
    /**
     * 1688产品上架（关系用户视角）
     * <pre>
     * 1688产品上架，仅关系用户（包含跨境、分销等关系）可见
     * 消息格式:
     * {
     * "productIds": "107680826",
     * "memberId": "shyxsscl",
     * "status": "RELATION_VIEW_PRODUCT_REPOST",
     * "msgSendTime": "2018-05-30 20: 29: 37"
     * }
     * status: 产品状态
     * <p>
     * RELATION_VIEW_PRODUCT_EXPIRE、
     * RELATION_VIEW_PRODUCT_NEW_OR_MODIFY、
     * RELATION_VIEW_PRODUCT_DELETE、
     * RELATION_VIEW_PRODUCT_REPOST
     * </p>
     * </pre>
     */
    PRODUCT_RELATION_VIEW_PRODUCT_REPOST("PRODUCT_RELATION_VIEW_PRODUCT_REPOST", "1688产品上架（关系用户视角）"),
    /**
     * 1688商品库存变更消息（关系用户视角）
     * <pre>
     * 商品库存变更消息，如下情况会导致触发商品库存变更：商家编辑商品、交易下单、订单关闭，回补库存、小二修改库存、API修改库存。
     * 接收某商品库存变更的前提，需先调用关注商品API关注某个商品，然后才能接收该商品库存变更的消息。
     * 消息格式:
     * {
     * "OfferInventoryChangeList": [{ // 商品库存变更列表
     * "offerId": 1234567890, // 商品ID
     * "offerOnSale": 100, // 在线可售offer数量
     * "skuId": 1234567890, // skuId
     * "skuOnSale": 20, // 在线可售sku数量
     * "quantity": -10, // 该offer整体库存变化数
     * "bizTime": "1564984329147" // 库存变更时间
     * }]
     * }
     * </pre>
     */
    PRODUCT_PRODUCT_INVENTORY_CHANGE("PRODUCT_PRODUCT_INVENTORY_CHANGE", "1688商品库存变更消息（关系用户视角）"),
    /**
     * 精选货源商品下架消息
     * <pre>
     * 精选货源商品下架消息
     * 消息格式:
     * {
     * "offerId": ************,
     * "type": "single_direct",
     * "openUid": "1117283046" //offer所属商家openUid
     * }
     * </pre>
     */
    PRODUCT_PFT_OFFER_QUIT("PRODUCT_PFT_OFFER_QUIT", "精选货源商品下架消息"),
    /**
     * 精选货源商品价格变动消息
     * <pre>
     * 精选货源商品价格变动消息
     * 消息格式:
     * {
     * "offerId": ************, // offerId
     * "minPrice": 2100, // 变更价格，分
     * "activeTime": 1662426000000, // 价格变更生效时间
     * "skuUpdateInfos": { // sku价格变更信息
     * "skuId": 4851072600824, // skuId
     * "historyRetailPrice": 1550 // 变更价格，分
     * }
     * }
     * </pre>
     */
    PRODUCT_PFT_OFFER_PRICE_MODIFY("PRODUCT_PFT_OFFER_PRICE_MODIFY", "精选货源商品价格变动消息"),
    /**
     * 一键铺货消息
     * <pre>
     * 用户触发一键铺货
     * 消息格式:
     * {
     * "offerId": "************",
     * "userInfo": "2804951212",
     * "action": "distribution"
     * }
     * </pre>
     */
    PRODUCT_PRODUCT_CROSSBOARD_INFORM("PRODUCT_PRODUCT_CROSSBOARD_INFORM", "一键铺货消息"),
    /**
     * 跨境设为货源
     * <pre>
     * 当用户在 ISV 中访问产品开发工具同款页面并将 1688 商品设为货源时，系统将生成一条关于货源的记录，并通过消息通知到开放平台，由开放平台将此消息推送至下游 ISV；
     * ISV 接受到此消息时，将产品和 1688 货源进行关联。
     * 消息格式:
     * {
     * "offerId": "************",
     * "clientId": "b2b-26800439112ddd0",
     * "productId": "560604911564",
     * "productUri": "http: 123test.com"
     * }
     * </pre>
     */
    CROSSBOARD_CROSSBOARD_ADD_SUPPLY("CROSSBOARD_CROSSBOARD_ADD_SUPPLY", "跨境设为货源"),
    /**
     * 1688产品审核（关系用户视角）
     * <pre>
     * 1688产品审核，仅关系用户（包含跨境、分销等关系）可见
     * 消息格式:
     * {
     * "productIds": "570872343603",
     * "memberId": "b2b-342071512394d025",
     * "status": "RELATION_VIEW_PRODUCT_AUDIT",
     * "msgSendTime": "2018-05-30 20: 26: 41"
     * }
     * status: 产品状态
     * <p>
     * RELATION_VIEW_PRODUCT_EXPIRE、
     * RELATION_VIEW_PRODUCT_NEW_OR_MODIFY、
     * RELATION_VIEW_PRODUCT_DELETE、
     * RELATION_VIEW_PRODUCT_REPOST
     * </p>
     * </pre>
     */
    PRODUCT_RELATION_VIEW_PRODUCT_AUDIT("PRODUCT_RELATION_VIEW_PRODUCT_AUDIT", "跨境货源审核"),
    ;

    private final String messageType;
    private final String messageDesc;

    GoodsMessageTypeEnums(String messageType, String messageDesc) {
        this.messageType = messageType;
        this.messageDesc = messageDesc;
    }

    /**
     * 根据消息类型获取枚举
     *
     * @param messageType 消息类型
     * @return 枚举
     */
    public static GoodsMessageTypeEnums fromMessageType(String messageType) {
        for (GoodsMessageTypeEnums type : GoodsMessageTypeEnums.values()) {
            if (type.getMessageType().equals(messageType)) {
                return type;
            }
        }
        return null;
    }

}
