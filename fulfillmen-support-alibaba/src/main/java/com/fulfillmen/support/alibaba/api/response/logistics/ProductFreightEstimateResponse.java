/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.logistics;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 商品中国国内运费预估响应
 *
 * <AUTHOR>
 * @created 2025-01-14
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:alibaba.product.freight.calculate-1">商品中国国内运费预估</a>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ProductFreightEstimateResponse extends BaseAlibabaResponse {

    private ApiResult result;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApiResult {

        private Boolean success;
        private String code;
        private ProductFreightModel result;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProductFreightModel {

        /**
         * 商品ID
         */
        private Long offerId;

        /**
         * 运费
         */
        private String freight;

        /**
         * 运费模板ID
         */
        private Long templateId;

        /**
         * 单个产品重量，单位：kg
         */
        private Double singleProductWeight;

        /**
         * 模板类型
         * <p>
         * 商家运费模板类型，1卖家承担运费，2用户自定义模板，3用户自定义官方模版
         * </p>
         */
        private Integer templateType;

        /**
         * 模板名称
         */
        private String templateName;

        /**
         * 子模板类型
         */
        private Integer subTemplateType;

        /**
         * 子模板名称
         */
        private String subTemplateName;

        /**
         * 首重/件费用
         */
        private String firstFee;

        /**
         * 首重/件单位
         * <p>
         * 如1代表【1件或者首重1千克是firstFee元】
         * </p>
         */
        private String firstUnit;

        /**
         * 续重/件费用
         */
        private String nextFee;

        /**
         * 续重/件单位
         */
        private String nextUnit;

        /**
         * 运费折扣
         * <p>
         * 空或者1，代表无折扣
         * </p>
         */
        private String discount;

        /**
         * 计费类型 0: 按重量，1: 按件数 2: 按体积
         */
        private String chargeType;

        /**
         * 是否包邮
         * <p>
         * 是否包邮，true: 包邮，false: 不包邮
         * </p>
         */
        private Boolean freePostage;

        /**
         * 尺寸值类型
         * <p>
         * 件重尺取值类型，1-为外层取值，2-为从productFreightSkuInfoModels中取值
         * </p>
         */
        private Integer sizeValueType;

        /**
         * 单商品宽度，单位厘米
         */
        private BigDecimal singleProductWidth;

        /**
         * 单商品高度，单位厘米
         */
        private BigDecimal singleProductHeight;

        /**
         * 单商品长度，单位厘米
         */
        private BigDecimal singleProductLength;

        /**
         * 商品运费SKU信息列表
         */
        private List<ProductFreightSkuInfoModel> productFreightSkuInfoModels;
    }

    /**
     * 商品运费SKU 包装信息列表
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProductFreightSkuInfoModel {

        /**
         * 商品SKU ID
         */
        private String skuId;

        /**
         * 单个SKU重量，单位千克
         */
        private Double singleSkuWeight;

        /**
         * 单个SKU宽度，单位厘米
         */
        private Double singleSkuWidth;

        /**
         * 单个SKU高度，单位厘米
         */
        private Double singleSkuHeight;

        /**
         * 单个SKU长度，单位厘米
         */
        private Double singleSkuLength;
    }
}