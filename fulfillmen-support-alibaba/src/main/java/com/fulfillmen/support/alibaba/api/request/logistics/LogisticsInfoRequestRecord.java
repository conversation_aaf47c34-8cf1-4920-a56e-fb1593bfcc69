/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.logistics;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Builder;
import lombok.Getter;

/**
 * 获取交易订单的物流信息请求
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Builder
public record LogisticsInfoRequestRecord(
    /**
     * 订单ID 必填
     */
    String orderId,

    /**
     * 字段列表，多个字段用半角逗号分隔。可选值：logisticsId,logisticsBillNo,status,logisticsCompanyId,logisticsCompanyName,
     * sendGoods,receiver,sender,logisticsOrderGoods 选填
     */
    String fields,

    /**
     * 站点信息，指定调用的是哪个站点的商品，1688站点值为china 选填
     */
    String webSite
) implements BaseAlibabaRequestRecord {

    /**
     * 1688站点
     */
    public static final String WEBSITE_CHINA = "china";

    /**
     * 创建获取交易订单的物流信息请求
     *
     * @param orderId 订单ID(必填)
     * @param fields  字段列表(选填,参考FIELD_常量)
     * @param webSite 站点信息(选填,参考WEBSITE_常量)
     * @return 获取交易订单的物流信息请求
     */
    public static LogisticsInfoRequestRecord of(String orderId, List<LogisticsInfoField> fields, String webSite) {
        return new LogisticsInfoRequestRecord(orderId, LogisticsInfoField.of(fields), webSite);
    }

    /**
     * 创建获取交易订单的物流信息请求(1688站点)
     *
     * @param orderId 订单ID(必填)
     * @param fields  字段列表(选填,参考FIELD_常量)
     * @return 获取交易订单的物流信息请求
     */
    public static LogisticsInfoRequestRecord of(String orderId, List<LogisticsInfoField> fields) {
        return of(orderId, fields, WEBSITE_CHINA);
    }

    /**
     * 创建获取交易订单的物流信息请求(1688站点,查询所有字段)
     *
     * @param orderId 订单ID(必填)
     * @return 获取交易订单的物流信息请求
     */
    public static LogisticsInfoRequestRecord of(String orderId) {
        return of(orderId, Collections.emptyList(), WEBSITE_CHINA);
    }

    @Override
    public void requireParams() {
        assertNotBlank(orderId, "订单ID不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("orderId", orderId);
        if (fields != null) {
            params.put("fields", fields);
        }
        if (webSite != null) {
            params.put("webSite", webSite);
        }
        return params;
    }

    // 字段列表，多个字段用半角逗号分隔。可选值：logisticsId,logisticsBillNo,status,logisticsCompanyId,logisticsCompanyName,
    // sendGoods,receiver,sender,logisticsOrderGoods
    @Getter
    public enum LogisticsInfoField {

        /**
         * 物流ID
         */
        LOGISTICS_ID("logisticsId"),

        /**
         * 物流单号
         */
        LOGISTICS_BILL_NO("logisticsBillNo"),

        /**
         * 物流状态
         */
        STATUS("status"),

        /**
         * 物流公司ID
         */
        LOGISTICS_COMPANY_ID("logisticsCompanyId"),

        /**
         * 物流公司名称
         */
        LOGISTICS_COMPANY_NAME("logisticsCompanyName"),

        /**
         * 发货信息
         */
        SEND_GOODS("sendGoods"),

        /**
         * 收货人信息
         */
        RECEIVER("receiver"),

        /**
         * 发货人信息
         */
        SENDER("sender"),

        /**
         * 物流订单商品信息
         */
        LOGISTICS_ORDER_GOODS("logisticsOrderGoods");

        private final String value;

        LogisticsInfoField(String value) {
            this.value = value;
        }

        @Override
        public String toString() {
            return value;
        }

        /**
         * 创建字段列表
         *
         * @param fields 字段列表
         * @return 字段列表
         */
        public static String of(LogisticsInfoField... fields) {
            return Arrays.stream(fields).map(LogisticsInfoField::toString).collect(Collectors.joining(","));
        }

        /**
         * 创建字段列表
         *
         * @param fields 字段列表
         * @return 字段列表
         */
        public static String of(List<LogisticsInfoField> fields) {
            if (fields == null || fields.isEmpty()) {
                return null;
            }
            return fields.stream().map(LogisticsInfoField::toString).collect(Collectors.joining(","));
        }
    }
}
