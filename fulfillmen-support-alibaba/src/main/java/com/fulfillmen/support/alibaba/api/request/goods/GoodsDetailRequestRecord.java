/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.goods;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import com.fulfillmen.support.alibaba.enums.LanguageEnum;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 多语言商品详情请求参数
 *
 * <AUTHOR>
 * @created 2025-01-17
 */
@Builder
public record GoodsDetailRequestRecord(
    @JsonProperty("offerId") Long offerId,
    @JsonProperty("country") String country,
    @JsonProperty("outMemberId") String outMemberId
) implements BaseAlibabaRequestRecord {

    /**
     * 创建多语言商品详情请求
     *
     * @param offerId 商品ID（必填）
     * @param country 语言（必填）示例：ja-日语 en-英语
     * @return 多语言商品详情请求
     */
    public static GoodsDetailRequestRecord of(Long offerId, LanguageEnum country) {
        return of(offerId, country, null);
    }

    /**
     * 创建多语言商品详情请求
     *
     * @param offerId     商品ID（必填）
     * @param country     语言（必填）示例：ja-日语 en-英语
     * @param outMemberId 外部用户ID（选填）
     * @return 多语言商品详情请求
     */
    public static GoodsDetailRequestRecord of(Long offerId, LanguageEnum country, String outMemberId) {
        return new GoodsDetailRequestRecord(offerId, country.getLanguage(), outMemberId);
    }

    @Override
    public void requireParams() {
        assertNotNull(offerId, "商品ID不能为空");
        assertTrue(offerId > 0, "Invalid offerId");
        assertNotNull(country, "语言不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("offerId", offerId.toString());
        params.put("country", country);
        params.put("outMemberId", outMemberId);
        return params;
    }
}