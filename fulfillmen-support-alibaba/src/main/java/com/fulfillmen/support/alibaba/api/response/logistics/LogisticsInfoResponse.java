/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.logistics;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import com.fulfillmen.support.alibaba.api.response.model.OpenPlatformLogisticsOrder;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 获取交易订单的物流信息响应
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LogisticsInfoResponse extends BaseAlibabaResponse {

    /**
     * 返回结果
     */
    private List<OpenPlatformLogisticsOrder> result;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误描述
     */
    private String errorMessage;
}