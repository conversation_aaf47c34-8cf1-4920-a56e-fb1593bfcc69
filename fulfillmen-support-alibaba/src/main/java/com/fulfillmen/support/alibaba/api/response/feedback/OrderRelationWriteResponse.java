/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.feedback;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 回传机构真实用户订单和1688订单的映射关系响应
 *
 * <AUTHOR>
 * @created 2025-01-15
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:order.relation.write-1">API文档</a>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderRelationWriteResponse extends BaseAlibabaResponse {

    /**
     * 响应结果
     */
    private Result result;

    @Data
    public static class Result {

        /**
         * 是否成功
         */
        private Boolean success;

        /**
         * 错误码
         */
        private String code;

        /**
         * 错误描述
         */
        private String message;
    }
}