/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model;

import java.util.List;
import lombok.Data;

/**
 * 查询退款退货原因结果
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Data
public class RefundReasonListResult {

    /**
     * 嵌套的结果对象
     */
    private InnerResult result;
    /**
     * 是否成功
     */
    private Boolean success;

    @Data
    public static class InnerResult {

        /**
         * 错误码 出现错误时返回
         */
        private String code;

        /**
         * 错误信息 出现错误时返回
         */
        private String message;

        /**
         * 退款原因列表
         */
        private List<RefundReason> reasons;
    }

    @Data
    public static class RefundReason {

        /**
         * 退款原因ID
         */
        private Long id;

        /**
         * 退款原因名称
         */
        private String name;

        /**
         * 是否需要上传凭证
         */
        private Boolean needVoucher;

        /**
         * 是否不支持退运费
         */
        private Boolean noRefundCarriage;

        /**
         * 提示信息
         */
        private String tip;
    }
}