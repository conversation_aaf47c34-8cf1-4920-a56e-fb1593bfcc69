/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.business;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 查询榜单列表请求记录
 *
 * @param rankId   榜单ID
 * @param rankType 榜单类型
 * @param limit    榜单商品个数，最多20
 * @param language 榜单商品语言
 * <AUTHOR>
 * @created 2025-01-23
 */
@Builder
public record RankQueryRequestRecord(
    @JsonProperty("rankId") String rankId,
    @JsonProperty("rankType") String rankType,
    @JsonProperty("limit") Integer limit,
    @JsonProperty("language") String language
) implements BaseAlibabaRequestRecord {

    private static final int MAX_LIMIT = 20;

    @Override
    public void requireParams() {
        assertNotBlank(rankId, "榜单ID不能为空");
        assertNotBlank(rankType, "榜单类型不能为空");
        assertNotNull(limit, "榜单商品个数不能为空");
        assertTrue(limit > 0 && limit <= MAX_LIMIT, "榜单商品个数必须大于0且不超过20");
        assertNotBlank(language, "榜单商品语言不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        Map<String, Object> rankQueryParams = new HashMap<>();
        rankQueryParams.put("rankId", rankId);
        rankQueryParams.put("rankType", rankType);
        rankQueryParams.put("limit", limit);
        rankQueryParams.put("language", language);

        params.put("rankQueryParams", toJsonString(rankQueryParams));
        return params;
    }
}