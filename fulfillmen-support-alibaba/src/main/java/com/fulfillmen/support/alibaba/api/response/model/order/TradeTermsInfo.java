/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fulfillmen.support.alibaba.api.response.util.AlibabaDateDeserializer;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 交易条款信息
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Data
public class TradeTermsInfo {

    /**
     * 支付状态
     * <pre>
     * 国际站：
     * WAIT_PAY(未支付),
     * PAYER_PAID(已完成支付),
     * PART_SUCCESS(部分支付成功),
     * PAY_SUCCESS(支付成功),
     * CLOSED(风控关闭),
     * CANCELLED(支付撤销),
     * SUCCESS(成功),
     * FAIL(失败)。
     * 1688:
     * 1(未付款);
     * 2(已付款);
     * 4(全额退款);
     * 6(卖家有收到钱，回款完成) ;
     * 7(未创建外部支付单);
     * 8 (付款前取消) ;
     * 9(正在支付中);
     * 12(账期支付,待到账)
     * </pre>
     */
    @JsonProperty("payStatus")
    private String payStatus;

    /**
     * 完成阶段支付时间
     */
    @JsonProperty("payTime")
    @JsonDeserialize(using = AlibabaDateDeserializer.class)
    private LocalDateTime payTime;

    /**
     * 支付方式
     * <pre>
     * 国际站：
     * <ul>
     * <li>ECL(融资支付)</li>
     * <li>CC(信用卡)</li>
     * <li>TT(线下TT)</li>
     * <li>ACH(echecking支付)</li>
     * </ul>
     * <p>
     * 1688:
     * <ul>
     * <li>1-支付宝</li>
     * <li>2-网商银行信任付</li>
     * <li>3-诚e赊</li>
     * <li>4-银行转账</li>
     * <li>5-赊销宝</li>
     * <li>6-电子承兑票据</li>
     * <li>7-账期支付</li>
     * <li>8-合并支付渠道</li>
     * <li>9-无打款</li>
     * <li>10-零售通赊购</li>
     * <li>13-支付平台</li>
     * <li>12-声明付款</li>
     * </ul>
     * </pre>
     */
    @JsonProperty("payWay")
    private String payWay;

    /**
     * 付款额
     */
    @JsonProperty("phasAmount")
    private BigDecimal phasAmount;

    /**
     * 阶段单id
     */
    @JsonProperty("phase")
    private Long phase;

    /**
     * 阶段条件，1688无此内容
     */
    @JsonProperty("phaseCondition")
    private String phaseCondition;

    /**
     * 阶段时间,1688无此内容
     */
    @JsonProperty("phaseDate")
    private String phaseDate;

    /**
     * 是否银行卡支付
     */
    @JsonProperty("cardPay")
    private Boolean cardPay;

    /**
     * 是否快捷支付
     */
    @JsonProperty("expressPay")
    private Boolean expressPay;

    /**
     * 支付方式描述
     */
    @JsonProperty("payWayDesc")
    private String payWayDesc;
}
