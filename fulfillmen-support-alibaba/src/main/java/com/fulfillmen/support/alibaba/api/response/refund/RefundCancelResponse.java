/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.refund;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import com.fulfillmen.support.alibaba.api.response.model.RefundCancelResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 取消退款申请响应
 * <p>
 * 买家取消退款申请的响应结果，包含取消操作的结果状态
 *
 * <AUTHOR>
 * @created 2025-01-13
 * @see RefundCancelResult
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RefundCancelResponse extends BaseAlibabaResponse {

    /**
     * 退款申请取消结果
     */
    private RefundCancelResult result;
}