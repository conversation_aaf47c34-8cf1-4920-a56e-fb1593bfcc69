/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service;

import com.fulfillmen.support.alibaba.api.request.order.OrderBuyerListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.order.OrderCancelRequestRecord;
import com.fulfillmen.support.alibaba.api.request.order.OrderCreateRequestRecord;
import com.fulfillmen.support.alibaba.api.request.order.OrderDetailRequestRecord;
import com.fulfillmen.support.alibaba.api.request.order.OrderPreviewRequestRecord;
import com.fulfillmen.support.alibaba.api.request.order.TradeFeedbackRequestRecord;
import com.fulfillmen.support.alibaba.api.response.order.OrderBuyerListResponse;
import com.fulfillmen.support.alibaba.api.response.order.OrderCancelResponse;
import com.fulfillmen.support.alibaba.api.response.order.OrderCreateResponse;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse;
import com.fulfillmen.support.alibaba.api.response.order.OrderPreviewResponse;
import com.fulfillmen.support.alibaba.api.response.order.TradeFeedbackResponse;
import reactor.core.publisher.Mono;

/**
 * 1688订单服务接口
 *
 * <AUTHOR>
 * @created 2025-01-10
 */
public interface IOrderService {

    /**
     * 取消交易
     *
     * @param request 取消交易请求
     * @return 取消交易响应
     */
    Mono<OrderCancelResponse> cancelOrder(OrderCancelRequestRecord request);

    /**
     * 获取订单列表(买家视角)
     *
     * @param request 获取订单列表请求
     * @return 订单列表响应
     */
    Mono<OrderBuyerListResponse> getBuyerOrderList(OrderBuyerListRequestRecord request);

    /**
     * 获取订单详情
     *
     * @param request 获取订单详情请求
     * @return 订单详情响应
     */
    Mono<OrderDetailResponse> getOrderDetail(OrderDetailRequestRecord request);

    /**
     * 买家补充订单留言
     *
     * @param request 补充留言请求
     * @return 补充留言响应
     */
    Mono<TradeFeedbackResponse> addFeedback(TradeFeedbackRequestRecord request);

    /**
     * 创建订单
     *
     * @param request 创建订单请求
     * @return 创建订单响应
     */
    Mono<OrderCreateResponse> createCrossOrder(OrderCreateRequestRecord request);

    /**
     * 预览订单
     *
     * <pre>
     * 订单创建只允许购买同一个供应商的商品。本接口返回创建订单相关的优惠等信息。
     * 1、校验商品数据是否允许订购。
     * 2、校验代销关系。
     * 3、校验库存、起批量、是否满足混批条件。
     * 错误码
     * 错误码 错误描述 解决方案
     * 500_001 商品[offerId]不支持在线交易，无法下单。 商品不支持在线交易，目前不能购买
     * 500_002 商品[offerId]不属于同一卖家或者没有指定specId。 存在多个卖家的商品或者商品没有指定specId
     * 500_003 商品[offerId ]不属于同一卖家或者规格[specId] 不属于商品[offerId] 存在多个卖家的商品或者商品不存在specId的规格
     * 500_004 商品[offerId_specId]库存不足，请核实库存后订购。 商品的某个规格库存不足
     * 500_005 商品[offerId]的购买数量不满足起批量限制。 商品的购买数量小于起批量
     * 500_006 商品[offerId]的购买数量或者价格不满足混批限制。 商品的购买数量或者总金额均不满足混批条件
     * 500_007 与供应商的代销关系不存在,不能使用saleproxy通道下单。 flow不能使用slproxy
     * 500_009 商品[offerId]的购买数量不满足批售起批量限制。 检查批售商品的购买数量
     * 500_008 商品规格[offerId_specId]的价格为0，不可以下单，请检查后重新提交。 检查下商品规格的价格
     * </pre>
     *
     * @param request 预览订单请求
     * @return 预览订单响应
     */
    Mono<OrderPreviewResponse> previewOrder(OrderPreviewRequestRecord request);
}
