/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fulfillmen.support.alibaba.api.response.util.AlibabaDateDeserializer;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * 国内物流信息
 *
 * <AUTHOR>
 * @created 2025-01-14
 */
@Data
public class TradeNativeLogisticsInfo {

    /**
     * 详细地址
     */
    @JsonProperty("address")
    private String address;

    /**
     * 县，区
     */
    @JsonProperty("area")
    private String area;

    /**
     * 省市区编码
     */
    @JsonProperty("areaCode")
    private String areaCode;

    /**
     * 城市
     */
    @JsonProperty("city")
    private String city;

    /**
     * 联系人姓名
     */
    @JsonProperty("contactPerson")
    private String contactPerson;

    /**
     * 传真
     */
    @JsonProperty("fax")
    private String fax;

    /**
     * 手机
     */
    @JsonProperty("mobile")
    private String mobile;

    /**
     * 省份
     */
    @JsonProperty("province")
    private String province;

    /**
     * 电话
     */
    @JsonProperty("telephone")
    private String telephone;

    /**
     * 邮编
     */
    @JsonProperty("zip")
    private String zip;

    /**
     * 运单明细
     */
    @JsonProperty("logisticsItems")
    private List<LogisticsItem> logisticsItems;

    /**
     * 镇，街道地址码
     */
    @JsonProperty("townCode")
    private String townCode;

    /**
     * 镇，街道
     */
    @JsonProperty("town")
    private String town;

    /**
     * 物流明细
     */
    @Data
    public static class LogisticsItem {

        /**
         * 主键id
         */
        @JsonProperty("id")
        private Long id;

        /**
         * 发货时间
         */
        @JsonProperty("deliveredTime")
        @JsonDeserialize(using = AlibabaDateDeserializer.class)
        private LocalDateTime deliveredTime;

        /**
         * 物流编号
         */
        @JsonProperty("logisticsCode")
        private String logisticsCode;

        /**
         * 发货类型
         * <pre>
         * SELF_SEND_GOODS("0")自行发货，
         * 在线发货ONLINE_SEND_GOODS("1"，不需要物流的发货 NO_LOGISTICS_SEND_GOODS("2")
         * </pre>
         */
        @JsonProperty("type")
        private String type;

        /**
         * 状态
         */
        @JsonProperty("status")
        private String status;

        /**
         * 修改时间
         */
        @JsonProperty("gmtModified")
        @JsonDeserialize(using = AlibabaDateDeserializer.class)
        private LocalDateTime gmtModified;

        /**
         * 创建时间
         */
        @JsonProperty("gmtCreate")
        @JsonDeserialize(using = AlibabaDateDeserializer.class)
        private LocalDateTime gmtCreate;

        /**
         * 运费(单位为元)
         */
        @JsonProperty("carriage")
        private BigDecimal carriage;

        /**
         * 发货省
         */
        @JsonProperty("fromProvince")
        private String fromProvince;

        /**
         * 发货市
         */
        @JsonProperty("fromCity")
        private String fromCity;

        /**
         * 发货区
         */
        @JsonProperty("fromArea")
        private String fromArea;

        /**
         * 发货街道地址
         */
        @JsonProperty("fromAddress")
        private String fromAddress;

        /**
         * 发货联系电话
         */
        @JsonProperty("fromPhone")
        private String fromPhone;

        /**
         * 发货联系手机
         */
        @JsonProperty("fromMobile")
        private String fromMobile;

        /**
         * 发货地址邮编
         */
        @JsonProperty("fromPost")
        private String fromPost;

        /**
         * 物流公司Id
         */
        @JsonProperty("logisticsCompanyId")
        private Long logisticsCompanyId;

        /**
         * 物流公司编号
         */
        @JsonProperty("logisticsCompanyNo")
        private String logisticsCompanyNo;

        /**
         * 物流公司名称
         */
        @JsonProperty("logisticsCompanyName")
        private String logisticsCompanyName;

        /**
         * 物流公司运单号
         */
        @JsonProperty("logisticsBillNo")
        private String logisticsBillNo;

        /**
         * 商品明细条目id，如有多个以,分隔
         */
        @JsonProperty("subItemIds")
        private String subItemIds;

        /**
         * 收货省
         */
        @JsonProperty("toProvince")
        private String toProvince;

        /**
         * 收货市
         */
        @JsonProperty("toCity")
        private String toCity;

        /**
         * 收货区
         */
        @JsonProperty("toArea")
        private String toArea;

        /**
         * 收货街道地址
         */
        @JsonProperty("toAddress")
        private String toAddress;

        /**
         * 收货联系电话
         */
        @JsonProperty("toPhone")
        private String toPhone;

        /**
         * 收货联系手机
         */
        @JsonProperty("toMobile")
        private String toMobile;

        /**
         * 收货地址邮编
         */
        @JsonProperty("toPost")
        private String toPost;

        /**
         * 物流姓名
         */
        @JsonProperty("noLogisticsName")
        private String noLogisticsName;

        /**
         * 联系方式
         */
        @JsonProperty("noLogisticsTel")
        private String noLogisticsTel;

        /**
         * 无需物流业务单号
         */
        @JsonProperty("noLogisticsBillNo")
        private String noLogisticsBillNo;

        /**
         * 无需物流类别
         * <pre>
         * 无需物流类别,
         * noLogisticsCondition=1 表示其他第三方物流、小型物充商、车队等,
         * noLogisticsCondition=2 表示补运费、差价,
         * noLogisticsCondition=3 表示卖家配送,
         * noLogisticsCondition=4 表示买家自提
         * noLogisticsCondition=5 表示其他原因
         * </pre>
         */
        @JsonProperty("noLogisticsCondition")
        private String noLogisticsCondition;
    }
}