/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api;

import com.fulfillmen.support.alibaba.api.response.refund.RefundBuyerListResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundBuyerOrderViewResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundBuyerSubmitResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundBuyerViewResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundCancelResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundCreateResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundOpLogListResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundReasonListResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundUploadEvidenceResponse;
import org.springframework.http.MediaType;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * 1688退款相关API
 * <p>
 * 包含以下接口： 1. 取消退款退货申请 2. 查询退款单列表(买家视角) 3. 买家提交退款货信息 4. 上传退款退货凭证 5. 查询退款退货原因 6. 创建退款退货申请 7. 查询退款单详情(根据退款单ID) 8. 退款单操作记录列表 9. 查询退款单详情(根据订单ID)
 *
 * <AUTHOR>
 * @created 2025-01-13
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.cancelRefund-1">取消退款API文档</a>
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.buyer.list-1">退款单列表API文档</a>
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.buyer.submit-1">提交退款信息API文档</a>
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.uploadEvidence-1">上传凭证API文档</a>
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.getRefundReasonList-1">退款原因API文档</a>
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.createRefund-1">创建退款API文档</a>
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.buyer.view-1">退款详情API文档</a>
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.OpLogList-1">操作记录API文档</a>
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.buyer.orderView-1">订单退款详情API文档</a>
 */
@HttpExchange("/")
public interface RefundAPI {

    /**
     * 取消退款退货申请
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 取消退款响应
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.cancelRefund-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.RefundAPI.CANCEL_REFUND, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<RefundCancelResponse> cancelRefund(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 查询退款单列表(买家视角)
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 退款单列表响应
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.buyer.list-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.RefundAPI.BUYER_LIST, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<RefundBuyerListResponse> queryRefundBuyerList(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 买家提交退款货信息
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 提交退款信息响应
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.buyer.submit-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.RefundAPI.BUYER_SUBMIT, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<RefundBuyerSubmitResponse> submitRefundBuyerInfo(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 上传退款退货凭证
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 上传凭证响应
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.uploadEvidence-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.RefundAPI.UPLOAD_EVIDENCE, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<RefundUploadEvidenceResponse> uploadRefundEvidence(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 查询退款退货原因
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 退款原因响应
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.getRefundReasonList-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.RefundAPI.REASON_LIST, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<RefundReasonListResponse> getRefundReasonList(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 创建退款退货申请
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 创建退款响应
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.createRefund-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.RefundAPI.CREATE, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<RefundCreateResponse> createRefund(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 查询退款单详情(根据退款单ID)
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 退款详情响应
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.buyer.view-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.RefundAPI.BUYER_VIEW, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<RefundBuyerViewResponse> queryRefundBuyerView(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 退款单操作记录列表
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 操作记录响应
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.OpLogList-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.RefundAPI.OP_LOG_LIST, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<RefundOpLogListResponse> queryRefundOpLogList(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 查询退款单详情(根据订单ID)
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 订单退款详情响应
     * @see <a href=
     *      "https://open.1688.com/api/apidocdetail.htm?spm=a260s.develop-solution-detail.0.0.4d0e55edEOTZYd&id=com.alibaba.trade:alibaba.trade.refund.OpQueryBatchRefundByOrderIdAndStatus-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.RefundAPI.BUYER_ORDER_VIEW, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<RefundBuyerOrderViewResponse> queryRefundBuyerOrderView(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);
}