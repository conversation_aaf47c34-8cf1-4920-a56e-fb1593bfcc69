/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.goods;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品优惠券领取响应
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsCouponClaimResponse extends BaseAlibabaResponse {

    private Result result;

    @Data
    public static class Result {

        /**
         * 是否成功
         */
        private Boolean success;

        /**
         * 优惠券结果
         */
        private CouponResult result;
    }

    @Data
    public static class CouponResult {

        /**
         * 优惠券ID列表
         */
        private String[] couponIds;
    }
}