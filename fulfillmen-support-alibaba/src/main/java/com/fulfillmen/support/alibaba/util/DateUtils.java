/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.util;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

/**
 * 日期工具类
 *
 * <AUTHOR>
 * @created 2025-01-15
 */
public class DateUtils {

    private static final String DATE_FORMAT = "yyyyMMddHHmmssSSS";
    private static final String TIMEZONE = "+0800";

    /**
     * 格式化日期为阿里巴巴API要求的格式 示例：20130417000000000+0800
     *
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    public static String formatDate(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        return sdf.format(date) + TIMEZONE;
    }
}