/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service;

import com.fulfillmen.support.alibaba.api.request.message.MessageConfirmRequestRecord;
import com.fulfillmen.support.alibaba.api.request.message.MessageCursorRequestRecord;
import com.fulfillmen.support.alibaba.api.request.message.MessageQueryFailedListRequestRecord;
import com.fulfillmen.support.alibaba.api.response.message.MessageConfirmResponse;
import com.fulfillmen.support.alibaba.api.response.message.MessageCursorResponse;
import com.fulfillmen.support.alibaba.api.response.message.MessageQueryFailedListResponse;
import reactor.core.publisher.Mono;

/**
 * 1688消息服务接口 提供消息查询、确认等功能
 *
 * <AUTHOR>
 * @created 2025-01-15
 */
public interface IMessageService {

    /**
     * 查询式获取失败的消息列表 获取的消息不会自动确认，需要调用方手动调用确认API来确认消息状态。需注意，确认后，会标记分页段的所有消息。
     *
     * @param request 查询请求
     * @return 失败消息列表响应
     */
    Mono<MessageQueryFailedListResponse> queryFailedMessageList(MessageQueryFailedListRequestRecord request);

    /**
     * 失败消息批量确认 手动调用确认API，确认消息已经被消费成功。仅当使用查询式获取失败消息的时候，才需要使用此接口进行确认。
     *
     * @param request 确认请求
     * @return 确认响应
     */
    Mono<MessageConfirmResponse> confirmFailedMessage(MessageConfirmRequestRecord request);

    /**
     * 游标式获取失败的消息列表 获取失败的消息列表，支持游标式分页。每次请求返回的消息会自动从消息队列中删除，所以下次请求不会再获取到相同的消息。
     *
     * @param request 请求参数
     * @return 失败消息列表响应
     */
    Mono<MessageCursorResponse> getMessageCursorList(MessageCursorRequestRecord request);
}