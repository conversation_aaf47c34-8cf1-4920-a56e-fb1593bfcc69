/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.member;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceException;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 1688会员注册请求
 *
 * <AUTHOR>
 * @created 2025-01-17
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:account.user.register-1">API文档</a>
 */
@Builder
public record MemberRegisterRequestRecord(
    /**
     * 账号注册入参
     */
    @JsonProperty("countryAccount") CountryAccount countryAccount
) implements BaseAlibabaRequestRecord {

    /**
     * 创建1688会员注册请求
     *
     * @param country     国家(必填)
     * @param site        站点(必填)
     * @param outLoginId  登录名(必填)
     * @param outMemberId 用户标识(必填)
     * @param email       邮箱(必填)
     * @param mobile      手机号(必填)
     * @param mobileArea  手机号所属地区(必填)
     * @param ip          IP地址(必填)
     * @return 1688会员注册请求
     */
    public static MemberRegisterRequestRecord of(String country,
        String site,
        String outLoginId,
        String outMemberId,
        String email,
        String mobile,
        String mobileArea,
        String ip) {
        var countryAccount = new CountryAccount(country, site, outLoginId, outMemberId, email, mobile, mobileArea, ip);
        return new MemberRegisterRequestRecord(countryAccount);
    }

    @Override
    public void requireParams() {
        assertNotNull(countryAccount, "账号注册入参不能为空");
        assertNotBlank(countryAccount.country(), "国家不能为空");
        assertNotBlank(countryAccount.site(), "站点不能为空");
        assertNotBlank(countryAccount.outLoginId(), "登录名不能为空");
        assertNotBlank(countryAccount.outMemberId(), "用户标识不能为空");
        assertNotBlank(countryAccount.email(), "邮箱不能为空");
        assertNotBlank(countryAccount.mobile(), "手机号不能为空");
        assertNotBlank(countryAccount.mobileArea(), "手机号所属地区不能为空");
        assertNotBlank(countryAccount.ip(), "IP地址不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        try {
            params.put("countryAccount", toJsonString(countryAccount));
            return params;
        } catch (Exception e) {
            throw new AlibabaServiceException("参数序列化失败", e);
        }
    }

    /**
     * 账号注册入参
     */
    public record CountryAccount(
        /**
         * 国家，关联的参数是country枚举
         */
        String country,

        /**
         * 站点，不超过16字节
         */
        String site,

        /**
         * 用户在其他的登录名，不超过60位字符
         */
        String outLoginId,

        /**
         * 用户在其他的用户标识，不超过60位
         */
        String outMemberId,

        /**
         * 邮箱，需符合校验格式，不超过60位
         */
        String email,

        /**
         * 手机号，需符合校验格式，不超过30位
         */
        String mobile,

        /**
         * 手机号所属地区，支持的参数是mobileArea枚举
         */
        String mobileArea,

        /**
         * IP地址，需符合校验格式
         */
        String ip
    ) {

    }
}