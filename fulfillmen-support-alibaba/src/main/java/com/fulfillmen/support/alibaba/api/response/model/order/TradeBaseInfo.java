/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fulfillmen.support.alibaba.api.response.model.Contact;
import com.fulfillmen.support.alibaba.api.response.util.AlibabaDateDeserializer;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * 订单基础信息
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Data
public class TradeBaseInfo {

    /**
     * 订单ID
     */
    private Long id;

    /**
     * 订单ID字符串格式
     */
    @JsonProperty("idOfStr")
    private String idOfStr;

    /**
     * 业务类型
     *
     * <pre>
     * 国际站：ta(信保),
     * wholesale(在线批发)。
     * 中文站：普通订单类型 = "cn";
     * 大额批发订单类型 = "ws";
     * 普通拿样订单类型 = "yp";
     * 一分钱拿样订单类型 = "yf";
     * 倒批(限时折扣)订单类型 = "fs";
     * 加工定制订单类型 = "cz";
     * 协议采购订单类型 = "ag";
     * 伙拼订单类型 = "hp";
     * 供销订单类型 = "supply";
     * 淘工厂订单 = "factory";
     * 快订下单 = "quick";
     * 享拼订单 = "xiangpin";
     * 当面付 = "f2f";
     * 存样服务 = "cyfw";
     * 代销订单 = "sp";
     * 微供订单 = "wg";
     * 零售通 = "lst";
     * 跨境='cb';
     * 分销='distribution';
     * 采源宝='cab';
     * 加工定制="manufact"
     * </pre>
     */
    private String businessType;

    /**
     * 买家主账号id
     */
    @JsonProperty("buyerID")
    private String buyerId;

    /**
     * 完成时间
     */
    @JsonProperty("completeTime")
    @JsonDeserialize(using = AlibabaDateDeserializer.class)
    private LocalDateTime completeTime;

    /**
     * 创建时间
     */
    @JsonProperty("createTime")
    @JsonDeserialize(using = AlibabaDateDeserializer.class)
    private LocalDateTime createTime;

    /**
     * 修改时间
     * <p>
     * 格式：yyyy-MM-dd HH:mm:ss 示例：20180614101942000+0800
     * </p>
     */
    @JsonProperty("modifyTime")
    @JsonDeserialize(using = AlibabaDateDeserializer.class)
    private LocalDateTime modifyTime;

    /**
     * 退款金额，单位为元
     */
    private BigDecimal refund;

    /**
     * 卖家主账号id
     */
    @JsonProperty("sellerID")
    private String sellerId;

    /**
     * 运费，单位为元
     */
    private BigDecimal shippingFee;

    /**
     * 订单状态
     * <p>
     * 交易状态 waitbuyerpay:等待买家付款; waitsellersend:等待卖家发货; waitbuyerreceive:等待买家收货; confirm_goods:已收货; success:交易成功; cancel:交易取消; terminated:交易终止; 未枚举:其他状态
     * </p>
     */
    private String status;

    /**
     * 订单总金额 单位元
     * <p>
     * 应付款总金额，totalAmount = ∑itemAmount + shippingFee，单位为元
     * </p>
     */
    private BigDecimal totalAmount;

    /**
     * 折扣信息，单位分
     */
    private BigDecimal discount;

    /**
     * 买家联系信息
     */
    private Contact buyerContact;

    /**
     * 卖家联系信息
     */
    private TradeSellContact sellerContact;

    /**
     * 交易类型
     *
     * <pre>
     * 1:担保交易
     * 2:预存款交易
     * 3:ETC境外收单交易
     * 4:即时到帐交易
     * 5:保障金安全交易
     * 6:统一交易流程
     * 7:分阶段付款
     * 8.货到付款交易
     * 9.信用凭证支付交易
     * 10.账期支付交易，
     * 50060 交易4.0
     * </pre>
     */
    private String tradeType;

    /**
     * 退款支付金额
     */
    private BigDecimal refundPayment;

    /**
     * 完全发货时间 格式：yyyy-MM-dd HH:mm:ss
     * <p>
     * 示例：20180614101942000+0800
     * </p>
     */
    @JsonProperty("allDeliveredTime")
    @JsonDeserialize(using = AlibabaDateDeserializer.class)
    private LocalDateTime allDeliveredTime;

    /**
     * 付款时间
     *
     * <pre>
     * 格式：yyyy-MM-dd HH:mm:ss
     * 示例：20180614101942000+0800
     * 注意：
     * 付款时间，如果有多次付款，这里返回的是首次付款时间
     * </pre>
     */
    @JsonProperty("payTime")
    @JsonDeserialize(using = AlibabaDateDeserializer.class)
    private LocalDateTime payTime;

    /**
     * 收货时间
     *
     * <pre>
     * 格式：yyyy-MM-dd HH:mm:ss
     * 示例：20180614101942000+0800
     * 注意：
     * 收货时间，这里返回的是完全收货时间
     * </pre>
     */
    @JsonProperty("receivingTime")
    @JsonDeserialize(using = AlibabaDateDeserializer.class)
    private LocalDateTime receivingTime;

    /**
     * 支付宝交易ID
     */
    @JsonProperty("alipayTradeId")
    private String alipayTradeId;

    /**
     * 产品总金额(该订单产品明细表中的产品金额的和)，单位元
     */
    @JsonProperty("sumProductPayment")
    private BigDecimal sumProductPayment;

    /**
     * 流程模板代码 - 示例：flow
     */
    @JsonProperty("flowTemplateCode")
    private String flowTemplateCode;

    /**
     * 是否自主订单（邀约订单）
     */
    @JsonProperty("sellerOrder")
    private Boolean sellerOrder;

    /**
     * 买家loginId，旺旺Id
     */
    @JsonProperty("buyerLoginId")
    private String buyerLoginId;

    /**
     * 卖家loginId，旺旺Id
     */
    @JsonProperty("sellerLoginId")
    private String sellerLoginId;

    /**
     * 关闭操作类型
     *
     * <pre>
     * 关闭订单操作类型。
     * CLOSE_TRADE_BY_SELLER:卖家关闭交易,
     * CLOSE_TRADE_BY_BOPS:BOPS后台关闭交易,
     * CLOSE_TRADE_BY_SYSTEM:系统（超时）关闭交易,
     * CLOSE_TRADE_BY_BUYER:买家关闭交易,
     * CLOSE_TRADE_BY_CREADIT:诚信保障投诉关闭
     * </pre>
     */
    @JsonProperty("closeOperateType")
    private String closeOperateType;

    /**
     * 红包金额，实付金额（totalAmount）已经计算过红包金额
     */
    @JsonProperty("couponFee")
    private BigDecimal couponFee;

    /**
     * 收货人信息
     */
    @JsonProperty("receiverInfo")
    private TradeReceiverInfo receiverInfo;

    /**
     * 交易类型描述 下单时指定的交易方式
     */
    @JsonProperty("tradeTypeDesc")
    private String tradeTypeDesc;

    /**
     * 支付渠道列表
     * <p>
     * 示例：["支付宝","跨境宝","银行转账"]
     * </p>
     *
     * <pre>
     * 支付渠道名称列表。一笔订单可能存在多种支付渠道。
     * 枚举值：
     * 支付宝,
     * 网商银行信任付,
     * 诚e赊,
     * 对公转账,
     * 赊销宝,
     * 账期支付,
     * 合并支付渠道,
     * 支付平台,
     * 声明付款,
     * 网商电子银行承兑汇票,
     * 银行转账,
     * 跨境宝,
     * 红包,
     * 其它
     * </pre>
     */
    @JsonProperty("payChannelList")
    private List<String> payChannelList;

    /**
     * 交易类型代码 下单时指定的交易方式tradeType
     */
    @JsonProperty("tradeTypeCode")
    private String tradeTypeCode;

    /**
     * 支付超时时间，定长情况时单位：秒，目前都是定长
     */
    @JsonProperty("payTimeout")
    private Long payTimeout;

    /**
     * 支付超时类型 支付超时TYPE，0：定长，1：固定时间
     */
    @JsonProperty("payTimeoutType")
    private Integer payTimeoutType;

    /**
     * 支付渠道代码列表
     * <p>
     * 示例：["alipay","cbpay","bank"]
     * </p>
     *
     * <pre>
     * 支付渠道code，payChannelCodeList的中文示意参见payChannelList
     * </pre>
     */
    @JsonProperty("payChannelCodeList")
    private List<String> payChannelCodeList;

    /**
     * 外部订单ID
     */
    @JsonProperty("outOrderId")
    private String outOrderId;

    /**
     * 是否一次性付款
     *
     * <pre>
     * 是否一次性付款，true:是，false:否
     * </pre>
     */
    @JsonProperty("stepPayAll")
    private Boolean stepPayAll;

    /**
     * 交易3.0分阶段订单列表 分阶段订单list
     */
    @JsonProperty("stepOrderList")
    private List<StepOrderModel> stepOrderList;

    /**
     * 新分阶段订单列表
     */
    @JsonProperty("newStepOrderList")
    private List<NewStepOrder> newStepOrderList;

    /**
     * 是否海外订单
     *
     * <pre>
     * 是否海外订单，true:是，false:否
     * </pre>
     */
    @JsonProperty("overSeaOrder")
    private Boolean overSeaOrder;

    /**
     * 卖家信用等级 example: L1
     */
    @JsonProperty("sellerCreditLevel")
    private String sellerCreditLevel;

    /**
     * 买家留言, 不超过 500 字
     */
    @JsonProperty("buyerFeedback")
    private String buyerFeedback;

    /**
     * 买家子账号 alitestforusv02:temp
     */
    @JsonProperty("subBuyerLoginId")
    private String subBuyerLoginId;

    /**
     * 关闭原因
     * <p>
     * buyerCancel:买家取消订单，sellerGoodsLack:卖家库存不足，other:其它
     * </p>
     */
    @JsonProperty("closeReason")
    private String closeReason;

    /**
     * 卖家支付宝id
     */
    @JsonProperty("sellerAlipayId")
    private String sellerAlipayId;

    /**
     * 买家数字id
     */
    @JsonProperty("buyerUserId")
    private Long buyerUserId;

    /**
     * 买家备忘信息
     */
    private String buyerMemo;

    /**
     * 买家备注标志
     */
    @JsonProperty("buyerRemarkIcon")
    private String buyerRemarkIcon;

    /**
     * 退款状态
     *
     * <pre>
     * 订单的售中退款状态，
     * 等待卖家同意：waitselleragree ，
     * 待买家修改：waitbuyermodify，
     * 等待买家退货：waitbuyersend，
     * 等待卖家确认收货：waitsellerreceive，
     * 退款成功：refundsuccess，
     * 退款失败：refundclose
     * </pre>
     */
    private String refundStatus;

    /**
     * 订单备注
     */
    private String remark;

    /**
     * 预订单ID
     */
    private Long preOrderId;

    /**
     * 订单确认时间
     */
    @JsonProperty("confirmedTime")
    @JsonDeserialize(using = AlibabaDateDeserializer.class)
    private LocalDateTime confirmedTime;

    /**
     * 关闭订单备注
     */
    private String closeRemark;

    /**
     * 分阶段法务协议地址
     */
    @JsonProperty("stepAgreementPath")
    private String stepAgreementPath;

    /**
     * 订单的售后退款状态
     */
    @JsonProperty("refundStatusForAs")
    private String refundStatusForAs;

    /**
     * 卖家数字id
     */
    @JsonProperty("sellerUserId")
    private Long sellerUserId;

    /**
     * 买家支付宝id
     */
    @JsonProperty("buyerAlipayId")
    private String buyerAlipayId;

    /**
     * 退款单ID
     */
    @JsonProperty("refundId")
    private String refundId;

    /**
     * 库存模式
     *
     * <pre>
     * 供货库存模式，jit（jit模式）或cang（仓发模式）
     * </pre>
     */
    @JsonProperty("inventoryMode")
    private String inventoryMode;

    /**
     * 新分阶段订单信息
     */
    @Data
    public static class NewStepOrder {

        /**
         * 阶段开始时间
         */
        @JsonProperty("gmtStart")
        @JsonDeserialize(using = AlibabaDateDeserializer.class)
        private LocalDateTime gmtStart;

        /**
         * 付款时间
         */
        @JsonProperty("gmtPay")
        @JsonDeserialize(using = AlibabaDateDeserializer.class)
        private LocalDateTime gmtPay;

        /**
         * 阶段结束时间
         */
        @JsonProperty("gmtEnd")
        @JsonDeserialize(using = AlibabaDateDeserializer.class)
        private LocalDateTime gmtEnd;

        /**
         * 阶段顺序编号
         */
        @JsonProperty("stepNo")
        private Integer stepNo;

        /**
         * 是否最后一个阶段
         */
        @JsonProperty("lastStep")
        private Boolean lastStep;

        /**
         * 阶段名称
         */
        @JsonProperty("stepName")
        private String stepName;

        /**
         * 激活状态 0：未激活，1：已激活
         */
        @JsonProperty("activeStatus")
        private Integer activeStatus;

        /**
         * 阶段付款状态 1未付款、2已付款、8付款前取消、12溢短补付款
         */
        @JsonProperty("payStatus")
        private Integer payStatus;

        /**
         * 物流环节状态 1未发货、2已发货、3已收货、4已全部退货、7发货前取消
         */
        @JsonProperty("logisticsStatus")
        private Integer logisticsStatus;

        /**
         * 阶段应付款（包含运费） 单位为元
         */
        @JsonProperty("payFee")
        private BigDecimal payFee;

        /**
         * 阶段已付款（包含运费）单位为元
         */
        @JsonProperty("paidFee")
        private BigDecimal paidFee;

        /**
         * 阶段商品价格分摊 ，单位为元
         */
        @JsonProperty("goodsFee")
        private BigDecimal goodsFee;

        /**
         * 阶段调整价格 单位为元
         */
        @JsonProperty("adjustFee")
        private BigDecimal adjustFee;

        /**
         * 阶段优惠价格 单位为元
         */
        @JsonProperty("discountFee")
        private BigDecimal discountFee;

        /**
         * 阶段的应付邮费 单位为元
         */
        @JsonProperty("postFee")
        private BigDecimal postFee;

        /**
         * 阶段已付的邮费 单位为元
         */
        @JsonProperty("paidPostFee")
        private BigDecimal paidPostFee;
    }

    /**
     * [交易3.0]分阶段交易，分阶段订单list
     */
    @Data
    public static class StepOrderModel {

        /**
         * 阶段id
         */
        @JsonProperty("stepOrderId")
        private Long stepOrderId;

        /**
         * 阶段状态
         *
         * <pre>
         * waitactivate 未开始（待激活）
         * waitsellerpush 等待卖家推进
         * success 本阶段完成
         * settlebill 分账
         * cancel 本阶段终止
         * inactiveandcancel 本阶段未开始便终止
         * waitbuyerpay 等待买家付款
         * waitsellersend 等待卖家发货
         * waitbuyerreceive 等待买家确认收货
         * waitselleract 等待卖家XX操作
         * waitbuyerconfirmaction 等待买家确认XX操作
         * </pre>
         */
        @JsonProperty("stepOrderStatus")
        private String stepOrderStatus;

        /**
         * 阶段付款状态
         *
         * <pre>
         * 1 未冻结/未付款
         * 2 已冻结/已付款
         * 4 已退款
         * 6 已转交易
         * 8 交易未付款被关闭
         * </pre>
         */
        @JsonProperty("stepPayStatus")
        private Integer stepPayStatus;

        /**
         * 是否最后一个阶段
         */
        @JsonProperty("lastStep")
        private Boolean lastStep;

        /**
         * 是否已打款给卖家
         */
        @JsonProperty("hasDisbursed")
        private Boolean hasDisbursed;

        /**
         * 创建时需要付款的金额，不含运费
         */
        @JsonProperty("payFee")
        private BigDecimal payFee;

        /**
         * 应付款（含运费）= 单价×数量-单品优惠-店铺优惠+运费+修改的金额（除运费外，均指分摊后的金额）
         */
        @JsonProperty("actualPayFee")
        private BigDecimal actualPayFee;

        /**
         * 本阶段分摊的店铺优惠
         */
        @JsonProperty("discountFee")
        private BigDecimal discountFee;

        /**
         * 本阶段分摊的单品优惠
         */
        @JsonProperty("itemDiscountFee")
        private BigDecimal itemDiscountFee;

        /**
         * 本阶段分摊的单价
         */
        @JsonProperty("price")
        private BigDecimal price;

        /**
         * 购买数量
         */
        @JsonProperty("amount")
        private Long amount;

        /**
         * 运费
         */
        @JsonProperty("postFee")
        private BigDecimal postFee;

        /**
         * 修改价格修改的金额
         */
        @JsonProperty("adjustFee")
        private BigDecimal adjustFee;

        /**
         * 创建时间
         */
        @JsonProperty("gmtCreate")
        @JsonDeserialize(using = AlibabaDateDeserializer.class)
        private LocalDateTime gmtCreate;

        /**
         * 修改时间
         */
        @JsonProperty("gmtModified")
        @JsonDeserialize(using = AlibabaDateDeserializer.class)
        private LocalDateTime gmtModified;

        /**
         * 开始时间
         */
        @JsonProperty("enterTime")
        @JsonDeserialize(using = AlibabaDateDeserializer.class)
        private LocalDateTime enterTime;

        /**
         * 卖家操作时间
         */
        @JsonProperty("sellerActionTime")
        @JsonDeserialize(using = AlibabaDateDeserializer.class)
        private LocalDateTime sellerActionTime;

        /**
         * 本阶段结束时间
         */
        @JsonProperty("endTime")
        @JsonDeserialize(using = AlibabaDateDeserializer.class)
        private LocalDateTime endTime;

        /**
         * 卖家操作留言路径
         */
        @JsonProperty("messagePath")
        private String messagePath;

        /**
         * 卖家上传图片凭据路径
         */
        @JsonProperty("picturePath")
        private String picturePath;

        /**
         * 卖家操作留言
         */
        @JsonProperty("message")
        private String message;

        /**
         * 使用的模板id
         */
        @JsonProperty("templateId")
        private Long templateId;

        /**
         * 阶段名称
         */
        @JsonProperty("stepName")
        private String stepName;

        /**
         * 卖家操作名称
         */
        @JsonProperty("sellerActionName")
        private String sellerActionName;

        /**
         * 买家不付款的超时时间(秒)
         */
        @JsonProperty("buyerPayTimeout")
        private Long buyerPayTimeout;

        /**
         * 买家不确认的超时时间
         */
        @JsonProperty("buyerConfirmTimeout")
        private Long buyerConfirmTimeout;

        /**
         * 是否需要卖家操作和买家确认
         */
        @JsonProperty("needSellerAction")
        private Boolean needSellerAction;

        /**
         * 阶段结束是否打款
         */
        @JsonProperty("transferAfterConfirm")
        private Boolean transferAfterConfirm;

        /**
         * 是否需要卖家推进
         */
        @JsonProperty("needSellerCallNext")
        private Boolean needSellerCallNext;

        /**
         * 是否允许即时到帐
         */
        @JsonProperty("instantPay")
        private Boolean instantPay;

        /**
         * 是否需要物流
         */
        @JsonProperty("needLogistics")
        private Boolean needLogistics;
    }

}