/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api;

import com.fulfillmen.support.alibaba.api.response.tools.LoginIdEncryptResponse;
import com.fulfillmen.support.alibaba.api.response.tools.PoolProductPullResponse;
import com.fulfillmen.support.alibaba.api.response.tools.RelationAddResponse;
import com.fulfillmen.support.alibaba.api.response.tools.WangwangNickDecryptResponse;
import com.fulfillmen.support.alibaba.api.response.tools.WangwangUrlResponse;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * 阿里巴巴工具类API
 * <p>
 * 包含以下接口： 1. 获取唤起旺旺聊天的链接 2. 拉取商品池中商品数据 3. 买卖家分销关系添加
 *
 * <AUTHOR>
 * @created 2025-01-16
 * @see <a href="https://open.1688.com/api/apidoclist.htm?tags=11">工具类API文档</a>
 */
@HttpExchange("/")
public interface ToolsAPI {

    /**
     * 获取唤起旺旺聊天的链接
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 旺旺聊天链接
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.account:account.wangwangUrl.get-1">API文档</a>
     */
    @PostExchange(ApiPaths.ToolsAPI.WANGWANG_URL)
    Mono<WangwangUrlResponse> getWangwangUrl(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 拉取商品池中商品数据
     * <p>
     * 通过商品池ID批量拉取池中商品数据，支持分页查询和排序。
     *
     * @param appKey 应用key
     * @param params 请求参数，包含： - offerPoolId: 商品池ID（必填） - cateId: 类目ID（可选） - taskId: 任务ID（必填） - language: 语言（可选） - pageNo: 页码（必填） - pageSize: 每页数量（必填） - sortField: 排序字段（可选） -
     *               sortType: 排序规则（可选）
     * @return 商品池数据
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:pool.product.pull-1">API文档</a>
     */
    @PostExchange(ApiPaths.ToolsAPI.POOL_PRODUCT_PULL)
    Mono<PoolProductPullResponse> pullPoolProducts(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 买卖家分销关系添加
     * <p>
     * 通过商品ID，添加买卖家分销关系。
     *
     * @param appKey 应用key
     * @param params 请求参数，包含： - offerId: 商品ID（必填）
     * @return 添加结果
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao:alibaba.fenxiao.relationadd-1">API文档</a>
     */
    @PostExchange(ApiPaths.ToolsAPI.RELATION_ADD)
    Mono<RelationAddResponse> addRelation(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 用户loginId加密转换为Openuid
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return Mono<LoginIdEncryptResponse> 响应
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.account:loginid.openuid.encrypt-1">API文档</a>
     */
    @PostExchange(ApiPaths.ToolsAPI.LOGIN_ID_ENCRYPT)
    Mono<LoginIdEncryptResponse> encryptLoginId(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * Openuid转换解密为旺旺昵称
     * <p>
     * 将待解密的openUid转换为用户唤起旺旺的旺旺昵称。 注意：该接口仅用于用户唤起旺旺，不允许用于其他用途。
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return Mono<WangwangNickDecryptResponse> 响应
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.account:wangwangnick.openuid.decrypt-1">API文档</a>
     */
    @PostExchange(ApiPaths.ToolsAPI.WANGWANG_NICK_DECRYPT)
    Mono<WangwangNickDecryptResponse> decryptWangwangNick(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);
}