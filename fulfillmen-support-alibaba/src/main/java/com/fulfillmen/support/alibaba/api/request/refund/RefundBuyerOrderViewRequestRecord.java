/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.refund;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;
import lombok.Getter;

/**
 * 根据订单ID查询退款单详情请求 该API为买家使用，卖家查询请使用alibaba.trade.refund.OpQueryBatchRefundByOrderIdAndStatus.sellerView 根据订单号实时查询退款单列表，目前只能查询到售中的退款单
 *
 * <AUTHOR>
 * @created 2025-01-13
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.OpQueryBatchRefundByOrderIdAndStatus-1">根据订单ID查询退款单详情</a>
 */
@Builder
public record RefundBuyerOrderViewRequestRecord(
    /**
     * 订单ID 示例值：2429989502226540788
     */
    String orderId,
    /**
     * 查询类型 1：活动 3：退款成功（只支持退款中和退款成功） 示例值：3
     */
    QueryType queryType
) implements BaseAlibabaRequestRecord {

    /**
     * 查询类型-活动
     */
    public static final String QUERY_TYPE_ACTIVITY = "1";

    /**
     * 查询类型-退款成功
     */
    public static final String QUERY_TYPE_REFUND_SUCCESS = "3";

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("orderId", orderId);
        params.put("queryType", queryType.getValue());
        return params;
    }

    @Override
    public void requireParams() {
        assertNotBlank(orderId, "orderId不能为空");
        assertNotNull(queryType, "queryType不能为空");
        // 验证queryType的值是否合法
        if (!(QUERY_TYPE_ACTIVITY.equals(queryType.getValue()) || QUERY_TYPE_REFUND_SUCCESS.equals(queryType
            .getValue()))) {
            throw new AlibabaServiceValidationException("queryType必须为1(活动)或3(退款成功)");
        }
    }

    /**
     * 根据订单ID查询退款单详情请求, 默认：查询类型为退款成功
     *
     * @param orderId 订单ID
     * @return 退款单详情请求
     */
    public static RefundBuyerOrderViewRequestRecord of(String orderId) {
        return new RefundBuyerOrderViewRequestRecord(orderId, QueryType.REFUND_SUCCESS);
    }

    @Getter
    public enum QueryType {

        /**
         * 活动
         */
        ACTIVITY("1"),
        /**
         * 退款成功
         */
        REFUND_SUCCESS("3");

        private final String value;

        QueryType(String value) {
            this.value = value;
        }

        /**
         * 根据值匹配枚举
         *
         * @param value 值
         * @return 枚举
         */
        public static QueryType of(String value) {
            return Arrays.stream(QueryType.values())
                .filter(type -> type.getValue().equals(value))
                .findFirst()
                .orElseThrow(() -> new AlibabaServiceValidationException("queryType必须为1(活动)或3(退款成功)"));
        }
    }

}
