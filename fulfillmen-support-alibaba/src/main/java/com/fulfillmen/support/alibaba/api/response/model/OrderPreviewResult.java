/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model;

import com.fulfillmen.support.alibaba.api.response.model.order.TradeModelExtension;
import com.fulfillmen.support.alibaba.api.response.model.order.TradePromotionModel;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeResultPayChannelInfo;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订单预览结果详情
 *
 * <AUTHOR>
 * @created 2025-01-09
 */
@Data
@NoArgsConstructor
public class OrderPreviewResult {

    /**
     * 计算完货品金额后再次进行的减免金额 单位: 分
     */
    private Long discountFee;

    /**
     * 当前交易在使用下单接口时可以支持的交易方式列表 其中的元素可以直接用于下单接口的tradeType入参 列表为空时，当前交易不可通过接口下单，需要在1688页面下单
     */
    private String[] tradeModeNameList;

    /**
     * 预览状态
     */
    private Boolean status;

    /**
     * 是否有淘货源单品优惠 false: 有单品优惠 true：没有单品优惠
     */
    private Boolean taoSampleSinglePromotion;

    /**
     * 订单总费用 单位: 分
     */
    private Long sumPayment;

    /**
     * 返回信息
     */
    private String message;

    /**
     * 总运费信息 单位: 分
     */
    private Long sumCarriage;

    /**
     * 返回码
     */
    private String resultCode;

    /**
     * 不包含运费的货品总费用 单位: 分
     */
    private Long sumPaymentNoCarriage;

    /**
     * 附加费 单位: 分
     */
    private Long additionalFee;

    /**
     * 订单下单流程标识
     */
    private String flowFlag;

    /**
     * 商品规格信息列表
     */
    private List<CreateOrderPreviewResultCargoModel> cargoList;

    /**
     * 可用店铺级别优惠列表
     */
    private List<TradePromotionModel> shopPromotionList;

    /**
     * 当前交易可以支持的交易方式列表 结果可以参照1688下单预览页面的交易方式
     */
    private List<TradeModelExtension> tradeModelList;

    /**
     * 当前交易支持的支付渠道信息
     */
    private List<TradeResultPayChannelInfo> payChannelInfos;
}