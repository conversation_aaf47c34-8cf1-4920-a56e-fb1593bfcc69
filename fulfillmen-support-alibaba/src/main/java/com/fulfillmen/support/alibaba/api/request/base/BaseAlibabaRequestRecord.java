/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.base;

import com.fulfillmen.starter.core.util.JacksonUtil;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import java.util.List;
import java.util.Map;
import org.springframework.util.Assert;

/**
 * 阿里巴巴请求基类
 *
 * <AUTHOR>
 * @created 2025-01-08
 */
public interface BaseAlibabaRequestRecord {

    /**
     * 转换为请求参数
     *
     * @return 请求参数Map
     */
    Map<String, String> toParams();

    /**
     * 参数校验
     *
     * @throws AlibabaServiceValidationException 当参数校验失败时抛出
     */
    void requireParams() throws AlibabaServiceValidationException;

    /**
     * 将对象转换为JSON字符串
     *
     * @param obj 要转换的对象
     * @return JSON字符串
     */
    default String toJsonString(Object obj) {
        return JacksonUtil.toJsonString(obj);
    }

    /**
     * 断言非空字符串
     *
     * @param value   值
     * @param message 错误消息
     * @throws AlibabaServiceValidationException 当校验失败时抛出
     */
    default void assertNotBlank(String value, String message) {
        try {
            Assert.hasText(value, message);
        } catch (IllegalArgumentException e) {
            throw new AlibabaServiceValidationException(message);
        }
    }

    /**
     * 断言不为空字符串
     *
     * @param value   值
     * @param message 错误消息
     * @throws AlibabaServiceValidationException 当校验失败时抛出
     */
    default void assertNotNull(Object value, String message) {
        try {
            Assert.notNull(value, message);
        } catch (IllegalArgumentException e) {
            throw new AlibabaServiceValidationException(message);
        }
    }

    /**
     * 断言列表不为空
     *
     * @param list    列表
     * @param message 错误消息
     * @throws AlibabaServiceValidationException 当校验失败时抛出
     */
    default <T> void assertNotEmpty(List<T> list, String message) {
        try {
            Assert.notEmpty(list, message);
        } catch (IllegalArgumentException e) {
            throw new AlibabaServiceValidationException(message);
        }
    }

    /**
     * 断言为真
     *
     * @param value   值
     * @param message 错误消息
     * @throws AlibabaServiceValidationException 当校验失败时抛出
     */
    default void assertTrue(boolean value, String message) {
        try {
            Assert.isTrue(value, message);
        } catch (IllegalArgumentException e) {
            throw new AlibabaServiceValidationException(message);
        }
    }

}