/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service.impl;

import com.fulfillmen.support.alibaba.api.ApiPaths;
import com.fulfillmen.support.alibaba.api.BusinessAPI;
import com.fulfillmen.support.alibaba.api.request.business.RankQueryRequestRecord;
import com.fulfillmen.support.alibaba.api.request.business.SellTrendRequestRecord;
import com.fulfillmen.support.alibaba.api.request.business.TopKeywordRequestRecord;
import com.fulfillmen.support.alibaba.api.response.business.RankQueryResponse;
import com.fulfillmen.support.alibaba.api.response.business.SellTrendResponse;
import com.fulfillmen.support.alibaba.api.response.business.TopKeywordResponse;
import com.fulfillmen.support.alibaba.autoconfigure.AlibabaProperties;
import com.fulfillmen.support.alibaba.service.IBusinessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 阿里巴巴商机服务实现类
 * <p>
 * 提供榜单查询等商机服务的具体实现。 所有方法都遵循响应式编程模式，使用 Project Reactor 进行异步处理。
 *
 * <AUTHOR>
 * @created 2025-01-23
 */
@Slf4j
@Service
public class BusinessServiceImpl extends BaseAlibabaServiceImpl implements IBusinessService {

    private static final String SERVICE_NAME = "商机服务";
    private static final String LOG_ITEM = "[商机服务]";
    private final BusinessAPI businessAPI;

    public BusinessServiceImpl(BusinessAPI businessAPI,
        AlibabaProperties alibabaProperties) {
        super(alibabaProperties);
        this.businessAPI = businessAPI;
    }

    @Override
    protected String getServiceName() {
        return SERVICE_NAME;
    }

    @Override
    public Mono<RankQueryResponse> queryRankList(RankQueryRequestRecord request) {
        log.debug("{} 开始处理榜单查询请求: rankId={}, rankType={}, limit={}, language={}", LOG_ITEM, request.rankId(), request
            .rankType(), request.limit(), request.language());

        return wrapWithErrorHandler("榜单查询", request, ApiPaths.BusinessAPI.RANK_QUERY, formParams -> businessAPI
            .queryRankList(appKey, formParams));
    }

    @Override
    public Mono<TopKeywordResponse> getTopKeywords(TopKeywordRequestRecord request) {
        log.debug("{} 开始处理热搜词查询请求: country={}, sourceId={}, hotKeywordType={}", LOG_ITEM, request.country(), request
            .sourceId(), request.hotKeywordType());

        return wrapWithErrorHandler("热搜词查询", request, ApiPaths.BusinessAPI.TOP_KEYWORD, formParams -> businessAPI
            .getTopKeywords(appKey, formParams));
    }

    @Override
    public Mono<SellTrendResponse> getSellTrend(SellTrendRequestRecord request) {
        log.debug("{} 开始处理销售趋势查询请求: offerId={}, startDate={}, endDate={}", LOG_ITEM, request.offerId(), request
            .startDate(), request.endDate());

        return wrapWithErrorHandler("销售趋势查询", request, ApiPaths.BusinessAPI.SELL_TREND, formParams -> businessAPI
            .getSellTrend(appKey, formParams));
    }
}