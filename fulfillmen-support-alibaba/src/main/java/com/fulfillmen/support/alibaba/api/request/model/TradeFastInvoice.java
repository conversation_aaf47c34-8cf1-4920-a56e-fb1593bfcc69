/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 1688交易快速创建订单发票信息
 *
 * <AUTHOR>
 * @created 2025-01-09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradeFastInvoice {

    /**
     * 发票类型 0：普通发票 1：增值税发票
     */
    private Integer invoiceType;

    /**
     * 省份名称
     */
    private String provinceText;

    /**
     * 城市名称
     */
    private String cityText;

    /**
     * 区域名称
     */
    private String areaText;

    /**
     * 镇名称
     */
    private String townText;

    /**
     * 邮政编码
     */
    private String postCode;

    /**
     * 街道地址
     */
    private String address;

    /**
     * 收票人姓名
     */
    private String fullName;

    /**
     * 电话号码
     */
    private String phone;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 购货公司名（发票抬头）
     */
    private String companyName;

    /**
     * 纳税识别码
     */
    private String taxpayerIdentifier;

    /**
     * 开户行及帐号
     */
    private String bankAndAccount;

    /**
     * 增值税本地发票号
     */
    private String localInvoiceId;
}