/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service;

import com.fulfillmen.support.alibaba.api.request.logistics.AddressCodeParseRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsCompanyListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsFreightTemplateRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsInfoRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsInsuranceRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsOutOrderIdRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsReceiveAddressRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsTraceRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.ProductFreightEstimateRequestRecord;
import com.fulfillmen.support.alibaba.api.response.logistics.AddressCodeParseResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsCompanyListResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsFreightTemplateResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsInfoResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsInsuranceResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsOutOrderIdResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsReceiveAddressResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsTraceResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.ProductFreightEstimateResponse;
import reactor.core.publisher.Mono;

/**
 * 1688物流服务接口
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
public interface ILogisticsService {

    /**
     * 获取物流模板详情 根据物流模板id获取买家承担的物流模板，运费模板ID为0表示运费说明说明，为1表示买家承担运费。
     *
     * @param request 获取物流模板详情请求
     * @return 物流模板详情响应
     */
    Mono<LogisticsFreightTemplateResponse> getFreightTemplate(LogisticsFreightTemplateRequestRecord request);

    /**
     * 根据地址解析地区码 根据地址信息解析出地区编码等信息。
     *
     * @param request 地址解析请求
     * @return 地址解析响应
     */
    Mono<AddressCodeParseResponse> parseAddressCode(AddressCodeParseRequestRecord request);

    /**
     * 根据运单号或无主件码查询外部订单ID
     *
     * @param request 查询外部订单ID请求
     * @return 外部订单ID响应
     */
    Mono<LogisticsOutOrderIdResponse> getOutOrderId(LogisticsOutOrderIdRequestRecord request);

    /**
     * 获取交易订单的物流信息(买家视角)
     *
     * @param request 获取物流信息请求
     * @return 物流信息响应
     */
    Mono<LogisticsInfoResponse> getLogisticsInfo(LogisticsInfoRequestRecord request);

    /**
     * 获取交易订单的物流跟踪信息(买家视角)
     *
     * @param request 获取物流跟踪信息请求
     * @return 物流跟踪信息响应
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:alibaba.trade.getLogisticsTraceInfo.buyerView-1">API文档</a>
     */
    Mono<LogisticsTraceResponse> getLogisticsTrace(LogisticsTraceRequestRecord request);

    /**
     * 买家获取保存的收货地址信息列表
     *
     * @param request 请求参数
     * @return 收货地址列表响应
     */
    Mono<LogisticsReceiveAddressResponse> getReceiveAddress(LogisticsReceiveAddressRequestRecord request);

    /**
     * 商品中国国内运费预估 根据商品ID、中国国内收货地址的省市区编码，预估商品的运费。
     *
     * @param request 运费预估请求
     * @return 运费预估响应
     */
    Mono<ProductFreightEstimateResponse> estimateFreight(ProductFreightEstimateRequestRecord request);

    /**
     * 获取所有的物流公司列表
     *
     * @param request 获取物流公司列表请求
     * @return 物流公司列表响应
     */
    Mono<LogisticsCompanyListResponse> getLogisticsCompanyList(LogisticsCompanyListRequestRecord request);

    /**
     * 运费险信息查询
     *
     * @param request 运费险信息查询请求
     * @return 运费险信息响应
     */
    Mono<LogisticsInsuranceResponse> getShippingInsurance(LogisticsInsuranceRequestRecord request);
}