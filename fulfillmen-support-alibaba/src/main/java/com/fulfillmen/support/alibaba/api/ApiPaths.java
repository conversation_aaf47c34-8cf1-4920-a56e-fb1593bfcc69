/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api;

/**
 * API 路径常量
 */
public interface ApiPaths {

    interface OrderAPI {

        /**
         * 取消订单
         */
        String CANCEL_ORDER = "param2/1/com.alibaba.trade/alibaba.trade.cancel/{APPKEY}";

        /**
         * 预览订单
         */
        String PREVIEW_ORDER = "param2/1/com.alibaba.trade/alibaba.createOrder.preview/{APPKEY}";

        /**
         * 获取订单详情
         */
        String GET_ORDER_DETAIL = "param2/1/com.alibaba.trade/alibaba.trade.get.buyerView/{APPKEY}";

        /**
         * 获取订单列表(买家视角)
         */
        String GET_BUYER_ORDER_LIST = "param2/1/com.alibaba.trade/alibaba.trade.getBuyerOrderList/{APPKEY}";

        /**
         * 买家补充订单留言
         */
        String ADD_FEEDBACK = "param2/1/com.alibaba.trade/alibaba.trade.addFeedback/{APPKEY}";

        /**
         * 跨境订单创建
         */
        String CREATE_CROSS_ORDER = "param2/1/com.alibaba.trade/alibaba.trade.createCrossOrder/{APPKEY}";
    }

    /**
     * API 路径常量
     */
    interface GoodsAPI {

        /**
         * 多语言关键词搜索
         */
        String SEARCH_GOODS = "param2/1/com.alibaba.fenxiao.crossborder/product.search.keywordQuery/{APPKEY}";
        /**
         * 跨境场景图片搜索
         */
        String SEARCH_GOODS_BY_IMAGE = "param2/1/com.alibaba.fenxiao.crossborder/product.search.imageQuery/{APPKEY}";
        /**
         * 上传图片获取imageId
         */
        String UPLOAD_IMAGE = "param2/1/com.alibaba.fenxiao.crossborder/product.image.upload/{APPKEY}";
        /**
         * 获取多语言商品详情
         */
        String GET_GOODS_DETAIL = "param2/1/com.alibaba.fenxiao.crossborder/product.search.queryProductDetail/{APPKEY}";
        /**
         * 获取多语言商品店铺列表
         */
        String GET_SELLER_GOODS = "param2/1/com.alibaba.fenxiao.crossborder/product.search.querySellerOfferList/{APPKEY}";
        /**
         * 商品推荐
         */
        String RECOMMEND_GOODS = "param2/1/com.alibaba.fenxiao.crossborder/product.search.offerRecommend/{APPKEY}";
        /**
         * 多语言搜索导航
         */
        String KEYWORD_NAVIGATION = "param2/1/com.alibaba.fenxiao.crossborder/product.search.keywordSNQuery/{APPKEY}";
        /**
         * 相关性商品推荐
         */
        String GET_RELATED_RECOMMEND = "param2/1/com.alibaba.fenxiao.crossborder/product.related.recommend/{APPKEY}";
        /**
         * 商品优惠券领取
         *
         * @see https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.marketing%3Acoupon.optimal.claim-1&aopApiCategory=category_new
         */
        String CLAIM_COUPON = "param2/1/com.alibaba.marketing/coupon.optimal.claim/{APPKEY}";

    }

    interface CategoryAPI {

        /**
         * 获取类目信息
         */
        String GET_CATEGORY = "param2/1/com.alibaba.product/alibaba.category.get/{APPKEY}";

        /**
         * 根据类目ID获取多语言
         */
        String GET_TRANSLATION_BY_ID = "param2/1/com.alibaba.fenxiao.crossborder/category.translation.getById/{APPKEY}";

        /**
         * 根据类目名称获取翻译
         */
        String GET_TRANSLATION = "param2/1/com.alibaba.fenxiao.crossborder/category.translation.getByKeyword/{APPKEY}";

        /**
         * 获取类目属性
         */
        String GET_ATTRIBUTES = "param2/1/com.alibaba.product/alibaba.category.attribute.get/{APPKEY}";
    }

    /**
     * 支付相关API路径
     */
    interface PayAPI {

        /**
         * 查询订单可用支付方式
         */
        String QUERY_PAY_WAY = "param2/1/com.alibaba.trade/alibaba.trade.payWay.query/{APPKEY}";

        /**
         * 获取诚e赊支付链接
         */
        String GET_CREDIT_PAY_URL = "param2/1/com.alibaba.trade/alibaba.creditPay.url.get/{APPKEY}";

        /**
         * 获取跨境宝支付链接
         */
        String GET_CROSS_BORDER_PAY_URL = "param2/1/com.alibaba.trade/alibaba.crossBorderPay.url.get/{APPKEY}";

        /**
         * 查询是否开通免密支付
         */
        String CHECK_PROTOCOL_PAY = "param2/1/com.alibaba.trade/alibaba.trade.pay.protocolPay.isopen/{APPKEY}";

        /**
         * 获取支付宝支付链接
         */
        String GET_ALIPAY_URL = "param2/1/com.alibaba.trade/alibaba.alipay.url.get/{APPKEY}";

        /**
         * 查询买家信用账期信息
         */
        String GET_ACCOUNT_PERIOD_LIST = "param2/1/com.alibaba.trade/alibaba.accountPeriod.list.buyerView/{APPKEY}";

        /**
         * 发起免密支付
         */
        String PREPARE_PROTOCOL_PAY = "param2/1/com.alibaba.trade/alibaba.trade.pay.protocolPay.preparePay/{APPKEY}";
    }

    /**
     * 退款相关API路径
     */
    interface RefundAPI {

        /**
         * 取消退款退货申请
         */
        String CANCEL_REFUND = "param2/1/com.alibaba.trade/alibaba.trade.cancelRefund/{APPKEY}";

        /**
         * 查询退款单列表(买家视角)
         */
        String BUYER_LIST = "param2/1/com.alibaba.trade/alibaba.trade.refund.buyer.queryOrderRefundList/{APPKEY}";

        /**
         * 买家提交退款货信息
         */
        String BUYER_SUBMIT = "param2/1/com.alibaba.trade/alibaba.trade.refund.returnGoods/{APPKEY}";

        /**
         * 上传退款退货凭证
         */
        String UPLOAD_EVIDENCE = "param2/1/com.alibaba.trade/alibaba.trade.uploadRefundVoucher/{APPKEY}";

        /**
         * 查询退款退货原因
         */
        String REASON_LIST = "param2/1/com.alibaba.trade/alibaba.trade.getRefundReasonList/{APPKEY}";

        /**
         * 创建退款退货申请
         */
        String CREATE = "param2/1/com.alibaba.trade/alibaba.trade.createRefund/{APPKEY}";

        /**
         * 查询退款单详情(根据退款单ID)
         */
        String BUYER_VIEW = "param2/1/com.alibaba.trade/alibaba.trade.refund.OpQueryOrderRefund/{APPKEY}";

        /**
         * 退款单操作记录列表
         */
        String OP_LOG_LIST = "param2/1/com.alibaba.trade/alibaba.trade.refund.OpQueryOrderRefundOperationList/{APPKEY}";

        /**
         * 查询退款单详情(根据订单ID)
         */
        String BUYER_ORDER_VIEW = "param2/1/com.alibaba.trade/alibaba.trade.refund.OpQueryBatchRefundByOrderIdAndStatus/{APPKEY}";
    }

    /**
     * 会员相关API路径
     */
    interface MemberAPI {

        /**
         * 1688会员注册
         */
        String REGISTER = "param2/1/com.alibaba.fenxiao.crossborder/account.user.register/{APPKEY}";

        /**
         * 批量添加子账号授权
         */
        String SUB_ACCOUNT_AUTH_ADD = "param2/1/system.oauth2/subaccount.auth.add/{APPKEY}";

        /**
         * 批量取消子账号授权
         */
        String SUB_ACCOUNT_AUTH_CANCEL = "param2/1/system.oauth2/subaccount.auth.cancel/{APPKEY}";

        /**
         * 批量查询子账号授权
         */
        String SUB_ACCOUNT_AUTH_LIST = "param2/1/system.oauth2/subaccount.auth.list/{APPKEY}";
    }

    /**
     * 物流相关API路径
     */
    interface LogisticsAPI {

        /**
         * 获取物流模板详情
         */
        String GET_FREIGHT_TEMPLATE = "param2/1/com.alibaba.logistics/alibaba.logistics.myFreightTemplate.list.get/{APPKEY}";

        /**
         * 根据地址解析地区码
         */
        String ADDRESS_CODE_PARSE = "param2/1/com.alibaba.trade/alibaba.trade.addresscode.parse/{APPKEY}";

        /**
         * 根据运单号或无主件码查询外部订单ID
         */
        String GET_OUT_ORDER_ID = "param2/1/com.alibaba.fenxiao.crossborder/logistics.order.getOutOrderId/{APPKEY}";

        /**
         * 获取交易订单的物流信息(买家视角)
         */
        String GET_LOGISTICS_INFO = "param2/1/com.alibaba.logistics/alibaba.trade.getLogisticsInfos.buyerView/{APPKEY}";

        /**
         * 获取交易订单的物流跟踪信息(买家视角)
         */
        String GET_LOGISTICS_TRACE = "param2/1/com.alibaba.logistics/alibaba.trade.getLogisticsTraceInfo.buyerView/{APPKEY}";

        /**
         * 买家获取保存的收货地址信息列表
         */
        String GET_RECEIVE_ADDRESS = "param2/1/com.alibaba.trade/alibaba.trade.receiveAddress.get/{APPKEY}";

        /**
         * 商品中国国内运费预估
         */
        String FREIGHT_ESTIMATE = "param2/1/com.alibaba.fenxiao.crossborder/product.freight.estimate/{APPKEY}";

        /**
         * 获取所有的物流公司列表
         */
        String GET_LOGISTICS_COMPANY_LIST = "param2/1/com.alibaba.logistics/alibaba.logistics.OpQueryLogisticCompanyList/{APPKEY}";

        /**
         * 运费险信息查询
         */
        String GET_SHIPPING_INSURANCE = "param2/1/com.alibaba.trade/shipping.insurance.get/{APPKEY}";
    }

    /**
     * 消息管理API路径 提供消息查询等功能
     *
     * @since 1.0.0
     */
    interface MessageAPI {

        /**
         * 查询式获取失败的消息列表 获取的消息不会自动确认，需要调用方手动调用确认API来确认消息状态
         */
        String QUERY_FAILED_MESSAGE_LIST = "param2/1/cn.alibaba.open/push.query.messageList/{APPKEY}";

        /**
         * 失败消息批量确认
         */
        String CONFIRM_FAILED_MESSAGE = "param2/1/cn.alibaba.open/push.message.confirm/{APPKEY}";

        /**
         * 游标式获取失败的消息列表
         */
        String MESSAGE_CURSOR_LIST = "param2/1/cn.alibaba.open/push.cursor.messageList/{APPKEY}";
    }

    /**
     * 回传数据相关API路径
     */
    interface FeedbackAPI {

        /**
         * 保存账号所属业务线
         */
        String ACCOUNT_BUSINESS_SAVE = "param2/1/com.alibaba.fenxiao.crossborder/account.business.save/{APPKEY}";

        /**
         * 国家站物流单回传
         */
        String LOGISTICS_ORDER_SYNC = "param2/1/com.alibaba.fenxiao.crossborder/trade.cross.logisticsOrderSync/{APPKEY}";

        /**
         * 回传机构真实用户订单和1688订单的映射关系
         */
        String ORDER_RELATION_WRITE = "param2/1/com.alibaba.fenxiao.crossborder/order.relation.write/{APPKEY}";

        /**
         * 下游销售订单同步
         */
        String ORDER_SYNC = "param2/1/com.alibaba.fenxiao.crossborder/trade.cross.orderSync/{APPKEY}";
    }

    /**
     * AI能力相关API路径
     */
    interface AiCapabilityAPI {

        /**
         * 商品文本翻译
         */
        String PRODUCT_TEXT_TRANSLATE = "param2/1/com.alibaba.fenxiao.crossborder/product.text.translate/{APPKEY}";

        /**
         * 商品标题生成
         */
        String PRODUCT_TITLE_GENERATE = "param2/1/com.alibaba.fenxiao.crossborder/text.generate.title/{APPKEY}";

        /**
         * 商品详描生成
         */
        String PRODUCT_DESC_GENERATE = "param2/1/com.alibaba.fenxiao.crossborder/text.generate.description/{APPKEY}";

        /**
         * 图片翻译
         */
        String IMAGE_TRANSLATE = "param2/1/com.alibaba.fenxiao.crossborder/image.elements.translate/{APPKEY}";

        /**
         * 图片裁剪
         */
        String IMAGE_CUT = "param2/1/com.alibaba.fenxiao.crossborder/image.elements.cut/{APPKEY}";

        /**
         * 图片高清放大
         */
        String IMAGE_ENLARGE = "param2/1/com.alibaba.fenxiao.crossborder/image.elements.enlarge/{APPKEY}";

        /**
         * 图片智能抠图
         */
        String IMAGE_MATTING = "param2/1/com.alibaba.fenxiao.crossborder/image.elements.matting/{APPKEY}";

        /**
         * 图片智能消除
         */
        String IMAGE_REMOVE = "param2/1/com.alibaba.fenxiao.crossborder/image.elements.remove/{APPKEY}";

        /**
         * 图像元素识别
         */
        String IMAGE_ELEMENTS_RECOGNITION = "param2/1/com.alibaba.fenxiao.crossborder/image.elements.recognition/{APPKEY}";
    }

    /**
     * 工具类API路径
     */
    interface ToolsAPI {

        /**
         * 获取唤起旺旺聊天的链接
         */
        String WANGWANG_URL = "param2/1/com.alibaba.account/account.wangwangUrl.get/{APPKEY}";

        /**
         * 拉取商品池中商品数据
         */
        String POOL_PRODUCT_PULL = "param2/1/com.alibaba.fenxiao.crossborder/pool.product.pull/{APPKEY}";

        /**
         * 买卖家分销关系添加
         */
        String RELATION_ADD = "param2/1/com.alibaba.fenxiao/alibaba.fenxiao.relationadd/{APPKEY}";

        /**
         * 用户loginId加密转换为Openuid接口
         */
        String LOGIN_ID_ENCRYPT = "param2/1/com.alibaba.account/loginid.openuid.encrypt/{APPKEY}";

        /**
         * Openuid转换解密为旺旺昵称接口
         */
        String WANGWANG_NICK_DECRYPT = "param2/1/com.alibaba.account/wangwangnick.openuid.decrypt/{APPKEY}";
    }

    /**
     * 商机API路径
     */
    interface BusinessAPI {

        /**
         * 查询榜单列表
         */
        String RANK_QUERY = "param2/1/com.alibaba.fenxiao.crossborder/product.topList.query/{APPKEY}";

        /**
         * 商品热搜词
         */
        String TOP_KEYWORD = "param2/1/com.alibaba.fenxiao.crossborder/product.search.topKeyword/{APPKEY}";

        /**
         * 商品每日销售数量趋势
         */
        String SELL_TREND = "param2/1/com.alibaba.fenxiao.crossborder/product.analyze.getPerdaySellQuantityTrendNew/{APPKEY}";
    }

}