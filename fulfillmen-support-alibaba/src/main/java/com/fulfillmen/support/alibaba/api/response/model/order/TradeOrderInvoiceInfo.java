/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 订单发票信息
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Data
public class TradeOrderInvoiceInfo {

    /**
     * 发票公司名称(即发票抬头-title)
     */
    @JsonProperty("invoiceCompanyName")
    private String invoiceCompanyName;

    /**
     * 发票类型.
     * <pre>
     * 0：普通发票，1:增值税发票，9未知类型
     * </pre>
     */
    @JsonProperty("invoiceType")
    private Integer invoiceType;

    /**
     * 本地发票号
     */
    @JsonProperty("localInvoiceId")
    private Long localInvoiceId;

    /**
     * 订单Id
     */
    @JsonProperty("orderId")
    private Long orderId;

    /**
     * (收件人)址区域编码
     */
    @JsonProperty("receiveCode")
    private String receiveCode;

    /**
     * (收件人) 省市区编码对应的文案(增值税发票信息)
     */
    @JsonProperty("receiveCodeText")
    private String receiveCodeText;

    /**
     * （收件者）发票收货人手机
     */
    @JsonProperty("receiveMobile")
    private String receiveMobile;

    /**
     * （收件者）发票收货人
     */
    @JsonProperty("receiveName")
    private String receiveName;

    /**
     * （收件者）发票收货人电话
     */
    @JsonProperty("receivePhone")
    private String receivePhone;

    /**
     * （收件者）发票收货地址邮编
     */
    @JsonProperty("receivePost")
    private String receivePost;

    /**
     * (收件人) 街道地址(增值税发票信息)
     */
    @JsonProperty("receiveStreet")
    private String receiveStreet;

    /**
     * (公司)银行账号
     */
    @JsonProperty("registerAccountId")
    private String registerAccountId;

    /**
     * (公司)开户银行
     */
    @JsonProperty("registerBank")
    private String registerBank;

    /**
     * (注册)省市区编码
     */
    @JsonProperty("registerCode")
    private String registerCode;

    /**
     * (注册)省市区文本
     */
    @JsonProperty("registerCodeText")
    private String registerCodeText;

    /**
     * （公司）注册电话
     */
    @JsonProperty("registerPhone")
    private String registerPhone;

    /**
     * (注册)街道地址
     */
    @JsonProperty("registerStreet")
    private String registerStreet;

    /**
     * 纳税人识别号
     */
    @JsonProperty("taxpayerIdentify")
    private String taxpayerIdentify;
}