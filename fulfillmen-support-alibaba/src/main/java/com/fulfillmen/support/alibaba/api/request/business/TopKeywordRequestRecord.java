/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.business;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 商品热搜词请求记录
 *
 * @param country        语言，参考开发参考枚举
 * @param sourceId       查询id，如类目id
 * @param hotKeywordType 热搜类型，目前只提供类目维度
 * <AUTHOR>
 * @created 2025-01-23
 */
@Builder
public record TopKeywordRequestRecord(
    @JsonProperty("country") String country,
    @JsonProperty("sourceId") String sourceId,
    @JsonProperty("hotKeywordType") String hotKeywordType
) implements BaseAlibabaRequestRecord {

    @Override
    public void requireParams() {
        assertNotBlank(country, "语言不能为空");
        assertNotBlank(sourceId, "查询ID不能为空");
        assertNotBlank(hotKeywordType, "热搜类型不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        Map<String, Object> topSeKeywordParam = new HashMap<>();
        topSeKeywordParam.put("country", country);
        topSeKeywordParam.put("sourceId", sourceId);
        topSeKeywordParam.put("hotKeywordType", hotKeywordType);

        params.put("topSeKeywordParam", toJsonString(topSeKeywordParam));
        return params;
    }
}