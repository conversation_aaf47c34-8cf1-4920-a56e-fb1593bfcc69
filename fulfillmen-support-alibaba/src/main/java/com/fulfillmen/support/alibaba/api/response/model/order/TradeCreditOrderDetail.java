/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 诚e赊支付详情
 * <p>
 * 仅在订单使用了诚e赊支付时返回
 *
 * <AUTHOR>
 * @created 2025-01-10
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.get.buyerView-1">API文档</a>
 */
@Data
public class TradeCreditOrderDetail {

    /**
     * 订单金额
     */
    @JsonProperty("payAmount")
    private Long payAmount;

    /**
     * 支付时间
     */
    @JsonProperty("createTime")
    private String createTime;

    /**
     * 状态
     */
    @JsonProperty("status")
    private String status;

    /**
     * 不再建议使用
     */
    @JsonProperty("gracePeriodEndTime")
    private String gracePeriodEndTime;

    /**
     * 状态描述
     */
    @JsonProperty("statusStr")
    private String statusStr;

    /**
     * 应还金额
     */
    @JsonProperty("restRepayAmount")
    private Long restRepayAmount;

    /**
     * 最晚还款时间
     */
    @JsonProperty("lastRepayTime")
    private String lastRepayTime;

    /**
     * 还款来源
     * <p>
     * 还款来源
     * <ul>
     * <li>KJPAY-跨境宝还款</li>
     * <li>OWN_FUNDS-自有资金还款</li>
     * <li>INSTALLMENT_REPAY-分期、贷款付还款</li>
     * </ul>
     */
    @JsonProperty("repaySource")
    private String repaySource;
}