/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.order;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import com.fulfillmen.support.alibaba.api.request.model.TradeFastAddress;
import com.fulfillmen.support.alibaba.api.request.model.TradeFastCargo;
import com.fulfillmen.support.alibaba.api.request.model.TradeFastCreateOrderEncryptOutOrderInfo;
import com.fulfillmen.support.alibaba.api.request.model.TradeFastInvoice;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import org.springframework.util.CollectionUtils;

/**
 * 预览订单请求
 *
 * <pre>
 * 订单创建只允许购买同一个供应商的商品。本接口返回创建订单相关的优惠等信息。
 * 1、校验商品数据是否允许订购。
 * 2、校验代销关系。
 * 3、校验库存、起批量、是否满足混批条件。
 * </pre>
 *
 * <AUTHOR>
 * @created 2025-01-10
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.createOrder.preview-1">API文档</a>
 */
@Builder
public record OrderPreviewRequestRecord(
    /**
     * 收货地址信息
     */
    TradeFastAddress addressParam,

    /**
     * 商品信息
     */
    List<TradeFastCargo> cargoParamList,

    /**
     * 发票信息
     */
    TradeFastInvoice invoiceParam,

    /**
     * <pre>
     * general（创建大市场订单），
     * fenxiao（创建分销订单）,
     * saleproxy流程将校验分销关系,
     * paired(火拼下单),
     * boutiquefenxiao(精选货源分销价下单，采购量1个使用包邮)，
     * boutiquepifa(精选货源批发价下单，采购量大于2使用).
     * flow如果为空的情况，会比价择优预览，并返回最优下单方式flow
     * </pre>
     */
    String flow,

    /**
     * 批发团instanceId,从alibaba.pifatuan.product.list获取
     */
    String instanceId,

    /**
     * 下游加密订单信息，用于下游打单使用
     */
    TradeFastCreateOrderEncryptOutOrderInfo encryptOutOrderInfo,

    /**
     * 分账普通下单采购单id，交易flow为"proxy"
     */
    String proxySettleRecordId,

    /**
     * 库存模式，jit（jit模式）或 cang（仓发模式）,目前只提供给AE使用
     */
    String inventoryMode,

    /**
     * 外部订单号
     */
    String outOrderId,

    /**
     * 上门揽收,目前AE供货可用，其他场景暂不开通 y或n,默认为n
     */
    String pickupService
) implements BaseAlibabaRequestRecord {

    /**
     * 创建 OrderPreviewRequestRecord 实例
     *
     * @param addressParam   收货地址信息
     * @param cargoParamList 商品信息
     * @return OrderPreviewRequestRecord 实例
     */
    public static OrderPreviewRequestRecord of(TradeFastAddress addressParam, List<TradeFastCargo> cargoParamList) {
        return new OrderPreviewRequestRecord(addressParam, cargoParamList, null, null, null, null, null, null, null, null);
    }

    /**
     * 创建 OrderPreviewRequestRecord 实例
     *
     * @param addressParam   收货地址信息
     * @param cargoParamList 商品信息
     * @param invoiceParam   发票信息
     * @return OrderPreviewRequestRecord 实例
     */
    public static OrderPreviewRequestRecord of(TradeFastAddress addressParam,
        List<TradeFastCargo> cargoParamList,
        TradeFastInvoice invoiceParam) {
        return new OrderPreviewRequestRecord(addressParam, cargoParamList, invoiceParam, null, null, null, null, null, null, null);
    }

    /**
     * 创建 OrderPreviewRequestRecord 实例
     *
     * @param addressParam   收货地址信息
     * @param cargoParamList 商品信息
     * @param flow           流程类型
     * @return OrderPreviewRequestRecord 实例
     */
    public static OrderPreviewRequestRecord of(TradeFastAddress addressParam,
        List<TradeFastCargo> cargoParamList,
        String flow) {
        return new OrderPreviewRequestRecord(addressParam, cargoParamList, null, flow, null, null, null, null, null, null);
    }

    @Override
    public void requireParams() {
        // 校验收货地址信息
        assertNotNull(addressParam, "收货地址信息不能为空");
        if (addressParam != null) {
            assertNotBlank(addressParam.getFullName(), "收货人姓名不能为空");
            assertNotBlank(addressParam.getMobile(), "收货人手机号不能为空");
            assertNotBlank(addressParam.getDistrictCode(), "收货地址区域编码不能为空");
            assertNotBlank(addressParam.getAddress(), "收货详细地址不能为空");
        }

        // 校验发票信息（如果有）
        if (invoiceParam != null) {
            assertNotNull(invoiceParam.getInvoiceType(), "发票类型不能为空");
            assertNotBlank(invoiceParam.getCompanyName(), "购货公司名不能为空");
            assertNotBlank(invoiceParam.getTaxpayerIdentifier(), "纳税识别码不能为空");
        }

        // 校验商品信息列表
        assertNotNull(cargoParamList, "商品信息列表不能为空");
        if (CollectionUtils.isEmpty(cargoParamList)) {
            throw new AlibabaServiceValidationException("商品信息列表不能为空");
        }

        // 校验商品信息
        assertNotNull(cargoParamList, "商品信息不能为空");
        if (!cargoParamList.isEmpty()) {
            for (TradeFastCargo cargo : cargoParamList) {
                assertNotNull(cargo.getOfferId(), "商品ID不能为空");
                assertNotNull(cargo.getQuantity(), "商品数量不能为空");
//                assertNotNull(cargo.getSpecId(), "商品规格ID不能为空");
            }
        }
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();

        // 添加收货地址信息
        params.put("addressParam", toJsonString(addressParam));

        // 添加商品信息
        params.put("cargoParamList", toJsonString(cargoParamList));

        // 添加发票信息
        if (invoiceParam != null) {
            params.put("invoiceParam", invoiceParam.toString());
        }

        // 添加其他非空参数
        if (flow != null) {
            params.put("flow", flow);
        }
        if (instanceId != null) {
            params.put("instanceId", instanceId);
        }
        if (encryptOutOrderInfo != null) {
            params.put("encryptOutOrderInfo", encryptOutOrderInfo.toString());
        }
        if (proxySettleRecordId != null) {
            params.put("proxySettleRecordId", proxySettleRecordId);
        }
        if (inventoryMode != null) {
            params.put("inventoryMode", inventoryMode);
        }
        if (outOrderId != null) {
            params.put("outOrderId", outOrderId);
        }
        if (pickupService != null) {
            params.put("pickupService", pickupService);
        }

        return params;
    }
}