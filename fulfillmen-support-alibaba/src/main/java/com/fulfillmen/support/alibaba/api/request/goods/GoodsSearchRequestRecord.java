/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.goods;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import com.fulfillmen.support.alibaba.enums.LanguageEnum;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品搜索请求参数
 * <pre>
 * 2025年06月28日23:49:14 add SaleFilterList filed
 * </pre>
 * 
 * <AUTHOR>
 * @created 2025-01-17
 */
@Builder
public record GoodsSearchRequestRecord(
    /**
     * 搜索关键词(选填)
     */
    @JsonProperty("keyword") String keyword,
    /**
     * 语言(必填)，如英语en_US
     */
    @JsonProperty("country") String country,
    /**
     * 每页大小(选填，默认20，最大50)
     */
    @JsonProperty("pageSize") Integer pageSize,
    /**
     * 筛选参数(选填)，多个通过英文逗号分隔
     */
    @JsonProperty("filter") String filter,
    /**
     * 排序参数(选填)，枚举参见解决方案介绍 示例：{"price":"asc"}
     */
    @JsonProperty("sort") String sort,
    /**
     * 外部用户ID(选填)
     */
    @JsonProperty("outMemberId") String outMemberId,
    /**
     * 批发价开始(选填)
     */
    @JsonProperty("priceStart") String priceStart,
    /**
     * 批发价结束(选填)
     */
    @JsonProperty("priceEnd") String priceEnd,
    /**
     * 类目ID(选填)
     */
    @JsonProperty("categoryId") Long categoryId,
    /**
     * 类目ID列表(选填)，多个通过英文逗号分隔
     */
    @JsonProperty("categoryIdList") String categoryIdList,
    /**
     * 商机(选填)
     */
    @JsonProperty("regionOpp") String regionOpp,
    /**
     * 寻源通工作台货盘ID(选填)
     */
    @JsonProperty("productCollectionId") String productCollectionId,
    /**
     * 搜索导航ID(选填)
     */
    @JsonProperty("snId") String snId,
    /**
     * 关键词是否已翻译(选填)，true的话就不翻译关键词
     */
    @JsonProperty("keywordTranslate") Boolean keywordTranslate,
    /**
     * 开始页码(选填，默认1)
     */
    @JsonProperty("beginPage") Integer beginPage,
    /**
     * 销量筛选参数(选填)，多个通过英文逗号分隔
     */
    @JsonProperty("saleFilterList") List<SaleFilterParam> saleFilterList
) implements BaseAlibabaRequestRecord {

    /**
     * 默认分页大小
     */
    private static final int DEFAULT_PAGE_SIZE = 20;
    /**
     * 默认页码
     */
    private static final int DEFAULT_PAGE_NO = 1;
    /**
     * 最大分页大小
     */
    private static final int MAX_PAGE_SIZE = 50;

    /**
     * 关键词搜索
     *
     * @param keyword 关键词（选填）
     * @param country 语言（必填）如en-英语，详细枚举参考开发人员参考菜单
     * @return 商品搜索请求
     */
    public static GoodsSearchRequestRecord of(String keyword, LanguageEnum country) {
        return of(keyword, country, DEFAULT_PAGE_NO, DEFAULT_PAGE_SIZE);
    }

    /**
     * 创建商品搜索请求
     *
     * @param keyword  关键词（选填）
     * @param country  语言（必填）如en-英语，详细枚举参考开发人员参考菜单
     * @param pageNo   页码（必填）
     * @param pageSize 每页条数（必填）
     * @return 商品搜索请求
     */
    public static GoodsSearchRequestRecord of(String keyword, LanguageEnum country, Integer pageNo, Integer pageSize) {
        return of(keyword, country, pageSize, pageNo, null, null, null, null, null, null, null, null, null, null, null, null);
    }

    /**
     * 创建商品搜索请求
     *
     * @param keyword             关键词（选填）
     * @param country             语言（必填）如en-英语，详细枚举参考开发人员参考菜单
     * @param pageSize            每页条数（必填）
     * @param filter              筛选参数（选填）多个通过英文逗号分隔，枚举参见解决方案介绍
     * @param sort                排序参数（选填）枚举参见解决方案介绍 示例：{"price":"asc"}
     * @param outMemberId         外部用户id（选填）
     * @param priceStart          批发价开始（选填）
     * @param priceEnd            批发价结束（选填）
     * @param categoryId          类目id（选填）
     * @param categoryIdList      类目id列表（选填）英文逗号隔开，支持多个类目并集
     * @param regionOpp           商机（选填）
     * @param productCollectionId 寻源通工作台货盘id（选填）
     * @param snId                搜索导航ID（选填）如978或978:1352
     * @param keywordTranslate    关键词是否已翻译（选填）默认未翻译，true的话就不翻译关键词
     * @param beginPage           开始页码（选填）
     * @param saleFilterList      销量筛选参数（选填）
     * @return 商品搜索请求
     */
    public static GoodsSearchRequestRecord of(String keyword, LanguageEnum country, Integer pageSize, Integer beginPage, String filter, String sort, String outMemberId, String priceStart, String priceEnd,
        Long categoryId, String categoryIdList, String regionOpp, String productCollectionId, String snId, Boolean keywordTranslate, List<SaleFilterParam> saleFilterList) {
        // 分页大小最大不超过50，建议20效果最佳
        pageSize = Math.min(pageSize, MAX_PAGE_SIZE);
        return new GoodsSearchRequestRecord(keyword, country.getLanguage(), pageSize, filter, sort, outMemberId, priceStart, priceEnd, categoryId, categoryIdList, regionOpp, productCollectionId, snId,
            keywordTranslate, beginPage, saleFilterList);
    }

    @Override
    public void requireParams() {
        assertNotNull(country, "语言不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        if (keyword != null) {
            params.put("keyword", keyword);
        }
        params.put("country", country);
        params.put("pageSize", pageSize == null ? String.valueOf(DEFAULT_PAGE_SIZE) : pageSize.toString());
        if (filter != null) {
            params.put("filter", filter);
        }
        if (sort != null) {
            params.put("sort", sort);
        }
        if (outMemberId != null) {
            params.put("outMemberId", outMemberId);
        }
        if (priceStart != null) {
            params.put("priceStart", priceStart);
        }
        if (priceEnd != null) {
            params.put("priceEnd", priceEnd);
        }
        if (categoryId != null) {
            params.put("categoryId", categoryId.toString());
        }
        if (categoryIdList != null) {
            params.put("categoryIdList", categoryIdList);
        }
        if (regionOpp != null) {
            params.put("regionOpp", regionOpp);
        }
        if (productCollectionId != null) {
            params.put("productCollectionId", productCollectionId);
        }
        if (snId != null) {
            params.put("snId", snId);
        }
        if (keywordTranslate != null) {
            params.put("keywordTranslate", keywordTranslate.toString());
        }
        params.put("beginPage", beginPage == null ? String.valueOf(DEFAULT_PAGE_NO) : beginPage.toString());
        // add saleFilterList 2025年06月28日23:18:52
        if (saleFilterList != null) {
            params.put("saleFilterList", toJsonString(saleFilterList));
        }
        return params;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SaleFilterParam {

        /**
         * 销量类型
         * <pre>
         * sales7:近7天销量,sales14:近14天销量,sales30:近30天销量,totalSales:总销量，传入多个取交集
         * </pre>
         */
        @JsonProperty("saleType")
        String saleType;
        /**
         * 最小销量
         */
        @JsonProperty("saleStart")
        String saleStart;
        /**
         * 最大销量
         */
        @JsonProperty("saleEnd")
        String saleEnd;
    }
}
