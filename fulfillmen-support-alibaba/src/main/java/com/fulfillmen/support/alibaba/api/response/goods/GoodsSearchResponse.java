/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.goods;

import java.util.List;
import lombok.Data;

/**
 * 1688商品搜索响应
 */
@Data
public class GoodsSearchResponse {

    /**
     * 返回结果
     */
    private Result result;

    /**
     * 返回结果包装类
     */
    @Data
    public static class Result {

        /**
         * 是否成功
         */
        private Boolean success;
        /**
         * 状态码
         */
        private String code;

        /**
         * 错误信息
         */
        private String message;

        /**
         * 搜索结果
         */
        private SearchResult result;
    }

    /**
     * 搜索结果
     */
    @Data
    public static class SearchResult {

        /**
         * 总记录数
         */
        private Integer totalRecords;
        /**
         * 总页数
         */
        private Integer totalPage;
        /**
         * 每页大小
         */
        private Integer pageSize;
        /**
         * 当前页码
         */
        private Integer currentPage;
        /**
         * 商品数据列表
         */
        private List<GoodsInfo> data;
    }

    /**
     * 商品信息
     * <pre>
     * A：目前价格梳理和解释汇总如下
     * price-批发价，不包邮，根据商家设置有起批量限制
     * jxhyPrice-一代代发精选货源价，包邮价格支持1件起批
     * pfJxhyPrice-批发精选货源价，不包邮价格2件起批
     * consignPrice-一件代发价，此时需要判断商品是否开通一件代发，否在在不为null的情况下为大市场批发价=price
     * </pre>
     */
    @Data
    public static class GoodsInfo {

        /**
         * 商品图片地址
         */
        private String imageUrl;
        /**
         * 中文标题
         */
        private String subject;
        /**
         * 外文标题
         */
        private String subjectTrans;
        /**
         * 商品ID
         */
        private Long offerId;
        /**
         * 是否精选货源
         */
        private Boolean isJxhy;
        /**
         * 价格信息
         */
        private PriceInfo priceInfo;
        /**
         * 复购率
         */
        private String repurchaseRate;
        /**
         * 30天销量
         */
        private Integer monthSold;
        /**
         * 向1688上报打点数据
         */
        private String traceInfo;
        /**
         * 是否一件代发
         */
        private Boolean isOnePsale;
        /**
         * 商家身份 示例：super_factory-超级工厂 powerful_merchants-实力商家 tp_member-诚信通会员
         */
        private List<String> sellerIdentities;
        /**
         * 商品标 示例：yx-严选, select-精选select
         */
        private List<String> offerIdentities;
        /**
         * 商品交易评分
         */
        private String tradeScore;
        /**
         * 商品白底图
         */
        private String whiteImage;
        /**
         * 营销模型
         */
        private PromotionModel promotionModel;
        /**
         * 一级类目
         */
        private Long topCategoryId;
        /**
         * 二级类目
         */
        private Long secondCategoryId;
        /**
         * 三级类目
         */
        private Long thirdCategoryId;
        /**
         * 是否专利商品
         */
        private Boolean isPatentProduct;
        /**
         * 商品上架时间
         */
        private String createDate;
        /**
         * 商品修改时间
         */
        private String modifyDate;
        /**
         * 跨境select标志
         */
        private Boolean isSelect;
        /**
         * 最小起订量
         */
        private Integer minOrderQuantity;
        /**
         * 卖家数据信息
         */
        private SellerDataInfo sellerDataInfo;
        /**
         * 简单发货信息
         */
        private ProductShippingInfo productSimpleShippingInfo;
        /**
         * 商品推广链接
         */
        private String promotionURL;
        /**
         * 插件返佣 token
         */
        private String token;
    }

    /**
     * 价格信息
     */
    @Data
    public static class PriceInfo {

        /**
         * 批发价
         */
        private String price;
        /**
         * 代销价格
         */
        private String consignPrice;
        /**
         * 促销价格 营销价
         */
        private String promotionPrice;
        /**
         * 代发精选货源价
         */
        private String jxhyPrice;
        /**
         * 批发精选货源价
         */
        private String pfJxhyPrice;
    }

    /**
     * 营销模型
     */
    @Data
    public static class PromotionModel {

        /**
         * 是否有促销
         */
        private Boolean hasPromotion;
        /**
         * 促销类型 示例：plus
         */
        private String promotionType;
    }

    /**
     * 卖家数据信息
     */
    @Data
    public static class SellerDataInfo {

        /**
         * 卖家交易勋章等级
         */
        private String tradeMedalLevel;
        /**
         * 综合服务体验分
         */
        private String compositeServiceScore;
        /**
         * 物流体验分
         */
        private String logisticsExperienceScore;
        /**
         * 纠纷投诉处理分
         */
        private String disputeComplaintScore;
        /**
         * 商品体验分
         */
        private String offerExperienceScore;
        /**
         * 售后体验分
         */
        private String afterSalesExperienceScore;
        /**
         * 咨询体验分
         */
        private String consultingExperienceScore;
        /**
         * 重复购买率
         */
        private String repeatPurchasePercent;
    }

    /**
     * 商品发货信息
     */
    @Data
    public static class ProductShippingInfo {

        /**
         * 发货时效 示例：shipIn24Hours-24小时发货 shipIn48Hours-48小时发货
         */
        private String shippingTimeGuarantee;
    }
}