/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.logistics;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 获取所有的物流公司列表请求
 *
 * <AUTHOR>
 * @created 2025-01-14
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:alibaba.logistics.OpQueryLogisticCompanyList-1">API文档</a>
 */
@Builder
public record LogisticsCompanyListRequestRecord() implements BaseAlibabaRequestRecord {

    /**
     * 创建获取所有的物流公司列表请求
     *
     * @return 获取所有的物流公司列表请求
     */
    public static LogisticsCompanyListRequestRecord of() {
        return new LogisticsCompanyListRequestRecord();
    }

    @Override
    public void requireParams() {
        // 无需参数校验
    }

    @Override
    public Map<String, String> toParams() {
        return new HashMap<>();
    }
}