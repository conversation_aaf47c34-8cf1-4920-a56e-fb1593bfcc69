/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model;

import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 支付方式查询结果
 * <p>
 * 包含订单的可用支付通道列表、支付金额、支付超时时间等信息
 *
 * <AUTHOR>
 * @created 2025-01-10
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.payWay.query-1">API文档</a>
 */
@Data
@NoArgsConstructor
public class PayWayQueryResult {

    /**
     * 可用支付通道列表
     * <p>
     * 包含支付宝、诚e赊、公对公转账等支付方式
     */
    private List<PayTypeInfo> channels;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 支付金额
     * <p>
     * 单位：分
     */
    private Long payFee;

    /**
     * 最晚支付时间
     * <p>
     * 格式：yyyy-MM-dd HH:mm:ss
     */
    private String timeout;
}