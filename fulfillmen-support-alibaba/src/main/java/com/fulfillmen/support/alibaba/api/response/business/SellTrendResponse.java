/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.business;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品每日销售数量趋势响应
 * <p>
 * 包含商品在指定时间段内的每日销售数量趋势数据。
 *
 * <AUTHOR>
 * @created 2025-01-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SellTrendResponse extends BaseAlibabaResponse {

    /**
     * 响应结果
     */
    private Result result;

    @Data
    public static class Result {

        /**
         * 是否成功
         */
        @JsonProperty("success")
        private Boolean success;

        /**
         * 错误码
         */
        @JsonProperty("retCode")
        private String retCode;

        /**
         * 返回信息
         */
        @JsonProperty("retMsg")
        private String retMsg;

        /**
         * 趋势数据列表
         */
        @JsonProperty("result")
        private List<OfferSellTrendDataModel> result;
    }

    @Data
    public static class OfferSellTrendDataModel {

        /**
         * 数据指标对应的日期，格式：yyyyMMdd
         */
        @JsonProperty("date")
        private String date;

        /**
         * 指标数据，表示当日销售数量
         */
        @JsonProperty("value")
        private String value;
    }
}