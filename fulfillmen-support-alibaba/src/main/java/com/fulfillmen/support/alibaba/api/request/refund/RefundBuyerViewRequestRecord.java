/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.refund;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 查询退款单详情请求
 *
 * <AUTHOR>
 * @created 2025-01-13
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.buyer.view-1">查询退款单详情</a>
 */
@Builder
public record RefundBuyerViewRequestRecord(
    /**
     * 退款单号
     */
    String refundId,

    /**
     * 是否需要退款协议信息
     */
    Boolean needProtocolInfo,

    /**
     * 是否需要退款凭证信息
     */
    Boolean needEvidenceInfo,

    /**
     * 是否需要退款留言信息
     */
    Boolean needMessageInfo
) implements BaseAlibabaRequestRecord {

    public static RefundBuyerViewRequestRecord of(String refundId) {
        return new RefundBuyerViewRequestRecord(refundId, null, null, null);
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("refundId", refundId);
        if (needProtocolInfo != null) {
            params.put("needProtocolInfo", String.valueOf(needProtocolInfo));
        }
        if (needEvidenceInfo != null) {
            params.put("needEvidenceInfo", String.valueOf(needEvidenceInfo));
        }
        if (needMessageInfo != null) {
            params.put("needMessageInfo", String.valueOf(needMessageInfo));
        }
        return params;
    }

    @Override
    public void requireParams() {
        assertNotBlank(refundId, "退款单号不能为空");
    }
}