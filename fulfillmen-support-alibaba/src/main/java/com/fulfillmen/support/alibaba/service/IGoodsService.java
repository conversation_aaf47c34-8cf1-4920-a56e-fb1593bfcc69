/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service;

import com.fulfillmen.support.alibaba.api.request.goods.GoodsCouponClaimRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsDetailRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsImageSearchRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsImageUploadRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsKeywordNavigationRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsRecommendRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsRelatedRecommendRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsSearchRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsSellerRequestRecord;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsCouponClaimResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsDetailResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsImageSearchResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsImageUploadResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsKeywordNavigationResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsRecommendResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsRelatedRecommendResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsSearchResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsSellerResponse;
import reactor.core.publisher.Mono;

/**
 * 1688商品服务接口 提供商品搜索、详情查询、推荐等功能
 *
 * <AUTHOR>
 * @created 2025-01-08
 */
public interface IGoodsService {

    /**
     * 搜索商品
     *
     * @param request 搜索请求参数
     * @return 商品搜索结果
     */
    Mono<GoodsSearchResponse> searchGoods(GoodsSearchRequestRecord request);

    /**
     * 图片搜索商品
     *
     * @param request 图片搜索请求参数
     * @return 商品搜索结果
     */
    Mono<GoodsImageSearchResponse> searchGoodsByImage(GoodsImageSearchRequestRecord request);

    /**
     * 上传图片
     *
     * @param request 图片上传请求参数
     * @return 图片上传结果
     */
    Mono<GoodsImageUploadResponse> uploadImage(GoodsImageUploadRequestRecord request);

    /**
     * 获取商品详情
     *
     * @param request 商品详情请求参数
     * @return 商品详情
     */
    Mono<GoodsDetailResponse> getGoodsDetail(GoodsDetailRequestRecord request);

    /**
     * 获取卖家商品列表
     *
     * @param request 卖家商品请求参数
     * @return 卖家商品列表
     */
    Mono<GoodsSellerResponse> getSellerGoods(GoodsSellerRequestRecord request);

    /**
     * 获取商品推荐
     *
     * @param request 商品推荐请求参数
     * @return 商品推荐结果
     */
    Mono<GoodsRecommendResponse> recommendGoods(GoodsRecommendRequestRecord request);

    /**
     * 获取关键词导航
     *
     * @param request 关键词导航请求参数
     * @return 关键词导航结果
     */
    Mono<GoodsKeywordNavigationResponse> getKeywordNavigation(GoodsKeywordNavigationRequestRecord request);

    /**
     * 获取相关商品推荐
     *
     * @param request 相关商品推荐请求参数
     * @return 相关商品推荐结果
     */
    Mono<GoodsRelatedRecommendResponse> getRelatedRecommend(GoodsRelatedRecommendRequestRecord request);

    /**
     * 领取优惠券
     *
     * @param request 优惠券领取请求参数
     * @return 优惠券领取结果
     */
    Mono<GoodsCouponClaimResponse> claimCoupon(GoodsCouponClaimRequestRecord request);
}