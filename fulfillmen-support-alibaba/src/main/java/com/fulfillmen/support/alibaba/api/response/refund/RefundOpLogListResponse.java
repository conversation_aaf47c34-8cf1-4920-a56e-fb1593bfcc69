/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.refund;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import com.fulfillmen.support.alibaba.api.response.model.RefundOpLogListResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询退款单操作日志响应
 * <p>
 * 查询退款单操作日志的响应结果，包含退款单的所有操作记录
 *
 * <AUTHOR>
 * @created 2025-01-13
 * @see RefundOpLogListResult
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RefundOpLogListResponse extends BaseAlibabaResponse {

    /**
     * 退款单操作日志查询结果
     */
    private RefundOpLogListResult result;
}