/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model;

import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * 查询退款单详情结果
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Data
public class RefundBuyerViewResult {

    /**
     * 退款单详情
     */
    private OpOrderRefundModelDetail opOrderRefundModelDetail;

    @Data
    public static class OpOrderRefundModelDetail {

        /**
         * 支付宝交易号
         */
        private String alipayPaymentId;

        /**
         * 申请退运费金额（单位：分）
         */
        private Long applyCarriage;

        /**
         * 申请退款金额（单位：分）
         */
        private Long applyPayment;

        /**
         * 申请原因
         */
        private String applyReason;

        /**
         * 申请原因ID
         */
        private Long applyReasonId;

        /**
         * 申请子原因ID
         */
        private Long applySubReasonId;

        /**
         * 买家会员ID
         */
        private String buyerMemberId;

        /**
         * 买家是否已发货
         */
        private Boolean buyerSendGoods;

        /**
         * 可退金额（单位：分）
         */
        private Long canRefundPayment;

        /**
         * 是否CRM修改退款
         */
        private Boolean crmModifyRefund;

        /**
         * 退款类型(1:退款 2:退款退货 3:换货)
         */
        private Integer disputeRequest;

        /**
         * 纠纷类型
         */
        private Integer disputeType;

        /**
         * 扩展信息
         */
        private Map<String, String> extInfo;

        /**
         * 冻结资金（单位：分）
         */
        private Long frozenFund;

        /**
         * 申请时间
         */
        private String gmtApply;

        /**
         * 完成时间
         */
        private String gmtCompleted;

        /**
         * 创建时间
         */
        private String gmtCreate;

        /**
         * 修改时间
         */
        private String gmtModified;

        /**
         * 超时时间
         */
        private String gmtTimeOut;

        /**
         * 是否已收到货物
         */
        private Boolean goodsReceived;

        /**
         * 货物状态(1:未收到货 2:已收到货)
         */
        private Integer goodsStatus;

        /**
         * 退款单ID
         */
        private Long id;

        /**
         * 是否新退款退货
         */
        private Boolean newRefundReturn;

        /**
         * 是否仅退款
         */
        private Boolean onlyRefund;

        /**
         * 订单条目数量映射
         */
        private Map<String, Integer> orderEntryCountMap;

        /**
         * 订单条目ID列表
         */
        private List<Long> orderEntryIdList;

        /**
         * 订单ID
         */
        private Long orderId;

        /**
         * 商品名称
         */
        private String productName;

        /**
         * 退运费金额（单位：分）
         */
        private Long refundCarriage;

        /**
         * 是否退货
         */
        private Boolean refundGoods;

        /**
         * 退款单号
         */
        private String refundId;

        /**
         * 退款金额（单位：分）
         */
        private Long refundPayment;

        /**
         * 拒绝原因ID
         */
        private Long rejectReasonId;

        /**
         * 拒绝次数
         */
        private Integer rejectTimes;

        /**
         * 卖家延迟支付
         */
        private Boolean sellerDelayDisburse;

        /**
         * 卖家会员ID
         */
        private String sellerMemberId;

        /**
         * 退款状态
         */
        private String status;

        /**
         * 是否支持新的分步支付
         */
        private Boolean supportNewSteppay;

        /**
         * 是否超时冻结
         */
        private Boolean timeOutFreeze;

        /**
         * 交易类型字符串
         */
        private String tradeTypeStr;

        /**
         * 退款操作记录列表
         */
        private List<Object> refundOperationList;

        /**
         * 买家登录ID
         */
        private String buyerLoginId;

        /**
         * 卖家登录ID
         */
        private String sellerLoginId;
    }
}