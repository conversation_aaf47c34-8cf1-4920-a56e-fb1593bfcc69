/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.autoconfigure;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 阿里巴巴开放平台配置属性
 *
 * <AUTHOR>
 * @date 2024-12-31 09:13
 * @description: 包含阿里巴巴开放平台的配置信息，包括1688和支付宝的配置
 * @since 1.0.0
 */
@Setter
@Getter
@ConfigurationProperties("alibaba")
public class AlibabaProperties {

    // 1688 配置
    private Open1688Properties open1688 = new Open1688Properties();

    // 支付宝 配置
    private AliPayProperties alipay = new AliPayProperties();

    @Getter
    @Setter
    public static class Open1688Properties {

        // 1688 api 接口配置
        private String appKey;
        private String secretKey;
        private String accessToken;
        private String gatewayUrl;
    }

    @Getter
    @Setter
    public static class AliPayProperties {

        private String appId;
        private String gatewayUrl;
    }

}
