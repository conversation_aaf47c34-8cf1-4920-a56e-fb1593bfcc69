/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.message;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import com.fulfillmen.support.alibaba.util.DateUtils;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 游标式获取失败的消息列表请求 获取的消息不会自动确认，需要调用方手动调用确认API来确认消息状态。
 * <p>
 * 参数说明： - createStartTime: 消息创建时间查找范围开始，选填，格式：yyyyMMddHHmmssSSS+0800 - createEndTime: 消息创建时间查找范围结束，选填，格式：yyyyMMddHHmmssSSS+0800 - quantity: 每次取的数据量，范围20-200，默认20，选填 - type:
 * 消息类型，选填 - userInfo: 用户Id，选填
 *
 * <AUTHOR>
 * @created 2025-01-17
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.p4p:alibaba.push.cursor.messageList-1">游标式获取失败的消息列表API文档</a>
 * @since 1.0.0
 */
@Builder
public record MessageCursorRequestRecord(
    /**
     * 消息创建时间查找范围开始 选填 格式：yyyyMMddHHmmssSSS+0800
     */
    Date createStartTime,

    /**
     * 消息创建时间查找范围结束 选填 格式：yyyyMMddHHmmssSSS+0800
     */
    Date createEndTime,

    /**
     * 每次取的数据量，范围20-200，默认20 选填
     */
    Integer quantity,

    /**
     * 消息类型 选填
     */
    String type,

    /**
     * 用户Id 选填
     */
    String userInfo
) implements BaseAlibabaRequestRecord {

    /**
     * 默认每次取的数据量
     */
    private static final int DEFAULT_QUANTITY = 20;

    /**
     * 最小每次取的数据量
     */
    private static final int MIN_QUANTITY = 20;

    /**
     * 最大每次取的数据量
     */
    private static final int MAX_QUANTITY = 200;

    /**
     * 创建游标式获取失败的消息列表请求
     *
     * @param createStartTime 消息创建时间查找范围开始(选填)
     * @param createEndTime   消息创建时间查找范围结束(选填)
     * @param quantity        每次取的数据量(选填,默认20,范围20-200)
     * @param type            消息类型(选填)
     * @param userInfo        用户Id(选填)
     * @return 游标式获取失败的消息列表请求
     */
    public static MessageCursorRequestRecord of(Date createStartTime,
        Date createEndTime,
        Integer quantity,
        String type,
        String userInfo) {
        return new MessageCursorRequestRecord(createStartTime, createEndTime, quantity == null
            ? DEFAULT_QUANTITY
            : Math.min(quantity, MAX_QUANTITY), type, userInfo);
    }

    /**
     * 创建游标式获取失败的消息列表请求(使用默认数据量)
     *
     * @param createStartTime 消息创建时间查找范围开始(选填)
     * @param createEndTime   消息创建时间查找范围结束(选填)
     * @param type            消息类型(选填)
     * @param userInfo        用户Id(选填)
     * @return 游标式获取失败的消息列表请求
     */
    public static MessageCursorRequestRecord of(Date createStartTime, Date createEndTime) {
        return of(createStartTime, createEndTime, null, null, null);
    }

    /**
     * 创建游标式获取失败的消息列表请求(使用默认数据量)
     *
     * @param createStartTime 消息创建时间查找范围开始(选填)
     * @param createEndTime   消息创建时间查找范围结束(选填)
     * @param type            消息类型(选填)
     * @param userInfo        用户Id(选填)
     * @return 游标式获取失败的消息列表请求
     */
    public static MessageCursorRequestRecord of(Date createStartTime,
        Date createEndTime,
        String type,
        String userInfo) {
        return of(createStartTime, createEndTime, DEFAULT_QUANTITY, type, userInfo);
    }

    @Override
    public void requireParams() {
        if (quantity != null && (quantity < MIN_QUANTITY || quantity > MAX_QUANTITY)) {
            throw new AlibabaServiceValidationException(String.format("每次取的数据量必须在%d-%d之间", MIN_QUANTITY, MAX_QUANTITY));
        }
        if (createStartTime != null && createEndTime != null && createStartTime.after(createEndTime)) {
            throw new AlibabaServiceValidationException("开始时间不能晚于结束时间");
        }
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        if (createStartTime != null) {
            params.put("createStartTime", DateUtils.formatDate(createStartTime));
        }
        if (createEndTime != null) {
            params.put("createEndTime", DateUtils.formatDate(createEndTime));
        }
        if (quantity != null) {
            params.put("quantity", String.valueOf(quantity));
        }
        if (type != null) {
            params.put("type", type);
        }
        if (userInfo != null) {
            params.put("userInfo", userInfo);
        }
        return params;
    }
}