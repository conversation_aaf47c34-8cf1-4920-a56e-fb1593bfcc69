/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service.impl;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequest;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import com.fulfillmen.support.alibaba.autoconfigure.AlibabaProperties;
import com.fulfillmen.support.alibaba.common.AlibabaRequestBuilder;
import com.fulfillmen.support.alibaba.common.AlibabaRequestRecordBuilder;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceException;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.MultiValueMap;
import reactor.core.publisher.Mono;

/**
 * Alibaba服务基类 提供统一的错误处理和日志记录
 *
 * <AUTHOR>
 * @created 2025-01-16
 */
@Slf4j
public abstract class BaseAlibabaServiceImpl {

    protected final String appKey;
    protected final String secretKey;
    protected final String accessToken;

    protected BaseAlibabaServiceImpl(AlibabaProperties alibabaProperties) {
        this.appKey = alibabaProperties.getOpen1688().getAppKey();
        this.secretKey = alibabaProperties.getOpen1688().getSecretKey();
        this.accessToken = alibabaProperties.getOpen1688().getAccessToken();
    }

    /**
     * 获取服务名称
     *
     * @return 服务名称
     */
    protected abstract String getServiceName();

    /**
     * 包装请求处理，提供统一的错误处理和日志记录
     *
     * @param operation 操作名称
     * @param request   请求对象
     * @param apiPath   API路径
     * @param apiCall   API调用函数
     * @param <T>       响应类型
     * @param <R>       请求类型
     * @return 响应结果
     */
    protected <T, R extends BaseAlibabaRequest> Mono<T> wrapWithErrorHandler(String operation,
        R request,
        String apiPath,
        Function<MultiValueMap<String, String>, Mono<T>> apiCall) {
        return wrapWithErrorHandler(operation, request, apiPath, apiCall, appKey, secretKey, accessToken);
    }

    /**
     * 包装请求处理，提供统一的错误处理和日志记录 (Record 版本)
     *
     * @param operation 操作名称
     * @param request   请求对象
     * @param apiPath   API路径
     * @param apiCall   API调用函数
     * @param <T>       响应类型
     * @param <R>       请求类型
     * @return 响应结果
     */
    protected <T, R extends BaseAlibabaRequestRecord> Mono<T> wrapWithErrorHandler(String operation,
        R request,
        String apiPath,
        Function<MultiValueMap<String, String>, Mono<T>> apiCall) {
        return wrapWithErrorHandler(operation, request, apiPath, apiCall, appKey, secretKey, accessToken);
    }

    /**
     * 包装JSON请求处理，提供统一的错误处理和日志记录
     *
     * @param operation 操作名称
     * @param request   请求对象
     * @param apiPath   API路径
     * @param apiCall   API调用函数
     * @param jsonKey   JSON参数键名
     * @param <T>       响应类型
     * @param <R>       请求类型
     * @return 响应结果
     */
    protected <T, R extends BaseAlibabaRequest> Mono<T> wrapWithErrorHandler(String operation,
        R request,
        String apiPath,
        Function<MultiValueMap<String, String>, Mono<T>> apiCall,
        String jsonKey) {
        return wrapWithErrorHandler(operation, request, apiPath, apiCall, appKey, secretKey, accessToken, jsonKey);
    }

    /**
     * 包装JSON请求处理，提供统一的错误处理和日志记录 (Record 版本)
     *
     * @param operation 操作名称
     * @param request   请求对象
     * @param apiPath   API路径
     * @param apiCall   API调用函数
     * @param jsonKey   JSON参数键名
     * @param <T>       响应类型
     * @param <R>       请求类型
     * @return 响应结果
     */
    protected <T, R extends BaseAlibabaRequestRecord> Mono<T> wrapWithErrorHandler(String operation,
        R request,
        String apiPath,
        Function<MultiValueMap<String, String>, Mono<T>> apiCall,
        String jsonKey) {
        return wrapWithErrorHandler(operation, request, apiPath, apiCall, appKey, secretKey, accessToken, jsonKey);
    }

    /**
     * 包装请求处理，提供统一的错误处理和日志记录
     */
    protected <T, R extends BaseAlibabaRequest> Mono<T> wrapWithErrorHandler(String operation, R request, String apiPath,
        Function<MultiValueMap<String, String>, Mono<T>> apiCall,
        String appKey,
        String secretKey,
        String accessToken) {
        String fullOperation = getServiceName() + "-" + operation;
        try {
            return AlibabaRequestBuilder
                .executeRequest(request, apiPath, apiCall, fullOperation, getServiceName(), appKey, secretKey, accessToken);
        } catch (Exception e) {
            if (e instanceof AlibabaServiceValidationException) {
                return Mono.error(e);
            }
            return Mono.error(new AlibabaServiceException(e.getMessage(), e));
        }
    }

    /**
     * 包装请求处理，提供统一的错误处理和日志记录 (Record 版本)
     */
    protected <T, R extends BaseAlibabaRequestRecord> Mono<T> wrapWithErrorHandler(String operation,
        R request, String apiPath, Function<MultiValueMap<String, String>, Mono<T>> apiCall,
        String appKey,
        String secretKey,
        String accessToken) {
        String fullOperation = getServiceName() + "-" + operation;
        try {
            return AlibabaRequestRecordBuilder
                .executeRequest(request, apiPath, apiCall, fullOperation, getServiceName(), appKey, secretKey, accessToken);
        } catch (Exception e) {
            if (e instanceof AlibabaServiceValidationException) {
                return Mono.error(e);
            }
            return Mono.error(new AlibabaServiceException(e.getMessage(), e));
        }
    }

    /**
     * 包装JSON请求处理，提供统一的错误处理和日志记录
     */
    protected <T, R extends BaseAlibabaRequest> Mono<T> wrapWithErrorHandler(String operation,
        R request, String apiPath, Function<MultiValueMap<String, String>, Mono<T>> apiCall,
        String appKey,
        String secretKey,
        String accessToken,
        String jsonKey) {
        String fullOperation = getServiceName() + "-" + operation;
        try {
            return AlibabaRequestBuilder
                .executeRequest(request, apiPath, apiCall, fullOperation, getServiceName(), appKey, secretKey, accessToken, true, jsonKey);
        } catch (Exception e) {
            if (e instanceof AlibabaServiceValidationException) {
                return Mono.error(e);
            }
            return Mono.error(new AlibabaServiceException(e.getMessage(), e));
        }
    }

    /**
     * 包装JSON请求处理，提供统一的错误处理和日志记录 (Record 版本)
     */
    protected <T, R extends BaseAlibabaRequestRecord> Mono<T> wrapWithErrorHandler(String operation,
        R request,
        String apiPath,
        Function<MultiValueMap<String, String>, Mono<T>> apiCall,
        String appKey,
        String secretKey,
        String accessToken,
        String jsonKey) {
        String fullOperation = getServiceName() + "-" + operation;
        try {
            return AlibabaRequestRecordBuilder
                .executeRequest(request, apiPath, apiCall, fullOperation, getServiceName(), appKey, secretKey, accessToken, true, jsonKey);
        } catch (Exception e) {
            if (e instanceof AlibabaServiceValidationException) {
                return Mono.error(e);
            }
            return Mono.error(new AlibabaServiceException(e.getMessage(), e));
        }
    }
}