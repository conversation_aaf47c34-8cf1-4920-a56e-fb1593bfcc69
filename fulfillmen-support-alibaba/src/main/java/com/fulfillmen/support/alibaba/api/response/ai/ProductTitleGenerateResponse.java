/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.ai;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 商品标题生成响应结果
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class ProductTitleGenerateResponse {

    /**
     * 生成结果
     */
    @JsonProperty("result")
    private GenerateResult result;

    /**
     * 生成结果详情
     */
    @Data
    public static class GenerateResult {

        /**
         * 请求是否成功
         */
        @JsonProperty("success")
        private Boolean success;

        /**
         * 错误码
         */
        @JsonProperty("code")
        private String code;

        /**
         * 错误信息
         */
        @JsonProperty("message")
        private String message;

        /**
         * 生成的标题内容
         */
        @JsonProperty("result")
        private String result;

    }
}