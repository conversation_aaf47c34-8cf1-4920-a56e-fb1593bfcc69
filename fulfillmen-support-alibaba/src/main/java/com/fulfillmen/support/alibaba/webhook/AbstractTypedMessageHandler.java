/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook;

import com.fulfillmen.starter.core.util.JacksonUtil;
import com.fulfillmen.support.alibaba.enums.CallbackMessageType;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * 抽象消息处理器
 *
 * <AUTHOR>
 * @date 2025/7/10
 * @since 1.0.0
 */
@Slf4j
public abstract class AbstractTypedMessageHandler<T> implements MessageHandler<T> {

    private final List<CallbackMessageType> supportedTypes;

    protected AbstractTypedMessageHandler(CallbackMessageType... types) {
        this.supportedTypes = Arrays.asList(types);
    }

    @Override
    public boolean canHandle(CallbackMessageType messageType) {
        return supportedTypes.contains(messageType);
    }

    @Override
    public List<CallbackMessageType> getSupportedTypes() {
        return Collections.unmodifiableList(supportedTypes);
    }

    /**
     * 具体的业务处理逻辑，由子类实现
     */
    protected abstract void doHandle(T data, MessageEvent<T> event);

    @Override
    public final MessageResult handle(MessageEvent<T> event) {
        try {
            // 获取并转换数据
            T data = getTypedData(event);

            // 业务处理
            doHandle(data, event);

            return MessageResult.success(event.getMsgId());

        } catch (Exception e) {
            log.error("消息处理异常: msgId={}, type={}",
                event.getMsgId(), event.getType(), e);
            return MessageResult.error(event.getMsgId(), e.getMessage());
        }
    }

    /**
     * 获取类型安全的数据
     * 如果event.getData()已经是正确类型则直接返回，否则从rawData转换
     */
    @SuppressWarnings("unchecked")
    private T getTypedData(MessageEvent<T> event) {
        Object eventData = event.getData();
        Class<T> targetClass = getDataClass();

        // 如果数据已经是正确类型，直接返回
        if (eventData != null && targetClass.isInstance(eventData)) {
            return (T) eventData;
        }

        // 否则从rawData转换
        if (event.getRawData() != null) {
            log.debug("数据类型不匹配，从rawData转换: expected={}, actual={}",
                targetClass.getSimpleName(),
                eventData != null ? eventData.getClass().getSimpleName() : "null");
            return event.getTypedData(targetClass);
        }

        // 如果rawData也为空，尝试直接转换eventData
        if (eventData != null) {
            log.debug("rawData为空，尝试直接转换eventData: from={} to={}",
                eventData.getClass().getSimpleName(), targetClass.getSimpleName());
            return JacksonUtil.convertToBean(JacksonUtil.toJsonString(eventData),
                targetClass);
        }

        log.warn("无法获取有效数据进行转换: msgId={}, type={}",
            event.getMsgId(), event.getType());
        return null;
    }

    /**
     * 获取数据类型Class，用于JSON转换
     */
    protected abstract Class<T> getDataClass();

}
