/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model;

import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * 查询退款单列表(买家视角)结果
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Data
public class RefundBuyerListResult {

    /**
     * 退款单列表
     */
    private List<OpOrderRefundModel> opOrderRefundModels;

    /**
     * 总记录数
     */
    private Integer totalCount;

    /**
     * 当前页码
     */
    private Integer currentPageNum;

    @Data
    public static class OpOrderRefundModel {

        /**
         * 支付宝交易号
         */
        private String alipayPaymentId;

        /**
         * 申请退运费金额（单位：分）
         */
        private Long applyCarriage;

        /**
         * 申请退款金额（单位：分）
         */
        private Long applyPayment;

        /**
         * 申请原因
         */
        private String applyReason;

        /**
         * 申请原因ID
         */
        private Long applyReasonId;

        /**
         * 申请子原因ID
         */
        private Long applySubReasonId;

        /**
         * 买家会员ID
         */
        private String buyerMemberId;

        /**
         * 买家用户ID
         */
        private Long buyerUserId;

        /**
         * 可退金额（单位：分）
         */
        private Long canRefundPayment;

        /**
         * 退款类型(1:退款 2:退款退货 3:换货)
         */
        private Integer disputeRequest;

        /**
         * 纠纷类型
         */
        private Integer disputeType;

        /**
         * 扩展信息
         */
        private Map<String, String> extInfo;

        /**
         * 冻结资金（单位：分）
         */
        private Long frozenFund;

        /**
         * 申请时间
         */
        private String gmtApply;

        /**
         * 完成时间
         */
        private String gmtCompleted;

        /**
         * 创建时间
         */
        private String gmtCreate;

        /**
         * 修改时间
         */
        private String gmtModified;

        /**
         * 货物状态(1:未收到货 2:已收到货)
         */
        private Integer goodsStatus;

        /**
         * 退款单ID
         */
        private Long id;

        /**
         * 订单ID
         */
        private Long orderId;

        /**
         * 商品名称
         */
        private String productName;

        /**
         * 实际退运费金额（单位：分）
         */
        private Long refundCarriage;

        /**
         * 退款单号
         */
        private String refundId;

        /**
         * 实际退款金额（单位：分）
         */
        private Long refundPayment;

        /**
         * 拒绝原因ID
         */
        private Long rejectReasonId;

        /**
         * 拒绝次数
         */
        private Integer rejectTimes;

        /**
         * 卖家会员ID
         */
        private String sellerMemberId;

        /**
         * 卖家用户ID
         */
        private Long sellerUserId;

        /**
         * 退款状态
         */
        private String status;

        /**
         * 交易类型
         */
        private String tradeTypeStr;

        /**
         * 买家登录ID
         */
        private String buyerLoginId;

        /**
         * 买家支付宝ID
         */
        private String buyerAlipayId;

        /**
         * 卖家登录ID
         */
        private String sellerLoginId;

        /**
         * 卖家支付宝ID
         */
        private String sellerAlipayId;

        /**
         * 子订单ID列表
         */
        private String orderEntryIds;

        /**
         * 退款金额
         */
        private Long refundFee;

        /**
         * 退款状态
         */
        private String refundStatus;

        /**
         * 退款阶段
         */
        private String refundPhase;

        /**
         * 是否超时冻结
         */
        private Boolean timeOutFreeze;

        /**
         * 是否需要卖家地址和电话
         */
        private Boolean needSellerAddressAndPhone;

        /**
         * 是否需要物流
         */
        private Boolean needLogistics;

        /**
         * 是否需要修改退款金额
         */
        private Boolean needModifyRefundFee;

        /**
         * 是否需要上传凭证
         */
        private Boolean needUploadVoucher;

        /**
         * 是否需要退回物流
         */
        private Boolean needLogisticsBack;

        /**
         * 是否需要仲裁
         */
        private Boolean needArbitration;

        /**
         * 是否需要买家修改退款金额
         */
        private Boolean needBuyerModifyRefundFee;

        /**
         * 是否需要买家上传凭证
         */
        private Boolean needBuyerUploadVoucher;

        /**
         * 是否需要买家退回物流
         */
        private Boolean needBuyerLogisticsBack;

        /**
         * 是否需要买家仲裁
         */
        private Boolean needBuyerArbitration;

        /**
         * 纠纷结束时间
         */
        private String disputeEndTime;

        /**
         * 纠纷开始时间
         */
        private String disputeStartTime;

        /**
         * 纠纷状态视图
         */
        private String disputeStatusView;

        /**
         * 纠纷类型视图
         */
        private String disputeTypeView;

        /**
         * 纠纷状态描述
         */
        private String disputeStatusDesc;

        /**
         * 纠纷类型描述
         */
        private String disputeTypeDesc;

        /**
         * 物流公司信息
         */
        private LogisticsCompany logisticsCompany;
    }

    @Data
    public static class LogisticsCompany {

        /**
         * 物流公司名称
         */
        private String companyName;

        /**
         * 物流公司编号
         */
        private String companyNo;

        /**
         * 物流公司电话
         */
        private String companyPhone;

        /**
         * 创建时间
         */
        private String gmtCreate;

        /**
         * 修改时间
         */
        private String gmtModified;

        /**
         * 物流公司ID
         */
        private Long id;

        /**
         * 外部ID
         */
        private String outerId;

        /**
         * 拼音
         */
        private String pinyin;

        /**
         * 拼写名称
         */
        private String spellName;

        /**
         * 是否支持打印
         */
        private Boolean supportPrint;

        /**
         * 物流公司网址
         */
        private String url;
    }
}