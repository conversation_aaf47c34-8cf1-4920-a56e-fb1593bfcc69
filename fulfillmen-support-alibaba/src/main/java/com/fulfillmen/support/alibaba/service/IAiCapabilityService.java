/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service;

import com.fulfillmen.support.alibaba.api.request.ai.ImageCutRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ImageElementsRecognitionRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ImageEnlargeRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ImageMattingRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ImageRemoveRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ImageTranslateRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ProductDescGenerateRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ProductTextTranslateRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ProductTitleGenerateRequestRecord;
import com.fulfillmen.support.alibaba.api.response.ai.ImageCutResponse;
import com.fulfillmen.support.alibaba.api.response.ai.ImageElementsRecognitionResponse;
import com.fulfillmen.support.alibaba.api.response.ai.ImageEnlargeResponse;
import com.fulfillmen.support.alibaba.api.response.ai.ImageMattingResponse;
import com.fulfillmen.support.alibaba.api.response.ai.ImageRemoveResponse;
import com.fulfillmen.support.alibaba.api.response.ai.ImageTranslateResponse;
import com.fulfillmen.support.alibaba.api.response.ai.ProductDescGenerateResponse;
import com.fulfillmen.support.alibaba.api.response.ai.ProductTextTranslateResponse;
import com.fulfillmen.support.alibaba.api.response.ai.ProductTitleGenerateResponse;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceException;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import reactor.core.publisher.Mono;

/**
 * AI能力服务接口
 * <p>
 * 提供以下AI能力服务： 1. 商品文本翻译 - 支持60多种语言间的精确翻译，专门为电商场景研发 2. 商品标题生成 - 基于淘宝电商数据训练，生成优化的商品标题 3. 商品详描生成 - 生成内容丰富、卖点精准的商品描述 4. 图片翻译 - 支持18种语向的图片文本翻译，专为电商图片定制 5. 图片裁剪 - 支持自动识别图像主体区域，调整图片尺寸 6.
 * 图片高清放大 - 支持2-4倍图片放大，提升清晰度 7. 图片智能抠图 - 自动识别主体并分离背景 8. 图片智能消除 - 去除图片中的文字、标识等元素 9. 图像元素识别 - 识别图片中的文字、Logo、水印及含字色块等元素
 * <p>
 * 所有方法都遵循响应式编程模式，返回 Mono 类型的响应对象。 方法调用失败会抛出 AlibabaServiceException 或其子类异常。
 *
 * <AUTHOR>
 * @created 2025-01-16
 */
public interface IAiCapabilityService {

    /**
     * 商品文本翻译
     * <p>
     * 支持将商品文本翻译成指定语言，专门为电商场景研发，实现60多种语向间的精确翻译。 借助电商特定数据训练提升翻译质量，并配备智能品牌识别与自定义干预功能。
     *
     * @param request 翻译请求参数，包含： - sourceTextList: 待翻译的文本列表（必填） - sourceLanguage: 源语言（必填） - targetLanguage: 目标语言（必填） - options: 翻译选项（可选）
     * @return 翻译结果，包含翻译后的文本数组
     * @throws AlibabaServiceValidationException 当必填参数为空或格式错误时
     * @throws AlibabaServiceException           当API调用失败时
     */
    Mono<ProductTextTranslateResponse> translateProductText(ProductTextTranslateRequestRecord request);

    /**
     * 商品标题生成
     * <p>
     * 基于淘宝电商数据训练，通过简单入参即可快速生成吸引力十足的标题。 能够凸显商品优势，帮助商家获取更多流量。
     *
     * @param request 标题生成请求参数，包含： - productName: 商品名称（必填） - targetLanguage: 目标语言（必填） - productCategory: 商品类目（可选） - productKeyword: 商品关键词（可选） - productDesc: 商品描述（可选） - options:
     *                生成选项（可选）
     * @return 生成的标题结果，包含优化后的商品标题
     * @throws AlibabaServiceValidationException 当必填参数为空或格式错误时
     * @throws AlibabaServiceException           当API调用失败时
     */
    Mono<ProductTitleGenerateResponse> generateProductTitle(ProductTitleGenerateRequestRecord request);

    /**
     * 商品详描生成
     * <p>
     * 专为海外电商商品定制，生成内容丰富、卖点精准、充满吸引力的商品描述。 解决商品详情文本信息缺失、重点不明确等信息效率低问题，提升商品转化率。
     *
     * @param request 详描生成请求参数，包含： - productName: 商品名称（必填） - targetLanguage: 目标语言（必填） - productCategory: 商品类目（可选） - productKeyword: 商品关键词（可选） - productDesc: 商品描述（可选） - options:
     *                生成选项（可选）
     * @return 生成的详描结果，包含生成的商品详细描述
     * @throws AlibabaServiceValidationException 当必填参数为空或格式错误时
     * @throws AlibabaServiceException           当API调用失败时
     */
    Mono<ProductDescGenerateResponse> generateProductDesc(ProductDescGenerateRequestRecord request);

    /**
     * 图片翻译
     * <p>
     * 专为电商图片定制的翻译服务，支持18种语向，精确处理图片内的复杂文本布局。 确保翻译内容与图片内容匹配，帮助电商平台和开发者轻松实现图像内容的多语言转换与呈现。
     *
     * @param request 图片翻译请求参数
     * @return 翻译结果，包含原始图片和翻译后的图片信息
     */
    Mono<ImageTranslateResponse> translateImage(ImageTranslateRequestRecord request);

    /**
     * 图片裁剪
     * <p>
     * 对输入的图像像素尺寸进行调整，支持自动识别图像主体区域， 将裁剪后的各类尺寸，适配各种场景设计需求。
     *
     * @param request 裁剪请求参数，包含： - imageUrl: 源图片URL，图片尺寸最大不超过100*100像素，小于5000*5000像素（必填） - width: 宽度，期望裁剪成的图片宽度，单位：像素，取值范围：100-5000（必填） - height:
     *                高度，期望裁剪成的图片高度，单位：像素，取值范围：100-5000（必填）
     * @return 裁剪结果，包含裁剪后的图片URL
     * @throws AlibabaServiceValidationException 当必填参数为空或格式错误时
     * @throws AlibabaServiceException           当API调用失败时
     */
    Mono<ImageCutResponse> cutImage(ImageCutRequestRecord request);

    /**
     * 图片高清放大
     * <p>
     * 利用AI算法对图片进行2-4倍的高分辨率放大，有效提升部分细节的清晰度， 改善图像纹理细节，全面提高图像清晰度与表现质量。
     *
     * @param request 放大请求参数，包含： - imageUrl: 源图片URL，图片尺寸应大于100*100像素，小于3000*5000像素（必填） - upscaleFactor: 放大倍数，默认为2，支持2~4（必填）
     * @return 放大结果，包含放大后的图片URL和尺寸信息
     * @throws AlibabaServiceValidationException 当必填参数为空或格式错误时
     * @throws AlibabaServiceException           当API调用失败时
     */
    Mono<ImageEnlargeResponse> enlargeImage(ImageEnlargeRequestRecord request);

    /**
     * 图片智能抠图
     * <p>
     * 自动识别图像中的显著主体，将主体从背景中分离，返回白色或透明背景的主体图像。 同时该产品还提供背景置换及定制尺寸选择，优化商品展示效果。
     *
     * @param request 抠图请求参数，包含： - imageUrl: 源图片URL（必填） - backgroundBGR: 背景颜色，默认为透明背景（可选） - height: 高度，指定返回的图像高度（可选） - width: 宽度，指定返回的图像宽度（可选）
     * @return 抠图结果，包含处理后的图片URL
     * @throws AlibabaServiceValidationException 当必填参数为空或格式错误时
     * @throws AlibabaServiceException           当API调用失败时
     */
    Mono<ImageMattingResponse> mattingImage(ImageMattingRequestRecord request);

    /**
     * 图片智能消除
     * <p>
     * 去除图片中的文字、特定标识、遮挡手势和牛皮癣，可提高图像使用内的美观度。
     *
     * @param request 消除请求参数，包含： - imageUrl: 源图片URL，支持JPG、JPEG、PNG、BMP格式（必填） - noobjRemoveCharacter: 非主体消除文字（可选） - noobjRemoveLogo: 非主体消除Logo（可选） - noobjRemoveNpx: 非主体消除牛皮癣（可选） -
     *                noobjRemoveQrcode: 非主体消除二维码（可选） - noobjRemoveWatermark: 非主体消除水印（可选） - objRemoveCharacter: 主体消除文字（可选） - objRemoveLogo: 主体消除Logo（可选） - objRemoveNpx: 主体消除牛皮癣（可选）
     *                - objRemoveQrcode: 主体消除二维码（可选） - objRemoveWatermark: 主体消除水印（可选）
     * @return 消除结果，包含处理后的图片URL
     * @throws AlibabaServiceValidationException 当必填参数为空或格式错误时
     * @throws AlibabaServiceException           当API调用失败时
     */
    Mono<ImageRemoveResponse> removeImage(ImageRemoveRequestRecord request);

    /**
     * 图像元素识别
     * <p>
     * 专为电商图片定制的元素识别服务，支持识别图片主体和非主体区域的文字、Logo、水印及含字色块等元素。 能够深入挖掘图像中的细节，帮助用户快速识别图片中的各种元素，提升图像筛选效率。
     *
     * @param request 识别请求参数，包含： - imageUrl: 图片URL（必填） - objectDetectElements: 检测图片主体上的元素（可选） - nonObjectDetectElements: 检测图片非主体上的元素（可选） - returnCharacter: 是否返回识别的文字OCR结果（可选） -
     *                returnBorderPixel: 是否返回主体边缘像素值（可选） - returnProductProp: 是否返回图像主体属性占比（可选） - returnProductNum: 是否返回主体数量（可选） - returnCharacterProp: 是否返回文字占比比例（可选）
     * @return 识别结果，包含图片中各类元素的识别结果
     * @throws AlibabaServiceValidationException 当必填参数为空或格式错误时
     * @throws AlibabaServiceException           当API调用失败时
     */
    Mono<ImageElementsRecognitionResponse> recognizeImageElements(ImageElementsRecognitionRequestRecord request);
}