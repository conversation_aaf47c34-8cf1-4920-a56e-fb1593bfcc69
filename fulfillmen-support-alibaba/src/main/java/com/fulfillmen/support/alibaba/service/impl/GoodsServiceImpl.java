/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service.impl;

import com.fulfillmen.support.alibaba.api.ApiPaths;
import com.fulfillmen.support.alibaba.api.GoodsAPI;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsCouponClaimRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsDetailRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsImageSearchRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsImageUploadRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsKeywordNavigationRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsRecommendRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsRelatedRecommendRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsSearchRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsSellerRequestRecord;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsCouponClaimResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsDetailResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsImageSearchResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsImageUploadResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsKeywordNavigationResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsRecommendResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsRelatedRecommendResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsSearchResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsSellerResponse;
import com.fulfillmen.support.alibaba.autoconfigure.AlibabaProperties;
import com.fulfillmen.support.alibaba.service.IGoodsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 商品服务实现类
 *
 * <AUTHOR>
 * @created 2025-01-16
 */
@Slf4j
@Service
public class GoodsServiceImpl extends BaseAlibabaServiceImpl implements IGoodsService {

    private static final String SERVICE_NAME = "商品服务";
    private final GoodsAPI goodsAPI;

    public GoodsServiceImpl(GoodsAPI goodsAPI, AlibabaProperties alibabaProperties) {
        super(alibabaProperties);
        this.goodsAPI = goodsAPI;
    }

    @Override
    protected String getServiceName() {
        return SERVICE_NAME;
    }

    @Override
    public Mono<GoodsSearchResponse> searchGoods(GoodsSearchRequestRecord request) {
        log.debug("开始处理商品搜索请求: {}", request);
        return wrapWithErrorHandler("商品搜索", request, ApiPaths.GoodsAPI.SEARCH_GOODS, formParams -> goodsAPI
            .searchGoods(appKey, formParams), "offerQueryParam");
    }

    @Override
    public Mono<GoodsImageSearchResponse> searchGoodsByImage(GoodsImageSearchRequestRecord request) {
        log.debug("开始处理图片搜索请求: {}", request);
        return wrapWithErrorHandler("图片搜索", request, ApiPaths.GoodsAPI.SEARCH_GOODS_BY_IMAGE, formParams -> goodsAPI
            .searchGoodsByImage(appKey, formParams), "offerQueryParam");
    }

    @Override
    public Mono<GoodsImageUploadResponse> uploadImage(GoodsImageUploadRequestRecord request) {
        log.debug("开始处理图片上传请求: {}", request);
        return wrapWithErrorHandler("图片上传", request, ApiPaths.GoodsAPI.UPLOAD_IMAGE, formParams -> goodsAPI
            .uploadImage(appKey, formParams), "uploadImageParam");
    }

    @Override
    public Mono<GoodsDetailResponse> getGoodsDetail(GoodsDetailRequestRecord request) {
        log.debug("开始处理商品详情请求: {}", request);
        return wrapWithErrorHandler("商品详情", request, ApiPaths.GoodsAPI.GET_GOODS_DETAIL, formParams -> goodsAPI
            .getGoodsDetail(appKey, formParams), "offerDetailParam");
    }

    @Override
    public Mono<GoodsSellerResponse> getSellerGoods(GoodsSellerRequestRecord request) {
        log.debug("开始处理卖家商品请求: {}", request);
        return wrapWithErrorHandler("卖家商品", request, ApiPaths.GoodsAPI.GET_SELLER_GOODS, formParams -> goodsAPI
            .getSellerGoods(appKey, formParams), "offerQueryParam");
    }

    @Override
    public Mono<GoodsRecommendResponse> recommendGoods(GoodsRecommendRequestRecord request) {
        log.debug("开始处理商品推荐请求: {}", request);
        return wrapWithErrorHandler("商品推荐", request, ApiPaths.GoodsAPI.RECOMMEND_GOODS, formParams -> goodsAPI
            .recommendGoods(appKey, formParams), "recommendOfferParam");
    }

    @Override
    public Mono<GoodsKeywordNavigationResponse> getKeywordNavigation(GoodsKeywordNavigationRequestRecord request) {
        log.debug("开始处理关键词导航请求: {}", request);
        return wrapWithErrorHandler("关键词导航", request, ApiPaths.GoodsAPI.KEYWORD_NAVIGATION, formParams -> goodsAPI
            .getKeywordNavigation(appKey, formParams), "snParams");
    }

    @Override
    public Mono<GoodsRelatedRecommendResponse> getRelatedRecommend(GoodsRelatedRecommendRequestRecord request) {
        log.debug("开始处理相关商品推荐请求: {}", request);
        return wrapWithErrorHandler("相关商品推荐", request, ApiPaths.GoodsAPI.GET_RELATED_RECOMMEND, formParams -> goodsAPI
            .getRelatedRecommend(appKey, formParams), "relatedQueryParams");
    }

    @Override
    public Mono<GoodsCouponClaimResponse> claimCoupon(GoodsCouponClaimRequestRecord request) {
        log.debug("开始处理优惠券领取请求: {}", request);
        return wrapWithErrorHandler("优惠券领取", request, ApiPaths.GoodsAPI.CLAIM_COUPON, formParams -> goodsAPI
            .claimCoupon(appKey, formParams));
    }
}
