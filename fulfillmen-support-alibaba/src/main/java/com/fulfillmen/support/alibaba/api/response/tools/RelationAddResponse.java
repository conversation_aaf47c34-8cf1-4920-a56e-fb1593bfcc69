/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.tools;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 买卖家分销关系添加响应
 * <p>
 * 添加买卖家分销关系的响应结果。
 *
 * <AUTHOR>
 * @created 2025-01-16
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao:alibaba.fenxiao.relationadd-1">API文档</a>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RelationAddResponse extends BaseAlibabaResponse {

    /**
     * 响应结果
     */
    private Result result;

    @Data
    public static class Result {

        /**
         * 是否成功
         */
        private Boolean success;

        /**
         * 错误码，成功时为200
         */
        private String code;

        /**
         * 错误描述，成功时为"操作成功"
         */
        private String message;

        /**
         * 操作结果，true表示添加成功
         */
        private Boolean result;
    }
}