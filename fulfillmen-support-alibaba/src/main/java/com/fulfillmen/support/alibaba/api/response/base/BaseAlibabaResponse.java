/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.base;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 1688 API 响应基类
 * <pre>
 * 由于1688 API的响应格式不统一，因此需要在每个响应类中手动映射响应字段
 * </pre>
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Data
public class BaseAlibabaResponse {

    /**
     * 是否成功（字符串类型） 注：部分老接口使用字符串类型表示成功状态
     */
    private String succes;

    /**
     * 响应码 注：部分接口使用code表示响应码，部分接口使用errorCode表示错误码
     */
    private String code;

    /**
     * 响应消息 注：部分接口使用msg表示响应消息，部分接口使用message表示响应消息
     */
    private String message;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误描述
     */
    private String errorMessage;

    /**
     * 是否成功（布尔类型） 注：新接口统一使用布尔类型表示成功状态
     */
    private Boolean success;

    /**
     * 错误信息（别名） 注：部分接口使用errorMsg代替errorMessage
     */
    @JsonProperty("errorMsg")
    private String errorMsg;
}