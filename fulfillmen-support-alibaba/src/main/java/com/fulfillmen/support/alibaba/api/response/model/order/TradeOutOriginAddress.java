/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 外部原始地址信息
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Data
public class TradeOutOriginAddress {

    /**
     * 省
     */
    @JsonProperty("province")
    private Place province;

    /**
     * 市
     */
    @JsonProperty("city")
    private Place city;

    /**
     * 区
     */
    @JsonProperty("area")
    private Place area;

    /**
     * 镇/街道
     */
    @JsonProperty("town")
    private Place town;

    /**
     * 详细地址
     */
    @JsonProperty("address")
    private String address;

    /**
     * 邮编
     */
    @JsonProperty("postCode")
    private String postCode;

    /**
     * 地址信息
     */
    @Data
    public static class Place {

        /**
         * 地址code
         */
        @JsonProperty("code")
        private String code;

        /**
         * 地址name
         */
        @JsonProperty("name")
        private String name;
    }
}