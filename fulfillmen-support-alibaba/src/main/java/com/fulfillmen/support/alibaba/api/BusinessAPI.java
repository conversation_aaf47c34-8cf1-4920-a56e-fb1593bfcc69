/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api;

import com.fulfillmen.support.alibaba.api.response.business.RankQueryResponse;
import com.fulfillmen.support.alibaba.api.response.business.SellTrendResponse;
import com.fulfillmen.support.alibaba.api.response.business.TopKeywordResponse;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * 阿里巴巴业务类API
 * <p>
 * 包含以下接口： 1. 查询榜单列表 - 获取指定榜单的商品信息 2. 商品热搜词 - 获取指定条件下的热搜关键词 3. 商品每日销售数量趋势 - 获取商品在指定时间段内的每日销售数量趋势
 *
 * <AUTHOR>
 * @created 2025-01-23
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:product.topList.query-1">查询榜单列表</a>
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:product.search.topKeyword-1">商品热搜词</a>
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:product.analyze.getPerdaySellQuantityTrendNew-1">商品每日销售数量趋势</a>
 */
@HttpExchange("/")
public interface BusinessAPI {

    /**
     * 查询榜单列表
     * <p>
     * 通过榜单ID批量拉取池中商品数据，支持分页查询和排序。
     *
     * @param appKey 应用key
     * @param params 请求参数，包含： - rankId: 榜单ID（必填） - rankType: 榜单类型（必填） - limit: 榜单商品个数，最多20（必填） - language: 榜单商品语言（必填）
     * @return 榜单列表数据
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:product.topList.query-1">查询榜单列表</a>
     */
    @PostExchange(ApiPaths.BusinessAPI.RANK_QUERY)
    Mono<RankQueryResponse> queryRankList(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 获取商品热搜词
     * <p>
     * 获取指定条件下的热搜关键词，支持多语言。
     *
     * @param appKey 应用key
     * @param params 请求参数，包含： - country: 语言，参考开发参考枚举（必填） - sourceId: 查询id，如类目id（必填） - hotKeywordType: 热搜类型，目前只提供类目维度（必填）
     * @return 热搜词列表数据
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:product.search.topKeyword-1">商品热搜词</a>
     */
    @PostExchange(ApiPaths.BusinessAPI.TOP_KEYWORD)
    Mono<TopKeywordResponse> getTopKeywords(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 获取商品每日销售数量趋势
     * <p>
     * 获取商品在指定时间段内的每日销售数量趋势数据。 注意：查询时间段最多支持1个月。
     *
     * @param appKey 应用key
     * @param params 请求参数，包含： - offerId: 商品id（必填） - startDate: 查询起始时间，格式：yyyyMMdd（必填） - endDate: 查询截止时间，格式：yyyyMMdd（必填）
     * @return 商品每日销售数量趋势数据
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:product.analyze.getPerdaySellQuantityTrendNew-1">商品每日销售数量趋势</a>
     */
    @PostExchange(ApiPaths.BusinessAPI.SELL_TREND)
    Mono<SellTrendResponse> getSellTrend(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);
}
