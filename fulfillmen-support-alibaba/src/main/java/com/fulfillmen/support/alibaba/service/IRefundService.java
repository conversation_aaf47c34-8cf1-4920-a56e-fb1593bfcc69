/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service;

import com.fulfillmen.support.alibaba.api.request.refund.RefundBuyerListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundBuyerOrderViewRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundBuyerSubmitRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundBuyerViewRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundCancelRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundCreateRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundOpLogListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundReasonListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundUploadEvidenceRequestRecord;
import com.fulfillmen.support.alibaba.api.response.refund.RefundBuyerListResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundBuyerOrderViewResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundBuyerSubmitResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundBuyerViewResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundCancelResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundCreateResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundOpLogListResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundReasonListResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundUploadEvidenceResponse;
import reactor.core.publisher.Mono;

/**
 * 1688退款服务接口
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
public interface IRefundService {

    /**
     * 取消退款申请
     *
     * @param request 取消退款申请请求
     * @return 取消退款申请响应
     */
    Mono<RefundCancelResponse> cancelRefund(RefundCancelRequestRecord request);

    /**
     * 查询退款单列表
     *
     * @param request 查询退款单列表请求
     * @return 退款单列表响应
     */
    Mono<RefundBuyerListResponse> queryRefundBuyerList(RefundBuyerListRequestRecord request);

    /**
     * 提交退款单物流信息
     *
     * @param request 提交退款单物流信息请求
     * @return 退款单物流信息提交响应
     */
    Mono<RefundBuyerSubmitResponse> submitRefundBuyerInfo(RefundBuyerSubmitRequestRecord request);

    /**
     * 上传退款凭证
     *
     * @param request 上传退款凭证请求
     * @return 退款凭证上传响应
     */
    Mono<RefundUploadEvidenceResponse> uploadRefundEvidence(RefundUploadEvidenceRequestRecord request);

    /**
     * 查询退款原因列表
     *
     * @param request 查询退款原因列表请求
     * @return 退款原因列表响应
     */
    Mono<RefundReasonListResponse> getRefundReasonList(RefundReasonListRequestRecord request);

    /**
     * 创建退款申请
     *
     * @param request 创建退款申请请求
     * @return 退款申请创建响应
     */
    Mono<RefundCreateResponse> createRefund(RefundCreateRequestRecord request);

    /**
     * 查询退款单详情(根据退款单ID)
     *
     * @param request 查询退款单详情请求
     * @return 退款单详情响应
     */
    Mono<RefundBuyerViewResponse> queryRefundBuyerView(RefundBuyerViewRequestRecord request);

    /**
     * 查询退款单操作日志
     *
     * @param request 查询退款单操作日志请求
     * @return 退款单操作日志响应
     */
    Mono<RefundOpLogListResponse> queryRefundOpLogList(RefundOpLogListRequestRecord request);

    /**
     * 查询退款单详情(根据订单ID)
     *
     * @param request 查询退款单详情请求
     * @return 退款单详情响应
     */
    Mono<RefundBuyerOrderViewResponse> queryRefundBuyerOrderView(RefundBuyerOrderViewRequestRecord request);
}