/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.pay;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 获取跨境宝支付链接响应
 * <p>
 * 包含收银台支付链接和不能批量支付的订单列表信息
 *
 * <AUTHOR>
 * @created 2025-01-10
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.crossBorderPay.url.get-1">API文档</a>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CrossBorderPayUrlResponse extends BaseAlibabaResponse {

    /**
     * 收银台支付链接
     * <p>
     * 用于跳转到跨境宝支付页面
     */
    private String payUrl;

    /**
     * 不能批量支付的订单列表
     * <p>
     * 由于额度及风控原因不能批量支付的订单ID列表
     */
    private List<Long> cantPayOrderList;
}