/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import lombok.extern.slf4j.Slf4j;

/**
 * 阿里巴巴API日期反序列化器
 * <p>
 * 支持以下日期格式：
 * 1. yyyyMMddHHmmssSSS+timezone (如: 20250625174800000+0800)
 * 2. yyyy-MM-dd HH:mm:ss (如: 2025-06-25 17:34:28)
 *
 * <AUTHOR>
 * @created 2025-01-12
 */
@Slf4j
public class AlibabaDateDeserializer extends JsonDeserializer<LocalDateTime> {

    private static final DateTimeFormatter ALIBABA_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
    private static final DateTimeFormatter STANDARD_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public LocalDateTime deserialize(JsonParser parser, DeserializationContext context) throws IOException {
        String dateStr = parser.getText();

        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }

        try {
            // 处理阿里巴巴特殊格式: yyyyMMddHHmmssSSS+timezone
            if (dateStr.contains("+") && dateStr.length() > 17) {
                return parseAlibabaDateFormat(dateStr);
            }

            // 处理标准格式: yyyy-MM-dd HH:mm:ss
            if (dateStr.contains("-") && dateStr.contains(":")) {
                return parseStandardDateFormat(dateStr);
            }

            log.warn("无法解析日期格式: {}", dateStr);
            return null;

        } catch (Exception e) {
            log.error("日期解析失败: {}", dateStr, e);
            return null;
        }
    }

    /**
     * 解析阿里巴巴日期格式: yyyyMMddHHmmssSSS+timezone
     */
    private LocalDateTime parseAlibabaDateFormat(String dateStr) throws DateTimeParseException {
        // 分离日期部分和时区部分
        int timezoneIndex = dateStr.lastIndexOf('+');
        if (timezoneIndex == -1) {
            timezoneIndex = dateStr.lastIndexOf('-');
        }

        String datePart = dateStr.substring(0, timezoneIndex);
        String timezonePart = dateStr.substring(timezoneIndex);

        // 解析带时区的日期时间
        String fullDateStr = datePart + timezonePart;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS[XXX][XX][X]");

        try {
            OffsetDateTime offsetDateTime = OffsetDateTime.parse(fullDateStr, formatter);
            return offsetDateTime.toLocalDateTime();
        } catch (DateTimeParseException e) {
            // 如果解析失败，尝试只解析日期部分（忽略时区）
            return LocalDateTime.parse(datePart, ALIBABA_DATE_FORMATTER);
        }
    }

    /**
     * 解析标准日期格式: yyyy-MM-dd HH:mm:ss
     */
    private LocalDateTime parseStandardDateFormat(String dateStr) throws DateTimeParseException {
        return LocalDateTime.parse(dateStr, STANDARD_DATE_FORMATTER);
    }
}
