/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service.impl;

import com.fulfillmen.support.alibaba.api.ApiPaths;
import com.fulfillmen.support.alibaba.api.CategoryAPI;
import com.fulfillmen.support.alibaba.api.request.category.CategoryAttributeRequestRecord;
import com.fulfillmen.support.alibaba.api.request.category.CategoryRequestRecord;
import com.fulfillmen.support.alibaba.api.request.category.CategoryTranslationByIdRequestRecord;
import com.fulfillmen.support.alibaba.api.request.category.CategoryTranslationRequestRecord;
import com.fulfillmen.support.alibaba.api.response.category.CategoryAttributeResponse;
import com.fulfillmen.support.alibaba.api.response.category.CategoryResponse;
import com.fulfillmen.support.alibaba.api.response.category.CategoryTranslationByIdResponse;
import com.fulfillmen.support.alibaba.api.response.category.CategoryTranslationResponse;
import com.fulfillmen.support.alibaba.autoconfigure.AlibabaProperties;
import com.fulfillmen.support.alibaba.service.ICategoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 类目服务实现类
 *
 * <AUTHOR>
 * @created 2025-01-16
 */
@Slf4j
@Service
public class CategoryServiceImpl extends BaseAlibabaServiceImpl implements ICategoryService {

    private static final String SERVICE_NAME = "类目服务";
    private final CategoryAPI categoryAPI;

    public CategoryServiceImpl(CategoryAPI categoryAPI,
        AlibabaProperties alibabaProperties) {
        super(alibabaProperties);
        this.categoryAPI = categoryAPI;
    }

    @Override
    protected String getServiceName() {
        return SERVICE_NAME;
    }

    @Override
    public Mono<CategoryResponse> getCategory(CategoryRequestRecord request) {
        log.debug("开始处理类目查询请求: {}", request);
        return wrapWithErrorHandler("获取类目信息", request, ApiPaths.CategoryAPI.GET_CATEGORY, formParams -> categoryAPI
            .getCategory(appKey, formParams));
    }

    @Override
    public Mono<CategoryTranslationByIdResponse> getTranslationById(CategoryTranslationByIdRequestRecord request) {
        log.debug("开始处理类目翻译查询请求: {}", request);
        return wrapWithErrorHandler("根据ID获取类目翻译", request, ApiPaths.CategoryAPI.GET_TRANSLATION_BY_ID, formParams -> categoryAPI
            .getTranslationById(appKey, formParams));
    }

    @Override
    public Mono<CategoryTranslationResponse> getTranslation(CategoryTranslationRequestRecord request) {
        log.debug("开始处理类目翻译请求: {}", request);
        return wrapWithErrorHandler("获取类目翻译", request, ApiPaths.CategoryAPI.GET_TRANSLATION, formParams -> categoryAPI
            .getTranslation(appKey, formParams));
    }

    @Override
    public Mono<CategoryAttributeResponse> getAttributes(CategoryAttributeRequestRecord request) {
        log.debug("开始处理类目属性请求: {}", request);
        return wrapWithErrorHandler("获取类目属性", request, ApiPaths.CategoryAPI.GET_ATTRIBUTES, formParams -> categoryAPI
            .getAttributes(appKey, formParams));
    }
}