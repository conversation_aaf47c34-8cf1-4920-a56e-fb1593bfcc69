/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.tools;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 拉取商品池中商品数据请求记录
 * <p>
 * 通过商品池ID批量拉取池中商品数据，支持分页查询和排序。 可以根据类目ID过滤商品，支持多语言。
 *
 * @param offerPoolId 商品池ID，用于标识要查询的商品池
 * @param cateId      类目ID，可选参数，用于按类目筛选商品
 * @param taskId      任务ID，用于标识本次查询任务
 * @param language    语言代码，可选参数，例如：en表示英语
 * @param pageNo      页码，从1开始的分页页码
 * @param pageSize    每页数量，最大不超过50条
 * @param sortField   排序字段，可选值：order1m（近1月订单数）/buyer1m（近1月买家数）
 * @param sortType    排序规则，可选值：ASC（升序）/DESC（降序）
 * <AUTHOR>
 * @created 2025-01-16
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:pool.product.pull-1">API文档</a>
 */
@Builder
public record PoolProductPullRequestRecord(
    @JsonProperty("offerPoolId") Long offerPoolId,
    @JsonProperty("cateId") Long cateId,
    @JsonProperty("taskId") String taskId,
    @JsonProperty("language") String language,
    @JsonProperty("pageNo") Integer pageNo,
    @JsonProperty("pageSize") Integer pageSize,
    @JsonProperty("sortField") String sortField,
    @JsonProperty("sortType") String sortType
) implements BaseAlibabaRequestRecord {

    private static final int DEFAULT_PAGE_SIZE = 20;
    private static final int MAX_PAGE_SIZE = 50;

    @Override
    public void requireParams() {
        assertNotNull(offerPoolId, "商品池ID不能为空");
        assertNotBlank(taskId, "任务ID不能为空");
        assertNotNull(pageNo, "页码不能为空");
        assertNotNull(pageSize, "每页数量不能为空");
        assertTrue(pageNo > 0, "页码必须大于0");
        assertTrue(pageSize > 0 && pageSize <= MAX_PAGE_SIZE, "每页数量必须大于0且不超过" + MAX_PAGE_SIZE);
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        Map<String, Object> queryParam = new HashMap<>();

        queryParam.put("offerPoolId", offerPoolId);
        if (cateId != null) {
            queryParam.put("cateId", cateId);
        }
        queryParam.put("taskId", taskId);
        if (language != null) {
            queryParam.put("language", language);
        }
        queryParam.put("pageNo", pageNo);
        queryParam.put("pageSize", Math.min(pageSize, MAX_PAGE_SIZE));
        if (sortField != null) {
            queryParam.put("sortField", sortField);
        }
        if (sortType != null) {
            queryParam.put("sortType", sortType);
        }

        params.put("offerPoolQueryParam", toJsonString(queryParam));
        return params;
    }
}