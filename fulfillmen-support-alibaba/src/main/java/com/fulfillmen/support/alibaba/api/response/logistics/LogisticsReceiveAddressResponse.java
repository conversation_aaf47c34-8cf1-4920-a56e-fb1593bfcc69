/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.logistics;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 买家获取保存的收货地址信息列表响应
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LogisticsReceiveAddressResponse extends BaseAlibabaResponse {

    /**
     * 返回结果
     */
    private Result result;

    @Data
    public static class Result {

        /**
         * 收货地址列表
         */
        private List<ReceiveAddressItem> receiveAddressItems;
    }

    @Data
    public static class ReceiveAddressItem {

        /**
         * addressId 示例: 322683081
         */
        private Long id;

        /**
         * 收货人姓名 示例: 张三
         */
        private String fullName;

        /**
         * 街道地址，不包括省市编码 示例: 网商路699
         */
        private String address;

        /**
         * 邮编 示例: 340000
         */
        private String post;

        /**
         * 电话 示例: 0517-8888888
         */
        private String phone;

        /**
         * 手机号 示例: 18012345678
         */
        private String mobilePhone;

        /**
         * 地址区域编码 示例: 330108
         */
        private String addressCode;

        /**
         * 地址区域编码对应的文本（包括国家、省、城市） 示例: 浙江省 杭州市 滨江区
         */
        private String addressCodeText;

        /**
         * 是否为默认 示例: false
         */
        private Boolean isDefault;

        /**
         * 镇编码 示例: 123
         */
        private String townCode;

        /**
         * 镇地址 示例: 长河镇
         */
        private String townName;
    }
}