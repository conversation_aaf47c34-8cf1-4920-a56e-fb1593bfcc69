/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.logistics;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 获取所有的物流公司列表响应
 *
 * <AUTHOR>
 * @created 2025-01-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LogisticsCompanyListResponse extends BaseAlibabaResponse {

    /**
     * 物流公司列表
     */
    private List<OpLogisticsCompanyModel> result;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误码描述
     */
    private String errorMessage;

    /**
     * 扩展错误码描述
     */
    private String extErrorMessage;

    @Data
    public static class OpLogisticsCompanyModel {

        /**
         * 物流公司ID
         */
        private Long id;

        /**
         * 物流公司名称
         */
        private String companyName;

        /**
         * 物流公司编号
         */
        private String companyNo;

        /**
         * 物流公司服务电话
         */
        private String companyPhone;

        /**
         * 是否支持打印
         */
        private Boolean supportPrint;

        /**
         * 全拼
         */
        private String spelling;
    }
}