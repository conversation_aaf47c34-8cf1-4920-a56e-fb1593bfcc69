/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.feedback;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 国家站物流单回传请求
 *
 * <AUTHOR>
 * @created 2025-01-17
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:trade.cross.logisticsOrderSync-1">物流单回传 API</a>
 */
@Builder
public record LogisticsOrderSyncRequestRecord(/**
                                               * 物流订单参数
                                               */
OrderLogisticsParam orderLogisticsParam) implements BaseAlibabaRequestRecord {

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        try {
            params.put("orderLogisticsParam", toJsonString(orderLogisticsParam));
        } catch (Exception e) {
            throw new AlibabaServiceValidationException("转换物流订单参数失败", e);
        }
        return params;
    }

    @Override
    public void requireParams() throws AlibabaServiceValidationException {
        assertNotNull(orderLogisticsParam, "物流订单参数不能为空");
        orderLogisticsParam.validate();
    }

    /**
     * 物流订单参数
     */
    public record OrderLogisticsParam(
        /**
         * 物流单号
         */
        @JsonProperty("logisticsId") String logisticsId,

        /**
         * 物流公司代码
         */
        @JsonProperty("logisticsCompanyCode") String logisticsCompanyCode,

        /**
         * 物流公司名称
         */
        @JsonProperty("logisticsCompanyName") String logisticsCompanyName,

        /**
         * 订单ID
         */
        @JsonProperty("orderId") Long orderId,

        /**
         * 运单号
         */
        @JsonProperty("logisticsBillNo") String logisticsBillNo,

        /**
         * 发货时间
         */
        @JsonProperty("gmtSend") String gmtSend,

        /**
         * 物流状态
         */
        @JsonProperty("status") String status,

        /**
         * 物流跟踪信息
         */
        @JsonProperty("logisticsTraceList") String logisticsTraceList
    ) {

        void validate() throws AlibabaServiceValidationException {
            if (logisticsId == null || logisticsId.isBlank()) {
                throw new AlibabaServiceValidationException("物流单号不能为空");
            }
            if (logisticsCompanyCode == null || logisticsCompanyCode.isBlank()) {
                throw new AlibabaServiceValidationException("物流公司代码不能为空");
            }
            if (logisticsCompanyName == null || logisticsCompanyName.isBlank()) {
                throw new AlibabaServiceValidationException("物流公司名称不能为空");
            }
            if (orderId == null) {
                throw new AlibabaServiceValidationException("订单ID不能为空");
            }
            if (logisticsBillNo == null || logisticsBillNo.isBlank()) {
                throw new AlibabaServiceValidationException("运单号不能为空");
            }
            if (gmtSend == null || gmtSend.isBlank()) {
                throw new AlibabaServiceValidationException("发货时间不能为空");
            }
            if (status == null || status.isBlank()) {
                throw new AlibabaServiceValidationException("物流状态不能为空");
            }
            if (logisticsTraceList == null || logisticsTraceList.isBlank()) {
                throw new AlibabaServiceValidationException("物流跟踪信息不能为空");
            }
        }
    }
}