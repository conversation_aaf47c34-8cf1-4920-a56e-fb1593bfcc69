/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.order;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 获取订单列表(买家视角)响应
 */
@Data
public class OrderBuyerListResponse {

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 总记录数
     */
    private Long totalRecord;

    /**
     * 订单列表
     */
    private List<TradeInfo> result;

    @Data
    public static class TradeInfo {

        /**
         * 基础信息
         */
        private BaseInfo baseInfo;

        /**
         * 商品明细信息
         */
        private List<ProductItem> productItems;

        /**
         * 物流信息
         */
        private List<LogisticsInfo> logisticsInfos;

        /**
         * 发票信息
         */
        private InvoiceInfo invoiceInfo;

        /**
         * 订单备注
         */
        private String memo;

        /**
         * 订单标签列表
         */
        private List<String> orderTags;
    }

    @Data
    public static class BaseInfo {

        /**
         * 业务类型
         */
        private String businessType;

        /**
         * 买家ID
         */
        private String buyerID;

        /**
         * 完成时间
         */
        private String completeTime;

        /**
         * 创建时间
         */
        private String createTime;

        /**
         * 订单ID
         */
        private Long id;

        /**
         * 修改时间
         */
        private String modifyTime;

        /**
         * 退款金额
         */
        private BigDecimal refund;

        /**
         * 卖家ID
         */
        private String sellerID;

        /**
         * 运费
         */
        private BigDecimal shippingFee;

        /**
         * 订单状态
         */
        private String status;

        /**
         * 订单总金额
         */
        private BigDecimal totalAmount;

        /**
         * 优惠金额
         */
        private BigDecimal discount;

        /**
         * 买家联系信息
         */
        private Contact buyerContact;

        /**
         * 卖家联系信息
         */
        private Contact sellerContact;

        /**
         * 交易类型
         */
        private String tradeType;

        /**
         * 退款支付金额
         */
        private BigDecimal refundPayment;

        /**
         * 订单ID(字符串格式)
         */
        private String idOfStr;

        /**
         * 支付宝交易号
         */
        private String alipayTradeId;

        /**
         * 收货人信息
         */
        private ReceiverInfo receiverInfo;

        /**
         * 买家登录ID
         */
        private String buyerLoginId;

        /**
         * 卖家登录ID
         */
        private String sellerLoginId;

        /**
         * 买家用户ID
         */
        private Long buyerUserId;

        /**
         * 卖家用户ID
         */
        private Long sellerUserId;

        /**
         * 买家支付宝ID
         */
        private String buyerAlipayId;

        /**
         * 卖家支付宝ID
         */
        private String sellerAlipayId;

        /**
         * 关闭原因
         */
        private String closeReason;

        /**
         * 商品总金额
         */
        private BigDecimal sumProductPayment;

        /**
         * 是否分阶段支付
         */
        private Boolean stepPayAll;

        /**
         * 支付状态
         */
        private String payStatus;

        /**
         * 支付时间
         */
        private String payTime;

        /**
         * 订单来源
         */
        private String orderSource;

        /**
         * 订单类型
         */
        private String orderType;

        /**
         * 订单子类型
         */
        private String subOrderType;

        /**
         * 订单流程状态
         */
        private String flowStatus;

        /**
         * 订单流程状态描述
         */
        private String flowStatusText;
    }

    @Data
    public static class ProductItem {

        /**
         * 商品金额
         */
        private BigDecimal itemAmount;

        /**
         * 商品名称
         */
        private String name;

        /**
         * 商品价格
         */
        private BigDecimal price;

        /**
         * 商品ID
         */
        private Long productID;

        /**
         * 商品图片URL列表
         */
        private List<String> productImgUrl;

        /**
         * 商品快照URL
         */
        private String productSnapshotUrl;

        /**
         * 购买数量
         */
        private Integer quantity;

        /**
         * 退款金额
         */
        private BigDecimal refund;

        /**
         * SKU ID
         */
        private Long skuID;

        /**
         * 商品状态
         */
        private String status;

        /**
         * 子订单ID
         */
        private Long subItemID;

        /**
         * 商品类型
         */
        private String type;

        /**
         * 单位
         */
        private String unit;

        /**
         * 商品货号
         */
        private String productCargoNumber;

        /**
         * SKU属性信息列表
         */
        private List<SkuInfo> skuInfos;

        /**
         * 分摊的优惠金额
         */
        private BigDecimal entryDiscount;

        /**
         * 规格ID
         */
        private String specId;

        /**
         * 数量系数
         */
        private Integer quantityFactor;

        /**
         * 状态描述
         */
        private String statusStr;

        /**
         * 商品描述
         */
        private String description;

        /**
         * 商品类目ID
         */
        private Long categoryId;

        /**
         * 商品单价
         */
        private BigDecimal unitPrice;
    }

    @Data
    public static class SkuInfo {

        /**
         * 属性名称
         */
        private String name;

        /**
         * 属性值
         */
        private String value;

        /**
         * 属性ID
         */
        private Long attributeID;
    }

    @Data
    public static class Contact {

        /**
         * 联系电话
         */
        private String phone;

        /**
         * 联系人姓名
         */
        private String name;

        /**
         * 联系人IM账号
         */
        private String imInPlatform;

        /**
         * 公司名称
         */
        private String companyName;

        /**
         * 电子邮箱
         */
        private String email;

        /**
         * 手机号码
         */
        private String mobile;

        /**
         * 联系地址
         */
        private String address;
    }

    @Data
    public static class ReceiverInfo {

        /**
         * 收货人姓名
         */
        private String toFullName;

        /**
         * 区域编码
         */
        private String toDivisionCode;

        /**
         * 邮编
         */
        private String toPost;

        /**
         * 收货地址
         */
        private String toArea;

        /**
         * 省份
         */
        private String toProvince;

        /**
         * 城市
         */
        private String toCity;

        /**
         * 区县
         */
        private String toCounty;

        /**
         * 详细地址
         */
        private String toAddress;

        /**
         * 手机号码
         */
        private String toMobile;

        /**
         * 电话号码
         */
        private String toPhone;

        /**
         * 电子邮箱
         */
        private String toEmail;
    }

    @Data
    public static class LogisticsInfo {

        /**
         * 物流公司编码
         */
        private String logisticsCompanyCode;

        /**
         * 物流公司名称
         */
        private String logisticsCompanyName;

        /**
         * 运单号
         */
        private String logisticsBillNo;

        /**
         * 物流状态
         */
        private String status;

        /**
         * 物流跟踪信息
         */
        private List<TraceInfo> traces;
    }

    @Data
    public static class TraceInfo {

        /**
         * 物流节点说明
         */
        private String content;

        /**
         * 状态发生时间
         */
        private String eventTime;

        /**
         * 状态
         */
        private String status;
    }

    @Data
    public static class InvoiceInfo {

        /**
         * 发票类型
         */
        private String invoiceType;

        /**
         * 发票抬头
         */
        private String invoiceTitle;

        /**
         * 纳税人识别号
         */
        private String taxpayerIdentifier;

        /**
         * 发票金额
         */
        private BigDecimal invoiceAmount;

        /**
         * 发票状态
         */
        private String status;
    }
}