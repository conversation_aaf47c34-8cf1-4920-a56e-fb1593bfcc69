/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.pay;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Builder;

/**
 * 获取跨境宝支付链接请求
 * <p>
 * 用于获取使用跨境宝支付的支付链接，支持批量订单支付
 *
 * <AUTHOR>
 * @created 2025-01-10
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.crossBorderPay.url.get-1">API文档</a>
 */
@Builder
public record CrossBorderPayUrlRequestRecord(
    /**
     * 订单ID列表
     * <p>
     * 必填字段，最多批量30个订单，订单过多会导致超时，建议一次10个订单
     */
    List<Long> orderIdList
) implements BaseAlibabaRequestRecord {

    /**
     * 创建 CrossBorderPayUrlRequestRecord 实例
     *
     * @param orderIdList 订单ID列表
     * @return CrossBorderPayUrlRequestRecord 实例
     * @throws IllegalArgumentException 如果参数无效
     */
    public static CrossBorderPayUrlRequestRecord of(List<Long> orderIdList) {
        return new CrossBorderPayUrlRequestRecord(orderIdList);
    }

    @Override
    public void requireParams() {
        assertNotNull(orderIdList, "订单ID列表不能为空");
        assertTrue(orderIdList.size() <= 30, "订单ID列表最多30个");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("orderIdList", toJsonString(orderIdList));
        return params;
    }
}