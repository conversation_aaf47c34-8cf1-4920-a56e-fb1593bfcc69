/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service;

import com.fulfillmen.support.alibaba.api.request.category.CategoryAttributeRequestRecord;
import com.fulfillmen.support.alibaba.api.request.category.CategoryRequestRecord;
import com.fulfillmen.support.alibaba.api.request.category.CategoryTranslationByIdRequestRecord;
import com.fulfillmen.support.alibaba.api.request.category.CategoryTranslationRequestRecord;
import com.fulfillmen.support.alibaba.api.response.category.CategoryAttributeResponse;
import com.fulfillmen.support.alibaba.api.response.category.CategoryResponse;
import com.fulfillmen.support.alibaba.api.response.category.CategoryTranslationByIdResponse;
import com.fulfillmen.support.alibaba.api.response.category.CategoryTranslationResponse;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceException;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import reactor.core.publisher.Mono;

/**
 * 1688类目服务接口 提供类目查询、翻译、属性获取等功能
 *
 * <AUTHOR>
 * @created 2025-01-08
 * @since 1.0.0
 */
public interface ICategoryService {

    /**
     * 获取类目信息
     *
     * @param request 类目请求参数，包含类目ID等信息
     * @return 返回类目详细信息
     * @throws AlibabaServiceException           当API调用失败时抛出
     * @throws AlibabaServiceValidationException 当必填参数校验失败时抛出
     */
    Mono<CategoryResponse> getCategory(CategoryRequestRecord request);

    /**
     * 获取类目翻译信息（通过ID）
     *
     * @param request 类目翻译请求参数，包含类目ID和目标语言等信息
     * @return 返回指定类目ID的多语言翻译信息
     * @throws AlibabaServiceException           当API调用失败时抛出
     * @throws AlibabaServiceValidationException 当必填参数校验失败时抛出
     */
    Mono<CategoryTranslationByIdResponse> getTranslationById(CategoryTranslationByIdRequestRecord request);

    /**
     * 获取类目翻译信息（通过关键词）
     *
     * @param request 类目翻译请求参数，包含类目名称和目标语言等信息
     * @return 返回匹配关键词的类目翻译信息
     * @throws AlibabaServiceException           当API调用失败时抛出
     * @throws AlibabaServiceValidationException 当必填参数校验失败时抛出
     */
    Mono<CategoryTranslationResponse> getTranslation(CategoryTranslationRequestRecord request);

    /**
     * 获取类目属性信息
     *
     * @param request 类目属性请求参数，包含类目ID和场景信息等
     * @return 返回类目的属性定义列表
     * @throws AlibabaServiceException           当API调用失败时抛出
     * @throws AlibabaServiceValidationException 当必填参数校验失败时抛出
     */
    Mono<CategoryAttributeResponse> getAttributes(CategoryAttributeRequestRecord request);
}