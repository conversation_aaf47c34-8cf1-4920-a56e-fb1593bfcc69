/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/7/31 19:36
 * @description: todo
 * @since 1.0.0
 */
@Getter
public enum StepOrderPayStatusEnums {

//    阶段付款状态。1未付款、2已付款、8付款前取消、12溢短补付款
    /**
     * 未付款
     */
    UNPAID(1, "未付款"),
    /**
     * 已付款
     */
    PAID(2, "已付款"),
    /**
     * 付款前取消
     */
    CANCEL_BEFORE_PAY(8, "付款前取消"),
    /**
     * 溢短补付款
     */
    OVER_SHORT_PAYMENT(12, "溢短补付款");

    @JsonValue
    private final int code;
    private final String desc;

    StepOrderPayStatusEnums(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据代码获取枚举
     */
    public static StepOrderPayStatusEnums fromCode(int code) {
        for (StepOrderPayStatusEnums status : StepOrderPayStatusEnums.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

}
