/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook.exception;

import com.fulfillmen.support.alibaba.exception.AlibabaServiceException;

/**
 * Webhook处理异常 用于表示在处理Webhook消息时发生的错误 例如签名验证失败、消息格式错误等
 *
 * <AUTHOR>
 * @date 2025/7/10
 * @since 1.0.0
 */
public class WebhookProcessingException extends AlibabaServiceException {

    public WebhookProcessingException(String message) {
        super(message);
    }

    public WebhookProcessingException(String message, Throwable cause) {
        super(message, cause);
    }
}