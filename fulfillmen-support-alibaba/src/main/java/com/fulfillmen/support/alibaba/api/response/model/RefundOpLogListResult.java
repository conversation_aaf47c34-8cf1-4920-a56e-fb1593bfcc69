/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

/**
 * 退款单操作记录列表结果
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Data
public class RefundOpLogListResult {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 操作记录列表
     */
    private List<RefundOpLog> opOrderRefundOperationModels;

    @Data
    public static class RefundOpLog {

        /**
         * 操作后的退款状态
         */
        private String afterOperateStatus;

        /**
         * 操作前的退款状态
         */
        private String beforeOperateStatus;

        /**
         * 分阶段订单单向操作关闭退款的阶段ID
         */
        private Long closeRefundStepId;

        /**
         * 是否小二修改过退款单
         */
        private Boolean crmModifyRefund;

        /**
         * 描述说明
         */
        @JsonProperty("discription")
        private String description;

        /**
         * 联系人EMAIL
         */
        private String email;

        /**
         * 运单号
         */
        private String freightBill;

        /**
         * 创建时间
         */
        @JsonFormat(pattern = "yyyyMMddHHmmssSSS+0800")
        private String gmtCreate;

        /**
         * 修改时间
         */
        @JsonFormat(pattern = "yyyyMMddHHmmssSSS+0800")
        private String gmtModified;

        /**
         * 主键，退款操作记录流水号
         */
        private Long id;

        /**
         * 凭证状态(1:正常 2:后台小二屏蔽)
         */
        private Integer messageStatus;

        /**
         * 联系人手机
         */
        private String mobile;

        /**
         * 留言类型(3:小二留言给卖家和买家 4:给买家的留言 5:给卖家的留言 7:cbu的普通留言)
         */
        private Integer msgType;

        /**
         * 操作备注
         */
        private String operateRemark;

        /**
         * 操作类型
         */
        private Integer operateTypeInt;

        /**
         * 操作者memberID
         */
        private String operatorId;

        /**
         * 操作者loginID
         */
        private String operatorLoginId;

        /**
         * 操作者角色ID(买家/卖家/系统)
         */
        private Integer operatorRoleId;

        /**
         * 操作者userID
         */
        private Long operatorUserId;

        /**
         * 联系人电话
         */
        private String phone;

        /**
         * 退货地址
         */
        private String refundAddress;

        /**
         * 退款记录ID
         */
        private String refundId;

        /**
         * 卖家拒绝退款原因
         */
        private String rejectReason;

        /**
         * 凭证图片地址列表
         */
        private List<String> vouchers;

        /**
         * 物流公司详情
         */
        private LogisticsCompany logisticsCompany;
    }

    @Data
    public static class LogisticsCompany {

        /**
         * 快递公司名称
         */
        private String companyName;

        /**
         * 物流公司编号
         */
        private String companyNo;

        /**
         * 物流公司服务电话
         */
        private String companyPhone;

        /**
         * 创建时间
         */
        @JsonFormat(pattern = "yyyyMMddHHmmssSSS+0800")
        private String gmtCreate;

        /**
         * 修改时间
         */
        @JsonFormat(pattern = "yyyyMMddHHmmssSSS+0800")
        private String gmtModified;

        /**
         * ID
         */
        private Long id;

        /**
         * 全拼
         */
        private String spelling;

        /**
         * 是否支持打印
         */
        private Boolean supportPrint;
    }
}