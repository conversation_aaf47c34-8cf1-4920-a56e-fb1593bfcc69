/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service;

import com.fulfillmen.support.alibaba.api.request.feedback.AccountBusinessSaveRequestRecord;
import com.fulfillmen.support.alibaba.api.request.feedback.LogisticsOrderSyncRequestRecord;
import com.fulfillmen.support.alibaba.api.request.feedback.OrderRelationWriteRequestRecord;
import com.fulfillmen.support.alibaba.api.request.feedback.OrderSyncRequestRecord;
import com.fulfillmen.support.alibaba.api.response.feedback.AccountBusinessSaveResponse;
import com.fulfillmen.support.alibaba.api.response.feedback.LogisticsOrderSyncResponse;
import com.fulfillmen.support.alibaba.api.response.feedback.OrderRelationWriteResponse;
import com.fulfillmen.support.alibaba.api.response.feedback.OrderSyncResponse;
import reactor.core.publisher.Mono;

/**
 * 1688回传数据相关服务接口
 *
 * <AUTHOR>
 * @created 2025-01-15
 */
public interface IFeedbackService {

    /**
     * 保存账号所属业务线
     *
     * @param request 请求参数
     * @return 保存结果
     */
    Mono<AccountBusinessSaveResponse> saveAccountBusiness(AccountBusinessSaveRequestRecord request);

    /**
     * 国家站物流单回传
     *
     * @param request 请求参数
     * @return 回传结果
     */
    Mono<LogisticsOrderSyncResponse> syncLogisticsOrder(LogisticsOrderSyncRequestRecord request);

    /**
     * 回传机构真实用户订单和1688订单的映射关系
     *
     * @param request 请求参数
     * @return 回传结果
     */
    Mono<OrderRelationWriteResponse> writeOrderRelation(OrderRelationWriteRequestRecord request);

    /**
     * 下游销售订单同步
     *
     * @param request 请求参数
     * @return 同步结果
     */
    Mono<OrderSyncResponse> syncOrder(OrderSyncRequestRecord request);
}