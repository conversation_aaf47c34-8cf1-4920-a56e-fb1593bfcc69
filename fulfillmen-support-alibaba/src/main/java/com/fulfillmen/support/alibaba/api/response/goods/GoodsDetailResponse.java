/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.goods;

import java.util.List;
import lombok.Data;

/**
 * 多语言商品详情响应
 */
@Data
public class GoodsDetailResponse {

    private Result result;

    @Data
    public static class Result {

        /**
         * 是否成功
         */
        private Boolean success;

        /**
         * 返回码
         */
        private String code;

        /**
         * 提示信息
         */
        private String message;

        /**
         * 商品详情
         */
        private ProductDetail result;
    }

    @Data
    public static class ProductDetail {

        /**
         * 商品ID
         */
        private Long offerId;

        /**
         * 类目ID
         */
        private Long categoryId;

        /**
         * 类目名称
         * 注意：该字段在某些接口返回中可能不存在
         */
        private String categoryName;

        /**
         * 中文标题
         */
        private String subject;

        /**
         * 译文标题
         */
        private String subjectTrans;

        /**
         * 详情描述
         */
        private String description;

        /**
         * 主视频
         * 注意：该字段在某些商品中可能不存在
         */
        private String mainVideo;

        /**
         * 详情视频
         * 注意：该字段在某些商品中可能不存在
         */
        private String detailVideo;

        /**
         * 图片模型
         */
        private ProductImage productImage;

        /**
         * 商品CPV属性
         */
        private List<ProductAttribute> productAttribute;

        /**
         * 商品SKU
         */
        private List<ProductSkuInfo> productSkuInfos;

        /**
         * 商品销售信息
         */
        private ProductSaleInfo productSaleInfo;

        /**
         * 商品包裹试算相关数据
         */
        private ProductShippingInfo productShippingInfo;

        /**
         * 是否精选货源
         */
        private Boolean isJxhy;

        /**
         * 商家加密ID
         */
        private String sellerOpenId;

        /**
         * 最小起批量数量
         */
        private Integer minOrderQuantity;

        /**
         * 一手数量
         */
        private Integer batchNumber;

        /**
         * 商品状态
         */
        private String status;

        /**
         * 商品服务标签
         */
        private List<TagInfoList> tagInfoList;

        /**
         * 打点信息
         */
        private String traceInfo;

        /**
         * 卖家混配配置
         */
        private SellerMixSetting sellerMixSetting;

        /**
         * 商品货号
         */
        private String productCargoNumber;

        /**
         * 商家属性数据
         */
        private SellerDataInfo sellerDataInfo;

        /**
         * 商品销量
         */
        private String soldOut;

        /**
         * 渠道价格数据
         */
        private ChannelPrice channelPrice;

        /**
         * 营销信息
         */
        private PromotionModel promotionModel;

        /**
         * 商品评分
         */
        private String tradeScore;

        /**
         * 一级类目
         */
        private Long topCategoryId;

        /**
         * 二级类目
         */
        private Long secondCategoryId;

        /**
         * 三级类目
         */
        private Long thirdCategoryId;

        /**
         * 多语言卖点
         */
        private List<String> sellingPoint;

        /**
         * 商家身份
         */
        private List<String> offerIdentities;

        /**
         * 创建时间
         */
        private String createDate;

        /**
         * 跨境select状态
         */
        private String isSelect;

        /**
         * 商品证书列表
         */
        private List<CertificateList> certificateList;

        /**
         * 营销【站内推广链接】跳转的1688商品详情页链接
         */
        private String promotionUrl;
    }

    @Data
    public static class ProductImage {

        /**
         * 图片列表
         */
        private List<String> images;

        /**
         * 白底图
         */
        private String whiteImage;
    }

    @Data
    public static class ProductAttribute {

        /**
         * 属性ID
         */
        private String attributeId;

        /**
         * 属性名称
         */
        private String attributeName;

        /**
         * 属性值
         */
        private String value;

        /**
         * 属性名称翻译
         */
        private String attributeNameTrans;

        /**
         * 属性值翻译
         */
        private String valueTrans;
    }

    @Data
    public static class ProductSkuInfo {

        /**
         * 库存
         */
        private Integer amountOnSale;

        /**
         * 价格
         */
        private String price;

        /**
         * 废弃字段，不再使用
         */
        @Deprecated
        private String jxhyPrice;

        /**
         * sku标识
         */
        private Long skuId;

        /**
         * 规格ID
         */
        private String specId;

        /**
         * 属性
         */
        private List<SkuAttribute> skuAttributes;

        /**
         * sku级别
         */
        private String cargoNumber;

        /**
         * 营销价
         */
        private String promotionPrice;

        /**
         * 代销价格
         */
        private String consignPrice;

        /**
         * 分销价格信息
         */
        private FenxiaoPriceInfo fenxiaoPriceInfo;
    }

    @Data
    public static class SkuAttribute {

        /**
         * 属性ID
         */
        private Long attributeId;

        /**
         * 属性名
         */
        private String attributeName;

        /**
         * 属性名翻译
         */
        private String attributeNameTrans;

        /**
         * 值
         */
        private String value;

        /**
         * 值翻译
         */
        private String valueTrans;

        /**
         * sku图片
         */
        private String skuImageUrl;
    }

    @Data
    public static class ProductSaleInfo {

        /**
         * 商品库存
         */
        private Integer amountOnSale;

        /**
         * 价格区间列表
         */
        private List<PriceRange> priceRangeList;

        /**
         * 报价类型
         */
        private Integer quoteType;

        /**
         * 单位信息
         */
        private UnitInfo unitInfo;

        /**
         * 分销销售信息
         */
        private FenxiaoSaleInfo fenxiaoSaleInfo;

        /**
         * 废弃字段，不再使用
         * 
         * @deprecated 该字段已废弃，不再使用
         */
        @Deprecated
        private String consignPrice;

        /**
         * 废弃字段，不再使用
         * 
         * @deprecated 该字段已废弃，不再使用
         */
        @Deprecated
        private String jxhyPrice;
    }

    @Data
    public static class UnitInfo {

        /**
         * 单位
         */
        private String unit;

        /**
         * 单位翻译
         */
        private String transUnit;
    }

    @Data
    public static class FenxiaoSaleInfo {

        /**
         * 是否一件代发包邮
         */
        private Boolean onePieceFreePostage;

        /**
         * 起批量
         */
        private Integer startQuantity;

        /**
         * 一件代发包邮价
         */
        private String onePiecePrice;

        /**
         * 分销价
         */
        private String offerPrice;
    }

    @Data
    public static class ProductShippingInfo {

        /**
         * 地址
         */
        private String sendGoodsAddressText;

        /**
         * 重量，单位kg
         */
        private Double weight;

        /**
         * 宽，单位cm
         */
        private Double width;

        /**
         * 高，单位cm
         */
        private Double height;

        /**
         * 长，单位cm
         */
        private Double length;

        /**
         * 发货时效保障
         */
        private String shippingTimeGuarantee;

        /**
         * SKU物流信息列表废弃，请使用 skuShippingDetails 替代
         * 
         * @deprecated 该字段已废弃，请使用 {@link #skuShippingDetails} 替代
         */
        @Deprecated
        private List<SkuShippingInfo> skuShippingInfoList;

        /**
         * SKU件重尺寸信息
         */
        private List<SkuShippingDetail> skuShippingDetails;

        /**
         * 件重尺数据来源
         */
        private String pkgSizeSource;
    }

    @Data
    public static class SkuShippingInfo {

        /**
         * 规格ID
         */
        private String specId;

        /**
         * SKU ID
         */
        private Long skuId;

        /**
         * 宽，单位cm
         */
        private Double width;

        /**
         * 长，单位cm
         */
        private Double length;

        /**
         * 高，单位cm
         */
        private Double height;

        /**
         * 重量，单位g
         */
        private Integer weight;
    }

    @Data
    public static class SkuShippingDetail {

        /**
         * skuId
         */
        private String skuId;

        /**
         * 宽, cm
         */
        private Double width;

        /**
         * 长, cm
         */
        private Double length;

        /**
         * 高, cm
         */
        private Double height;

        /**
         * 重, kg
         */
        private Double weight;

        /**
         * 官方测量长度，单位cm
         */
        private Double officialLength;

        /**
         * 官方测量宽度，单位cm
         */
        private Double officialWidth;

        /**
         * 官方测量高度，单位cm
         */
        private Double officialHeight;

        /**
         * 官方测量重量，单位kg
         */
        private Double officialWeight;

        /**
         * AI 预测重量，单位kg
         */
        private Double aiWeight;

        /**
         * AI 预测重量在该商品叶子类目准确率
         */
        private String aiWeightAccuracy;

        /**
         * 件重尺来源
         */
        private String pkgSizeSource;
    }

    @Data
    public static class TagInfoList {

        /**
         * 服务名，isOnePsale-一件代发，select-跨境select货盘
         */
        private String key;

        /**
         * 是否开通
         */
        private Boolean value;
    }

    @Data
    public static class SellerMixSetting {

        /**
         * 是否普通混批
         */
        private Boolean generalHunpi;

        /**
         * 混批金额
         */
        private Integer mixAmount;

        /**
         * 混批数量
         */
        private Integer mixNumber;
    }

    @Data
    public static class SellerDataInfo {

        /**
         * 卖家交易勋章
         */
        private String tradeMedalLevel;

        /**
         * 综合服务分
         */
        private String compositeServiceScore;

        /**
         * 物流体验分
         */
        private String logisticsExperienceScore;

        /**
         * 纠纷解决分
         */
        private String disputeComplaintScore;

        /**
         * 商品体验分
         */
        private String offerExperienceScore;

        /**
         * 咨询体验分
         */
        private String consultingExperienceScore;

        /**
         * 卖家回头率
         */
        private String repeatPurchasePercent;

        /**
         * 退换体验分
         */
        private String afterSalesExperienceScore;

        /**
         * 最近30天48H揽收率
         */
        private String collect30DayWithin48HPercent;

        /**
         * 最近30天品质退款率
         */
        private String qualityRefundWithin30Day;
    }

    @Data
    public static class PromotionModel {

        /**
         * 是否有营销
         */
        private Boolean hasPromotion;

        /**
         * 营销类型，如：plus-plus会员
         */
        private String promotionType;
    }

    @Data
    public static class CertificateList {

        /**
         * 证书名字，如：外观专利证书或授权书证书
         */
        private String certificateName;

        /**
         * 证书编号
         */
        private String certificateCode;

        /**
         * 证书图片
         */
        private List<String> certificatePhotoList;
    }

    @Data
    public static class ChannelPrice {

        /**
         * 渠道sku价格列表
         */
        private List<ChannelSkuPrice> channelSkuPriceList;
    }

    @Data
    public static class ChannelSkuPrice {

        /**
         * sku id
         */
        private Long skuId;

        /**
         * 渠道价格
         */
        private String currentPrice;
    }

    @Data
    public static class PriceRange {

        /**
         * 起批量
         */
        private Integer startQuantity;

        /**
         * 批发价
         */
        private String price;

        /**
         * 营销价
         */
        private String promotionPrice;
    }

    @Data
    public static class FenxiaoPriceInfo {

        /**
         * 一件代发价
         * 注意：该字段在某些商品中可能不存在
         */
        private String onePiecePrice;

        /**
         * 分销价
         */
        private String offerPrice;
    }
}