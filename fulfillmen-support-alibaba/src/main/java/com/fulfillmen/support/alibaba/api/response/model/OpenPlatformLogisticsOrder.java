/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model;

import java.util.List;
import lombok.Data;

/**
 * 物流订单信息
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Data
public class OpenPlatformLogisticsOrder {

    /**
     * 物流单号
     */
    private String logisticsId;

    /**
     * 运单号
     */
    private String logisticsBillNo;

    /**
     * 订单条目ID
     */
    private String orderEntryIds;

    /**
     * 物流状态
     */
    private String status;

    /**
     * 物流公司ID
     */
    private String logisticsCompanyId;

    /**
     * 物流公司名称
     */
    private String logisticsCompanyName;

    /**
     * 物流公司编号
     */
    private String logisticsCompanyNo;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 服务特性
     */
    private String serviceFeature;

    /**
     * 系统发货时间
     */
    private String gmtSystemSend;

    /**
     * 发货商品列表
     */
    private List<OpenPlatformLogisticsOrderSendGood> sendGoods;

    /**
     * 收货人信息
     */
    private OpenPlatformLogisticsReceiver receiver;

    /**
     * 发货人信息
     */
    private OpenPlatformLogisticsSender sender;

    /**
     * 物流订单商品列表
     */
    private List<OpenPlatformLogisticsOrderSendGood> logisticsOrderGoods;

    /**
     * 物流和订单关联模型
     */
    private List<OpenPlatformLogisticsOrderSendGood> logisticsOrderSendGood;
}