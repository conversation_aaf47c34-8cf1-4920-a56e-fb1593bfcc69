/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service.impl;

import com.fulfillmen.support.alibaba.api.ApiPaths;
import com.fulfillmen.support.alibaba.api.FeedbackAPI;
import com.fulfillmen.support.alibaba.api.request.feedback.AccountBusinessSaveRequestRecord;
import com.fulfillmen.support.alibaba.api.request.feedback.LogisticsOrderSyncRequestRecord;
import com.fulfillmen.support.alibaba.api.request.feedback.OrderRelationWriteRequestRecord;
import com.fulfillmen.support.alibaba.api.request.feedback.OrderSyncRequestRecord;
import com.fulfillmen.support.alibaba.api.response.feedback.AccountBusinessSaveResponse;
import com.fulfillmen.support.alibaba.api.response.feedback.LogisticsOrderSyncResponse;
import com.fulfillmen.support.alibaba.api.response.feedback.OrderRelationWriteResponse;
import com.fulfillmen.support.alibaba.api.response.feedback.OrderSyncResponse;
import com.fulfillmen.support.alibaba.autoconfigure.AlibabaProperties;
import com.fulfillmen.support.alibaba.service.IFeedbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 1688回传数据相关服务实现类
 *
 * <AUTHOR>
 * @created 2025-01-15
 */
@Slf4j
@Service
public class FeedbackServiceImpl extends BaseAlibabaServiceImpl implements IFeedbackService {

    private static final String SERVICE_NAME = "回传数据服务";

    private final FeedbackAPI feedbackAPI;

    public FeedbackServiceImpl(AlibabaProperties alibabaProperties, FeedbackAPI feedbackAPI) {
        super(alibabaProperties);
        this.feedbackAPI = feedbackAPI;
    }

    @Override
    protected String getServiceName() {
        return SERVICE_NAME;
    }

    @Override
    public Mono<AccountBusinessSaveResponse> saveAccountBusiness(AccountBusinessSaveRequestRecord request) {
        log.debug("[{}] 开始保存账号所属业务线, request: {}", SERVICE_NAME, request);
        return wrapWithErrorHandler("保存账号所属业务线", request, ApiPaths.FeedbackAPI.ACCOUNT_BUSINESS_SAVE, formParams -> feedbackAPI
            .saveAccountBusiness(appKey, formParams));
    }

    @Override
    public Mono<LogisticsOrderSyncResponse> syncLogisticsOrder(LogisticsOrderSyncRequestRecord request) {
        log.debug("[{}] 国家站物流单回传, request: {}", getServiceName(), request);
        return wrapWithErrorHandler("国家站物流单回传", request, ApiPaths.FeedbackAPI.LOGISTICS_ORDER_SYNC, formParams -> feedbackAPI
            .syncLogisticsOrder(appKey, formParams));
    }

    @Override
    public Mono<OrderRelationWriteResponse> writeOrderRelation(OrderRelationWriteRequestRecord request) {
        log.debug("[{}] 回传机构真实用户订单和1688订单的映射关系, request: {}", getServiceName(), request);
        return wrapWithErrorHandler("回传订单映射关系", request, ApiPaths.FeedbackAPI.ORDER_RELATION_WRITE, formParams -> feedbackAPI
            .writeOrderRelation(appKey, formParams));
    }

    @Override
    public Mono<OrderSyncResponse> syncOrder(OrderSyncRequestRecord request) {
        log.debug("[{}] 开始同步下游销售订单, request: {}", SERVICE_NAME, request);
        return wrapWithErrorHandler("同步下游销售订单", request, ApiPaths.FeedbackAPI.ORDER_SYNC, formParams -> feedbackAPI
            .syncOrder(appKey, formParams));
    }
}