/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook;

import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import java.nio.charset.StandardCharsets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 阿里巴巴Webhook消息签名验证器
 * <p>
 * 按照官方文档实现： 1. 签名内容：key + value (即 "message" + JSON串) 2. 算法：HMAC-SHA1 3. 输出：十六进制字符串
 *
 * <AUTHOR>
 * @date 2025/7/10
 * @since 1.0.0
 */
@Slf4j
@Component
public class SignatureValidator {

    @Value("${alibaba.open1688.secret-key:}")
    private String webhookSecret;

    /**
     * 验证阿里巴巴Webhook消息签名
     * <p>
     * 官方算法： String[] datas = new String[1]; datas[0] = key + value; // key="message", value=JSON串 byte[] signature = SecurityUtil.hmacSha1(datas, toBytes(appSecretKey)); return
     * encodeHexStr(signature);
     *
     * @param payload   原始消息内容（JSON串）
     * @param signature 签名（十六进制字符串）
     * @return 验证结果
     */
    public boolean validate(String payload, String signature) {
        if (webhookSecret == null || webhookSecret.isEmpty()) {
            log.warn("Webhook secret未配置，跳过签名验证");
            // 如果没有配置密钥，则跳过验证
            return true;
        }

        if (signature == null || signature.isEmpty()) {
            log.error("签名为空");
            return false;
        }

        try {
            String expectedSignature = calculateSignature(payload);
            boolean isValid = expectedSignature.equals(signature);

            if (!isValid) {
                log.error("签名验证失败，期望: {}, 实际: {}", expectedSignature, signature);
            }

            return isValid;

        } catch (Exception e) {
            log.error("签名验证异常", e);
            return false;
        }
    }

    /**
     * 计算阿里巴巴Webhook消息签名
     * <p>
     * 按照官方算法：message + JSON串 -> HMAC-SHA1 -> 十六进制字符串
     * 
     * @param payload 原始消息内容（JSON串）
     * @return 签名
     */
    public String calculateSignature(String payload) {
        return calculateSignature(payload, webhookSecret);
    }

    /**
     * 计算阿里巴巴Webhook消息签名
     * <p>
     * 按照官方算法：message + JSON串 -> HMAC-SHA1 -> 十六进制字符串
     * 
     * @param payload   原始消息内容（JSON串）
     * @param secretKey 密钥
     * @return 签名
     */
    public String calculateSignature(String payload, String secretKey) {
        // 按照官方文档：key="message", value=JSON串
        byte[] secretBytes = secretKey.getBytes(StandardCharsets.UTF_8);
        String signContent = "message" + payload;
        // 使用HMAC-SHA1算法计算签名
        HMac mac = new HMac(HmacAlgorithm.HmacSHA1, secretBytes);
        // 返回十六进制字符串（小写）
        return mac.digestHex(signContent).toUpperCase();
    }
}