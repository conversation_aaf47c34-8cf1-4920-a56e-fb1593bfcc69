/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.refund;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import com.fulfillmen.support.alibaba.util.DateUtils;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Builder;
import lombok.Getter;
import org.springframework.util.CollectionUtils;

/**
 * 买家查询退款列表请求
 *
 * <AUTHOR>
 * @created 2025-01-13
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.buyer.queryOrderRefundList-1">API文档</a>
 */
@Builder
public record RefundBuyerListRequestRecord(
    /**
     * 订单ID
     */
    Long orderId,

    /**
     * 退款申请开始时间（毫秒）
     */
    Date applyStartTime,

    /**
     * 退款申请结束时间（毫秒）
     */
    Date applyEndTime,

    /**
     * 退款状态列表，多个以英文逗号分隔
     * <p>
     * 可选值:
     * 等待卖家同意 waitselleragree;
     * 退款成功 refundsuccess;
     * 退款关闭 refundclose;
     * 待买家修改 waitbuyermodify;
     * 等待买家退货 waitbuyersend;
     * 等待卖家确认收货 waitsellerreceive
     *
     * @see RefundStatus#of
     */
    List<String> refundStatusSet,

    /**
     * 卖家memberId
     */
    String sellerMemberId,

    /**
     * 物流单号（支持模糊查询）
     */
    String logisticsNo,

    /**
     * 退款修改开始时间（毫秒）
     */
    Date modifyStartTime,

    /**
     * 退款修改结束时间（毫秒）
     */
    Date modifyEndTime,

    /**
     * 1:售中退款，2:售后退款；0:所有退款单
     */
    DisputeType disputeType,

    /**
     * 页码
     */
    Integer page,
    /**
     * 每页条数
     */
    Integer pageSize
) implements BaseAlibabaRequestRecord {

    /**
     * 创建请求对象
     *
     * @param orderId 订单ID
     * @param page    页码
     * @return 请求对象
     */
    public static RefundBuyerListRequestRecord of(Long orderId, Integer page, Integer pageSize) {
        return new RefundBuyerListRequestRecord(orderId, null, null, null, null, null, null, null, null, page, pageSize);
    }

    /**
     * 创建请求对象
     *
     * @param page     页码
     * @param pageSize 每页条数
     * @return 请求对象
     */
    public static RefundBuyerListRequestRecord of(Integer page, Integer pageSize) {
        return new RefundBuyerListRequestRecord(null, null, null, null, null, null, null, null, null, page, pageSize);
    }

    @Override
    public void requireParams() {

    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        if (orderId != null) {
            params.put("orderId", String.valueOf(orderId));
        }
        if (applyStartTime != null) {
            params.put("applyStartTime", DateUtils.formatDate(applyStartTime));
        }
        if (applyEndTime != null) {
            params.put("applyEndTime", DateUtils.formatDate(applyEndTime));
        }
        if (!CollectionUtils.isEmpty(refundStatusSet)) {
            try {
                params.put("refundStatusSet", toJsonString(refundStatusSet));
            } catch (Exception e) {
                throw new IllegalArgumentException("退款状态列表序列化失败", e);
            }
        }
        if (sellerMemberId != null) {
            params.put("sellerMemberId", sellerMemberId);
        }
        if (pageSize != null) {
            params.put("pageSize", String.valueOf(pageSize));
        }
        if (logisticsNo != null) {
            params.put("logisticsNo", logisticsNo);
        }
        if (modifyStartTime != null) {
            params.put("modifyStartTime", DateUtils.formatDate(modifyStartTime));
        }
        if (modifyEndTime != null) {
            params.put("modifyEndTime", DateUtils.formatDate(modifyEndTime));
        }
        if (disputeType != null) {
            params.put("bizType", String.valueOf(disputeType.getValue()));
        }
        if (page != null) {
            params.put("page", String.valueOf(page));
        }
        return params;
    }

    @Getter
    public enum DisputeType {

        /**
         * 所有退款单
         */
        ALL(0, "所有退款单"),
        /**
         * 售中退款
         */
        BEFORE_SALE(1, "售中退款"),
        /**
         * 售后退款
         */
        AFTER_SALE(2, "售后退款");

        private final Integer value;
        private final String desc;

        DisputeType(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    @Getter
    public enum RefundStatus {

        /**
         * 等待卖家同意
         */
        WAIT_SELLER_AGREE("waitselleragree"),
        /**
         * 退款成功
         */
        REFUND_SUCCESS("refundsuccess"),
        /**
         * 退款关闭
         */
        REFUND_CLOSE("refundclose"),
        /**
         * 待买家修改
         */
        WAIT_BUYER_MODIFY("waitbuyermodify"),
        /**
         * 等待买家退货
         */
        WAIT_BUYER_SEND("waitbuyersend"),
        /**
         * 等待卖家确认收货
         */
        WAIT_SELLER_RECEIVE("waitsellerreceive");

        private final String value;

        RefundStatus(String value) {
            this.value = value;
        }

        /**
         * 将枚举值转换为字符串列表
         *
         * @param values 枚举值
         * @return 字符串列表
         */
        public static List<String> of(RefundStatus... values) {
            return Arrays.stream(values).map(RefundStatus::getValue).collect(Collectors.toList());
        }
    }
}
