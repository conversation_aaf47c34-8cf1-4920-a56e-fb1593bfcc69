/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.logistics;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import com.fulfillmen.support.alibaba.api.response.model.ReceiveAddress;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 根据地址解析地区码响应
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddressCodeParseResponse extends BaseAlibabaResponse {

    /**
     * 返回结果
     */
    private ReceiveAddress result;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误描述
     */
    private String errorMessage;
}