/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service.impl;

import com.fulfillmen.support.alibaba.api.ApiPaths;
import com.fulfillmen.support.alibaba.api.RefundAPI;
import com.fulfillmen.support.alibaba.api.request.refund.RefundBuyerListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundBuyerOrderViewRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundBuyerSubmitRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundBuyerViewRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundCancelRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundCreateRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundOpLogListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundReasonListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundUploadEvidenceRequestRecord;
import com.fulfillmen.support.alibaba.api.response.refund.RefundBuyerListResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundBuyerOrderViewResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundBuyerSubmitResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundBuyerViewResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundCancelResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundCreateResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundOpLogListResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundReasonListResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundUploadEvidenceResponse;
import com.fulfillmen.support.alibaba.autoconfigure.AlibabaProperties;
import com.fulfillmen.support.alibaba.service.IRefundService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 退款服务实现类
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Slf4j
@Service
public class RefundServiceImpl extends BaseAlibabaServiceImpl implements IRefundService {

    private static final String SERVICE_NAME = "退款服务";
    private final RefundAPI refundAPI;

    public RefundServiceImpl(RefundAPI refundAPI, AlibabaProperties alibabaProperties) {
        super(alibabaProperties);
        this.refundAPI = refundAPI;
    }

    @Override
    protected String getServiceName() {
        return SERVICE_NAME;
    }

    @Override
    public Mono<RefundCancelResponse> cancelRefund(RefundCancelRequestRecord request) {
        log.debug("开始处理退款取消请求: {}", request);
        return wrapWithErrorHandler("取消退款", request, ApiPaths.RefundAPI.CANCEL_REFUND, formParams -> refundAPI
            .cancelRefund(appKey, formParams));
    }

    @Override
    public Mono<RefundBuyerListResponse> queryRefundBuyerList(RefundBuyerListRequestRecord request) {
        log.debug("开始处理买家退款列表请求: {}", request);
        return wrapWithErrorHandler("查询买家退款列表", request, ApiPaths.RefundAPI.BUYER_LIST, formParams -> refundAPI
            .queryRefundBuyerList(appKey, formParams));
    }

    @Override
    public Mono<RefundBuyerSubmitResponse> submitRefundBuyerInfo(RefundBuyerSubmitRequestRecord request) {
        log.debug("开始处理买家退款信息提交请求: {}", request);
        return wrapWithErrorHandler("提交买家退款信息", request, ApiPaths.RefundAPI.BUYER_SUBMIT, formParams -> refundAPI
            .submitRefundBuyerInfo(appKey, formParams));
    }

    @Override
    public Mono<RefundUploadEvidenceResponse> uploadRefundEvidence(RefundUploadEvidenceRequestRecord request) {
        log.debug("开始处理退款凭证上传请求: {}", request);
        return wrapWithErrorHandler("上传退款凭证", request, ApiPaths.RefundAPI.UPLOAD_EVIDENCE, formParams -> refundAPI
            .uploadRefundEvidence(appKey, formParams));
    }

    @Override
    public Mono<RefundReasonListResponse> getRefundReasonList(RefundReasonListRequestRecord request) {
        log.debug("开始处理退款原因列表请求: {}", request);
        return wrapWithErrorHandler("获取退款原因列表", request, ApiPaths.RefundAPI.REASON_LIST, formParams -> refundAPI
            .getRefundReasonList(appKey, formParams));
    }

    @Override
    public Mono<RefundCreateResponse> createRefund(RefundCreateRequestRecord request) {
        log.debug("开始处理退款创建请求: {}", request);
        return wrapWithErrorHandler("创建退款", request, ApiPaths.RefundAPI.CREATE, formParams -> refundAPI
            .createRefund(appKey, formParams), "param");
    }

    @Override
    public Mono<RefundBuyerViewResponse> queryRefundBuyerView(RefundBuyerViewRequestRecord request) {
        log.debug("开始处理买家退款详情请求: {}", request);
        return wrapWithErrorHandler("查询买家退款详情", request, ApiPaths.RefundAPI.BUYER_VIEW, formParams -> refundAPI
            .queryRefundBuyerView(appKey, formParams));
    }

    @Override
    public Mono<RefundOpLogListResponse> queryRefundOpLogList(RefundOpLogListRequestRecord request) {
        log.debug("开始处理退款操作日志列表请求: {}", request);
        return wrapWithErrorHandler("查询退款操作日志列表", request, ApiPaths.RefundAPI.OP_LOG_LIST, formParams -> refundAPI
            .queryRefundOpLogList(appKey, formParams));
    }

    @Override
    public Mono<RefundBuyerOrderViewResponse> queryRefundBuyerOrderView(RefundBuyerOrderViewRequestRecord request) {
        log.debug("开始处理订单退款详情请求: {}", request);
        return wrapWithErrorHandler("查询订单退款详情", request, ApiPaths.RefundAPI.BUYER_ORDER_VIEW, formParams -> refundAPI
            .queryRefundBuyerOrderView(appKey, formParams));
    }
}