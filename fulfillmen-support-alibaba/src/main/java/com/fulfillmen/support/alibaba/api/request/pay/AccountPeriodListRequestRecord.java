/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.pay;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 查询买家信用账期信息请求
 * <p>
 * 用于查询买家信用账期的所有账期信息
 *
 * <AUTHOR>
 * @created 2025-01-10
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.accountPeriod.list.buyerView-1">API文档</a>
 */
@Builder
public record AccountPeriodListRequestRecord(
    /**
     * 页码
     */
    Long pageIndex,

    /**
     * 卖家ID
     * <p>
     * 不填则查询全部
     */
    String sellerLoginId
) implements BaseAlibabaRequestRecord {

    /**
     * 查询指定页码的账期信息
     *
     * @param pageIndex     页码
     * @param sellerLoginId 卖家ID
     * @return 查询指定页码的账期信息
     */
    public static AccountPeriodListRequestRecord of(Long pageIndex, String sellerLoginId) {
        return new AccountPeriodListRequestRecord(pageIndex, sellerLoginId);
    }

    /**
     * 查询全部账期信息
     *
     * @return 查询全部账期信息
     */
    public static AccountPeriodListRequestRecord of() {
        return new AccountPeriodListRequestRecord(null, null);
    }

    @Override
    public void requireParams() {
        // 无必填参数
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        if (pageIndex != null) {
            params.put("pageIndex", String.valueOf(pageIndex));
        }
        if (sellerLoginId != null) {
            params.put("sellerLoginId", sellerLoginId);
        }
        return params;
    }
}