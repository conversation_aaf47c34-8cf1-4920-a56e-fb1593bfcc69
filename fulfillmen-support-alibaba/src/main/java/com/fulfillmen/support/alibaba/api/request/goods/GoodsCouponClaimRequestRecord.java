/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.goods;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Builder;

/**
 * 商品优惠券领取请求
 *
 * <AUTHOR>
 * @created 2025-01-17
 */
@Builder
public record GoodsCouponClaimRequestRecord(List<Long> offerIds) implements BaseAlibabaRequestRecord {

    /**
     * 创建商品优惠券领取请求
     *
     * @param offerIds 商品ID列表(必填,最多10个)
     * @return 商品优惠券领取请求
     */
    public static GoodsCouponClaimRequestRecord of(List<Long> offerIds) {
        return new GoodsCouponClaimRequestRecord(offerIds);
    }

    @Override
    public void requireParams() {
        assertNotEmpty(offerIds, "商品ID列表不能为空");
        if (offerIds.size() > 10) {
            throw new AlibabaServiceValidationException("单次最多支持10个商品");
        }
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("offerIds", toJsonString(offerIds));
        return params;
    }
}