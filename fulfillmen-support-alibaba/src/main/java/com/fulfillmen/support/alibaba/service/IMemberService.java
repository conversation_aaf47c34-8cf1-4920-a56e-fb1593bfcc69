/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service;

import com.fulfillmen.support.alibaba.api.request.member.MemberRegisterRequestRecord;
import com.fulfillmen.support.alibaba.api.request.member.SubAccountAuthAddRequestRecord;
import com.fulfillmen.support.alibaba.api.request.member.SubAccountAuthCancelRequestRecord;
import com.fulfillmen.support.alibaba.api.request.member.SubAccountAuthListRequestRecord;
import com.fulfillmen.support.alibaba.api.response.member.MemberRegisterResponse;
import com.fulfillmen.support.alibaba.api.response.member.SubAccountAuthAddResponse;
import com.fulfillmen.support.alibaba.api.response.member.SubAccountAuthCancelResponse;
import com.fulfillmen.support.alibaba.api.response.member.SubAccountAuthListResponse;
import reactor.core.publisher.Mono;

/**
 * 1688会员服务接口
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
public interface IMemberService {

    /**
     * 注册1688会员账号
     *
     * @param request 注册请求
     * @return 注册结果
     */
    Mono<MemberRegisterResponse> register(MemberRegisterRequestRecord request);

    /**
     * 批量添加子账号授权
     *
     * @param request 授权请求
     * @return 授权结果
     */
    Mono<SubAccountAuthAddResponse> addSubAccountAuth(SubAccountAuthAddRequestRecord request);

    /**
     * 批量取消子账号授权
     *
     * @param request 取消请求
     * @return 取消结果
     */
    Mono<SubAccountAuthCancelResponse> cancelSubAccountAuth(SubAccountAuthCancelRequestRecord request);

    /**
     * 批量查询子账号授权
     *
     * @param request 查询请求
     * @return 查询结果
     */
    Mono<SubAccountAuthListResponse> listSubAccountAuth(SubAccountAuthListRequestRecord request);
}