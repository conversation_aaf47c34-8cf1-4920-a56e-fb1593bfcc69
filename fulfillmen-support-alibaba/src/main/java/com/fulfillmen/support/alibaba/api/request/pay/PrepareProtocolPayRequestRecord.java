/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.pay;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 发起免密支付请求
 * <p>
 * 用于发起免密支付，支持诚E赊和支付宝自动扣款
 *
 * <AUTHOR>
 * @created 2025-01-10
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.pay.protocolPay.preparePay-1">API文档</a>
 */
@Builder
public record PrepareProtocolPayRequestRecord(
    /**
     * 订单ID
     */
    Long orderId,

    /**
     * 支付通道
     * <p>
     * 跨境宝支付传入kjpayV2
     */
    String payChannel,

    /**
     * 付款总金额
     * <p>
     * 单位：分
     */
    Long payAmount,

    /**
     * 请求ID
     */
    String opRequestId
) implements BaseAlibabaRequestRecord {

    /**
     * 创建 PrepareProtocolPayRequestRecord 实例
     *
     * @param orderId     订单ID
     * @param payChannel  支付通道
     * @param payAmount   付款总金额
     * @param opRequestId 请求ID
     * @return PrepareProtocolPayRequestRecord 实例
     * @throws IllegalArgumentException 如果参数无效
     */
    public static PrepareProtocolPayRequestRecord of(Long orderId,
        String payChannel,
        Long payAmount,
        String opRequestId) {
        return new PrepareProtocolPayRequestRecord(orderId, payChannel, payAmount, opRequestId);
    }

    /**
     * 创建 PrepareProtocolPayRequestRecord 实例
     *
     * @param orderId 订单ID
     * @return PrepareProtocolPayRequestRecord 实例
     * @throws IllegalArgumentException 如果参数无效
     */
    public static PrepareProtocolPayRequestRecord ofOrderId(Long orderId) {
        return of(orderId, null, null, null);
    }

    @Override
    public void requireParams() {
        assertNotNull(orderId, "订单ID不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("orderId", orderId.toString());
        params.put("payChannel", payChannel);
        params.put("payAmount", payAmount.toString());
        params.put("opRequestId", opRequestId);
        return params;
    }
}