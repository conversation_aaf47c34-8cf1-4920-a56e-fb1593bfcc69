/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.logistics;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import com.fulfillmen.support.alibaba.api.response.model.OutOrderIdResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 根据运单号或无主件码查询外部订单ID响应
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LogisticsOutOrderIdResponse extends BaseAlibabaResponse {

    /**
     * 返回结果
     */
    private OutOrderIdResult result;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误描述
     */
    private String errorMessage;
}