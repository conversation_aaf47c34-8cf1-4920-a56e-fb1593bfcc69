/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.goods;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsKeywordNavigationResponse extends BaseAlibabaResponse {

    private NavigationResult result;

    @Data
    public static class NavigationResult {

        private Boolean success;
        private String retCode;
        private String retMsg;
        private List<NavigationCategory> result;
    }

    @Data
    public static class NavigationCategory {

        private String id;
        private String name;
        private String translateName;
        private List<NavigationItem> children;
    }

    @Data
    public static class NavigationItem {

        private String id;
        private String name;
        private String translateName;
    }
}