/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api;

import com.fulfillmen.support.alibaba.api.response.pay.AccountPeriodListResponse;
import com.fulfillmen.support.alibaba.api.response.pay.AlipayUrlResponse;
import com.fulfillmen.support.alibaba.api.response.pay.CheckProtocolPayResponse;
import com.fulfillmen.support.alibaba.api.response.pay.CreditPayUrlResponse;
import com.fulfillmen.support.alibaba.api.response.pay.CrossBorderPayUrlResponse;
import com.fulfillmen.support.alibaba.api.response.pay.PayWayQueryResponse;
import com.fulfillmen.support.alibaba.api.response.pay.PrepareProtocolPayResponse;
import org.springframework.http.MediaType;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * 1688支付相关 API 接口
 * <p>
 * 包含以下接口： 1. 查询订单可用支付方式 2. 获取诚e赊支付链接 3. 获取跨境宝支付链接 4. 查询是否开通免密支付 5. 获取支付宝支付链接 6. 查询买家信用账期信息 7. 发起免密支付
 *
 * <AUTHOR>
 * @created 2025-01-10
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.payWay.query-1">查询支付方式API文档</a>
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.creditPay.url.get-1">诚e赊支付API文档</a>
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.crossBorderPay.url.get-1">跨境宝支付API文档</a>
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.pay.protocolPay.isopen-1">免密支付查询API文档</a>
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.alipay.url.get-1">支付宝支付API文档</a>
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.accountPeriod.list.buyerView-1">账期查询API文档</a>
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.pay.protocolPay.preparePay-1">发起免密支付API文档</a>
 */
@HttpExchange("/")
public interface PayAPI {

    /**
     * 查询订单可用支付方式
     * <p>
     * 查询某笔未付订单可以使用的支付方式通道
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 支付方式查询响应
     * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.payWay.query-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.PayAPI.QUERY_PAY_WAY, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<PayWayQueryResponse> queryPayWay(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 获取诚e赊支付链接
     * <p>
     * 获取使用诚e赊支付的支付链接，支持批量订单支付，最多30个订单
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 诚e赊支付链接响应
     * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.creditPay.url.get-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.PayAPI.GET_CREDIT_PAY_URL, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<CreditPayUrlResponse> getCreditPayUrl(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 获取跨境宝支付链接
     * <p>
     * 获取使用跨境宝支付的支付链接，支持批量订单支付，最多30个订单
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 跨境宝支付链接响应
     * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.crossBorderPay.url.get-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.PayAPI.GET_CROSS_BORDER_PAY_URL, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<CrossBorderPayUrlResponse> getCrossBorderPayUrl(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 查询是否开通免密支付
     * <p>
     * 查询是否开通代扣协议
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 免密支付查询响应
     * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.pay.protocolPay.isopen-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.PayAPI.CHECK_PROTOCOL_PAY, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<CheckProtocolPayResponse> checkProtocolPay(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 获取支付宝支付链接
     * <p>
     * 获取批量订单的支付宝收银台链接，单个订单返回1688收银台地址，多个订单返回支付宝收银台地址
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 支付宝支付链接响应
     * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.alipay.url.get-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.PayAPI.GET_ALIPAY_URL, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<AlipayUrlResponse> getAlipayUrl(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 查询买家信用账期信息
     * <p>
     * 查询买家信用账期的所有账期信息，可翻页查看，每次返回不超过10条
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 账期信息响应
     * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.accountPeriod.list.buyerView-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.PayAPI.GET_ACCOUNT_PERIOD_LIST, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<AccountPeriodListResponse> getAccountPeriodList(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 发起免密支付
     * <p>
     * 发起免密支付，会自动判断是否开通了支付宝免密签约的免密支付，并发起扣款 优先发起诚E赊自动扣款，如果失败，则尝试支付宝自动扣款
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 免密支付响应
     * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.pay.protocolPay.preparePay-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.PayAPI.PREPARE_PROTOCOL_PAY, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<PrepareProtocolPayResponse> prepareProtocolPay(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);
}