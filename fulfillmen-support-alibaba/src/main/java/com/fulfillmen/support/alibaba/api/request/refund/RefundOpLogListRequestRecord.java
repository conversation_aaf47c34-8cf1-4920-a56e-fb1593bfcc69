/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.refund;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 查询退款操作记录列表请求
 *
 * <AUTHOR>
 * @created 2025-01-13
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.opLogList-1">查询退款操作记录列表</a>
 */
@Builder
public record RefundOpLogListRequestRecord(
    /**
     * 退款单号
     */
    String refundId,
    /**
     * 页码，从1开始
     */
    Integer pageNo,
    /**
     * 每页条数，默认20，最大100
     */
    Integer pageSize
) implements BaseAlibabaRequestRecord {

    public static RefundOpLogListRequestRecord of(String refundId) {
        return new RefundOpLogListRequestRecord(refundId, 1, 20);
    }

    @Override
    public void requireParams() {
        assertNotBlank(refundId, "退款单号不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("refundId", refundId);
        if (pageNo != null) {
            params.put("pageNo", String.valueOf(pageNo));
        }
        if (pageSize != null) {
            params.put("pageSize", String.valueOf(pageSize));
        }
        return params;
    }
}