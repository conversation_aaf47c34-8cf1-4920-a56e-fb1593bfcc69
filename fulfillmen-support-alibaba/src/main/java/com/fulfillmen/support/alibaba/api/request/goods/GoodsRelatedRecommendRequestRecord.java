/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.goods;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import com.fulfillmen.support.alibaba.enums.LanguageEnum;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 相关性商品推荐请求
 *
 * <AUTHOR>
 * @created 2025-01-17
 */
@Builder
public record GoodsRelatedRecommendRequestRecord(
    /**
     * 商品ID(必填)
     */
    Long offerId,
    /**
     * 语言(必填)，如英语en_US
     */
    String language,
    /**
     * 页码(选填，默认1)
     */
    Integer pageNo,
    /**
     * 每页大小(选填，默认10，最大50)
     */
    Integer pageSize
) implements BaseAlibabaRequestRecord {

    /**
     * 默认分页大小
     */
    private static final int DEFAULT_PAGE_SIZE = 10;
    /**
     * 默认页码
     */
    private static final int DEFAULT_PAGE_NO = 1;
    /**
     * 最大分页大小
     */
    private static final int MAX_PAGE_SIZE = 50;

    /**
     * 创建相关性商品推荐请求
     *
     * @param offerId  商品ID(必填)
     * @param language 语言(必填)，如英语en_US
     * @return 相关性商品推荐请求
     */
    public static GoodsRelatedRecommendRequestRecord of(Long offerId, LanguageEnum language) {
        return of(offerId, language, DEFAULT_PAGE_NO, DEFAULT_PAGE_SIZE);
    }

    /**
     * 创建相关性商品推荐请求
     *
     * @param offerId  商品ID(必填)
     * @param language 语言(必填)，如英语en_US
     * @param pageNo   页码(选填)，从1开始
     * @param pageSize 每页数量(选填)，最大50
     * @return 相关性商品推荐请求
     */
    public static GoodsRelatedRecommendRequestRecord of(Long offerId,
        LanguageEnum language,
        Integer pageNo,
        Integer pageSize) {
        // 分页大小最大不超过50，建议20效果最佳
        pageSize = Math.min(pageSize, MAX_PAGE_SIZE);
        return new GoodsRelatedRecommendRequestRecord(offerId, language.getLanguage(), pageNo, pageSize);
    }

    @Override
    public void requireParams() {
        assertNotNull(offerId, "商品ID不能为空");
        assertNotNull(language, "语言不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("offerId", offerId.toString());
        params.put("language", language);
        params.put("pageNo", pageNo == null ? String.valueOf(DEFAULT_PAGE_NO) : pageNo.toString());
        params.put("pageSize", pageSize == null ? String.valueOf(DEFAULT_PAGE_SIZE) : pageSize.toString());
        return params;
    }
}