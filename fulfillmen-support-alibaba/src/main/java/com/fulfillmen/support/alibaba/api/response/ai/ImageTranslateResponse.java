/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.ai;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 图片翻译响应
 *
 * <AUTHOR>
 * @created 2025-01-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ImageTranslateResponse extends BaseAlibabaResponse {

    /**
     * 错误码
     */
    private String code;

    /**
     * 错误码
     */
    private String retCode;

    /**
     * 错误描述
     */
    private String retMsg;

    /**
     * 错误描述
     */
    private String message;

    /**
     * 翻译结果
     */
    private TranslatedImageModel result;

    @Data
    public static class TranslatedImageModel {

        /**
         * 原始图片URL
         */
        private String originalImageUrl;

        /**
         * 原始语言代码
         */
        private String originalLanguage;

        /**
         * 翻译后的图片像素数据
         */
        private String translatedImagePixelData;

        /**
         * 翻译后的图片URL
         */
        private String translatedImageUrl;

        /**
         * 翻译后的语言代码
         */
        private String translatedLanguage;
    }
}