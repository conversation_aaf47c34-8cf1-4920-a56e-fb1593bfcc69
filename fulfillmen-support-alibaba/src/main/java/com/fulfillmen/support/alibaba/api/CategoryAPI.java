/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api;

import com.fulfillmen.support.alibaba.api.response.category.CategoryAttributeResponse;
import com.fulfillmen.support.alibaba.api.response.category.CategoryResponse;
import com.fulfillmen.support.alibaba.api.response.category.CategoryTranslationByIdResponse;
import com.fulfillmen.support.alibaba.api.response.category.CategoryTranslationResponse;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceException;
import org.springframework.http.MediaType;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * 1688类目管理 API 接口
 * <p>
 * 包含以下接口： 1. 查询类目信息 2. 查询类目翻译信息 3. 根据类目ID查询多语言类目 4. 获取类目属性
 *
 * <AUTHOR>
 * @created 2025-01-02
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.category:alibaba.category.get-1">类目查询API文档</a>
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.category:alibaba.category.translate-1">类目翻译API文档</a>
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.category:alibaba.category.translateById-1">类目ID翻译API文档</a>
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.category:alibaba.category.attribute.get-1">类目属性API文档</a>
 */
@HttpExchange("/")
public interface CategoryAPI {

    /**
     * 查询类目信息
     * <p>
     * 根据类目ID获取类目的基本信息，包括名称、层级、父类目等
     *
     * @param appKey 应用key - 1688开放平台应用的唯一标识
     * @param params 请求参数，包含以下字段： - categoryID: 类目ID（必填） - _aop_signature: 签名信息（必填）
     * @return Mono<CategoryResponse> 返回类目信息，包含类目的基本信息
     * @throws AlibabaServiceException 当API调用失败时抛出
     * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.category:alibaba.category.get-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.CategoryAPI.GET_CATEGORY, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<CategoryResponse> getCategory(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 查询类目翻译信息
     * <p>
     * 根据类目名称获取多语言翻译结果，支持多种语言
     *
     * @param appKey 应用key - 1688开放平台应用的唯一标识
     * @param params 请求参数，包含以下字段： - outMemberId: 外部会员ID（必填） - language: 目标语言（必填） - cateName: 类目名称（必填）
     * @return Mono<CategoryTranslationResponse> 返回类目的多语言翻译结果
     * @throws AlibabaServiceException 当API调用失败时抛出
     * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.category:alibaba.category.translate-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.CategoryAPI.GET_TRANSLATION, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<CategoryTranslationResponse> getTranslation(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 根据类目ID查询多语言类目
     * <p>
     * 根据类目ID获取指定语言的类目信息，支持多种语言
     *
     * @param appKey 应用key - 1688开放平台应用的唯一标识
     * @param params 请求参数，包含以下字段： - categoryId: 类目ID（必填） - language: 目标语言（必填）
     * @return Mono<CategoryTranslationByIdResponse> 返回指定类目ID的多语言信息
     * @throws AlibabaServiceException 当API调用失败时抛出
     * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.category:alibaba.category.translateById-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.CategoryAPI.GET_TRANSLATION_BY_ID, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<CategoryTranslationByIdResponse> getTranslationById(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 获取类目属性
     * <p>
     * 获取类目的属性定义，包括属性名称、类型、可选值等
     *
     * @param appKey 应用key - 1688开放平台应用的唯一标识
     * @param params 请求参数，包含以下字段： - categoryID: 类目ID（必填） - webSite: 站点信息（必填） - scene: 场景信息（必填）
     * @return Mono<CategoryAttributeResponse> 返回类目的属性定义列表
     * @throws AlibabaServiceException 当API调用失败时抛出
     * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.category:alibaba.category.attribute.get-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.CategoryAPI.GET_ATTRIBUTES, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<CategoryAttributeResponse> getAttributes(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);
}
