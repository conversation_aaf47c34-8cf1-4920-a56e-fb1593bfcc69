/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 1688交易快速创建订单商品信息
 *
 * <AUTHOR>
 * @created 2025-01-09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradeFastCargo {

    /**
     * 商品对应的offer id
     */
    private Long offerId;

    /**
     * 加密的offer id
     */
    private String openOfferId;

    /**
     * 商品规格id
     */
    private String specId;

    /**
     * 商品数量
     */
    private Double quantity;

    /**
     * 外部下游会员ID
     */
    private String outMemberId;
}