/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api;

import com.fulfillmen.support.alibaba.api.response.logistics.AddressCodeParseResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsCompanyListResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsFreightTemplateResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsInfoResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsInsuranceResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsOutOrderIdResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsReceiveAddressResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsTraceResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.ProductFreightEstimateResponse;
import org.springframework.http.MediaType;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * 1688物流相关API
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@HttpExchange("/")
public interface LogisticsAPI {

    /**
     * 获取物流模板详情 根据物流模板id获取买家承担的物流模板，运费模板ID为0表示运费说明说明，为1表示买家承担运费。
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 物流模板详情响应
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:alibaba.logistics.myFreightTemplate.list.get-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.LogisticsAPI.GET_FREIGHT_TEMPLATE, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<LogisticsFreightTemplateResponse> getFreightTemplate(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 根据地址解析地区码 根据地址信息解析出地区编码等信息。
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 地址解析响应
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.addresscode.parse-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.LogisticsAPI.ADDRESS_CODE_PARSE, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<AddressCodeParseResponse> parseAddressCode(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 根据运单号或无主件码查询外部订单ID
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 外部订单ID响应
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:logistics.order.getOutOrderId-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.LogisticsAPI.GET_OUT_ORDER_ID, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<LogisticsOutOrderIdResponse> getOutOrderId(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 获取交易订单的物流信息(买家视角)
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 物流信息响应
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:alibaba.trade.getLogisticsInfos.buyerView-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.LogisticsAPI.GET_LOGISTICS_INFO, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<LogisticsInfoResponse> getLogisticsInfo(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 获取交易订单的物流跟踪信息(买家视角)
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 物流跟踪信息响应
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:alibaba.trade.getLogisticsTraceInfo.buyerView-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.LogisticsAPI.GET_LOGISTICS_TRACE, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<LogisticsTraceResponse> getLogisticsTrace(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 买家获取保存的收货地址信息列表
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 收货地址列表响应
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.receiveAddress.get-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.LogisticsAPI.GET_RECEIVE_ADDRESS, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<LogisticsReceiveAddressResponse> getReceiveAddress(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 商品中国国内运费预估 根据商品ID、中国国内收货地址的省市区编码，预估商品的运费。
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 运费预估响应
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:product.freight.estimate-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.LogisticsAPI.FREIGHT_ESTIMATE, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<ProductFreightEstimateResponse> estimateFreight(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 获取所有的物流公司列表
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 物流公司列表响应
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:alibaba.logistics.OpQueryLogisticCompanyList-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.LogisticsAPI.GET_LOGISTICS_COMPANY_LIST, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<LogisticsCompanyListResponse> getLogisticsCompanyList(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 运费险信息查询
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 运费险信息响应
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:shipping.insurance.get-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.LogisticsAPI.GET_SHIPPING_INSURANCE, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<LogisticsInsuranceResponse> getShippingInsurance(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);
}