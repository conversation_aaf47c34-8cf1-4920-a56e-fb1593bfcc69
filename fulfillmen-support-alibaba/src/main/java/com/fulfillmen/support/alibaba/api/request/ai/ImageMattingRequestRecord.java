/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.ai;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 图片智能抠图请求记录
 * <p>
 * 自动识别图像中的显著主体，将主体从背景中分离，返回白色或透明背景的主体图像。 同时该产品还提供背景置换及定制尺寸选择，优化商品展示效果。
 *
 * @param imageUrl      源图片URL
 * @param backgroundBGR 背景颜色，默认为透明背景，格式为"[R,G,B]"，如"[255,255,255]"表示白色背景
 * @param height        高度，指定返回的图像高度
 * @param width         宽度，指定返回的图像宽度
 * <AUTHOR>
 * @created 2025-01-22
 * @see com.fulfillmen.support.alibaba.api.AiCapabilityAPI#mattingImage
 */
@Builder
public record ImageMattingRequestRecord(
    @JsonProperty("imageUrl") String imageUrl,
    @JsonProperty("backgroundBGR") String backgroundBGR,
    @JsonProperty("height") Integer height,
    @JsonProperty("width") Integer width
) implements BaseAlibabaRequestRecord {

    @Override
    public void requireParams() {
        assertNotBlank(imageUrl, "imageUrl不能为空");

        // 如果指定了高度和宽度，需要验证范围
        if (height != null) {
            assertTrue(height >= 100 && height <= 5000, "height必须在100-5000之间");
        }
        if (width != null) {
            assertTrue(width >= 100 && width <= 5000, "width必须在100-5000之间");
        }
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("imageUrl", imageUrl);

        if (backgroundBGR != null) {
            params.put("backgroundBGR", backgroundBGR);
        }
        if (height != null) {
            params.put("height", height.toString());
        }
        if (width != null) {
            params.put("width", width.toString());
        }
        return params;
    }
}