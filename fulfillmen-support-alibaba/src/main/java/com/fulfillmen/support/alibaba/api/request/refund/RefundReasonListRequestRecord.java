/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.refund;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Getter;

/**
 * 查询退款原因列表请求
 *
 * <AUTHOR>
 * @created 2025-01-13
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.getRefundReasonList-1">API文档</a>
 */
@Builder
public record RefundReasonListRequestRecord(
    /**
     * 订单号
     */
    String orderId,

    /**
     * 子订单号
     */
    List<Long> orderEntryIds,

    /**
     * 货物状态
     */
    GoodsStatus goodsStatus
) implements BaseAlibabaRequestRecord {

    @Override
    public void requireParams() {
        assertNotBlank(orderId, "订单号不能为空");
        assertNotNull(orderEntryIds, "子订单号不能为空");
        assertNotNull(goodsStatus, "退款类型不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("orderId", orderId);
        params.put("orderEntryIds", toJsonString(orderEntryIds));
        params.put("goodsStatus", String.valueOf(goodsStatus.getValue()));
        return params;
    }

    @Getter
    public enum GoodsStatus {

        /**
         * 售中等待买家发货
         */
        REFUND_WAIT_SELLER_SEND("refundWaitSellerSend"),
        /**
         * 售中等待买家收货
         */
        REFUND_WAIT_BUYER_RECEIVE("refundWaitBuyerReceive"),
        /**
         * 售中已收货（未确认完成交易）
         */
        REFUND_BUYER_RECEIVED("refundBuyerReceived"),
        /**
         * 售后未收货
         */
        AFTERSALE_BUYER_NOT_RECEIVED("aftersaleBuyerNotReceived"),
        /**
         * 售后已收到货
         */
        AFTERSALE_BUYER_RECEIVED("aftersaleBuyerReceived");

        private final String value;

        GoodsStatus(String value) {
            this.value = value;
        }
    }
}
