/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.ai;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 商品详描生成响应结果
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class ProductDescGenerateResponse {

    /**
     * 外层结果包装
     */
    @JsonProperty("result")
    private ResultWrapper result;

    /**
     * 结果包装类
     */
    @Data
    public static class ResultWrapper {

        /**
         * 请求是否成功
         */
        @JsonProperty("success")
        private Boolean success;

        /**
         * 错误码
         */
        @JsonProperty("retCode")
        private String retCode;

        /**
         * 错误信息
         */
        @JsonProperty("retMsg")
        private String retMsg;

        /**
         * 生成的详描内容
         */
        @JsonProperty("result")
        private String result;
    }
}