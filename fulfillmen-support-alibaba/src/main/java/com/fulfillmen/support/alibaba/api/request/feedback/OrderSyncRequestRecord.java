/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.feedback;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Builder;

/**
 * 下游销售订单同步请求
 *
 * <AUTHOR>
 * @created 2025-01-17
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:order.sync-1">下游销售订单同步 API</a>
 */
@Builder
public record OrderSyncRequestRecord(@JsonProperty("orderParam") OrderParam orderParam) implements BaseAlibabaRequestRecord {

    @Override
    public void requireParams() throws AlibabaServiceValidationException {
        assertNotNull(orderParam, "订单参数不能为空");
        orderParam.validate(this);
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("orderParam", toJsonString(orderParam));
        return params;
    }

    /**
     * 订单参数
     */
    public record OrderParam(
        /**
         * 订单ID
         */
        @JsonProperty("orderId") String orderId,

        /**
         * 商品ID
         */
        @JsonProperty("productId") String productId,

        /**
         * 商品名称
         */
        @JsonProperty("productName") String productName,

        /**
         * SKU ID
         */
        @JsonProperty("skulId") String skulId,

        /**
         * SKU名称
         */
        @JsonProperty("skuName") String skuName,

        /**
         * 购买数量
         */
        @JsonProperty("buyAmount") Long buyAmount,

        /**
         * 创建时间
         */
        @JsonProperty("createTime") String createTime,

        /**
         * 下游用户ID
         */
        @JsonProperty("outMemberId") String outMemberId,

        /**
         * 支付时间
         */
        @JsonProperty("payTime") String payTime,

        /**
         * 结束时间
         */
        @JsonProperty("endTime") String endTime,

        /**
         * 实付金额
         */
        @JsonProperty("paidFee") Long paidFee,

        /**
         * 退款状态
         */
        @JsonProperty("refundStatus") String refundStatus,

        /**
         * 订单状态
         */
        @JsonProperty("status") String status,

        /**
         * 子订单列表
         */
        @JsonProperty("subOrderParamList") List<SubOrderParam> subOrderParamList
    ) {

        void validate(OrderSyncRequestRecord request) {
            request.assertNotBlank(orderId, "订单ID不能为空");
            request.assertNotNull(buyAmount, "购买数量不能为空");
            request.assertNotBlank(createTime, "创建时间不能为空");
            request.assertNotBlank(outMemberId, "下游用户ID不能为空");
            request.assertNotBlank(payTime, "支付时间不能为空");
            request.assertNotNull(paidFee, "实付金额不能为空");

            if (subOrderParamList != null && !subOrderParamList.isEmpty()) {
                subOrderParamList.forEach(subOrder -> subOrder.validate(request));
            }
        }
    }

    /**
     * 子订单参数
     */
    public record SubOrderParam(
        /**
         * 子订单ID
         */
        @JsonProperty("orderId") String orderId,

        /**
         * 商品ID
         */
        @JsonProperty("productId") String productId,

        /**
         * 商品名称
         */
        @JsonProperty("productName") String productName,

        /**
         * SKU ID
         */
        @JsonProperty("skulId") String skulId,

        /**
         * SKU名称
         */
        @JsonProperty("skuName") String skuName,

        /**
         * 购买数量
         */
        @JsonProperty("buyAmount") Long buyAmount,

        /**
         * 实付金额
         */
        @JsonProperty("paidFee") Long paidFee,

        /**
         * 退款状态
         */
        @JsonProperty("refundStatus") String refundStatus
    ) {

        void validate(OrderSyncRequestRecord request) {
            request.assertNotBlank(orderId, "子订单ID不能为空");
            request.assertNotBlank(productId, "商品ID不能为空");
            request.assertNotBlank(productName, "商品名称不能为空");
            request.assertNotBlank(skulId, "SKU ID不能为空");
            request.assertNotBlank(skuName, "SKU名称不能为空");
            request.assertNotNull(buyAmount, "购买数量不能为空");
            request.assertNotNull(paidFee, "实付金额不能为空");
        }
    }
}