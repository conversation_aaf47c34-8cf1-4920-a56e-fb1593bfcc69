/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api;

import com.fulfillmen.support.alibaba.api.response.goods.GoodsCouponClaimResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsDetailResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsImageSearchResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsImageUploadResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsKeywordNavigationResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsRecommendResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsRelatedRecommendResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsSearchResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsSellerResponse;
import org.springframework.http.MediaType;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * 1688商品管理 API 接口
 * <p>
 * 包含以下接口： 1. 跨境场景商品搜索 2. 跨境场景图片搜索商品 3. 上传图片获取imageId 4. 获取多语言商品详情 5.
 * 获取多语言商品店铺列表 6. 商品推荐 7. 多语言搜索导航 8. 相关性商品推荐 9. 商品优惠券领取
 *
 * <AUTHOR>
 * @created 2025-01-08
 * @see <a href=
 *      "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.cross.productList.search-1">商品搜索API文档</a>
 * @see <a href=
 *      "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.cross.similar.productList.search-1">图片搜索API文档</a>
 * @see <a href=
 *      "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.cross.productInfo.get-1">商品详情API文档</a>
 * @see <a href=
 *      "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.cross.similar.offer.search-1">相关商品推荐API文档</a>
 * @see <a href=
 *      "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.cross.productList.recommend-1">商品推荐API文档</a>
 * @see <a href=
 *      "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.cross.searchNavigator.get-1">搜索导航API文档</a>
 * @see <a href=
 *      "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.cross.coupon.claim-1">优惠券领取API文档</a>
 */
@HttpExchange("/")
public interface GoodsAPI {

    /**
     * 跨境场景商品搜索
     * <p>
     * 支持按关键词搜索商品，返回多语言商品信息，包含商品基本信息、价格、库存等
     *
     * @param appKey 应用key 1688开放平台应用的唯一标识
     * @param params 请求参数 包含关键词、语言、分页等信息
     * @return 商品搜索结果 包含商品列表及分页信息
     * @see <a
     *      href=
     *      "https://open.1688.com/api/apidocdetail.htm?spm=a260s.develop-solution-detail.0.0.4d0e55edwTZKPE&id=com.alibaba.fenxiao.crossborder:product.search.keywordSNQuery-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.GoodsAPI.SEARCH_GOODS, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<GoodsSearchResponse> searchGoods(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 跨境场景图片搜索商品
     * <p>
     * 支持通过图片搜索相似商品，需要先调用图片上传接口获取图片ID
     *
     * @param appKey 应用key 1688开放平台应用的唯一标识
     * @param params 请求参数 包含图片ID、目标语言等信息
     * @return 商品搜索结果 包含相似商品列表及分页信息
     * @see <a
     *      href=
     *      "https://open.1688.com/api/apidocdetail.htm?spm=a260s.develop-solution-detail.0.0.4d0e55edwTZKPE&id=com.alibaba.fenxiao.crossborder:product.search.imageQuery-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.GoodsAPI.SEARCH_GOODS_BY_IMAGE, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<GoodsImageSearchResponse> searchGoodsByImage(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 上传图片获取imageId
     * <p>
     * 上传图片到1688平台，用于后续的图片搜索
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 图片上传结果
     * @see <a
     *      href=
     *      "https://open.1688.com/api/apidocdetail.htm?spm=a260s.develop-solution-detail.0.0.4d0e55edwTZKPE&id=com.alibaba.fenxiao.crossborder:product.image.upload-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.GoodsAPI.UPLOAD_IMAGE, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<GoodsImageUploadResponse> uploadImage(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 获取多语言商品详情
     * <p>
     * 获取商品的详细信息，包括商品属性、规格、图片、描述等
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 商品详情
     * @see <a href=
     *      "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.cross.productInfo.get-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.GoodsAPI.GET_GOODS_DETAIL, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<GoodsDetailResponse> getGoodsDetail(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 获取多语言商品店铺列表
     * <p>
     * 获取店铺的商品列表，支持分页查询
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 商品店铺列表
     * @see <a
     *      href=
     *      "https://open.1688.com/api/apidocdetail.htm?spm=a260s.develop-solution-detail.0.0.4d0e55edwTZKPE&id=com.alibaba.fenxiao.crossborder:product.search.querySellerOfferList-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.GoodsAPI.GET_SELLER_GOODS, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<GoodsSellerResponse> getSellerGoods(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 商品推荐
     * <p>
     * 基于用户行为和商品特征，推荐相关商品
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 推荐商品列表
     * @see <a
     *      href=
     *      "https://open.1688.com/api/apidocdetail.htm?spm=a260s.develop-solution-detail.0.0.4d0e55edwTZKPE&id=com.alibaba.fenxiao.crossborder:product.search.offerRecommend-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.GoodsAPI.RECOMMEND_GOODS, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<GoodsRecommendResponse> recommendGoods(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 多语言搜索导航
     * <p>
     * 获取搜索结果的分类、属性等导航信息
     *
     * @param appKey 应用Key
     * @param params 请求参数
     * @return 导航信息
     * @see <a
     *      href=
     *      "https://open.1688.com/api/apidocdetail.htm?spm=a260s.develop-solution-detail.0.0.4d0e55edwTZKPE&id=com.alibaba.fenxiao.crossborder:product.search.querySellerOfferList-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.GoodsAPI.KEYWORD_NAVIGATION, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<GoodsKeywordNavigationResponse> getKeywordNavigation(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 相关性商品推荐
     * <p>
     * 根据商品ID获取相似商品推荐
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 推荐商品列表
     * @see <a href=
     *      "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:product.related.recommend-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.GoodsAPI.GET_RELATED_RECOMMEND, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<GoodsRelatedRecommendResponse> getRelatedRecommend(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 商品优惠券领取
     * <p>
     * 支持用户领取指定商品的优惠券，每个优惠券限领一次
     *
     * @param appKey 应用key 1688开放平台应用的唯一标识
     * @param params 请求参数 包含商品ID、优惠券ID等信息
     * @return 优惠券领取结果 包含领取状态和优惠券详情
     * @see <a href=
     *      "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.marketing%3Acoupon.optimal.claim-1&aopApiCategory=category_new">API文档</a>
     */
    @PostExchange(value = ApiPaths.GoodsAPI.CLAIM_COUPON, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<GoodsCouponClaimResponse> claimCoupon(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);
}
