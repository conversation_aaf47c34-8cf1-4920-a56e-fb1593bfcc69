/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.ai;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 图像元素识别响应
 *
 * <AUTHOR>
 * @created 2025-01-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ImageElementsRecognitionResponse extends BaseAlibabaResponse {

    private Result result;

    @Data
    public static class Result {

        /**
         * 调用是否成功
         */
        private Boolean success;

        /**
         * 错误码：200 代表调用成功，其他的错误代码见参考错误码说明
         */
        private String code;

        /**
         * 成功/失败信息
         */
        private String message;

        /**
         * 识别结果
         */
        private RecognitionResult result;
    }

    @Data
    public static class RecognitionResult {

        /**
         * 识别的文字内容列表
         */
        private List<String> recText;

        /**
         * 主体占比
         */
        private String pdProp;

        /**
         * 主体是否有水印
         */
        private Boolean objWatermark;

        /**
         * 主体是否有logo
         */
        private Boolean objLogo;

        /**
         * 主体是否有二维码
         */
        private Boolean objQrcode;

        /**
         * 主体是否有文字
         */
        private Boolean objCharacter;

        /**
         * 非主体是否有水印
         */
        private Boolean noobWatermark;

        /**
         * 非主体是否有logo
         */
        private Boolean noobLogo;

        /**
         * 非主体是否有二维码
         */
        private Boolean noobQrcode;

        /**
         * 非主体是否有文字
         */
        private Boolean noobCharacter;

        /**
         * 主体图边缘的距离
         */
        private String borderPixel;

        /**
         * 图像主体是否含有色情暴恐
         */
        private Boolean objNgx;
    }
}
