/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api;

import com.fulfillmen.support.alibaba.api.response.member.MemberRegisterResponse;
import com.fulfillmen.support.alibaba.api.response.member.SubAccountAuthAddResponse;
import com.fulfillmen.support.alibaba.api.response.member.SubAccountAuthCancelResponse;
import com.fulfillmen.support.alibaba.api.response.member.SubAccountAuthListResponse;
import org.springframework.http.MediaType;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * 1688会员相关API
 * <p>
 * 包含以下接口： 1. 1688会员注册 2. 批量添加子账号授权 3. 批量取消子账号授权 4. 批量查询子账号授权
 *
 * <AUTHOR>
 * @created 2025-01-13
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:account.user.register-1">会员注册API文档</a>
 */
@HttpExchange("/")
public interface MemberAPI {

    /**
     * 注册1688会员账号
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 注册结果
     * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:account.user.register-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.MemberAPI.REGISTER, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<MemberRegisterResponse> register(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 批量添加子账号授权
     * <p>
     * 批量对某个主账号下的子账号添加权限，前提是主账号已经授权。
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 授权结果
     * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=system.oauth2:subaccount.auth.add-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.MemberAPI.SUB_ACCOUNT_AUTH_ADD, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<SubAccountAuthAddResponse> addSubAccountAuth(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 批量取消子账号授权
     * <p>
     * 对子账号的授权批量取消。
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 取消结果
     * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=system.oauth2:subaccount.auth.cancel-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.MemberAPI.SUB_ACCOUNT_AUTH_CANCEL, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<SubAccountAuthCancelResponse> cancelSubAccountAuth(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 批量查询子账号授权
     * <p>
     * 批量查询主账号下子账号的授权状况。
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 查询结果
     * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=system.oauth2:subaccount.auth.list-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.MemberAPI.SUB_ACCOUNT_AUTH_LIST, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<SubAccountAuthListResponse> listSubAccountAuth(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);
}