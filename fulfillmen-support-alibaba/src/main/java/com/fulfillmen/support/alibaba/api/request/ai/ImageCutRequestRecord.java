/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.ai;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 图片裁剪请求记录
 * <p>
 * 用于对输入的图像像素尺寸进行调整，支持自动识别图像主体区域， 将裁剪后的各类尺寸，适配各种场景设计需求。
 *
 * @param imageUrl 源图片URL，图片尺寸最大不超过100*100像素，小于5000*5000像素
 * @param width    宽度，期望裁剪成的图片宽度，单位：像素，取值范围：100-5000
 * @param height   高度，期望裁剪成的图片高度，单位：像素，取值范围：100-5000
 * <AUTHOR>
 * @created 2025-01-22
 * @see com.fulfillmen.support.alibaba.api.AiCapabilityAPI#cutImage
 */
@Builder
public record ImageCutRequestRecord(
    @JsonProperty("imageUrl") String imageUrl,
    @JsonProperty("width") String width,
    @JsonProperty("height") String height
) implements BaseAlibabaRequestRecord {

    @Override
    public void requireParams() {
        assertNotBlank(imageUrl, "imageUrl不能为空");
        assertNotBlank(width, "width不能为空");
        assertNotBlank(height, "height不能为空");

        // 验证宽度和高度的范围
        int widthValue = Integer.parseInt(width);
        int heightValue = Integer.parseInt(height);
        if (widthValue < 100 || widthValue > 5000) {
            throw new IllegalArgumentException("width必须在100-5000之间");
        }
        if (heightValue < 100 || heightValue > 5000) {
            throw new IllegalArgumentException("height必须在100-5000之间");
        }
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("imageUrl", imageUrl);
        params.put("width", width);
        params.put("height", height);
        return params;
    }
}