/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api;

import com.fulfillmen.support.alibaba.api.response.order.OrderBuyerListResponse;
import com.fulfillmen.support.alibaba.api.response.order.OrderCancelResponse;
import com.fulfillmen.support.alibaba.api.response.order.OrderCreateResponse;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse;
import com.fulfillmen.support.alibaba.api.response.order.OrderPreviewResponse;
import com.fulfillmen.support.alibaba.api.response.order.TradeFeedbackResponse;
import org.springframework.http.MediaType;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * 1688订单管理 API 接口
 * <p>
 * 包含以下接口： 1. 取消交易 2. 获取订单列表(买家视角) 3. 获取订单详情 4. 买家补充订单留言 5. 创建跨境订单 6. 预览订单
 *
 * <AUTHOR>
 * @created 2025-01-08
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.cancel-1">取消交易API文档</a>
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.getBuyerOrderList-1">获取订单列表API文档</a>
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.get.buyerView-1">获取订单详情API文档</a>
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.addFeedback-1">补充订单留言API文档</a>
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.crossBorder.trade.createCrossOrder-1">创建跨境订单API文档</a>
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.previewOrder-1">预览订单API文档</a>
 */
@HttpExchange("/")
public interface OrderAPI {

    /**
     * 取消交易
     * <p>
     * 买家取消未付款订单，取消后不可恢复
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 取消结果
     * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.cancel-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.OrderAPI.CANCEL_ORDER, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<OrderCancelResponse> cancelOrder(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 获取订单列表(买家视角)
     * <p>
     * 获取买家订单列表，支持按照订单状态、创建时间等条件筛选
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 订单列表
     * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.getBuyerOrderList-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.OrderAPI.GET_BUYER_ORDER_LIST, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<OrderBuyerListResponse> getBuyerOrderList(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 获取订单详情
     * <p>
     * 获取订单的详细信息，包括商品信息、收货地址、物流信息等
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 订单详情
     * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.get.buyerView-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.OrderAPI.GET_ORDER_DETAIL, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<OrderDetailResponse> getOrderDetail(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 买家补充订单留言
     * <p>
     * 买家对订单进行留言补充，可用于与卖家沟通
     *
     * @param appKey 应用标识
     * @param params 请求参数
     * @return 补充留言响应
     * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.addFeedback-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.OrderAPI.ADD_FEEDBACK, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<TradeFeedbackResponse> addFeedback(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 创建跨境订单
     * <p>
     * 创建跨境交易订单，支持多商品下单
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 创建订单响应
     * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.crossBorder.trade.createCrossOrder-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.OrderAPI.CREATE_CROSS_ORDER, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<OrderCreateResponse> createCrossOrder(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 预览订单
     * <p>
     * 在创建订单前预览订单信息，包括价格、运费等
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 订单预览数据
     * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.previewOrder-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.OrderAPI.PREVIEW_ORDER, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<OrderPreviewResponse> previewOrder(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);
}