/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fulfillmen.support.alibaba.api.response.util.AlibabaDateDeserializer;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * 跨境报关信息
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Data
public class TradeCrossCustoms {

    /**
     * 报关单ID
     */
    @JsonProperty("id")
    private Long id;

    /**
     * 创建时间
     */
    @JsonProperty("gmtCreate")
    @JsonDeserialize(using = AlibabaDateDeserializer.class)
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @JsonProperty("gmtModified")
    @JsonDeserialize(using = AlibabaDateDeserializer.class)
    private LocalDateTime gmtModified;

    /**
     * 买家ID
     */
    @JsonProperty("buyerId")
    private Long buyerId;

    /**
     * 主订单ID
     */
    @JsonProperty("orderId")
    private String orderId;

    /**
     * 业务数据类型，默认 1： 报关单
     */
    @JsonProperty("type")
    private Integer type;

    /**
     * 报关信息列表
     */
    @JsonProperty("attributes")
    private List<CustomsAttributesInfo> attributes;

    /**
     * 报关属性信息
     */
    @Data
    public static class CustomsAttributesInfo {

        /**
         * sku标识
         */
        @JsonProperty("sku")
        private String sku;

        /**
         * 中文名称
         */
        @JsonProperty("cName")
        private String cName;

        /**
         * 英文名称
         */
        @JsonProperty("enName")
        private String enName;

        /**
         * 申报价值
         */
        @JsonProperty("amount")
        private Double amount;

        /**
         * 数量
         */
        @JsonProperty("quantity")
        private Double quantity;

        /**
         * 重量（kg）
         */
        @JsonProperty("weight")
        private Double weight;

        /**
         * 报关币种
         */
        @JsonProperty("currency")
        private String currency;
    }
}