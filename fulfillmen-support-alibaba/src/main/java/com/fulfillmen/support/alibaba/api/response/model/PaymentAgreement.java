/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 支付协议信息
 * <p>
 * 描述支付通道的签约状态信息
 *
 * <AUTHOR>
 * @created 2025-01-10
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.pay.protocolPay.isopen-1">API文档</a>
 */
@Data
@NoArgsConstructor
public class PaymentAgreement {

    /**
     * 支付通道
     * <p>
     * 例如：ALIPAY、SHEGOU等
     */
    private String payChannel;

    /**
     * 签约绑定状态
     */
    private String bindingStatus;

    /**
     * 签约状态
     */
    private String signedStatus;

    /**
     * 签约URL（可选）
     */
    private String signUrl;
}