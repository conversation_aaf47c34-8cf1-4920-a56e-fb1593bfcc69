/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.enums;

import lombok.Getter;

/**
 * 物流消息类型
 * <pre>
 * 物流消息类型
 * 物流消息类型包括：
 * 物流单状态变更（买家视角）- LOGISTICS_BUYER_VIEW_TRACE
 * 物流单号修改消息 - LOGISTICS_MAIL_NO_CHANGE
 * 1688跨境物流包裹消息 - LOGISTICS_GLOBAL_1688_PACKAGE_CHANGE
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/9 19:06
 * @description: todo
 * @since 1.0.0
 */
@Getter
public enum LogisticsMessageTypeEnums implements CallbackMessageType {

    /**
     * 物流单状态变更（买家视角）
     * <pre>
     * 1688物流单状态变更消息，包括发货、揽收、运输、派送、签收五个节点首次触发时的消息，仅买家或买家授权的用户能接收到
     * {
     * "OrderLogisticsTracingModel": {
     * "logisticsId": "12345", // 物流编号
     * "cpCode": "123", // cp code
     * "mailNo": "123456", // 运单号
     * "statusChanged": "CONSIGN",
     * "changeTime": "", //发生变化的时间
     * "orderLogsItems": [{ // 该物流单关联的订单信息
     * "orderId": 123456, // 交易主单id
     * "orderEntryId": 12345678 // 交易子单id
     * }]
     * }
     * }
     * statusChanged: 物流单发生变化的状态
     * <p>
     * 包括发货（CONSIGN）、
     * 揽收（ACCEPT）、
     * 运输（TRANSPORT）、
     * 派送（DELIVERING）、
     * 签收（SIGN）
     * </p>
     * </pre>
     */
    LOGISTICS_BUYER_VIEW_TRACE("LOGISTICS_BUYER_VIEW_TRACE", "物流单状态变更（买家视角）"),
    /**
     * 物流单号修改消息
     * <pre>
     * 针对1688物流单号修改的情况，及时通知下游买家
     * {
     * "MailNoChangeModel": {
     * "logisticsId": "123456", // 物流编号
     * "oldCpCode": "1234", // 更改前的cp code
     * "newCpCode": "12345", // 更改后的cp code
     * "oldMailNo": "123", // 更改前的运单号
     * "newMailNo": "1234", // 更改后的运单号
     * "eventTime": "", // 事件发生时间
     * "orderLogsItems": [{ // 该物流单关联的订单信息
     * "orderId": 12345, // 交易主单id
     * "orderEntryId": 12345678 // 交易子单id
     * }]
     * }
     * }
     * </pre>
     */
    LOGISTICS_MAIL_NO_CHANGE("LOGISTICS_MAIL_NO_CHANGE", "物流单号修改消息"),
    /**
     * 1688跨境物流包裹消息
     * <pre>
     * 1688物流单状态变更消息，包括发货、揽收、运输、派送、签收五个节点首次触发时的消息，仅卖家或卖家授权的用户能接收到
     * {
     * "OrderLogisticsTracingModel": {
     * "logisticsId": "12345", // 物流编号
     * "cpCode": "123", // cp code
     * "mailNo": "123456", // 运单号
     * "statusChanged": "ACCEPT", // 物流单发生变化的状态
     * "changeTime": "", //发生变化的时间
     * "orderLogsItems": { // 该物流单关联的订单信息
     * "orderId": 12345, // 交易主单id
     * "orderEntryId": 1234567 // 交易子单id
     * }
     * }
     * }
     * statusChanged: 物流单发生变化的状态
     * <p>
     * 包括发货（CONSIGN）、
     * 揽收（ACCEPT）、
     * 运输（TRANSPORT）、
     * 派送（DELIVERING）、
     * 签收（SIGN）
     * </p>
     * </pre>
     */
    LOGISTICS_GLOBAL_1688_PACKAGE_CHANGE("LOGISTICS_GLOBAL_1688_PACKAGE_CHANGE", "1688跨境物流包裹消息"),
    ;

    private final String messageType;
    private final String messageDesc;

    LogisticsMessageTypeEnums(String messageType, String messageDesc) {
        this.messageType = messageType;
        this.messageDesc = messageDesc;
    }

    /**
     * 根据消息类型获取枚举
     *
     * @param messageType 消息类型
     * @return 枚举
     */
    public static LogisticsMessageTypeEnums fromMessageType(String messageType) {
        for (LogisticsMessageTypeEnums type : LogisticsMessageTypeEnums.values()) {
            if (type.getMessageType().equals(messageType)) {
                return type;
            }
        }
        return null;
    }

}
