/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.logistics;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 买家获取保存的收货地址信息列表请求
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Builder
public record LogisticsReceiveAddressRequestRecord() implements BaseAlibabaRequestRecord {

    /**
     * 创建买家获取保存的收货地址信息列表请求
     *
     * @return 买家获取保存的收货地址信息列表请求
     */
    public static LogisticsReceiveAddressRequestRecord of() {
        return new LogisticsReceiveAddressRequestRecord();
    }

    @Override
    public Map<String, String> toParams() {
        return new HashMap<>();
    }

    @Override
    public void requireParams() {
        // 该接口无需参数校验
    }
}