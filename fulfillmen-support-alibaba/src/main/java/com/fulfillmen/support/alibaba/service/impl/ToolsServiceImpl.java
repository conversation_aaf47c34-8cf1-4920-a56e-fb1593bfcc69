/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service.impl;

import static com.fulfillmen.support.alibaba.api.ApiPaths.ToolsAPI.LOGIN_ID_ENCRYPT;
import static com.fulfillmen.support.alibaba.api.ApiPaths.ToolsAPI.POOL_PRODUCT_PULL;
import static com.fulfillmen.support.alibaba.api.ApiPaths.ToolsAPI.RELATION_ADD;
import static com.fulfillmen.support.alibaba.api.ApiPaths.ToolsAPI.WANGWANG_NICK_DECRYPT;
import static com.fulfillmen.support.alibaba.api.ApiPaths.ToolsAPI.WANGWANG_URL;

import com.fulfillmen.support.alibaba.api.ToolsAPI;
import com.fulfillmen.support.alibaba.api.request.tools.LoginIdEncryptRequestRecord;
import com.fulfillmen.support.alibaba.api.request.tools.PoolProductPullRequestRecord;
import com.fulfillmen.support.alibaba.api.request.tools.RelationAddRequestRecord;
import com.fulfillmen.support.alibaba.api.request.tools.WangwangNickDecryptRequestRecord;
import com.fulfillmen.support.alibaba.api.request.tools.WangwangUrlRequestRecord;
import com.fulfillmen.support.alibaba.api.response.tools.LoginIdEncryptResponse;
import com.fulfillmen.support.alibaba.api.response.tools.PoolProductPullResponse;
import com.fulfillmen.support.alibaba.api.response.tools.RelationAddResponse;
import com.fulfillmen.support.alibaba.api.response.tools.WangwangNickDecryptResponse;
import com.fulfillmen.support.alibaba.api.response.tools.WangwangUrlResponse;
import com.fulfillmen.support.alibaba.autoconfigure.AlibabaProperties;
import com.fulfillmen.support.alibaba.service.IToolsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 阿里巴巴工具类服务实现
 *
 * <AUTHOR>
 * @created 2025-01-16
 */
@Slf4j
@Service
public class ToolsServiceImpl extends BaseAlibabaServiceImpl implements IToolsService {

    private static final String SERVICE_NAME = "工具服务";

    private final ToolsAPI toolsAPI;

    public ToolsServiceImpl(AlibabaProperties alibabaProperties, ToolsAPI toolsAPI) {
        super(alibabaProperties);
        this.toolsAPI = toolsAPI;
    }

    @Override
    protected String getServiceName() {
        return SERVICE_NAME;
    }

    @Override
    public Mono<WangwangUrlResponse> getWangwangUrl(WangwangUrlRequestRecord request) {
        log.debug("[{}] 开始获取旺旺聊天链接, request: {}", SERVICE_NAME, request);
        return wrapWithErrorHandler("获取旺旺聊天链接", request, WANGWANG_URL, formParams -> toolsAPI
            .getWangwangUrl(appKey, formParams));
    }

    @Override
    public Mono<PoolProductPullResponse> pullPoolProducts(PoolProductPullRequestRecord request) {
        log.debug("[{}] 开始拉取商品池数据, request: {}", SERVICE_NAME, request);
        return wrapWithErrorHandler("拉取商品池数据", request, POOL_PRODUCT_PULL, formParams -> toolsAPI
            .pullPoolProducts(appKey, formParams));
    }

    @Override
    public Mono<RelationAddResponse> addRelation(RelationAddRequestRecord request) {
        log.debug("[{}] 开始添加买卖家分销关系, request: {}", SERVICE_NAME, request);
        return wrapWithErrorHandler("添加买卖家分销关系", request, RELATION_ADD, formParams -> toolsAPI
            .addRelation(appKey, formParams));
    }

    @Override
    public Mono<LoginIdEncryptResponse> encryptLoginId(LoginIdEncryptRequestRecord request) {
        log.info("开始加密用户loginId: {}", request.loginId());

        return wrapWithErrorHandler("加密用户loginId", request, LOGIN_ID_ENCRYPT, formParams -> toolsAPI
            .encryptLoginId(appKey, formParams));
    }

    @Override
    public Mono<WangwangNickDecryptResponse> decryptWangwangNick(WangwangNickDecryptRequestRecord request) {
        log.info("开始解密旺旺昵称, openUid: {}", request.openUid());

        return wrapWithErrorHandler("解密旺旺昵称", request, WANGWANG_NICK_DECRYPT, formParams -> toolsAPI
            .decryptWangwangNick(appKey, formParams));
    }
}