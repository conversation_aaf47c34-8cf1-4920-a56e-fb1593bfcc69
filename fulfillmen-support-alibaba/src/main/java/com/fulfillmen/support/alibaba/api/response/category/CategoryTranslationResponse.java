/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.category;

import java.util.List;
import lombok.Data;

/**
 * 1688类目翻译查询响应
 *
 * <AUTHOR>
 * @date 2025-01-02 10:13
 * @description: 1688平台类目翻译API的响应对象
 * @since 1.0.0
 */
@Data
public class CategoryTranslationResponse {

    /**
     * 响应结果包装
     */
    private ResultWrapper result;

    @Data
    public static class ResultWrapper {

        /**
         * 是否成功
         */
        private Boolean success;

        /**
         * 错误码
         */
        private String code;

        /**
         * 错误信息
         */
        private String message;

        /**
         * 翻译结果
         */
        private List<CategoryTranslation> result;
    }

    @Data
    public static class CategoryTranslation {

        /**
         * 类目ID
         */
        private Long categoryId;

        /**
         * 类目中文名称
         */
        private String chineseName;

        /**
         * 类目翻译名称
         */
        private String translatedName;

        /**
         * 语种
         */
        private String language;

        /**
         * 是否叶子类目
         */
        private Boolean leaf;

        /**
         * 类目层级
         */
        private String level;

        /**
         * 父类目ID
         */
        private Long parentCateId;
    }
}