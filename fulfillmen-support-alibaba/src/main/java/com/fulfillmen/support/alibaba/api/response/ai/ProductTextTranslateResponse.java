/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.ai;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 商品文本翻译响应结果
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class ProductTextTranslateResponse {

    /**
     * 请求是否成功
     */
    @JsonProperty("success")
    private Boolean success;

    /**
     * 错误码
     */
    @JsonProperty("code")
    private String code;

    /**
     * 错误信息
     */
    @JsonProperty("message")
    private String message;

    /**
     * 翻译结果 返回翻译后的文本列表，与请求的sourceTextList一一对应 返回的是一个 "["翻译后文本 1","翻译后文本 2"]"
     */
    @JsonProperty("result")
    private String result;
}