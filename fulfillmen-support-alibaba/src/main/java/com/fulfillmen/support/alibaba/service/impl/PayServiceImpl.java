/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service.impl;

import com.fulfillmen.support.alibaba.api.ApiPaths;
import com.fulfillmen.support.alibaba.api.PayAPI;
import com.fulfillmen.support.alibaba.api.request.pay.AccountPeriodListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.AlipayUrlRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.CheckProtocolPayRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.CreditPayUrlRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.CrossBorderPayUrlRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.PayWayQueryRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.PrepareProtocolPayRequestRecord;
import com.fulfillmen.support.alibaba.api.response.pay.AccountPeriodListResponse;
import com.fulfillmen.support.alibaba.api.response.pay.AlipayUrlResponse;
import com.fulfillmen.support.alibaba.api.response.pay.CheckProtocolPayResponse;
import com.fulfillmen.support.alibaba.api.response.pay.CreditPayUrlResponse;
import com.fulfillmen.support.alibaba.api.response.pay.CrossBorderPayUrlResponse;
import com.fulfillmen.support.alibaba.api.response.pay.PayWayQueryResponse;
import com.fulfillmen.support.alibaba.api.response.pay.PrepareProtocolPayResponse;
import com.fulfillmen.support.alibaba.autoconfigure.AlibabaProperties;
import com.fulfillmen.support.alibaba.service.IPayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 支付服务实现类
 *
 * <AUTHOR>
 * @created 2025-01-10
 */
@Slf4j
@Service
public class PayServiceImpl extends BaseAlibabaServiceImpl implements IPayService {

    private static final String SERVICE_NAME = "支付服务";
    private final PayAPI payAPI;

    public PayServiceImpl(PayAPI payAPI, AlibabaProperties alibabaProperties) {
        super(alibabaProperties);
        this.payAPI = payAPI;
    }

    @Override
    protected String getServiceName() {
        return SERVICE_NAME;
    }

    @Override
    public Mono<AccountPeriodListResponse> getAccountPeriodList(AccountPeriodListRequestRecord request) {
        log.debug("开始查询买家信用账期信息: {}", request);
        return wrapWithErrorHandler("查询买家信用账期信息", request, ApiPaths.PayAPI.GET_ACCOUNT_PERIOD_LIST, formParams -> payAPI
            .getAccountPeriodList(appKey, formParams));
    }

    @Override
    public Mono<AlipayUrlResponse> getAlipayUrl(AlipayUrlRequestRecord request) {
        log.debug("开始获取支付宝支付链接: {}", request);
        return wrapWithErrorHandler("获取支付宝支付链接", request, ApiPaths.PayAPI.GET_ALIPAY_URL, formParams -> payAPI
            .getAlipayUrl(appKey, formParams));
    }

    @Override
    public Mono<CheckProtocolPayResponse> checkProtocolPay(CheckProtocolPayRequestRecord request) {
        log.debug("开始查询是否开通免密支付: {}", request);
        return wrapWithErrorHandler("查询是否开通免密支付", request, ApiPaths.PayAPI.CHECK_PROTOCOL_PAY, formParams -> payAPI
            .checkProtocolPay(appKey, formParams));
    }

    @Override
    public Mono<CreditPayUrlResponse> getCreditPayUrl(CreditPayUrlRequestRecord request) {
        log.debug("开始获取诚e赊支付链接: {}", request);
        return wrapWithErrorHandler("获取诚e赊支付链接", request, ApiPaths.PayAPI.GET_CREDIT_PAY_URL, formParams -> payAPI
            .getCreditPayUrl(appKey, formParams));
    }

    @Override
    public Mono<CrossBorderPayUrlResponse> getCrossBorderPayUrl(CrossBorderPayUrlRequestRecord request) {
        log.debug("开始获取跨境宝支付链接: {}", request);
        return wrapWithErrorHandler("获取跨境宝支付链接", request, ApiPaths.PayAPI.GET_CROSS_BORDER_PAY_URL, formParams -> payAPI
            .getCrossBorderPayUrl(appKey, formParams));
    }

    @Override
    public Mono<PayWayQueryResponse> getPayWayQuery(PayWayQueryRequestRecord request) {
        log.debug("开始查询订单可用支付方式: {}", request);
        return wrapWithErrorHandler("查询订单可用支付方式", request, ApiPaths.PayAPI.QUERY_PAY_WAY, formParams -> payAPI
            .queryPayWay(appKey, formParams));
    }

    @Override
    public Mono<PrepareProtocolPayResponse> prepareProtocolPay(PrepareProtocolPayRequestRecord request) {
        log.debug("开始发起免密支付: {}", request);
        return wrapWithErrorHandler("发起免密支付", request, ApiPaths.PayAPI.PREPARE_PROTOCOL_PAY, formParams -> payAPI
            .prepareProtocolPay(appKey, formParams), "tradeWithholdPreparePayParam");
    }
}