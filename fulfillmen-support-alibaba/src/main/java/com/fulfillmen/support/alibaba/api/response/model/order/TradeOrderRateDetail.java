/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fulfillmen.support.alibaba.api.response.util.AlibabaDateDeserializer;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 评价详情
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Data
public class TradeOrderRateDetail {

    /**
     * 评价星级
     */
    @JsonProperty("starLevel")
    private Integer starLevel;

    /**
     * 评价详情
     */
    @JsonProperty("content")
    private String content;

    /**
     * 收到评价的用户昵称
     */
    @JsonProperty("receiverNick")
    private String receiverNick;

    /**
     * 发送评价的用户昵称
     */
    @JsonProperty("posterNick")
    private String posterNick;

    /**
     * 评价上线时间
     */
    @JsonProperty("publishTime")
    @JsonDeserialize(using = AlibabaDateDeserializer.class)
    private LocalDateTime publishTime;
}