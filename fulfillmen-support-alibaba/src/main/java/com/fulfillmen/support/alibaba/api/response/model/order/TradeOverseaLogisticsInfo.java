/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

/**
 * 海外收货地址
 *
 * <AUTHOR>
 * @date 2025/7/22 13:49
 * @description: todo
 * @since 1.0.0
 */
@Data
public class TradeOverseaLogisticsInfo {

    /**
     * 海外物流单号
     * <pre>
     * ["TN0003077L" ]
     * </pre>
     */
    @JsonProperty("overseasLogisticsIds")
    private List<String> overseasLogisticsIds;

    /**
     * 海外收货地址
     * <pre>
     * "addressDetail":"test", "cityName":"Г.Курчатов", "areaName":" ",
     * "warehouseContactName":"Zeus", "mobile":"123456789", "fixedPhone":"123456789",
     * "postCode":"1111", "provinceName":"Абайская область", "countryName":"Казахстан",
     * </pre>
     */
    @JsonProperty("overseasTransportUserAddrInfo")
    private OverseasTransportUserAddrInfo overseasTransportUserAddrInfo;

    @Data
    public static class OverseasTransportUserAddrInfo {

        /**
         * 物流码 示例值: 123
         */
        @JsonProperty("postCode")
        private String postCode;

        /**
         * 国家 示例值: 越南
         */
        @JsonProperty("countryName")
        private String countryName;

        /**
         * 省份 示例值: xx
         */
        @JsonProperty("provinceName")
        private String provinceName;

        /**
         * 城市 示例值: x x
         */
        @JsonProperty("cityName")
        private String cityName;

        /**
         * 区 示例值: xx
         */
        @JsonProperty("areaName")
        private String areaName;

        /**
         * 地址 示例值: xx
         */
        @JsonProperty("addressDetail")
        private String addressDetail;

        /**
         * 手机号 示例值: 123
         */
        @JsonProperty("mobile")
        private String mobile;

        /**
         * 手机号 示例值: 123
         */
        @JsonProperty("fixedPhone")
        private String fixedPhone;

        /**
         * 仓库联系人 示例值: aa
         */
        @JsonProperty("warehouseContactName")
        private String warehouseContactName;

        /**
         * 仓库名称 示例值: aa
         */
        @JsonProperty("warehouseName")
        private String warehouseName;
    }

}
