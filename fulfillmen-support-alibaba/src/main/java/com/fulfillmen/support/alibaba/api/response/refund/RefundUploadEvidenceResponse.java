/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.refund;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import com.fulfillmen.support.alibaba.api.response.model.RefundUploadEvidenceResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 上传退款凭证响应
 * <p>
 * 买家上传退款凭证的响应结果，包含上传后的退款单状态等信息
 *
 * <AUTHOR>
 * @created 2025-01-13
 * @see RefundUploadEvidenceResult
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RefundUploadEvidenceResponse extends BaseAlibabaResponse {

    /**
     * 退款凭证上传结果
     */
    private RefundUploadEvidenceResult result;
}