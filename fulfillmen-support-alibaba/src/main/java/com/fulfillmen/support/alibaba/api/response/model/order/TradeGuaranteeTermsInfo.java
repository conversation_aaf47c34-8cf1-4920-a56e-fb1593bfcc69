/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 保障条款信息
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Data
public class TradeGuaranteeTermsInfo {

    /**
     * 保障信息
     *
     * <pre>
     * 自愿选择向买家提供“交期保障”服务
     * </pre>
     */
    @JsonProperty("assuranceInfo")
    private String assuranceInfo;

    /**
     * 保障类型
     *
     * <pre>
     * jqbz
     * 保障方式。国际站：TA(信保)
     * </pre>
     */
    @JsonProperty("assuranceType")
    private String assuranceType;

    /**
     * 质量保证类型。
     *
     * <pre>
     * 国际站：pre_shipment(发货前),post_delivery(发货后)
     * </pre>
     */
    @JsonProperty("qualityAssuranceType")
    private String qualityAssuranceType;

    /**
     * 保障条款值
     *
     * <pre>
     * 保障条款值，比如交期保障里，6表示6天
     * </pre>
     */
    @JsonProperty("value")
    private String value;
}
