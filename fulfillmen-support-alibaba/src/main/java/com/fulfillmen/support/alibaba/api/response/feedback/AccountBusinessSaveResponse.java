/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.feedback;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 保存账号所属业务线响应
 *
 * <AUTHOR>
 * @created 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AccountBusinessSaveResponse extends BaseAlibabaResponse {

    /**
     * 响应结果
     */
    private Result result;

    @Data
    public static class Result {

        /**
         * 保存结果 true成功，false失败
         */
        private Boolean saveResult;
    }
}