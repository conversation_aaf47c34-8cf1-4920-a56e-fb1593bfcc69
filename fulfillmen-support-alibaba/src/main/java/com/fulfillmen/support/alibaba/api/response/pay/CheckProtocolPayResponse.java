/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.pay;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import com.fulfillmen.support.alibaba.api.response.model.ProtocolPayResult;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查询是否开通免密支付响应
 * <p>
 * 包含各支付通道的签约状态信息
 *
 * <AUTHOR>
 * @created 2025-01-10
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.pay.protocolPay.isopen-1">API文档</a>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CheckProtocolPayResponse extends BaseAlibabaResponse {

    /**
     * 查询结果
     */
    private ProtocolPayResult result;
}