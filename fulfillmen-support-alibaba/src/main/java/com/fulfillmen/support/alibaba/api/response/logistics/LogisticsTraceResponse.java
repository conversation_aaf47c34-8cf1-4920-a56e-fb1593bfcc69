/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.logistics;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import com.fulfillmen.support.alibaba.api.response.model.LogisticsTraceInfo;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 物流跟踪信息响应
 *
 * <AUTHOR>
 * @created 2025-01-14
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:alibaba.trade.getLogisticsTraceInfo.buyerView-1">API文档</a>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LogisticsTraceResponse extends BaseAlibabaResponse {

    /**
     * 跟踪单详情
     */
    private List<LogisticsTraceInfo> logisticsTrace;
}