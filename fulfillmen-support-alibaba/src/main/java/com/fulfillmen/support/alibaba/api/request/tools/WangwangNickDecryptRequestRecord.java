/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.tools;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * Openuid转换解密为旺旺昵称请求
 *
 * <AUTHOR>
 * @created 2025-01-16
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.account:wangwangnick.openuid.decrypt-1">API文档</a>
 */
@Builder
public record WangwangNickDecryptRequestRecord(
    /**
     * 待解密的openUid
     */
    String openUid
) implements BaseAlibabaRequestRecord {

    /**
     * 创建请求实例
     *
     * @param openUid 待解密的openUid
     * @return 请求实例
     */
    public static WangwangNickDecryptRequestRecord of(String openUid) {
        return new WangwangNickDecryptRequestRecord(openUid);
    }

    @Override
    public void requireParams() {
        assertNotBlank(openUid, "待解密的openUid不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("openUid", openUid);
        return params;
    }
}