/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.tools;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 获取唤起旺旺聊天链接请求
 *
 * <AUTHOR>
 * @created 2025-01-16
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.wangwang.url.get-1">获取唤起旺旺聊天链接</a>
 */
@Builder
public record WangwangUrlRequestRecord(/**
                                        * 聊天对象openUid
                                        */
String toOpenUid) implements BaseAlibabaRequestRecord {

    public static WangwangUrlRequestRecord of(String toOpenUid) {
        return new WangwangUrlRequestRecord(toOpenUid);
    }

    @Override
    public void requireParams() {
        assertNotBlank(toOpenUid, "聊天对象openUid不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("toOpenUid", toOpenUid);
        return params;
    }
}