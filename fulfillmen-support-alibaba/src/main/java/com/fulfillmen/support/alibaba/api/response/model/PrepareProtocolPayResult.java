/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 免密支付结果
 * <p>
 * 包含支付成功通道和支付状态信息
 *
 * <AUTHOR>
 * @created 2025-01-10
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.pay.protocolPay.preparePay-1">API文档</a>
 */
@Data
@NoArgsConstructor
public class PrepareProtocolPayResult {

    /**
     * 支付成功通道
     */
    private String payChannel;

    /**
     * 支付是否成功
     * <p>
     * 在超时的情况下，可能返回false但实际扣款成功的情况，需要查询订单实际支付状态
     */
    private Boolean paySuccess;
}