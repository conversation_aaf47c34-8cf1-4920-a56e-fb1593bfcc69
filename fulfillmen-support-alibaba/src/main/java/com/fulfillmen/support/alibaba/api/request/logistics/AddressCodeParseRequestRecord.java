/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.logistics;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 根据地址解析地区码请求
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Builder
public record AddressCodeParseRequestRecord(
    /**
     * 地址信息 必填
     */
    String addressInfo
) implements BaseAlibabaRequestRecord {

    /**
     * 创建根据地址解析地区码请求
     *
     * @param addressInfo 地址信息(必填)
     * @return 根据地址解析地区码请求
     */
    public static AddressCodeParseRequestRecord of(String addressInfo) {
        return new AddressCodeParseRequestRecord(addressInfo);
    }

    @Override
    public void requireParams() {
        assertNotBlank(addressInfo, "地址信息不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("addressInfo", addressInfo);
        return params;
    }
}