/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.tools;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 获取唤起旺旺聊天链接响应
 *
 * <AUTHOR>
 * @created 2025-01-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WangwangUrlResponse extends BaseAlibabaResponse {

    /**
     * 返回结果
     */
    private Result result;

    @Data
    public static class Result {

        /**
         * 返回码
         */
        private String code;

        /**
         * 返回信息
         */
        private String message;

        /**
         * 旺旺聊天链接
         */
        private String result;

        /**
         * 返回码列表
         */
        private List<String> retCodes;

        /**
         * 子返回码
         */
        private String subCode;

        /**
         * 子返回信息
         */
        private String subMessage;

        /**
         * 是否成功
         */
        private Boolean success;
    }
}