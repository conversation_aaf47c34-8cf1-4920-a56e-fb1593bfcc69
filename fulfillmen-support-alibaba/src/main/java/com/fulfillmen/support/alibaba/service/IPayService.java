/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service;

import com.fulfillmen.support.alibaba.api.request.pay.AccountPeriodListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.AlipayUrlRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.CheckProtocolPayRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.CreditPayUrlRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.CrossBorderPayUrlRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.PayWayQueryRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.PrepareProtocolPayRequestRecord;
import com.fulfillmen.support.alibaba.api.response.pay.AccountPeriodListResponse;
import com.fulfillmen.support.alibaba.api.response.pay.AlipayUrlResponse;
import com.fulfillmen.support.alibaba.api.response.pay.CheckProtocolPayResponse;
import com.fulfillmen.support.alibaba.api.response.pay.CreditPayUrlResponse;
import com.fulfillmen.support.alibaba.api.response.pay.CrossBorderPayUrlResponse;
import com.fulfillmen.support.alibaba.api.response.pay.PayWayQueryResponse;
import com.fulfillmen.support.alibaba.api.response.pay.PrepareProtocolPayResponse;
import reactor.core.publisher.Mono;

/**
 * 支付服务接口
 * <p>
 * 提供支付相关的功能,包括:
 * <ul>
 * <li>查询买家信用账期信息</li>
 * <li>获取支付宝支付链接</li>
 * <li>查询是否开通免密支付</li>
 * <li>获取诚e赊支付链接</li>
 * <li>获取跨境宝支付链接</li>
 * <li>查询订单可用支付方式</li>
 * <li>发起免密支付</li>
 * </ul>
 *
 * <AUTHOR>
 * @created 2025-01-10
 */
public interface IPayService {

    /**
     * 查询买家信用账期信息
     *
     * @param request 查询买家信用账期信息请求
     * @return 查询买家信用账期信息响应
     */
    Mono<AccountPeriodListResponse> getAccountPeriodList(AccountPeriodListRequestRecord request);

    /**
     * 获取支付宝支付链接
     *
     * @param request 获取支付宝支付链接请求
     * @return 获取支付宝支付链接响应
     */
    Mono<AlipayUrlResponse> getAlipayUrl(AlipayUrlRequestRecord request);

    /**
     * 查询是否开通免密支付
     *
     * @param request 查询是否开通免密支付请求
     * @return 查询是否开通免密支付响应
     */
    Mono<CheckProtocolPayResponse> checkProtocolPay(CheckProtocolPayRequestRecord request);

    /**
     * 获取诚e赊支付链接
     *
     * @param request 获取诚e赊支付链接请求
     * @return 获取诚e赊支付链接响应
     */
    Mono<CreditPayUrlResponse> getCreditPayUrl(CreditPayUrlRequestRecord request);

    /**
     * 获取跨境宝支付链接
     *
     * @param request 获取跨境宝支付链接请求
     * @return 获取跨境宝支付链接响应
     */
    Mono<CrossBorderPayUrlResponse> getCrossBorderPayUrl(CrossBorderPayUrlRequestRecord request);

    /**
     * 查询订单可用支付方式
     *
     * @param request 查询订单可用支付方式请求
     * @return 查询订单可用支付方式响应
     */
    Mono<PayWayQueryResponse> getPayWayQuery(PayWayQueryRequestRecord request);

    /**
     * 发起免密支付
     *
     * @param request 发起免密支付请求
     * @return 发起免密支付响应
     */
    Mono<PrepareProtocolPayResponse> prepareProtocolPay(PrepareProtocolPayRequestRecord request);
}