/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 买家补充订单留言请求
 * <p>
 * 用于买家对订单进行留言补充说明
 *
 * <AUTHOR>
 * @created 2025-01-10
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.addFeedback-1">API文档</a>
 */
@Builder
public record TradeFeedbackRequestRecord(
    /**
     * 订单ID
     */
    @JsonProperty("orderId") String orderId,

    /**
     * 留言内容
     */
    String feedback
) implements BaseAlibabaRequestRecord {

    /**
     * 创建 TradeFeedbackRequestRecord 实例
     *
     * @param orderId  订单ID
     * @param feedback 留言内容
     * @return TradeFeedbackRequestRecord 实例
     * @throws IllegalArgumentException 如果参数无效
     */
    public static TradeFeedbackRequestRecord of(String orderId, String feedback) {
        return new TradeFeedbackRequestRecord(orderId, feedback);
    }

    @Override
    public void requireParams() {

        assertNotNull(orderId, "订单ID不能为空");
        assertNotBlank(feedback, "留言内容不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("orderId", orderId.toString());
        params.put("feedback", feedback);
        return params;
    }
}