/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.logistics;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 运费险信息查询响应
 *
 * <AUTHOR>
 * @created 2025-01-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LogisticsInsuranceResponse extends BaseAlibabaResponse {

    /**
     * 返回结果
     */
    private ResultModel result;

    @Data
    public static class ResultModel {

        /**
         * 是否成功
         */
        private Boolean success;

        /**
         * 响应码
         */
        private String code;

        /**
         * 响应信息
         */
        private String message;

        /**
         * 返回结果
         */
        private TradeFreightPolicyResult result;
    }

    @Data
    public static class TradeFreightPolicyResult {

        /**
         * 保单id
         */
        private Long insuranceId;

        /**
         * 订单id
         */
        private Long orderId;

        /**
         * 理赔单信息
         */
        private List<TradeClaimResult> tradeClaimList;
    }

    @Data
    public static class TradeClaimResult {

        /**
         * 申请时间
         */
        private Date applicationTime;

        /**
         * 理赔金额
         */
        private Long claimAmount;

        /**
         * 理赔单id
         */
        private String claimId;

        /**
         * 打款时间
         */
        private Date payTime;

        /**
         * 理赔状态
         */
        private String status;

        /**
         * 支付宝交易流水号
         */
        private String tradeNO;
    }
}