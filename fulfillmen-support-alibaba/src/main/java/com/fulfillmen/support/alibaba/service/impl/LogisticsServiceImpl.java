/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service.impl;

import com.fulfillmen.support.alibaba.api.ApiPaths;
import com.fulfillmen.support.alibaba.api.LogisticsAPI;
import com.fulfillmen.support.alibaba.api.request.logistics.AddressCodeParseRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsCompanyListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsFreightTemplateRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsInfoRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsInsuranceRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsOutOrderIdRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsReceiveAddressRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsTraceRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.ProductFreightEstimateRequestRecord;
import com.fulfillmen.support.alibaba.api.response.logistics.AddressCodeParseResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsCompanyListResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsFreightTemplateResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsInfoResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsInsuranceResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsOutOrderIdResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsReceiveAddressResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsTraceResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.ProductFreightEstimateResponse;
import com.fulfillmen.support.alibaba.autoconfigure.AlibabaProperties;
import com.fulfillmen.support.alibaba.service.ILogisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 物流服务实现类
 *
 * <AUTHOR>
 * @created 2025-01-16
 */
@Slf4j
@Service
public class LogisticsServiceImpl extends BaseAlibabaServiceImpl implements ILogisticsService {

    private static final String SERVICE_NAME = "物流服务";
    private final LogisticsAPI logisticsAPI;

    public LogisticsServiceImpl(LogisticsAPI logisticsAPI,
        AlibabaProperties alibabaProperties) {
        super(alibabaProperties);
        this.logisticsAPI = logisticsAPI;
    }

    @Override
    protected String getServiceName() {
        return SERVICE_NAME;
    }

    @Override
    public Mono<LogisticsFreightTemplateResponse> getFreightTemplate(LogisticsFreightTemplateRequestRecord request) {
        log.debug("开始处理运费模板请求: {}", request);
        return wrapWithErrorHandler("获取运费模板", request, ApiPaths.LogisticsAPI.GET_FREIGHT_TEMPLATE, formParams -> logisticsAPI
            .getFreightTemplate(appKey, formParams));
    }

    @Override
    public Mono<AddressCodeParseResponse> parseAddressCode(AddressCodeParseRequestRecord request) {
        log.debug("开始处理地址解析请求: {}", request);
        return wrapWithErrorHandler("解析地址编码", request, ApiPaths.LogisticsAPI.ADDRESS_CODE_PARSE, formParams -> logisticsAPI
            .parseAddressCode(appKey, formParams));
    }

    @Override
    public Mono<LogisticsOutOrderIdResponse> getOutOrderId(LogisticsOutOrderIdRequestRecord request) {
        log.debug("开始处理获取物流单号请求: {}", request);
        return wrapWithErrorHandler("获取物流单号", request, ApiPaths.LogisticsAPI.GET_OUT_ORDER_ID, formParams -> logisticsAPI
            .getOutOrderId(appKey, formParams));
    }

    @Override
    public Mono<LogisticsInfoResponse> getLogisticsInfo(LogisticsInfoRequestRecord request) {
        log.debug("开始处理物流信息请求: {}", request);
        return wrapWithErrorHandler("获取物流信息", request, ApiPaths.LogisticsAPI.GET_LOGISTICS_INFO, formParams -> logisticsAPI
            .getLogisticsInfo(appKey, formParams));
    }

    @Override
    public Mono<LogisticsTraceResponse> getLogisticsTrace(LogisticsTraceRequestRecord request) {
        log.debug("开始处理物流轨迹请求: {}", request);
        return wrapWithErrorHandler("获取物流轨迹", request, ApiPaths.LogisticsAPI.GET_LOGISTICS_TRACE, formParams -> logisticsAPI
            .getLogisticsTrace(appKey, formParams));
    }

    @Override
    public Mono<LogisticsReceiveAddressResponse> getReceiveAddress(LogisticsReceiveAddressRequestRecord request) {
        log.debug("开始处理收货地址请求: {}", request);
        return wrapWithErrorHandler("获取收货地址", request, ApiPaths.LogisticsAPI.GET_RECEIVE_ADDRESS, formParams -> logisticsAPI
            .getReceiveAddress(appKey, formParams));
    }

    @Override
    public Mono<ProductFreightEstimateResponse> estimateFreight(ProductFreightEstimateRequestRecord request) {
        log.debug("开始处理运费预估请求: {}", request);
        return wrapWithErrorHandler("商品中国国内运费预估", request, ApiPaths.LogisticsAPI.FREIGHT_ESTIMATE, formParams -> logisticsAPI
            .estimateFreight(appKey, formParams), "productFreightQueryParamsNew");
    }

    @Override
    public Mono<LogisticsCompanyListResponse> getLogisticsCompanyList(LogisticsCompanyListRequestRecord request) {
        log.debug("开始处理物流公司列表请求: {}", request);
        return wrapWithErrorHandler("获取物流公司列表", request, ApiPaths.LogisticsAPI.GET_LOGISTICS_COMPANY_LIST, formParams -> logisticsAPI
            .getLogisticsCompanyList(appKey, formParams));
    }

    @Override
    public Mono<LogisticsInsuranceResponse> getShippingInsurance(LogisticsInsuranceRequestRecord request) {
        log.debug("开始处理运费险信息请求: {}", request);
        return wrapWithErrorHandler("查询运费险信息", request, ApiPaths.LogisticsAPI.GET_SHIPPING_INSURANCE, formParams -> logisticsAPI
            .getShippingInsurance(appKey, formParams));
    }
}