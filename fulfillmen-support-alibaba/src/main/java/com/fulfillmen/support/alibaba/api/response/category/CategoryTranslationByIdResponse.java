/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.category;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 根据类目ID查询多语言类目响应
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CategoryTranslationByIdResponse extends BaseAlibabaResponse {

    /**
     * 返回结果包装
     */
    @JsonProperty("result")
    private ResultWrapper result;

    @Data
    public static class ResultWrapper {

        /**
         * 是否成功
         */
        @JsonProperty("success")
        private Boolean success;

        /**
         * 错误码
         */
        @JsonProperty("code")
        private String code;

        /**
         * 错误信息
         */
        @JsonProperty("message")
        private String message;

        /**
         * 返回结果
         */
        @JsonProperty("result")
        private Result result;
    }

    @Data
    public static class Result {

        /**
         * 是否来自缓存
         */
        @JsonProperty("fromCache")
        private Boolean fromCache;

        /**
         * 子类目列表
         */
        @JsonProperty("children")
        private List<Child> children;

        /**
         * 类目层级
         */
        @JsonProperty("level")
        private String level;

        /**
         * 中文名称
         */
        @JsonProperty("chineseName")
        private String chineseName;

        /**
         * 语言
         */
        @JsonProperty("language")
        private String language;

        /**
         * 是否叶子节点
         */
        @JsonProperty("leaf")
        private String leaf;

        /**
         * 翻译后的名称
         */
        @JsonProperty("translatedName")
        private String translatedName;

        /**
         * 类目ID
         */
        @JsonProperty("categoryId")
        private Long categoryId;

        /**
         * 父类目ID
         */
        @JsonProperty("parentCateId")
        private String parentCateId;
    }

    @Data
    public static class Child {

        /**
         * 类目ID
         */
        @JsonProperty("categoryId")
        private Long categoryId;

        /**
         * 中文名称
         */
        @JsonProperty("chineseName")
        private String chineseName;

        /**
         * 翻译后的名称
         */
        @JsonProperty("translatedName")
        private String translatedName;

        /**
         * 语言
         */
        @JsonProperty("language")
        private String language;

        /**
         * 是否叶子节点
         */
        @JsonProperty("leaf")
        private Boolean leaf;

        /**
         * 类目层级
         */
        @JsonProperty("level")
        private String level;

        /**
         * 父类目ID
         */
        @JsonProperty("parentCateId")
        private Long parentCateId;
    }
}