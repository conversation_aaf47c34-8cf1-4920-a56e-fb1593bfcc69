/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model;

import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 账期信息
 * <p>
 * 描述买家的信用账期详细信息
 *
 * <AUTHOR>
 * @created 2025-01-10
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.accountPeriod.list.buyerView-1">API文档</a>
 */
@Data
@NoArgsConstructor
public class AccountPeriodInfo {

    /**
     * 卖家loginId
     */
    private String sellerLoginId;

    /**
     * 卖家公司名
     */
    private String sellerCompanyName;

    /**
     * 授信日期
     */
    private Date gmtQuota;

    /**
     * 授信额度值
     * <p>
     * 单位：分
     */
    private Long quota;

    /**
     * 可用授信额度值
     * <p>
     * 单位：分
     */
    private Long surplusQuota;

    /**
     * 状态描述
     */
    private String statusStr;

    /**
     * 账期日期描述
     */
    private String tapDateStr;

    /**
     * 逾期次数
     */
    private Integer tapOverdue;
}