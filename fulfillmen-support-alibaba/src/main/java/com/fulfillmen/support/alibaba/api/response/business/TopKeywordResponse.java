/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.business;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品热搜词响应
 * <p>
 * 包含热搜词列表数据，每个热搜词包含中文和对应的译文。
 *
 * <AUTHOR>
 * @created 2025-01-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TopKeywordResponse extends BaseAlibabaResponse {

    /**
     * 响应结果
     */
    private Result result;

    @Data
    public static class Result {

        /**
         * 是否成功
         */
        @JsonProperty("success")
        private Boolean success;

        /**
         * 响应码
         */
        @JsonProperty("code")
        private String code;

        /**
         * 响应消息
         */
        @JsonProperty("message")
        private String message;

        /**
         * 热搜词列表
         */
        @JsonProperty("result")
        private List<TopSeKeywordModel> result;
    }

    @Data
    public static class TopSeKeywordModel {

        /**
         * 中文热词，用于词搜
         */
        @JsonProperty("seKeyword")
        private String seKeyword;

        /**
         * 译文热词，用于展示
         */
        @JsonProperty("seKeywordTranslation")
        private String seKeywordTranslation;
    }
}