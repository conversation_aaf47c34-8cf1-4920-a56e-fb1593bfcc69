/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.business;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询榜单列表响应
 *
 * <AUTHOR>
 * @created 2025-01-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RankQueryResponse extends BaseAlibabaResponse {

    private Result result;

    @Data
    public static class Result {

        @JsonProperty("success")
        private Boolean success;

        @JsonProperty("code")
        private String code;

        @JsonProperty("message")
        private String message;

        @JsonProperty("result")
        private RankModel result;
    }

    @Data
    public static class RankModel {

        @JsonProperty("rankId")
        private String rankId;

        @JsonProperty("rankName")
        private String rankName;

        @JsonProperty("rankType")
        private String rankType;

        @JsonProperty("rankProductModels")
        private List<RankProductModel> rankProductModels;
    }

    @Data
    public static class RankProductModel {

        @JsonProperty("itemId")
        private Long itemId;

        @JsonProperty("title")
        private String title;

        @JsonProperty("translateTitle")
        private String translateTitle;

        @JsonProperty("imgUrl")
        private String imgUrl;

        @JsonProperty("sort")
        private Integer sort;

        @JsonProperty("serviceList")
        private List<String> serviceList;

        @JsonProperty("buyerNum")
        private Integer buyerNum;

        @JsonProperty("soldOut")
        private Integer soldOut;

        @JsonProperty("goodsScore")
        private String goodsScore;
    }
}