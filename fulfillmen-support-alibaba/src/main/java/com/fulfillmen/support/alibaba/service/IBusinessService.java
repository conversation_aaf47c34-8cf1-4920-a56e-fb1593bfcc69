/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service;

import com.fulfillmen.support.alibaba.api.request.business.RankQueryRequestRecord;
import com.fulfillmen.support.alibaba.api.request.business.SellTrendRequestRecord;
import com.fulfillmen.support.alibaba.api.request.business.TopKeywordRequestRecord;
import com.fulfillmen.support.alibaba.api.response.business.RankQueryResponse;
import com.fulfillmen.support.alibaba.api.response.business.SellTrendResponse;
import com.fulfillmen.support.alibaba.api.response.business.TopKeywordResponse;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceException;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import reactor.core.publisher.Mono;

/**
 * 阿里巴巴业务服务接口
 * <p>
 * 提供以下业务服务： 1. 榜单查询 - 获取指定榜单的商品信息，支持多语言 2. 商品热搜词 - 获取指定条件下的热搜关键词，支持多语言 3. 商品每日销售数量趋势 - 获取商品在指定时间段内的每日销售数量趋势
 *
 * <AUTHOR>
 * @created 2025-01-23
 */
public interface IBusinessService {

    /**
     * 查询榜单列表
     * <p>
     * 通过榜单ID批量拉取池中商品数据，支持多语言查询。 可以获取榜单中的商品详细信息，包括标题、图片、销量等数据。
     *
     * @param request 榜单查询请求参数，包含： - rankId: 榜单ID（必填） - rankType: 榜单类型（必填） - limit: 榜单商品个数，最多20（必填） - language: 榜单商品语言（必填）
     * @return 榜单列表数据，包含商品的详细信息
     * @throws AlibabaServiceValidationException 当必填参数为空或格式错误时
     * @throws AlibabaServiceException           当API调用失败时
     */
    Mono<RankQueryResponse> queryRankList(RankQueryRequestRecord request);

    /**
     * 获取商品热搜词
     * <p>
     * 获取指定条件下的热搜关键词，支持多语言。 可以获取不同类目下的热门搜索词，帮助优化搜索和推荐。
     *
     * @param request 热搜词查询请求参数，包含： - country: 语言，参考开发参考枚举（必填） - sourceId: 查询id，如类目id（必填） - hotKeywordType: 热搜类型，目前只提供类目维度（必填）
     * @return 热搜词列表数据，包含中文和译文
     * @throws AlibabaServiceValidationException 当必填参数为空或格式错误时
     * @throws AlibabaServiceException           当API调用失败时
     */
    Mono<TopKeywordResponse> getTopKeywords(TopKeywordRequestRecord request);

    /**
     * 获取商品每日销售数量趋势
     * <p>
     * 获取商品在指定时间段内的每日销售数量趋势数据。 可以分析商品的销售表现，了解销量变化趋势。 注意：查询时间段最多支持1个月。
     *
     * @param request 销售趋势查询请求参数，包含： - offerId: 商品id（必填） - startDate: 查询起始时间，格式：yyyyMMdd（必填） - endDate: 查询截止时间，格式：yyyyMMdd（必填）
     * @return 商品每日销售数量趋势数据
     * @throws AlibabaServiceValidationException 当必填参数为空或格式错误时
     * @throws AlibabaServiceException           当API调用失败时
     */
    Mono<SellTrendResponse> getSellTrend(SellTrendRequestRecord request);
}