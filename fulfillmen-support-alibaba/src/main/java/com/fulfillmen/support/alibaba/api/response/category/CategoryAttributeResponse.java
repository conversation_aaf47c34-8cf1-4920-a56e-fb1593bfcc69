/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.category;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类目属性响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CategoryAttributeResponse {

    /**
     * 是否成功
     */
    @JsonProperty("success")
    private Boolean success;

    /**
     * 错误码
     */
    @JsonProperty("errorCode")
    private String errorCode;

    /**
     * 错误信息
     */
    @JsonProperty("errorMsg")
    private String errorMsg;

    /**
     * 属性列表
     */
    @JsonProperty("attributes")
    private List<Attribute> attributes;

    /**
     * 属性层级映射
     */
    @JsonProperty("attributeLevelMapStr")
    private Map<String, Object> attributeLevelMapStr;

    /**
     * 层级属性关系列表
     */
    @JsonProperty("levelAttrRelList")
    private List<LevelAttrRel> levelAttrRelList;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Attribute {

        /**
         * 属性ID
         */
        @JsonProperty("attrID")
        private Long attrId;

        /**
         * 属性名称
         */
        private String name;

        /**
         * 是否必填
         */
        private Boolean required;

        /**
         * 是否SKU属性
         */
        private Boolean isSKUAttribute;

        /**
         * 单位列表
         */
        private List<String> units;

        /**
         * 属性值列表
         */
        private List<AttrValue> attrValues;

        /**
         * 输入类型 下拉框:1, 多选框:2 单选框:3, 文本输入框:0, 数字输入框:-1, 下拉框表:4, 日期: 5
         */
        private String inputType;

        /**
         * 是否支持自定义属性值
         */
        private Boolean isSupportCustomizeValue;

        /**
         * 是否支持自定义图片
         */
        private Boolean isSupportCustomizeImage;

        /**
         * 英文名称
         */
        private String enName;

        /**
         * 父属性ID
         */
        @JsonProperty("parentAttrID")
        private String parentAttrId;

        /**
         * 父属性值ID
         */
        @JsonProperty("parentAttrValueID")
        private String parentAttrValueId;

        /**
         * 属性方面 产品属性:0, 交易属性:3, spu配置属性:5
         */
        private String aspect;

        /**
         * 字段类型 int: 数字; string:字符串; enum: 枚举
         */
        private String fieldType;

        /**
         * 是否规格图片属性
         */
        private Boolean isSpecPicAttr;

        /**
         * 是否为一级属性
         */
        private Boolean firstLevel;

        /**
         * 属性类型 0: 产品属性, 1: 规格属性, 2: 规格扩展性
         */
        private String attrType;

        /**
         * 排序值
         */
        private Integer sort;

        /**
         * 是否推荐属性
         */
        private Boolean recommendAttr;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttrValue {

        /**
         * 属性值ID
         */
        @JsonProperty("attrValueID")
        private Long attrValueId;

        /**
         * 属性值名称
         */
        private String name;

        /**
         * 英文名称
         */
        private String enName;

        /**
         * 子属性ID列表
         */
        private List<Long> childAttrs;

        /**
         * 是否SKU属性值
         */
        private Boolean isSKU;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LevelAttrRel {

        /**
         * 属性ID
         */
        @JsonProperty("fid")
        private Integer fid;

        /**
         * 子级属性
         */
        private List<Integer> subFids;

        /**
         * 属性类型 0和空都为顶级属性层级关系，1为加工性层级关系，后面其它的可用
         */
        private Integer attrType;
    }
}