/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.member;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import com.fulfillmen.support.alibaba.api.response.model.MemberRegisterResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 1688会员注册响应
 *
 * <AUTHOR>
 * @created 2025-01-13
 * @see MemberRegisterResult
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MemberRegisterResponse extends BaseAlibabaResponse {

    /**
     * 注册结果
     */
    private MemberRegisterResult result;
}