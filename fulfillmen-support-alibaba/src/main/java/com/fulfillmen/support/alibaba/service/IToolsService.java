/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service;

import com.fulfillmen.support.alibaba.api.request.tools.LoginIdEncryptRequestRecord;
import com.fulfillmen.support.alibaba.api.request.tools.PoolProductPullRequestRecord;
import com.fulfillmen.support.alibaba.api.request.tools.RelationAddRequestRecord;
import com.fulfillmen.support.alibaba.api.request.tools.WangwangNickDecryptRequestRecord;
import com.fulfillmen.support.alibaba.api.request.tools.WangwangUrlRequestRecord;
import com.fulfillmen.support.alibaba.api.response.tools.LoginIdEncryptResponse;
import com.fulfillmen.support.alibaba.api.response.tools.PoolProductPullResponse;
import com.fulfillmen.support.alibaba.api.response.tools.RelationAddResponse;
import com.fulfillmen.support.alibaba.api.response.tools.WangwangNickDecryptResponse;
import com.fulfillmen.support.alibaba.api.response.tools.WangwangUrlResponse;
import reactor.core.publisher.Mono;

/**
 * 阿里巴巴工具类服务接口
 *
 * <AUTHOR>
 * @created 2025-01-16
 */
public interface IToolsService {

    /**
     * 获取唤起旺旺聊天的链接
     *
     * @param request 请求参数
     * @return 旺旺聊天链接
     */
    Mono<WangwangUrlResponse> getWangwangUrl(WangwangUrlRequestRecord request);

    /**
     * 拉取商品池中商品数据
     * <p>
     * 通过商品池ID批量拉取池中商品数据，支持分页查询和排序。 可以根据类目ID过滤商品，支持多语言。
     *
     * @param request 请求参数，包含商品池ID、类目ID、任务ID等
     * @return 商品池数据，包含商品ID、类目ID和商品池总数信息
     */
    Mono<PoolProductPullResponse> pullPoolProducts(PoolProductPullRequestRecord request);

    /**
     * 买卖家分销关系添加
     * <p>
     * 通过商品ID，添加买卖家分销关系。
     *
     * @param request 请求参数，包含商品ID
     * @return 添加结果
     */
    Mono<RelationAddResponse> addRelation(RelationAddRequestRecord request);

    /**
     * 用户loginId加密转换为Openuid
     *
     * @param request 请求参数
     * @return Mono<LoginIdEncryptResponse> 响应
     */
    Mono<LoginIdEncryptResponse> encryptLoginId(LoginIdEncryptRequestRecord request);

    /**
     * Openuid转换解密为旺旺昵称
     * <p>
     * 将待解密的openUid转换为用户唤起旺旺的旺旺昵称。 注意：该接口仅用于用户唤起旺旺，不允许用于其他用途。
     *
     * @param request 请求参数
     * @return Mono<WangwangNickDecryptResponse> 响应
     */
    Mono<WangwangNickDecryptResponse> decryptWangwangNick(WangwangNickDecryptRequestRecord request);
}