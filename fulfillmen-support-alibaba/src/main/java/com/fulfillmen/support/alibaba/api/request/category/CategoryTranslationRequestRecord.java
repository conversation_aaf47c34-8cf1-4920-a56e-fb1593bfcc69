/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.category;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 类目名称翻译请求
 * <p>
 * 用于将类目名称翻译成指定语言。
 *
 * <AUTHOR>
 * @created 2025-01-17
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:category.name.translate-1">类目名称翻译API文档</a>
 */
@Builder
public record CategoryTranslationRequestRecord(
    /**
     * 类目名称（必填）
     */
    String cateName,

    /**
     * 语言代码（必填）
     */
    String language,

    /**
     * 外部会员ID（选填）
     */
    String outMemberId
) implements BaseAlibabaRequestRecord {

    @Override
    public void requireParams() {
        assertNotBlank(cateName, "类目名称不能为空");
        assertNotBlank(language, "语言代码不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("cateName", cateName);
        params.put("language", language);
        if (outMemberId != null) {
            params.put("outMemberId", outMemberId);
        }
        return params;
    }
}