/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.member;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Builder;

/**
 * 批量取消子账号授权请求
 *
 * <AUTHOR>
 * @created 2025-01-17
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=system.oauth2:subaccount.auth.cancel-1">API文档</a>
 */
@Builder
public record SubAccountAuthCancelRequestRecord(
    /**
     * 子账号id列表(必填)
     */
    List<String> subUserIdentityList
) implements BaseAlibabaRequestRecord {

    /**
     * 创建批量取消子账号授权请求
     *
     * @param subUserIdentityList 子账号id列表(必填)
     * @return 批量取消子账号授权请求
     */
    public static SubAccountAuthCancelRequestRecord of(List<String> subUserIdentityList) {
        return new SubAccountAuthCancelRequestRecord(subUserIdentityList);
    }

    @Override
    public void requireParams() {
        assertNotEmpty(subUserIdentityList, "子账号id列表不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("subUserIdentityList", toJsonString(subUserIdentityList));
        return params;
    }
}