/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.tools;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 用户loginId加密转换为Openuid请求
 *
 * <AUTHOR>
 * @created 2025-01-13
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.account:loginid.openuid.encrypt-1">API文档</a>
 */
@Builder
public record LoginIdEncryptRequestRecord(
    /**
     * 用户登录名
     */
    String loginId
) implements BaseAlibabaRequestRecord {

    /**
     * 创建请求实例
     *
     * @param loginId 用户登录名
     * @return 请求实例
     */
    public static LoginIdEncryptRequestRecord of(String loginId) {
        return new LoginIdEncryptRequestRecord(loginId);
    }

    @Override
    public void requireParams() {
        assertNotBlank(loginId, "用户登录名不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("loginId", loginId);
        return params;
    }
}