/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.order;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 1688跨境订单创建响应对象 用于封装创建订单后的返回结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderCreateResponse extends BaseAlibabaResponse {

    /**
     * 创建订单结果 包含订单创建的详细信息
     */
    private OrderCreateResult result;

    @Data
    public static class OrderCreateResult {

        /**
         * 订单总金额（单位分） 注：一次创建多个订单时，该字段为空 示例值：100
         */
        private Long totalSuccessAmount;

        /**
         * 订单ID 注：一次创建多个订单时，该字段为空 示例值：*********
         */
        private String orderId;

        /**
         * 运费，单位：分 注：一次创建多个订单时，该字段为空
         */
        private Long postFee;

        /**
         * 账期信息对象 注：非账期支付订单返回空
         */
        private AccountPeriod accountPeriod;

        /**
         * 失败商品信息列表 记录下单失败的商品详情
         */
        private List<FailedOffer> failedOfferList;

        /**
         * 订单列表 注：一次创建多个订单时返回
         */
        private List<Order> orderList;
    }

    @Data
    public static class AccountPeriod {

        /**
         * 账期类型 1：一个月指定日期结算一次 3：两个月指定日期结算一次 6：三个月指定日期结算一次 5：按收货时间和账期日期结算 示例值：1
         */
        private Integer tapType;

        /**
         * 账期结算日期 - 按月结算类型此值代表具体某日 - 按收货时间结算时此值代表结算时间周期 示例值：12
         */
        private Integer tapDate;

        /**
         * 逾期次数 示例值：0
         */
        private Integer tapOverdue;
    }

    @Data
    public static class FailedOffer {

        /**
         * 下单失败的商品ID 示例值：************
         */
        private String offerId;

        /**
         * 下单失败的商品规格ID 示例值：b26be072650b185beaf205cbae88530d
         */
        private String specId;

        /**
         * 下单失败的错误编码
         */
        private String errorCode;

        /**
         * 下单失败的错误描述
         */
        private String errorMessage;
    }

    @Data
    public static class Order {

        /**
         * 运费（单位：分）
         */
        private Long postFee;

        /**
         * 订单实付款金额（单位：分）
         */
        private Long orderAmount;

        /**
         * 描述信息
         */
        private String message;

        /**
         * 返回码
         */
        private String resultCode;

        /**
         * 是否成功
         */
        private Boolean success;

        /**
         * 订单号
         */
        private String orderId;
    }
}