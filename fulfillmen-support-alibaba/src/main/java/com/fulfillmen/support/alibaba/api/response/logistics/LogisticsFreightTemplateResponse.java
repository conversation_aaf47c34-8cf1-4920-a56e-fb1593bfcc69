/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.logistics;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import com.fulfillmen.support.alibaba.api.response.model.FreightTemplate;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 获取物流模板详情响应
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LogisticsFreightTemplateResponse extends BaseAlibabaResponse {

    /**
     * 返回结果
     */
    private List<FreightTemplate> result;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误描述
     */
    private String errorMsg;
}