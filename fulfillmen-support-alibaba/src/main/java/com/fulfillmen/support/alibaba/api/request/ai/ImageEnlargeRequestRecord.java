/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.ai;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 图片高清放大请求记录
 * <p>
 * 用于将图片放大1-4倍，提升图片清晰度。 利用AI算法对图片进行高分辨率放大，有效提升部分细节的清晰度， 改善图像纹理细节，全面提高图像清晰度与表现质量。
 *
 * @param imageUrl      源图片URL，图片尺寸应大于100*100像素，小于3000*5000像素
 * @param upscaleFactor 放大倍数，默认为2，支持2~4
 * <AUTHOR>
 * @created 2025-01-22
 * @see com.fulfillmen.support.alibaba.api.AiCapabilityAPI#enlargeImage
 */
@Builder
public record ImageEnlargeRequestRecord(
    @JsonProperty("imageUrl") String imageUrl,
    @JsonProperty("upscaleFactor") Integer upscaleFactor
) implements BaseAlibabaRequestRecord {

    @Override
    public void requireParams() {
        assertNotBlank(imageUrl, "imageUrl不能为空");
        assertNotNull(upscaleFactor, "upscaleFactor不能为空");

        // 验证放大倍数的范围
        if (upscaleFactor < 2 || upscaleFactor > 4) {
            throw new IllegalArgumentException("upscaleFactor必须在2-4之间");
        }
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("imageUrl", imageUrl);
        params.put("upscaleFactor", upscaleFactor.toString());
        return params;
    }
}