/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.feedback;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 下游销售订单同步响应
 *
 * <AUTHOR>
 * @created 2025-01-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderSyncResponse extends BaseAlibabaResponse {

    /**
     * 返回结果
     */
    private Result result;

    @Data
    public static class Result {

        /**
         * 是否成功
         */
        private Boolean success;

        /**
         * 错误码
         */
        private String code;

        /**
         * 返回信息
         */
        private String message;
    }
}