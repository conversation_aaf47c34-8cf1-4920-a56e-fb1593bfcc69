/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.logistics;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 运费险信息查询请求
 *
 * <AUTHOR>
 * @created 2025-01-14
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:shipping.insurance.get-1">API文档</a>
 */
@Builder
public record LogisticsInsuranceRequestRecord(
    /**
     * 订单号 必填
     */
    Long orderId,

    /**
     * 运费险类型 必填 givenByPlatform 平台赠送 givenByMerchant 商家赠送
     */
    String type
) implements BaseAlibabaRequestRecord {

    /**
     * 平台赠送
     */
    public static final String TYPE_GIVEN_BY_PLATFORM = "givenByPlatform";

    /**
     * 商家赠送
     */
    public static final String TYPE_GIVEN_BY_MERCHANT = "givenByMerchant";

    /**
     * 创建运费险信息查询请求
     *
     * @param orderId 订单号(必填)
     * @param type    运费险类型(必填,参考TYPE_常量)
     * @return 运费险信息查询请求
     */
    public static LogisticsInsuranceRequestRecord of(Long orderId, String type) {
        return new LogisticsInsuranceRequestRecord(orderId, type);
    }

    /**
     * 创建平台赠送运费险信息查询请求
     *
     * @param orderId 订单号(必填)
     * @return 运费险信息查询请求
     */
    public static LogisticsInsuranceRequestRecord ofPlatform(Long orderId) {
        return of(orderId, TYPE_GIVEN_BY_PLATFORM);
    }

    /**
     * 创建商家赠送运费险信息查询请求
     *
     * @param orderId 订单号(必填)
     * @return 运费险信息查询请求
     */
    public static LogisticsInsuranceRequestRecord ofMerchant(Long orderId) {
        return of(orderId, TYPE_GIVEN_BY_MERCHANT);
    }

    @Override
    public void requireParams() {
        assertNotNull(orderId, "订单号不能为空");
        assertNotBlank(type, "运费险类型不能为空");
        assertTrue(TYPE_GIVEN_BY_PLATFORM.equals(type) || TYPE_GIVEN_BY_MERCHANT.equals(type), "运费险类型必须是平台赠送或商家赠送");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("orderId", String.valueOf(orderId));
        params.put("type", type);
        return params;
    }
}