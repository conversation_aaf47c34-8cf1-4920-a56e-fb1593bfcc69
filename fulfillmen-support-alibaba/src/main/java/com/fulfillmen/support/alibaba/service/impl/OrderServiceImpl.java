/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.service.impl;

import com.fulfillmen.support.alibaba.api.ApiPaths;
import com.fulfillmen.support.alibaba.api.OrderAPI;
import com.fulfillmen.support.alibaba.api.request.order.OrderBuyerListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.order.OrderCancelRequestRecord;
import com.fulfillmen.support.alibaba.api.request.order.OrderCreateRequestRecord;
import com.fulfillmen.support.alibaba.api.request.order.OrderDetailRequestRecord;
import com.fulfillmen.support.alibaba.api.request.order.OrderPreviewRequestRecord;
import com.fulfillmen.support.alibaba.api.request.order.TradeFeedbackRequestRecord;
import com.fulfillmen.support.alibaba.api.response.order.OrderBuyerListResponse;
import com.fulfillmen.support.alibaba.api.response.order.OrderCancelResponse;
import com.fulfillmen.support.alibaba.api.response.order.OrderCreateResponse;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse;
import com.fulfillmen.support.alibaba.api.response.order.OrderPreviewResponse;
import com.fulfillmen.support.alibaba.api.response.order.TradeFeedbackResponse;
import com.fulfillmen.support.alibaba.autoconfigure.AlibabaProperties;
import com.fulfillmen.support.alibaba.service.IOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 订单服务实现类
 *
 * <AUTHOR>
 * @created 2025-01-16
 */
@Slf4j
@Service
public class OrderServiceImpl extends BaseAlibabaServiceImpl implements IOrderService {

    private static final String SERVICE_NAME = "订单服务";
    private final OrderAPI orderAPI;

    public OrderServiceImpl(OrderAPI orderAPI, AlibabaProperties alibabaProperties) {
        super(alibabaProperties);
        this.orderAPI = orderAPI;
    }

    @Override
    protected String getServiceName() {
        return SERVICE_NAME;
    }

    @Override
    public Mono<OrderBuyerListResponse> getBuyerOrderList(OrderBuyerListRequestRecord request) {
        log.debug("开始处理买家订单列表请求: {}", request);
        return wrapWithErrorHandler("获取买家订单列表", request, ApiPaths.OrderAPI.GET_BUYER_ORDER_LIST, formParams -> orderAPI
            .getBuyerOrderList(appKey, formParams));
    }

    @Override
    public Mono<OrderDetailResponse> getOrderDetail(OrderDetailRequestRecord request) {
        log.debug("开始处理订单详情请求: {}", request);
        return wrapWithErrorHandler("获取订单详情", request, ApiPaths.OrderAPI.GET_ORDER_DETAIL, formParams -> orderAPI
            .getOrderDetail(appKey, formParams));
    }

    @Override
    public Mono<TradeFeedbackResponse> addFeedback(TradeFeedbackRequestRecord request) {
        log.debug("开始处理买家补充订单留言请求: {}", request);
        return wrapWithErrorHandler("买家补充订单留言", request, ApiPaths.OrderAPI.ADD_FEEDBACK, formParams -> orderAPI
            .addFeedback(appKey, formParams), "tradeFeedbackParam");
    }

    @Override
    public Mono<OrderCancelResponse> cancelOrder(OrderCancelRequestRecord request) {
        log.debug("开始处理订单取消请求: {}", request);
        return wrapWithErrorHandler("取消订单", request, ApiPaths.OrderAPI.CANCEL_ORDER, formParams -> orderAPI
            .cancelOrder(appKey, formParams));
    }

    @Override
    public Mono<OrderCreateResponse> createCrossOrder(OrderCreateRequestRecord request) {
        log.debug("开始处理跨境订单创建请求: {}", request);
        return wrapWithErrorHandler("创建跨境订单", request, ApiPaths.OrderAPI.CREATE_CROSS_ORDER, formParams -> orderAPI
            .createCrossOrder(appKey, formParams));
    }

    @Override
    public Mono<OrderPreviewResponse> previewOrder(OrderPreviewRequestRecord request) {
        log.debug("开始处理订单预览请求: {}", request);
        return wrapWithErrorHandler("预览订单", request, ApiPaths.OrderAPI.PREVIEW_ORDER, formParams -> orderAPI
            .previewOrder(appKey, formParams));
    }
}