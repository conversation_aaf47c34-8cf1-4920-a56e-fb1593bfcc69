/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品优惠信息 店铺优惠信息
 *
 * <AUTHOR>
 * @created 2025-01-09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradePromotionModel {

    /**
     * 优惠券ID
     */
    private String promotionId;
    /**
     * 是否默认选中
     */
    private Boolean selected;
    /**
     * 优惠券名称
     */
    private String text;
    /**
     * 优惠券描述
     */
    private String desc;
    /**
     * 是否包邮
     */
    private Boolean freePostage;
    /**
     * 优惠金额, 单位: 分
     */
    private Long discountFee;

}
