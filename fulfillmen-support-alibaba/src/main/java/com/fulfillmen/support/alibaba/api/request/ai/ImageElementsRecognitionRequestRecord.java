/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.ai;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Builder;

/**
 * 图像元素识别请求记录 &lt;p&gt; 用于识别图片中的特定元素，包括主体和非主体区域的文字、Logo、水印及含字色块等元素。 能够深入挖掘图像中的细节，帮助用户快速识别图片中的各种元素，提升图像筛选效率。
 *
 * <AUTHOR>
 * @created 2025-01-23
 * @see com.fulfillmen.support.alibaba.api.AiCapabilityAPI#recognizeImageElements
 */
@Builder
public record ImageElementsRecognitionRequestRecord(
    /**
     * 图片URL（必填）
     */
    @JsonProperty("imageUrl") String imageUrl,

    /**
     * 检测图片主体上的元素（1=水印; 2=Logo; 3=文字; 4=条形码）
     */
    @JsonProperty("objectDetectElements") List<String> objectDetectElements,

    /**
     * 检测图片非主体上的元素（1=水印; 2=Logo; 3=文字; 4=条形码）
     */
    @JsonProperty("nonObjectDetectElements") List<String> nonObjectDetectElements,

    /**
     * 是否返回识别的文字OCR结果（1是，0否），不传默认为0
     */
    @JsonProperty("returnCharacter") Integer returnCharacter,

    /**
     * 是否返回主体边缘像素值（1是，0否），不传默认为0
     */
    @JsonProperty("returnBorderPixel") Integer returnBorderPixel,

    /**
     * 是否返回图像主体属性占比（1是，0否），不传默认为0
     */
    @JsonProperty("returnProductProp") Integer returnProductProp,

    /**
     * 是否返回主体数量（1是，0否），不传默认为0
     */
    @JsonProperty("returnProductNum") Integer returnProductNum,

    /**
     * 是否返回文字占比比例（1是，0否），不传默认为0
     */
    @JsonProperty("returnCharacterProp") Integer returnCharacterProp
) implements BaseAlibabaRequestRecord {

    @Override
    public void requireParams() {
        assertNotBlank(imageUrl, "图片URL不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("imageUrl", imageUrl);

        if (objectDetectElements != null && !objectDetectElements.isEmpty()) {
            params.put("objectDetectElements", String.join(",", objectDetectElements));
        }

        if (nonObjectDetectElements != null && !nonObjectDetectElements.isEmpty()) {
            params.put("nonObjectDetectElements", String.join(",", nonObjectDetectElements));
        }

        if (returnCharacter != null) {
            params.put("returnCharacter", returnCharacter.toString());
        }

        if (returnBorderPixel != null) {
            params.put("returnBorderPixel", returnBorderPixel.toString());
        }

        if (returnProductProp != null) {
            params.put("returnProductProp", returnProductProp.toString());
        }

        if (returnProductNum != null) {
            params.put("returnProductNum", returnProductNum.toString());
        }

        if (returnCharacterProp != null) {
            params.put("returnCharacterProp", returnCharacterProp.toString());
        }

        return params;
    }
}
