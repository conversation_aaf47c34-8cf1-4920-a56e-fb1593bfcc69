/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.logistics;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 获取交易订单的物流跟踪信息请求
 *
 * <AUTHOR>
 * @created 2025-01-14
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:alibaba.trade.getLogisticsTraceInfo.buyerView-1">API文档</a>
 */
@Builder
public record LogisticsTraceRequestRecord(
    /**
     * 该订单下的物流编号 选填
     */
    String logisticsId,

    /**
     * 订单号 必填
     */
    Long orderId,

    /**
     * 是1688业务还是icbu业务 必填
     */
    String webSite
) implements BaseAlibabaRequestRecord {

    /**
     * 创建获取交易订单的物流跟踪信息请求
     *
     * @param orderId     订单号(必填)
     * @param webSite     业务类型(必填)
     * @param logisticsId 物流编号(选填)
     * @return 获取交易订单的物流跟踪信息请求
     */
    public static LogisticsTraceRequestRecord of(Long orderId, String webSite, String logisticsId) {
        return new LogisticsTraceRequestRecord(logisticsId, orderId, webSite);
    }

    /**
     * 创建获取交易订单的物流跟踪信息请求(不指定物流编号)
     *
     * @param orderId 订单号(必填)
     * @param webSite 业务类型(必填)
     * @return 获取交易订单的物流跟踪信息请求
     */
    public static LogisticsTraceRequestRecord of(Long orderId, String webSite) {
        return of(orderId, webSite, null);
    }

    @Override
    public void requireParams() {
        assertNotNull(orderId, "订单号不能为空");
        assertNotBlank(webSite, "业务类型不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        if (logisticsId != null) {
            params.put("logisticsId", logisticsId);
        }
        params.put("orderId", String.valueOf(orderId));
        params.put("webSite", webSite);
        return params;
    }
}