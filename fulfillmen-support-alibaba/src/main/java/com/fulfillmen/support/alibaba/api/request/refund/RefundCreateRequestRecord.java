/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.refund;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Getter;
import org.springframework.util.CollectionUtils;

/**
 * 创建退款申请请求
 *
 * <AUTHOR>
 * @created 2025-01-13
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.create-1">创建退款申请</a>
 */
@Builder
public record RefundCreateRequestRecord(
    /**
     * 订单ID
     */
    Long orderId,

    /**
     * 子订单列表
     */
    List<OrderEntryCount> orderEntryCountList,

    /**
     * 退款类型(1:退款 2:退款退货 3:换货)
     */
    DisputeRequest disputeRequest,

    /**
     * 申请退款金额（单位：分）
     */
    Long applyPayment,

    /**
     * 申请退运费金额（单位：分）
     */
    Long applyCarriage,

    /**
     * 申请原因
     */
    String applyReason,

    /**
     * 申请原因ID
     */
    Long applyReasonId,

    /**
     * 申请说明
     */
    String description,

    /**
     * 货物状态(1:未收到货 2:已收到货)
     */
    GoodsStatus goodsStatus,

    /**
     * 凭证图片地址列表
     */
    List<String> vouchers
) implements BaseAlibabaRequestRecord {

    /**
     * 构造方法
     *
     * @param orderId        订单ID
     * @param disputeRequest 退款类型
     * @param applyPayment   申请退款金额
     * @param applyReason    申请原因
     * @param applyReasonId  申请原因ID
     * @param goodsStatus    货物状态
     */
    public static RefundCreateRequestRecord of(Long orderId,
        DisputeRequest disputeRequest,
        Long applyPayment,
        String applyReason,
        Long applyReasonId,
        GoodsStatus goodsStatus) {
        return new RefundCreateRequestRecord(orderId, null, disputeRequest, applyPayment, null, applyReason, applyReasonId, null, goodsStatus, null);
    }

    /**
     * 构造方法
     *
     * @param orderId             订单ID
     * @param orderEntryCountList 子订单列表
     * @param disputeRequest      退款类型
     * @param applyPayment        申请退款金额
     * @param applyReason         申请原因
     * @param applyReasonId       申请原因ID
     * @param goodsStatus         货物状态
     */
    public static RefundCreateRequestRecord of(Long orderId,
        List<OrderEntryCount> orderEntryCountList,
        DisputeRequest disputeRequest,
        Long applyPayment,
        String applyReason,
        Long applyReasonId,
        GoodsStatus goodsStatus) {
        return new RefundCreateRequestRecord(orderId, orderEntryCountList, disputeRequest, applyPayment, null, applyReason, applyReasonId, null, goodsStatus, null);
    }

    /**
     * 构造方法
     *
     * @param orderId             订单ID
     * @param orderEntryCountList 子订单列表
     * @param disputeRequest      退款类型
     * @param applyPayment        申请退款金额
     * @param applyReason         申请原因
     * @param applyReasonId       申请原因ID
     * @param goodsStatus         货物状态
     */
    public static RefundCreateRequestRecord of(Long orderId,
        List<OrderEntryCount> orderEntryCountList,
        DisputeRequest disputeRequest,
        Long applyPayment,
        String applyReason,
        Long applyReasonId,
        GoodsStatus goodsStatus,
        List<String> vouchers) {
        return new RefundCreateRequestRecord(orderId, orderEntryCountList, disputeRequest, applyPayment, null, applyReason, applyReasonId, null, goodsStatus, vouchers);
    }

    /**
     * 子订单列表
     */
    public record OrderEntryCount(

        /**
         * 子订单ID
         */
        Long id,

        /**
         * 申请退款数量
         */
        Integer count
    ) {

    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("orderId", String.valueOf(orderId));
        if (!CollectionUtils.isEmpty(orderEntryCountList)) {
            try {
                params.put("orderEntryCountList", toJsonString(orderEntryCountList));
            } catch (Exception e) {
                throw new IllegalArgumentException("子订单列表序列化失败", e);
            }
        }
        params.put("disputeRequest", String.valueOf(disputeRequest.getValue()));
        params.put("applyPayment", String.valueOf(applyPayment));
        if (applyCarriage != null) {
            params.put("applyCarriage", String.valueOf(applyCarriage));
        }
        params.put("applyReason", applyReason);
        params.put("applyReasonId", String.valueOf(applyReasonId));
        if (description != null) {
            params.put("description", description);
        }
        params.put("goodsStatus", String.valueOf(goodsStatus.getValue()));
        if (vouchers != null && !vouchers.isEmpty()) {
            try {
                params.put("vouchers", toJsonString(vouchers));
            } catch (Exception e) {
                throw new IllegalArgumentException("凭证列表序列化失败", e);
            }
        }
        return params;
    }

    @Override
    public void requireParams() {
        assertNotNull(orderId, "订单ID不能为空");
        assertNotNull(disputeRequest, "退款类型不能为空");
        assertNotNull(applyPayment, "退款金额不能为空");
        assertNotBlank(applyReason, "申请原因不能为空");
        assertNotNull(applyReasonId, "申请原因ID不能为空");
        assertNotNull(goodsStatus, "货物状态不能为空");
    }

    @Getter
    public static enum DisputeRequest {

        /* (1:退款 2:退款退货 3:换货) */
        REFUND(1, "退款"),
        REFUND_RETURN(2, "退款退货"),
        EXCHANGE(3, "换货");

        private final int value;
        private final String desc;

        DisputeRequest(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    @Getter
    public static enum GoodsStatus {

        /* (1:未收到货 2:已收到货) */
        NOT_RECEIVED(1, "未收到货"),
        RECEIVED(2, "已收到货");

        private final int value;
        private final String desc;

        GoodsStatus(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }
    }
}
