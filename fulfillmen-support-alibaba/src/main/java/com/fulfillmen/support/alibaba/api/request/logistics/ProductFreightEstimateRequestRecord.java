/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.logistics;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Builder;

/**
 * 商品中国国内运费预估请求
 *
 * <AUTHOR>
 * @created 2025-01-14
 */
@Builder
public record ProductFreightEstimateRequestRecord(
    /**
     * 商品ID 必填
     */
    Long offerId,

    /**
     * 中国省份编码 必填 如浙江省330000
     */
    String toProvinceCode,

    /**
     * 中国城市编码 必填 如杭州市330100
     */
    String toCityCode,

    /**
     * 中国地区编码 必填 如浙江区330108
     */
    String toCountryCode,

    /**
     * 购买件数 必填
     */
    Long totalNum,

    /**
     * sku件数列表 必填
     */
    List<LogisticsSkuNumModelRecord> logisticsSkuNumModels
) implements BaseAlibabaRequestRecord {

    /**
     * sku件数模型
     */
    public record LogisticsSkuNumModelRecord(
        /**
         * skuId 必填
         */
        String skuId,

        /**
         * 数量 必填
         */
        Long number
    ) {

        /**
         * 创建sku件数模型
         *
         * @param skuId  skuId(必填)
         * @param number 数量(必填)
         * @return sku件数模型
         */
        public static LogisticsSkuNumModelRecord of(String skuId, Long number) {
            return new LogisticsSkuNumModelRecord(skuId, number);
        }
    }

    /**
     * 创建商品中国国内运费预估请求
     *
     * @param offerId               商品ID(必填)
     * @param toProvinceCode        中国省份编码(必填)
     * @param toCityCode            中国城市编码(必填)
     * @param toCountryCode         中国地区编码(必填)
     * @param totalNum              购买件数(必填)
     * @param logisticsSkuNumModels sku件数列表(必填)
     * @return 商品中国国内运费预估请求
     */
    public static ProductFreightEstimateRequestRecord of(Long offerId,
        String toProvinceCode,
        String toCityCode,
        String toCountryCode,
        Long totalNum,
        List<LogisticsSkuNumModelRecord> logisticsSkuNumModels) {
        return new ProductFreightEstimateRequestRecord(offerId, toProvinceCode, toCityCode, toCountryCode, totalNum, logisticsSkuNumModels);
    }

    @Override
    public void requireParams() {
        assertNotNull(offerId, "商品ID不能为空");
        assertNotBlank(toProvinceCode, "省份编码不能为空");
        assertNotBlank(toCityCode, "城市编码不能为空");
        assertNotBlank(toCountryCode, "地区编码不能为空");
        assertNotNull(totalNum, "购买件数不能为空");
        assertTrue(totalNum > 0, "购买件数必须大于0");
        assertNotEmpty(logisticsSkuNumModels, "sku件数列表不能为空");
        logisticsSkuNumModels.forEach(model -> {
            assertNotBlank(model.skuId(), "skuId不能为空");
            assertNotNull(model.number(), "sku数量不能为空");
            assertTrue(model.number() > 0, "sku数量必须大于0");
        });
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();

        // 处理基本字段
        params.put("offerId", String.valueOf(offerId));
        params.put("toProvinceCode", toProvinceCode);
        params.put("toCityCode", toCityCode);
        params.put("toCountryCode", toCountryCode);
        params.put("totalNum", String.valueOf(totalNum));

        // 将logisticsSkuNumModels转换为JSON字符串
        params.put("logisticsSkuNumModels", toJsonString(logisticsSkuNumModels));

        return params;
    }
}