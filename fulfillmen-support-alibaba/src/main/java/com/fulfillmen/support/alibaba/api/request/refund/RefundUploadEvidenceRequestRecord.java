/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.refund;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 上传退款退货凭证请求
 *
 * <AUTHOR>
 * @created 2025-01-13
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.uploadEvidence-1">上传退款退货凭证</a>
 */
@Builder
public record RefundUploadEvidenceRequestRecord(
    /**
     * 退款单号
     */
    String refundId,

    /**
     * 凭证图片URL
     */
    String evidenceUrl,

    /**
     * 凭证描述
     */
    String description
) implements BaseAlibabaRequestRecord {

    public static RefundUploadEvidenceRequestRecord of(String refundId, String evidenceUrl) {
        return new RefundUploadEvidenceRequestRecord(refundId, evidenceUrl, null);
    }

    @Override
    public void requireParams() {
        assertNotBlank(refundId, "退款单号不能为空");
        assertNotBlank(evidenceUrl, "凭证图片URL不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("refundId", refundId);
        params.put("evidenceUrl", evidenceUrl);
        if (description != null) {
            params.put("description", description);
        }
        return params;
    }
}