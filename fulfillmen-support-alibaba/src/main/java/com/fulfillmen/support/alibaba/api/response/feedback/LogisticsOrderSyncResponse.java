/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.feedback;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 国家站物流单回传响应
 *
 * <AUTHOR>
 * @created 2025-01-14
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:trade.cross.logisticsOrderSync-1">API文档</a>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LogisticsOrderSyncResponse extends BaseAlibabaResponse {

    /**
     * 返回结果
     */
    private LogisticsOrderSyncResult result;

    @Data
    public static class LogisticsOrderSyncResult {

        /**
         * 错误码
         */
        private String code;

        /**
         * 错误信息
         */
        private String message;

        /**
         * 是否成功
         */
        private Boolean success;
    }
}