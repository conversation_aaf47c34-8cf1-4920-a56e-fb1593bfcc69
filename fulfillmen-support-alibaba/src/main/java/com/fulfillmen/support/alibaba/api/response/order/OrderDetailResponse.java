/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeBaseInfo;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeCrossCustoms;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeEncryptOutOrderInfo;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeGuaranteeTermsInfo;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeNativeLogisticsInfo;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeOrderBizInfo;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeOrderInvoiceInfo;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeOrderRateInfo;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeOverseaLogisticsInfo;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeProductItem;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeTermsInfo;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单详情响应
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderDetailResponse extends BaseAlibabaResponse {

    /**
     * 订单详情
     */
    private OrderDetail result;

    @Data
    public static class OrderDetail {

        /**
         * 基础信息
         */
        @JsonProperty("baseInfo")
        private TradeBaseInfo baseInfo;

        /**
         * 订单业务信息
         */
        @JsonProperty("orderBizInfo")
        private TradeOrderBizInfo orderBizInfo;

        /**
         * 交易条款
         */
        @JsonProperty("tradeTerms")
        private List<TradeTermsInfo> tradeTerms;

        /**
         * 商品条目信息
         */
        @JsonProperty("productItems")
        private List<TradeProductItem> productItems;

        /**
         * 国内物流
         */
        @JsonProperty("nativeLogistics")
        private TradeNativeLogisticsInfo nativeLogistics;

        /**
         * 发票信息
         */
        @JsonProperty("orderInvoiceInfo")
        private TradeOrderInvoiceInfo orderInvoiceInfo;

        /**
         * 保障条款
         */
        @JsonProperty("guaranteesTerms")
        private TradeGuaranteeTermsInfo guaranteesTerms;

        /**
         * 订单评价信息
         */
        @JsonProperty("orderRateInfo")
        private TradeOrderRateInfo orderRateInfo;

        /**
         * 跨境地址扩展信息
         */
        @JsonProperty("overseasExtraAddress")
        private OverseasExtraAddress overseasExtraAddress;

        /**
         * 跨境报关信息
         */
        @JsonProperty("customs")
        private TradeCrossCustoms customs;

        /**
         * 采购单详情列表，为大企业采购订单独有域
         */
        @JsonProperty("quoteList")
        private List<QuoteInfo> quoteList;

        /**
         * 订单扩展属性
         */
        @JsonProperty("extAttributes")
        private List<ExtAttribute> extAttributes;

        /**
         * 是否下游脱敏信息创建的订单
         */
        @JsonProperty("fromEncryptOrder")
        private Boolean fromEncryptOrder;

        /**
         * 外部订单信息
         */
        @JsonProperty("encryptOutOrderInfo")
        private TradeEncryptOutOrderInfo encryptOutOrderInfo;

        /**
         * 海外物流信息
         */
        @JsonProperty("overseaLogisticsInfo")
        private TradeOverseaLogisticsInfo overseaLogisticsInfo;
    }

    /**
     * 跨境地址扩展信息
     */
    @Data
    public static class OverseasExtraAddress {

        /**
         * 路线名称
         */
        @JsonProperty("channelName")
        private String channelName;

        /**
         * 路线id
         */
        @JsonProperty("channelId")
        private String channelId;

        /**
         * 货代公司id
         */
        @JsonProperty("shippingCompanyId")
        private String shippingCompanyId;

        /**
         * 货代公司名称
         */
        @JsonProperty("shippingCompanyName")
        private String shippingCompanyName;

        /**
         * 国家code
         */
        @JsonProperty("countryCode")
        private String countryCode;

        /**
         * 国家
         */
        @JsonProperty("country")
        private String country;

        /**
         * 买家邮箱
         */
        @JsonProperty("email")
        private String email;
    }

    /**
     * 采购单详情
     */
    @Data
    public static class QuoteInfo {

        /**
         * 供应单项的名称
         */
        @JsonProperty("productQuoteName")
        private String productQuoteName;

        /**
         * 价格，单位：元
         */
        @JsonProperty("price")
        private BigDecimal price;

        /**
         * 购买数量
         */
        @JsonProperty("count")
        private Double count;
    }

    /**
     * 扩展属性
     */
    @Data
    public static class ExtAttribute {

        /**
         * 键
         */
        @JsonProperty("key")
        private String key;

        /**
         * 值
         */
        @JsonProperty("value")
        private String value;

        /**
         * 描述
         */
        @JsonProperty("description")
        private String description;
    }

}