/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.goods;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import com.fulfillmen.support.alibaba.enums.LanguageEnum;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 商品图片搜索请求参数
 *
 * <AUTHOR>
 * @created 2025-01-17
 */
@Builder
public record GoodsImageSearchRequestRecord(
    /**
     * 图片ID
     */
    @JsonProperty("imageId") String imageId,
    /**
     * 语言
     */
    @JsonProperty("country") String country,
    /**
     * 分页大小
     */
    @JsonProperty("pageSize") Integer pageSize,
    /**
     * 开始页码
     */
    @JsonProperty("beginPage") Integer beginPage,
    /**
     * 主体选择
     */
    @JsonProperty("region") String region,
    /**
     * 筛选参数
     */
    @JsonProperty("filter") String filter,
    /**
     * 排序参数
     */
    @JsonProperty("sort") String sort,
    /**
     * 外部用户ID
     */
    @JsonProperty("outMemberId") String outMemberId,
    /**
     * 批发价开始
     */
    @JsonProperty("priceStart") String priceStart,
    /**
     * 批发价结束
     */
    @JsonProperty("priceEnd") String priceEnd,
    /**
     * 类目ID
     */
    @JsonProperty("categoryId") Long categoryId,
    /**
     * 图片地址
     */
    @JsonProperty("imageAddress") String imageAddress,
    /**
     * 关键词
     */
    @JsonProperty("keyword") String keyword,
    /**
     * 多模态图搜文案
     */
    @JsonProperty("auxiliaryText") String auxiliaryText,
    /**
     * 寻源通工作台货盘ID
     */
    @JsonProperty("productCollectionId") String productCollectionId,
    /**
     * 搜索词是否已翻译
     */
    @JsonProperty("keywordTranslate") Boolean keywordTranslate
) implements BaseAlibabaRequestRecord {

    /**
     * 默认分页大小
     */
    private static final int DEFAULT_PAGE_SIZE = 20;
    /**
     * 默认页码
     */
    private static final int DEFAULT_PAGE_NO = 1;
    /**
     * 最大分页大小
     */
    private static final int MAX_PAGE_SIZE = 50;

    /**
     * 创建商品图片搜索请求
     *
     * @param imageId 图片ID（必填）
     * @param country 语言（必填）如en-英语，详细枚举参考开发人员参考菜单
     * @return 商品图片搜索请求
     */
    public static GoodsImageSearchRequestRecord of(String imageId, LanguageEnum country) {
        return of(imageId, country, DEFAULT_PAGE_SIZE, DEFAULT_PAGE_NO, null, null, null, null, null, null, null, null, null, null, null, null);
    }

    // 重载请求 增加分页参数
    public static GoodsImageSearchRequestRecord of(String imageId,
        LanguageEnum country,
        Integer beginPage,
        Integer pageSize) {
        // 分页大小最大不超过50，建议20效果最佳
        pageSize = Math.min(pageSize, MAX_PAGE_SIZE);
        return of(imageId, country, pageSize, beginPage, null, null, null, null, null, null, null, null, null, null, null, null);
    }

    // 重载请求 增加分页参数
    public static GoodsImageSearchRequestRecord of(String imageId,
        String imageAddress,
        String keyword,
        LanguageEnum country,
        Integer beginPage,
        Integer pageSize) {
        // 分页大小最大不超过50，建议20效果最佳
        pageSize = Math.min(pageSize, MAX_PAGE_SIZE);
        return of(imageId, country, pageSize, beginPage, null, null, null, null, null, null, null, imageAddress, keyword, null, null, null);
    }

    /**
     * 创建商品图片搜索请求
     *
     * @param imageId             图片ID（必填）
     * @param country             语言（必填）如en-英语，详细枚举参考开发人员参考菜单
     * @param pageSize            分页大小（必填），最大不超过50，建议20效果最佳
     * @param beginPage           分页页码（选填）
     * @param region              主体选择（选填）
     * @param filter              筛选参数（选填），多个通过英文逗号分隔
     * @param sort                排序参数（选填），枚举参见解决方案介绍 示例：{"price":"asc"}
     * @param outMemberId         外部用户ID（选填）
     * @param priceStart          批发价开始（选填）
     * @param priceEnd            批发价结束（选填）
     * @param categoryId          类目ID（选填）
     * @param imageAddress        图片地址（选填），仅使用1688图片链接或接口返回，其他不保证有效数据返回
     * @param keyword             在结果中搜索（选填）
     * @param auxiliaryText       多模态图搜文案（选填）
     * @param productCollectionId 寻源通工作台货盘ID（选填）
     * @param keywordTranslate    搜索词是否已经翻译（选填），true的话直接搜索，不翻译关键词
     * @return 商品图片搜索请求
     */
    public static GoodsImageSearchRequestRecord of(String imageId,
        LanguageEnum country,
        Integer pageSize,
        Integer beginPage,
        String region,
        String filter,
        String sort,
        String outMemberId,
        String priceStart,
        String priceEnd,
        Long categoryId,
        String imageAddress,
        String keyword,
        String auxiliaryText,
        String productCollectionId,
        Boolean keywordTranslate) {
        // 分页大小最大不超过50，建议20效果最佳
        pageSize = Math.min(pageSize, MAX_PAGE_SIZE);
        return GoodsImageSearchRequestRecord.builder()
            .imageId(imageId)
            .country(country.getLanguage())
            .pageSize(pageSize)
            .beginPage(beginPage)
            .region(region)
            .filter(filter)
            .sort(sort)
            .outMemberId(outMemberId)
            .priceStart(priceStart)
            .priceEnd(priceEnd)
            .categoryId(categoryId)
            .imageAddress(imageAddress)
            .keyword(keyword)
            .auxiliaryText(auxiliaryText)
            .productCollectionId(productCollectionId)
            .keywordTranslate(keywordTranslate)
            .build();
    }

    @Override
    public void requireParams() {
        // 设置默认分页参数
        assertTrue(imageId != null || imageAddress != null, "图片ID和图片地址不能同时为空");
        assertNotNull(country, "语言不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("imageId", imageId);
        params.put("country", country);
        params.put("pageSize", pageSize == null ? String.valueOf(DEFAULT_PAGE_SIZE) : pageSize.toString());
        params.put("beginPage", beginPage == null ? String.valueOf(DEFAULT_PAGE_NO) : beginPage.toString());
        if (region != null) {
            params.put("region", region);
        }
        if (filter != null) {
            params.put("filter", filter);
        }
        if (sort != null) {
            params.put("sort", sort);
        }
        if (outMemberId != null) {
            params.put("outMemberId", outMemberId);
        }
        if (priceStart != null) {
            params.put("priceStart", priceStart);
        }
        if (priceEnd != null) {
            params.put("priceEnd", priceEnd);
        }
        if (categoryId != null) {
            params.put("categoryId", categoryId.toString());
        }
        if (imageAddress != null) {
            params.put("imageAddress", imageAddress);
        }
        if (keyword != null) {
            params.put("keyword", keyword);
        }
        if (auxiliaryText != null) {
            params.put("auxiliaryText", auxiliaryText);
        }
        if (productCollectionId != null) {
            params.put("productCollectionId", productCollectionId);
        }
        if (keywordTranslate != null) {
            params.put("keywordTranslate", keywordTranslate.toString());
        }
        return params;
    }
}
