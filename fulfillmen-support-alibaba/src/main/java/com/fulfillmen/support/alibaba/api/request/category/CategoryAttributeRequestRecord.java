/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.category;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 类目属性请求
 * <p>
 * 用于获取指定类目的属性信息。
 *
 * <AUTHOR>
 * @created 2025-01-17
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:category.attribute.get-1">类目属性API文档</a>
 */
@Builder
public record CategoryAttributeRequestRecord(
    /**
     * 类目ID（必填）
     */
    @JsonProperty("categoryID") Long categoryId,

    /**
     * 站点（必填）
     */
    String webSite,

    /**
     * 场景（选填）
     */
    String scene
) implements BaseAlibabaRequestRecord {

    @Override
    public void requireParams() {
        assertNotNull(categoryId, "类目ID不能为空");
        assertNotBlank(webSite, "站点不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("categoryID", String.valueOf(categoryId));
        params.put("webSite", webSite);
        if (scene != null) {
            params.put("scene", scene);
        }
        return params;
    }
}