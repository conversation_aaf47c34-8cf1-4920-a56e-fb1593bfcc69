/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model;

import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 免密支付结果
 * <p>
 * 包含支付协议信息列表
 *
 * <AUTHOR>
 * @created 2025-01-10
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.pay.protocolPay.isopen-1">API文档</a>
 */
@Data
@NoArgsConstructor
public class ProtocolPayResult {

    /**
     * 签约状态列表
     */
    private List<PaymentAgreement> paymentAgreements;
}