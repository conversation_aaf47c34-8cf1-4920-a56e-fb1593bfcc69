/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.integration;

import static org.assertj.core.api.Assertions.assertThat;

import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.api.request.message.MessageConfirmRequestRecord;
import com.fulfillmen.support.alibaba.api.request.message.MessageCursorRequestRecord;
import com.fulfillmen.support.alibaba.api.request.message.MessageQueryFailedListRequestRecord;
import com.fulfillmen.support.alibaba.service.IMessageService;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.test.StepVerifier;

/**
 * 1688消息API集成测试
 *
 * <AUTHOR>
 * @created 2025-01-15
 * @since 1.0.0
 */
@Slf4j
class MessageAPIIntegrationTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    @Autowired
    private IMessageService messageService;

    @Test
    void shouldIntegrate() {
        log.info("{} 开始集成测试查询失败消息列表 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var endDateTime = LocalDateTime.now();
        var startDateTime = endDateTime.minusHours(24); // 24小时前
        var request = MessageQueryFailedListRequestRecord.of(Date.from(startDateTime.atZone(ZoneId.systemDefault())
            .toInstant()), Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant()));
        log.info("{}请求参数: createStartTime={}, createEndTime={}, page={}, pageSize={}", LOG_ITEM, request
            .createStartTime(), request.createEndTime(), request.page(), request.pageSize());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = messageService.queryFailedMessageList(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();

            // 业务检查
            assertThat(result.getPushMessagePage()).isNotNull();
            assertThat(result.getPushMessagePage().getDatas()).isNotNull();

            // 记录日志
            result.getPushMessagePage().getDatas().forEach(data -> {
                log.info("{}数据: {}", LOG_ITEM, data);
            });

            // 记录性能指标
            recordMetrics("查询失败消息列表", startTime, result != null);
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("查询失败消息列表");
        log.info("{} 集成测试查询失败消息列表完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldConfirmFailedMessage() {
        log.info("{} 开始集成测试失败消息批量确认 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given - 先查询失败消息列表获取真实的消息ID
        var endDateTime = LocalDateTime.now();
        var startDateTime = endDateTime.minusHours(24); // 24小时前
        var queryRequest = MessageQueryFailedListRequestRecord.of(Date.from(startDateTime.atZone(ZoneId.systemDefault())
            .toInstant()), Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant()));
        log.info("{}请求参数: createStartTime={}, createEndTime={}, page={}, pageSize={}", LOG_ITEM, queryRequest
            .createStartTime(), queryRequest.createEndTime(), queryRequest.page(), queryRequest.pageSize());

        // 查询并获取消息ID
        var msgIds = messageService.queryFailedMessageList(queryRequest).map(response -> {
            if (response.getPushMessagePage() != null && response.getPushMessagePage().getDatas() != null && !response
                .getPushMessagePage()
                .getDatas()
                .isEmpty()) {
                return response.getPushMessagePage().getDatas().stream().map(msg -> msg.getMsgId()).limit(2).toList();
            }
            return Arrays.asList(115284598540L, 115277073257L); // 如果没有查到，使用默认值
        }).block();

        // 构建确认请求
        var request = MessageConfirmRequestRecord.of(msgIds);
        log.info("{}请求参数: msgIdList={}", LOG_ITEM, request.msgIdList());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = messageService.confirmFailedMessage(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getIsSuccess()).isTrue();
            log.info("{}响应结果: isSuccess={}", LOG_ITEM, result.getIsSuccess());

            // 记录性能指标
            recordMetrics("ConfirmFailedMessage", startTime, result.getIsSuccess());
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("ConfirmFailedMessage");
        log.info("{} 集成测试失败消息批量确认完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleConfirmFailedMessageWithInvalidMsgIds() {
        log.info("{} 开始集成测试无效消息ID确认 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = MessageConfirmRequestRecord.of(Arrays.asList(-1L, -2L));
        log.info("{}请求参数: msgIdList={}", LOG_ITEM, request.msgIdList());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = messageService.confirmFailedMessage(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getIsSuccess()).isFalse();
            log.info("{}错误响应: code={}, message={}", LOG_ITEM, result.getErrorCode(), result.getErrorMessage());

            // 记录性能指标
            recordMetrics("ConfirmFailedMessageError", startTime, result.getIsSuccess());
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("ConfirmFailedMessageError");
        log.info("{} 集成测试无效消息ID确认完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetMessageCursorList() {
        log.info("{} 开始集成测试游标式获取失败消息列表 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        // Given
        var endDateTime = LocalDateTime.now();
        var startDateTime = endDateTime.minusHours(24); // 24小时前
        var request = MessageCursorRequestRecord.of(Date.from(startDateTime.atZone(ZoneId.systemDefault())
            .toInstant()), Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant()));
        log.info("{}请求参数: createStartTime={}, createEndTime={}, quantity={}", LOG_ITEM, request
            .createStartTime(), request.createEndTime(), request.quantity());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = messageService.getMessageCursorList(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            assertThat(result).isNotNull();

            if (result.getPushMessageList() != null && !result.getPushMessageList().isEmpty()) {
                var firstMessage = result.getPushMessageList().get(0);
                log.info("{}消息ID: {}", LOG_ITEM, firstMessage.getMsgId());
                log.info("{}消息类型: {}", LOG_ITEM, firstMessage.getType());
                log.info("{}用户信息: {}", LOG_ITEM, firstMessage.getUserInfo());
                log.info("{}创建时间: {}", LOG_ITEM, new Date(firstMessage.getGmtBorn()));
            } else {
                log.info("{}无失败消息", LOG_ITEM);
            }
            // 记录性能指标
            recordMetrics("GetMessageCursorList", startTime, true);
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GetMessageCursorList");
        log.info("{} 集成测试游标式获取失败消息列表完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleGetMessageCursorListWithInvalidQuantity() {
        log.info("{} 开始集成测试游标式获取失败消息列表-无效数量 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        // Given
        var request = MessageCursorRequestRecord.of(null, null, 201, null, null);
        log.info("{}请求参数: quantity={}", LOG_ITEM, request.quantity());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(messageService.getMessageCursorList(request)).expectErrorMatches(throwable -> {
            log.error("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            // 记录性能指标
            recordMetrics("GetMessageCursorListError", startTime, false);
            return throwable instanceof com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException && throwable
                .getMessage()
                .contains("每次取的数据量必须在20-200之间");
        }).verify();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GetMessageCursorListError");
        log.info("{} 集成测试游标式获取失败消息列表-无效数量完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }
}