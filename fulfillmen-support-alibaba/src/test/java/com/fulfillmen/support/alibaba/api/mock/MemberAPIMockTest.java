/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.mock;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.api.MemberAPI;
import com.fulfillmen.support.alibaba.api.request.member.MemberRegisterRequestRecord;
import com.fulfillmen.support.alibaba.api.request.member.SubAccountAuthAddRequestRecord;
import com.fulfillmen.support.alibaba.api.request.member.SubAccountAuthCancelRequestRecord;
import com.fulfillmen.support.alibaba.api.request.member.SubAccountAuthListRequestRecord;
import com.fulfillmen.support.alibaba.api.response.member.MemberRegisterResponse;
import com.fulfillmen.support.alibaba.api.response.member.SubAccountAuthAddResponse;
import com.fulfillmen.support.alibaba.api.response.member.SubAccountAuthCancelResponse;
import com.fulfillmen.support.alibaba.api.response.member.SubAccountAuthListResponse;
import com.fulfillmen.support.alibaba.api.response.model.MemberRegisterResult;
import com.fulfillmen.support.alibaba.api.response.model.SubAccountAuthAddResult;
import com.fulfillmen.support.alibaba.api.response.model.SubAccountAuthCancelResult;
import com.fulfillmen.support.alibaba.api.response.model.SubAccountAuthListResult;
import com.fulfillmen.support.alibaba.service.IMemberService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

/**
 * 1688会员API Mock测试
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Slf4j
class MemberAPIMockTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    @Autowired
    private IMemberService memberService;

    @MockBean
    private MemberAPI memberAPI;

    @Test
    void shouldRegisterMember() {
        // Given
        var request = MemberRegisterRequestRecord
            .of("CN", "1688", "<EMAIL>", "TEST_001", "<EMAIL>", "***********", "86", "127.0.0.1");

        MemberRegisterResult result = new MemberRegisterResult();
        result.setSuccess(true);
        result.setCode("50000");
        result.setMessage("success");
        result.setResult(true);

        MemberRegisterResponse expectedResponse = new MemberRegisterResponse();
        expectedResponse.setResult(result);

        when(memberAPI.register(any(), any())).thenReturn(Mono.just(expectedResponse));

        // When & Then
        StepVerifier.create(memberService.register(request)).expectNextMatches(response -> {
            assertThat(response).isNotNull();
            assertThat(response.getResult()).isNotNull();
            assertThat(response.getResult().getSuccess()).isTrue();
            assertThat(response.getResult().getCode()).isEqualTo("50000");
            assertThat(response.getResult().getResult()).isTrue();
            return true;
        }).verifyComplete();
    }

    @Test
    void shouldHandleRegisterFailure() {
        // Given
        var request = MemberRegisterRequestRecord
            .of("CN", "1688", "<EMAIL>", "TEST_001", "<EMAIL>", "***********", "86", "127.0.0.1");

        MemberRegisterResult result = new MemberRegisterResult();
        result.setSuccess(false);
        result.setCode("50001");
        result.setMessage("registration failed");
        result.setResult(false);

        MemberRegisterResponse expectedResponse = new MemberRegisterResponse();
        expectedResponse.setResult(result);

        when(memberAPI.register(any(), any())).thenReturn(Mono.just(expectedResponse));

        // When & Then
        StepVerifier.create(memberService.register(request)).expectNextMatches(response -> {
            assertThat(response).isNotNull();
            assertThat(response.getResult()).isNotNull();
            assertThat(response.getResult().getSuccess()).isFalse();
            assertThat(response.getResult().getCode()).isEqualTo("50001");
            assertThat(response.getResult().getResult()).isFalse();
            return true;
        }).verifyComplete();
    }

    @Test
    void shouldAddSubAccountAuth() {
        // Given
        var request = SubAccountAuthAddRequestRecord.of(List.of("SUB_001", "SUB_002"));

        Map<String, String> returnValue = new HashMap<>();
        returnValue.put("SUB_001", "success");
        returnValue.put("SUB_002", "success");

        SubAccountAuthAddResult result = new SubAccountAuthAddResult();
        result.setSuccess(true);
        result.setReturnValue(returnValue);

        SubAccountAuthAddResponse expectedResponse = new SubAccountAuthAddResponse();
        expectedResponse.setResult(result);

        when(memberAPI.addSubAccountAuth(any(), any())).thenReturn(Mono.just(expectedResponse));

        // When & Then
        StepVerifier.create(memberService.addSubAccountAuth(request)).expectNextMatches(response -> {
            assertThat(response).isNotNull();
            assertThat(response.getResult()).isNotNull();
            assertThat(response.getResult().getSuccess()).isTrue();
            assertThat(response.getResult().getReturnValue()).isNotEmpty()
                .containsEntry("SUB_001", "success")
                .containsEntry("SUB_002", "success");
            return true;
        }).verifyComplete();
    }

    @Test
    void shouldCancelSubAccountAuth() {
        // Given
        var request = SubAccountAuthCancelRequestRecord.of(List.of("SUB_001", "SUB_002"));

        SubAccountAuthCancelResult result = new SubAccountAuthCancelResult();
        result.setSuccess(true);
        result.setReturnValue(true);

        SubAccountAuthCancelResponse expectedResponse = new SubAccountAuthCancelResponse();
        expectedResponse.setResult(result);

        when(memberAPI.cancelSubAccountAuth(any(), any())).thenReturn(Mono.just(expectedResponse));

        // When & Then
        StepVerifier.create(memberService.cancelSubAccountAuth(request)).expectNextMatches(response -> {
            assertThat(response).isNotNull();
            assertThat(response.getResult()).isNotNull();
            assertThat(response.getResult().getSuccess()).isTrue();
            assertThat(response.getResult().getReturnValue()).isTrue();
            return true;
        }).verifyComplete();
    }

    @Test
    void shouldListSubAccountAuth() {
        // Given
        var request = SubAccountAuthListRequestRecord.of(List.of("SUB_001", "SUB_002"));

        SubAccountAuthListResult.AuthRelationDTO auth1 = new SubAccountAuthListResult.AuthRelationDTO();
        auth1.setSubOwnerId("SUB_001");
        auth1.setStatus("AUTHORIZED");

        SubAccountAuthListResult.AuthRelationDTO auth2 = new SubAccountAuthListResult.AuthRelationDTO();
        auth2.setSubOwnerId("SUB_002");
        auth2.setStatus("AUTHORIZED");

        SubAccountAuthListResult result = new SubAccountAuthListResult();
        result.setSuccess(true);
        result.setReturnValue(new SubAccountAuthListResult.AuthRelationDTO[]{auth1, auth2});

        SubAccountAuthListResponse expectedResponse = new SubAccountAuthListResponse();
        expectedResponse.setResult(result);

        when(memberAPI.listSubAccountAuth(any(), any())).thenReturn(Mono.just(expectedResponse));

        // When & Then
        StepVerifier.create(memberService.listSubAccountAuth(request)).expectNextMatches(response -> {
            assertThat(response).isNotNull();
            assertThat(response.getResult()).isNotNull();
            assertThat(response.getResult().getSuccess()).isTrue();
            assertThat(response.getResult().getReturnValue()).hasSize(2);
            assertThat(response.getResult().getReturnValue()[0].getSubOwnerId()).isEqualTo("SUB_001");
            assertThat(response.getResult().getReturnValue()[1].getSubOwnerId()).isEqualTo("SUB_002");
            return true;
        }).verifyComplete();
    }
}