/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.mock;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import cn.hutool.json.JSONUtil;
import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.api.OrderAPI;
import com.fulfillmen.support.alibaba.api.request.model.TradeFastAddress;
import com.fulfillmen.support.alibaba.api.request.model.TradeFastCargo;
import com.fulfillmen.support.alibaba.api.request.order.OrderBuyerListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.order.OrderBuyerListRequestRecord.BizTypesEnum;
import com.fulfillmen.support.alibaba.api.request.order.OrderBuyerListRequestRecord.OrderStatusEnum;
import com.fulfillmen.support.alibaba.api.request.order.OrderCancelRequestRecord;
import com.fulfillmen.support.alibaba.api.request.order.OrderDetailRequestRecord;
import com.fulfillmen.support.alibaba.api.request.order.OrderPreviewRequestRecord;
import com.fulfillmen.support.alibaba.api.request.order.TradeFeedbackRequestRecord;
import com.fulfillmen.support.alibaba.api.response.order.OrderBuyerListResponse;
import com.fulfillmen.support.alibaba.api.response.order.OrderCancelResponse;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse;
import com.fulfillmen.support.alibaba.api.response.order.OrderPreviewResponse;
import com.fulfillmen.support.alibaba.api.response.order.TradeFeedbackResponse;
import com.fulfillmen.support.alibaba.service.IOrderService;
import java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

/**
 * 1688订单API Mock测试
 *
 * <AUTHOR>
 * @created 2025-01-10
 */
@Slf4j
class OrderAPIMockTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    @Autowired
    private IOrderService orderService;

    @MockBean
    private OrderAPI orderAPI;

    @Test
    void shouldPreviewOrder() {
        // Given
        TradeFastAddress address = TradeFastAddress.builder()
            .fullName("张三")
            .mobile("15251667788")
            .phone("0517-88990077")
            .postCode("000000")
            .cityText("杭州市")
            .provinceText("浙江省")
            .areaText("滨江区")
            .address("网商路699号")
            .districtCode("310107")
            .build();

        TradeFastCargo cargo = TradeFastCargo.builder()
            .offerId(5544563483344L)
            .specId("b266e072650618beaf205cbae88530d")
            .quantity(5.0)
            .build();

        OrderPreviewRequestRecord request = OrderPreviewRequestRecord.of(address, Collections
            .singletonList(cargo), "general");

        log.info("{}开始预览订单测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}流程类型: {}", LOG_ITEM, request.flow());
        log.info("{}收货人: {}", LOG_ITEM, request.addressParam().getFullName());
        log.info("{}商品ID: {}", LOG_ITEM, request.cargoParamList().get(0).getOfferId());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // Mock 成功响应
        OrderPreviewResponse successResponse = JSONUtil.toBean("""
            {
                "success": true,
                "orderPreviewResult": {
                    "status": true,
                    "sumPayment": 1000,
                    "sumCarriage": 100,
                    "sumPaymentNoCarriage": 900,
                    "cargoList": [
                        {
                            "offerId": 5544563483344,
                            "amount": 5.0,
                            "specId": "b266e072650618beaf205cbae88530d",
                            "finalUnitPrice": 180
                        }
                    ]
                }
            }""", OrderPreviewResponse.class);

        when(orderAPI.previewOrder(any(), any())).thenReturn(Mono.just(successResponse));

        // When
        var response = orderService.previewOrder(request);

        // Then
        StepVerifier.create(response).assertNext(previewResponse -> {
            // 记录API调用指标
            recordMetrics("PreviewOrder", startTime, previewResponse != null && previewResponse.getSuccess());

            assertThat(previewResponse).isNotNull();
            assertThat(previewResponse.getSuccess()).isTrue();
            assertThat(previewResponse.getOrderPreviewResult()).isNotEmpty();

            var resultList = previewResponse.getOrderPreviewResult();
            resultList.forEach(result -> {
                assertThat(result.getStatus()).isTrue();
                assertThat(result.getSumPayment()).isEqualTo(1000);
                assertThat(result.getSumCarriage()).isEqualTo(100);
                assertThat(result.getSumPaymentNoCarriage()).isEqualTo(900);
                assertThat(result.getCargoList()).hasSize(1);

                // 打印预览结果
                log.info("{}预览订单结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
                log.info("{}是否成功: {}", LOG_ITEM, previewResponse.getSuccess());
                log.info("{}订单总金额: {}", LOG_ITEM, result.getSumPayment());
                log.info("{}商品总金额: {}", LOG_ITEM, result.getSumPaymentNoCarriage());
                log.info("{}运费: {}", LOG_ITEM, result.getSumCarriage());

                if (result.getCargoList() != null && !result.getCargoList().isEmpty()) {
                    var firstCargo = result.getCargoList().get(0);
                    log.info("{}商品信息{}", LOG_SEPARATOR, LOG_SEPARATOR);
                    log.info("{}商品ID: {}", LOG_ITEM, firstCargo.getOfferId());
                    log.info("{}商品数量: {}", LOG_ITEM, firstCargo.getAmount());
                    log.info("{}商品单价: {}", LOG_ITEM, firstCargo.getFinalUnitPrice());
                }
            });
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("PreviewOrder");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandlePreviewOrderError() {
        // Given
        TradeFastAddress addressParam = TradeFastAddress.builder()
            .addressId(123456789L)
            .fullName("张三")
            .mobile("13800138000")
            .phone("010-12345678")
            .postCode("100000")
            .cityText("北京市")
            .provinceText("北京")
            .areaText("朝阳区")
            .townText("望京街道")
            .address("望京SOHO T1 C座")
            .districtCode("110105")
            .build();

        TradeFastCargo cargoParam = TradeFastCargo.builder()
            .offerId(999999999999L)
            .specId("invalid_spec_id")
            .quantity(1.0)
            .build();

        OrderPreviewRequestRecord request = OrderPreviewRequestRecord.of(addressParam, Collections
            .singletonList(cargoParam), "general");

        log.info("{}开始预览订单错误测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}流程类型: {}", LOG_ITEM, request.flow());
        log.info("{}收货人: {}", LOG_ITEM, request.addressParam().getFullName());
        log.info("{}商品ID: {}", LOG_ITEM, request.cargoParamList().get(0).getOfferId());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // Mock 错误响应
        OrderPreviewResponse errorResponse = JSONUtil.toBean("""
            {
                "success": false,
                "errorCode": "400_5",
                "errorMessage": "商品不存在或已下架",
                "orderPreviewResult": {
                    "status": false,
                    "message": "商品不存在或已下架",
                    "resultCode": "400_5"
                }
            }""", OrderPreviewResponse.class);

        when(orderAPI.previewOrder(any(), any())).thenReturn(Mono.just(errorResponse));

        // When
        var response = orderService.previewOrder(request);

        // Then
        StepVerifier.create(response).assertNext(previewResponse -> {
            // 记录API调用指标
            recordMetrics("PreviewOrderError", startTime, false);

            assertThat(previewResponse).isNotNull();
            assertThat(previewResponse.getSuccess()).isFalse();
            assertThat(previewResponse.getErrorCode()).isEqualTo("400_5");
            assertThat(previewResponse.getErrorMessage()).isEqualTo("商品不存在或已下架");

            // 打印错误结果
            log.info("{}预览订单错误结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, previewResponse.getSuccess());
            log.info("{}错误码: {}", LOG_ITEM, previewResponse.getErrorCode());
            log.info("{}错误信息: {}", LOG_ITEM, previewResponse.getErrorMessage());

            assertThat(previewResponse.getOrderPreviewResult()).isNotEmpty();
            var resultList = previewResponse.getOrderPreviewResult();
            resultList.forEach(result -> {
                log.info("{}预览结果状态: {}", LOG_ITEM, result.getStatus());
                log.info("{}预览结果消息: {}", LOG_ITEM, result.getMessage());
                log.info("{}预览结果代码: {}", LOG_ITEM, result.getResultCode());
            });
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("PreviewOrderError");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldCancelOrder() {
        // Given
        var request = OrderCancelRequestRecord.of("1688", 1234567890L, "buyerCancel");

        log.info("{}开始取消订单测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}订单ID: {}", LOG_ITEM, request.tradeId());
        log.info("{}取消原因: {}", LOG_ITEM, request.cancelReason());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // Mock 成功响应
        OrderCancelResponse successResponse = JSONUtil.toBean("""
            {
                "success": true,
                "errorCode": null,
                "errorMessage": null
            }""", OrderCancelResponse.class);

        when(orderAPI.cancelOrder(any(), any())).thenReturn(Mono.just(successResponse));

        // When
        var response = orderService.cancelOrder(request);

        // Then
        StepVerifier.create(response).assertNext(cancelResponse -> {
            // 记录API调用指标
            recordMetrics("CancelOrder", startTime, cancelResponse != null && cancelResponse.getSuccess());

            assertThat(cancelResponse).isNotNull();
            assertThat(cancelResponse.getSuccess()).isTrue();
            assertThat(cancelResponse.getErrorCode()).isNull();
            assertThat(cancelResponse.getErrorMessage()).isNull();

            // 打印取消结果
            log.info("{}取消订单结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, cancelResponse.getSuccess());
            if (cancelResponse.getErrorCode() != null) {
                log.info("{}错误码: {}", LOG_ITEM, cancelResponse.getErrorCode());
                log.info("{}错误信息: {}", LOG_ITEM, cancelResponse.getErrorMessage());
            }
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("CancelOrder");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleCancelOrderError() {
        // Given
        var request = OrderCancelRequestRecord.of("1688", 9999999999L, "buyerCancel");

        log.info("{}开始取消订单错误测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}订单ID: {}", LOG_ITEM, request.tradeId());
        log.info("{}取消原因: {}", LOG_ITEM, request.cancelReason());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // Mock 错误响应
        OrderCancelResponse errorResponse = JSONUtil.toBean("""
            {
                "success": false,
                "errorCode": "400",
                "errorMessage": "订单不存在或已取消"
            }""", OrderCancelResponse.class);

        when(orderAPI.cancelOrder(any(), any())).thenReturn(Mono.just(errorResponse));

        // When
        var response = orderService.cancelOrder(request);

        // Then
        StepVerifier.create(response).assertNext(cancelResponse -> {
            // 记录API调用指标
            recordMetrics("CancelOrderError", startTime, false);

            assertThat(cancelResponse).isNotNull();
            assertThat(cancelResponse.getSuccess()).isFalse();
            assertThat(cancelResponse.getErrorCode()).isEqualTo("400");
            assertThat(cancelResponse.getErrorMessage()).isEqualTo("订单不存在或已取消");

            // 打印错误结果
            log.info("{}取消订单错误结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, cancelResponse.getSuccess());
            log.info("{}错误码: {}", LOG_ITEM, cancelResponse.getErrorCode());
            log.info("{}错误信息: {}", LOG_ITEM, cancelResponse.getErrorMessage());
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("CancelOrderError");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetBuyerOrderList() {
        // Given
        var request = OrderBuyerListRequestRecord
            .of(BizTypesEnum.CN, OrderStatusEnum.WAIT_BUYER_PAY, 1, 20, null, null, null, null, null, false, false, null, false, "20240101000000000+0800", "20241231235959999+0800",
                null, null);

        log.info("{}开始获取订单列表测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}页码: {}", LOG_ITEM, request.page());
        log.info("{}每页数量: {}", LOG_ITEM, request.pageSize());
        log.info("{}订单状态: {}", LOG_ITEM, request.orderStatus());
        log.info("{}创建开始时间: {}", LOG_ITEM, request.createStartTime());
        log.info("{}创建结束时间: {}", LOG_ITEM, request.createEndTime());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // Mock 成功响应
        OrderBuyerListResponse successResponse = JSONUtil.toBean("""
            {
                "errorCode": null,
                "errorMessage": null,
                "totalRecord": 100,
                "result": [
                    {
                        "baseInfo": {
                            "id": 1234567890,
                            "createTime": "20240105103000000+0800",
                            "status": "waitbuyerpay",
                            "totalAmount": 1000.00
                        },
                    "tradeProductItems": [
                            {
                                "productID": 5544563483344,
                                "name": "测试商品1",
                                "quantity": 5,
                                "price": 200.00
                            }
                        ]
                    }
                ]
            }""", OrderBuyerListResponse.class);

        when(orderAPI.getBuyerOrderList(any(), any())).thenReturn(Mono.just(successResponse));

        // When
        var response = orderService.getBuyerOrderList(request);

        // Then
        StepVerifier.create(response).assertNext(listResponse -> {
            // 记录API调用指标
            recordMetrics("GetBuyerOrderList", startTime, listResponse != null && listResponse.getErrorCode() == null);

            assertThat(listResponse).isNotNull();
            assertThat(listResponse.getErrorCode()).isNull();
            assertThat(listResponse.getErrorMessage()).isNull();
            assertThat(listResponse.getTotalRecord()).isEqualTo(100L);
            assertThat(listResponse.getResult()).hasSize(1);

            var firstOrder = listResponse.getResult().get(0);
            assertThat(firstOrder.getBaseInfo().getId()).isEqualTo(1234567890L);
            assertThat(firstOrder.getBaseInfo().getStatus()).isEqualTo("waitbuyerpay");
            assertThat(firstOrder.getProductItems()).hasSize(1);

            // 打印订单列表结果
            log.info("{}获取订单列表结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, listResponse.getErrorCode() == null);
            log.info("{}错误码: {}", LOG_ITEM, listResponse.getErrorCode());
            log.info("{}错误信息: {}", LOG_ITEM, listResponse.getErrorMessage());
            log.info("{}总记录数: {}", LOG_ITEM, listResponse.getTotalRecord());
            log.info("{}本页订单数: {}", LOG_ITEM, listResponse.getResult().size());

            if (!listResponse.getResult().isEmpty()) {
                var order = listResponse.getResult().get(0);
                log.info("{}订单ID: {}", LOG_ITEM, order.getBaseInfo().getId());
                log.info("{}订单状态: {}", LOG_ITEM, order.getBaseInfo().getStatus());
                log.info("{}创建时间: {}", LOG_ITEM, order.getBaseInfo().getCreateTime());
                log.info("{}订单总金额: {}", LOG_ITEM, order.getBaseInfo().getTotalAmount());
            }
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GetBuyerOrderList");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleGetBuyerOrderListError() {
        // Given
        var request = OrderBuyerListRequestRecord
            .of(BizTypesEnum.CN, OrderStatusEnum.WAIT_BUYER_PAY, 1, 20, null, null, null, null, null, false, false, null, false, "20240101000000000+0800", "20241231235959999+0800",
                null, null);

        log.info("{}开始获取订单列表错误测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}页码: {}", LOG_ITEM, request.page());
        log.info("{}每页数量: {}", LOG_ITEM, request.pageSize());
        log.info("{}订单状态: {}", LOG_ITEM, request.orderStatus());
        log.info("{}创建开始时间: {}", LOG_ITEM, request.createStartTime());
        log.info("{}创建结束时间: {}", LOG_ITEM, request.createEndTime());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // Mock 错误响应
        OrderBuyerListResponse errorResponse = JSONUtil.toBean("""
            {
                "errorCode": "400",
                "errorMessage": "无效的订单状态",
                "totalRecord": 0,
                "result": []
            }""", OrderBuyerListResponse.class);

        when(orderAPI.getBuyerOrderList(any(), any())).thenReturn(Mono.just(errorResponse));

        // When
        var response = orderService.getBuyerOrderList(request);

        // Then
        StepVerifier.create(response).assertNext(listResponse -> {
            // 记录API调用指标
            recordMetrics("GetBuyerOrderListError", startTime, false);

            assertThat(listResponse).isNotNull();
            assertThat(listResponse.getErrorCode()).isEqualTo("400");
            assertThat(listResponse.getErrorMessage()).isEqualTo("无效的订单状态");
            assertThat(listResponse.getTotalRecord()).isZero();
            assertThat(listResponse.getResult()).isEmpty();

            // 打印错误结果
            log.info("{}获取订单列表错误结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, listResponse.getErrorCode() == null);
            log.info("{}错误码: {}", LOG_ITEM, listResponse.getErrorCode());
            log.info("{}错误信息: {}", LOG_ITEM, listResponse.getErrorMessage());
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GetBuyerOrderListError");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetOrderDetail() {
        // Given
        var request = new OrderDetailRequestRecord("1688", 1234567890L, true, true, true);

        log.info("{}开始获取订单详情测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}订单ID: {}", LOG_ITEM, request.orderId());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // Mock 成功响应
        OrderDetailResponse successResponse = JSONUtil.toBean("""
            {
                "errorCode": null,
                "errorMessage": null,
                "result": {
                    "baseInfo": {
                        "id": 1234567890,
                        "createTime": "20240105103000000+0800",
                        "status": "waitbuyerpay",
                        "totalAmount": 1000.00
                    },
                "tradeProductItems": [
                        {
                            "productID": 5544563483344,
                            "name": "测试商品1",
                            "quantity": 5,
                            "price": 200.00
                        }
                    ]
                }
            }""", OrderDetailResponse.class);

        when(orderAPI.getOrderDetail(any(), any())).thenReturn(Mono.just(successResponse));

        // When
        var response = orderService.getOrderDetail(request);

        // Then
        StepVerifier.create(response).assertNext(detailResponse -> {
            // 记录API调用指标
            recordMetrics("GetOrderDetail", startTime, detailResponse != null && detailResponse.getErrorCode() == null);

            assertThat(detailResponse).isNotNull();
            assertThat(detailResponse.getErrorCode()).isNull();
            assertThat(detailResponse.getErrorMessage()).isNull();
            assertThat(detailResponse.getResult()).isNotNull();

            var orderDetail = detailResponse.getResult();
            assertThat(orderDetail.getBaseInfo().getId()).isEqualTo(1234567890L);
            assertThat(orderDetail.getBaseInfo().getStatus()).isEqualTo("waitbuyerpay");
            assertThat(orderDetail.getProductItems()).hasSize(1);

            // 打印订单详情结果
            log.info("{}获取订单详情结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, detailResponse.getErrorCode() == null);
            log.info("{}错误码: {}", LOG_ITEM, detailResponse.getErrorCode());
            log.info("{}错误信息: {}", LOG_ITEM, detailResponse.getErrorMessage());

            var baseInfo = orderDetail.getBaseInfo();
            log.info("{}订单ID: {}", LOG_ITEM, baseInfo.getId());
            log.info("{}订单状态: {}", LOG_ITEM, baseInfo.getStatus());
            log.info("{}创建时间: {}", LOG_ITEM, baseInfo.getCreateTime());
            log.info("{}订单总金额: {}", LOG_ITEM, baseInfo.getTotalAmount());
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GetOrderDetail");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleGetOrderDetailError() {
        // Given
        var request = new OrderDetailRequestRecord("1688", 9999999999L, true, true, true);

        log.info("{}开始获取订单详情错误测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}订单ID: {}", LOG_ITEM, request.orderId());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // Mock 错误响应
        OrderDetailResponse errorResponse = JSONUtil.toBean("""
            {
                "errorCode": "400",
                "errorMessage": "订单不存在",
                "result": null
            }""", OrderDetailResponse.class);

        when(orderAPI.getOrderDetail(any(), any())).thenReturn(Mono.just(errorResponse));

        // When
        var response = orderService.getOrderDetail(request);

        // Then
        StepVerifier.create(response).assertNext(detailResponse -> {
            // 记录API调用指标
            recordMetrics("GetOrderDetailError", startTime, false);

            assertThat(detailResponse).isNotNull();
            assertThat(detailResponse.getErrorCode()).isEqualTo("400");
            assertThat(detailResponse.getErrorMessage()).isEqualTo("订单不存在");
            assertThat(detailResponse.getResult()).isNull();

            // 打印错误结果
            log.info("{}获取订单详情错误结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, detailResponse.getErrorCode() == null);
            log.info("{}错误码: {}", LOG_ITEM, detailResponse.getErrorCode());
            log.info("{}错误信息: {}", LOG_ITEM, detailResponse.getErrorMessage());
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GetOrderDetailError");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldAddFeedback() {
        // Given
        var request = TradeFeedbackRequestRecord.of("1234567890", "测试留言");

        log.info("{}开始添加订单留言测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}订单ID: {}", LOG_ITEM, request.orderId());
        log.info("{}留言内容: {}", LOG_ITEM, request.feedback());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // Mock 成功响应
        TradeFeedbackResponse successResponse = JSONUtil.toBean("""
            {
                "success": true,
                "code": null,
                "message": null,
                "result": {
                    "success": true,
                    "errorCode": null,
                    "errorInfo": null
                }
            }""", TradeFeedbackResponse.class);

        when(orderAPI.addFeedback(any(), any())).thenReturn(Mono.just(successResponse));

        // When
        var response = orderService.addFeedback(request);

        // Then
        StepVerifier.create(response).assertNext(feedbackResponse -> {
            // 记录API调用指标
            recordMetrics("AddFeedback", startTime, feedbackResponse != null && feedbackResponse.getSuccess());

            assertThat(feedbackResponse).isNotNull();
            assertThat(feedbackResponse.getSuccess()).isTrue();
            assertThat(feedbackResponse.getCode()).isNull();
            assertThat(feedbackResponse.getMessage()).isNull();
            assertThat(feedbackResponse.getResult()).isNotNull();
            assertThat(feedbackResponse.getResult().getSuccess()).isTrue();
            assertThat(feedbackResponse.getResult().getErrorCode()).isNull();
            assertThat(feedbackResponse.getResult().getErrorInfo()).isNull();

            // 打印添加留言结果
            log.info("{}添加订单留言结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, feedbackResponse.getSuccess());
            log.info("{}错误码: {}", LOG_ITEM, feedbackResponse.getCode());
            log.info("{}错误信息: {}", LOG_ITEM, feedbackResponse.getMessage());
            log.info("{}结果详情: {}", LOG_ITEM, feedbackResponse.getResult());
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("AddFeedback");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleAddFeedbackError() {
        // Given
        var request = TradeFeedbackRequestRecord.of("9999999999", "测试留言");

        log.info("{}开始添加订单留言错误测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}订单ID: {}", LOG_ITEM, request.orderId());
        log.info("{}留言内容: {}", LOG_ITEM, request.feedback());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // Mock 错误响应
        TradeFeedbackResponse errorResponse = JSONUtil.toBean("""
            {
                "success": false,
                "code": "400",
                "message": "订单不存在",
                "result": {
                    "success": false,
                    "errorCode": "400",
                    "errorInfo": "订单不存在"
                }
            }""", TradeFeedbackResponse.class);

        when(orderAPI.addFeedback(any(), any())).thenReturn(Mono.just(errorResponse));

        // When
        var response = orderService.addFeedback(request);

        // Then
        StepVerifier.create(response).assertNext(feedbackResponse -> {
            // 记录API调用指标
            recordMetrics("AddFeedback", startTime, feedbackResponse != null && feedbackResponse.getSuccess());

            assertThat(feedbackResponse).isNotNull();
            assertThat(feedbackResponse.getSuccess()).isFalse();
            assertThat(feedbackResponse.getCode()).isEqualTo("400");
            assertThat(feedbackResponse.getMessage()).isEqualTo("订单不存在");
            assertThat(feedbackResponse.getResult()).isNotNull();
            assertThat(feedbackResponse.getResult().getSuccess()).isFalse();
            assertThat(feedbackResponse.getResult().getErrorCode()).isEqualTo("400");
            assertThat(feedbackResponse.getResult().getErrorInfo()).isEqualTo("订单不存在");

            // 打印错误结果
            log.info("{}添加订单留言错误结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, feedbackResponse.getSuccess());
            log.info("{}错误码: {}", LOG_ITEM, feedbackResponse.getCode());
            log.info("{}错误信息: {}", LOG_ITEM, feedbackResponse.getMessage());
            log.info("{}结果详情: {}", LOG_ITEM, feedbackResponse.getResult());
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("AddFeedback");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }
}