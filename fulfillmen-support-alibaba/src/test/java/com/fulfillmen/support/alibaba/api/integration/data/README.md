# 订单详情测试数据框架 - 通用验证版本

## 概述

这个测试数据框架提供了一个灵活、可扩展的方式来测试订单详情API，支持三种不同级别的验证模式，从基础的API响应验证到精确的数值匹配验证。

## 🚀 核心特性

### 1. 三种验证模式
- **BASIC_ONLY**: 仅验证API调用成功和基础响应结构，适用于任意有效订单ID
- **STRUCTURE_VALIDATION**: 验证数据结构完整性和业务逻辑合理性，不验证具体数值
- **DETAILED_VALIDATION**: 进行精确的数值匹配验证，适用于已知的特定订单

### 2. 通用性设计
- 默认使用通用验证模式，无需硬编码特定订单数据
- 支持任意有效订单ID的测试
- 重点关注数据展示和基础验证而非精确匹配

### 3. 灵活的断言策略
- 验证响应结构的完整性（非空检查、字段存在性）
- 验证数据类型的正确性
- 验证业务逻辑的合理性（如时间顺序、状态一致性）
- 避免对具体数值的严格匹配（除非明确指定）

### 4. 详细的数据展示
- 保留详细的日志输出，展示查询到的实际数据
- 显示订单基础信息、商品项目概览、物流和交易信息概要
- 支持不同验证模式下的不同展示级别

## 数据模型结构

```
OrderTestData
├── orderId: Long                    // 订单ID
├── description: String              // 测试描述
├── baseInfo: BaseInfoExpected       // 基础信息
├── tradeProductItems: ProductItemsExpected // 商品项目
├── logistics: LogisticsExpected     // 物流信息
├── tradeTerms: TradeTermsExpected   // 交易条款
├── orderBizInfo: OrderBizInfoExpected // 业务信息
└── orderRateInfo: OrderRateInfoExpected // 评价信息
```

### BaseInfoExpected
- 订单状态、金额、买卖家信息
- 联系人信息、时间戳验证控制

### ProductItemsExpected
- 商品数量、类型验证
- 第一个商品的详细信息
- 总金额计算验证

### LogisticsExpected
- 收货地址、联系人信息
- 物流公司、运单号、状态
- 发货地址信息

### TradeTermsExpected
- 支付状态、金额、方式
- 支持 BigDecimal 类型的金额

### OrderBizInfoExpected
- 采源宝、诚e赊、dropshipping标识
- 运费险信息

### OrderRateInfoExpected
- 买卖家评价状态

## 🔧 使用方法

### 1. 快速开始 - 基础验证模式

最简单的使用方式，适用于任意有效订单ID：

```java
static Stream<OrderTestData> orderDetailTestDataProvider() {
    return Stream.of(
        // 基础验证 - 仅验证API响应结构，适用于任意订单
        OrderTestData.basicValidation(YOUR_ORDER_ID, "您的订单描述"),

        // 结构验证 - 验证数据完整性和业务逻辑
        OrderTestData.structureValidation(ANOTHER_ORDER_ID, "另一个订单的结构验证")
    );
}
```

### 2. 三种验证模式详解

#### 基础验证模式 (BASIC_ONLY)
```java
// 适用于任意有效订单ID，仅验证API调用成功和基础结构
OrderTestData.basicValidation(2829032257200540788L, "基础验证示例")
```
**验证内容：**
- API调用成功
- 响应结构基本完整性
- 必要字段存在性
- 显示订单概要信息

#### 结构验证模式 (STRUCTURE_VALIDATION)
```java
// 验证数据结构完整性和业务逻辑合理性
OrderTestData.structureValidation(2626652931211540788L, "结构验证示例")
```
**验证内容：**
- 基础验证的所有内容
- 数据类型正确性
- 业务逻辑合理性（时间顺序、金额为正等）
- 商品项目结构验证
- 显示详细订单信息

#### 详细验证模式 (DETAILED_VALIDATION)
```java
// 进行精确的数值匹配验证，需要提供具体的预期数据
OrderTestData.builder()
    .orderId(2626652931211540788L)
    .description("详细验证示例")
    .validationMode(OrderTestData.ValidationMode.DETAILED_VALIDATION)
    .baseInfo(BaseInfoExpected.builder()
        .status("success")
        .totalAmount("36800")
        .buyerID("b2b-2207416548807a4d12")
        // ... 其他精确的预期值
        .build())
    .build()
```
**验证内容：**
- 结构验证的所有内容
- 精确的数值匹配验证
- 详细的字段值验证

### 3. 运行参数化测试

```bash
# 运行所有订单详情测试
mvn test -Dtest=OrderAPIIntegrationTest#shouldGetOrderDetailParameterized -Ptest

# 运行特定的测试方法
mvn test -Dtest=OrderAPIIntegrationTest -Ptest
```

### 4. 实际使用示例

#### 场景1：测试新的订单ID
```java
// 只需要提供订单ID，无需任何预期数据
static Stream<OrderTestData> orderDetailTestDataProvider() {
    return Stream.of(
        OrderTestData.basicValidation(1234567890123456789L, "新订单基础验证"),
        OrderTestData.structureValidation(9876543210987654321L, "另一个订单结构验证")
    );
}
```

#### 场景2：混合验证模式
```java
static Stream<OrderTestData> orderDetailTestDataProvider() {
    return Stream.of(
        // 快速验证多个订单
        OrderTestData.basicValidation(ORDER_ID_1, "订单1基础验证"),
        OrderTestData.basicValidation(ORDER_ID_2, "订单2基础验证"),
        OrderTestData.basicValidation(ORDER_ID_3, "订单3基础验证"),

        // 重点验证某个订单的结构
        OrderTestData.structureValidation(IMPORTANT_ORDER_ID, "重要订单结构验证"),

        // 精确验证已知订单
        createKnownOrderDetailed()
    );
}
```

#### 场景3：从外部配置读取订单ID
```java
static Stream<OrderTestData> orderDetailTestDataProvider() {
    // 从配置文件或环境变量读取订单ID列表
    List<Long> orderIds = getOrderIdsFromConfig();

    return orderIds.stream()
        .map(orderId -> OrderTestData.basicValidation(orderId, "配置订单-" + orderId));
}
```

## 配置示例

参考 `order-test-data-examples.json` 文件查看完整的配置示例，包括：

1. **多商品类型订单**：包含配件链接、接地床笠、接地床单等
2. **单一商品类型订单**：只包含一种商品类型
3. **跨境订单**：包含采源宝、dropshipping等特殊业务标识

## 📋 最佳实践

### 1. 验证模式选择策略
- **日常测试**: 优先使用基础验证模式，快速验证API可用性
- **回归测试**: 使用结构验证模式，确保数据结构稳定性
- **精确测试**: 仅对关键订单使用详细验证模式

### 2. 测试数据组织
```java
static Stream<OrderTestData> orderDetailTestDataProvider() {
    return Stream.of(
        // 分组1: 快速验证 - 验证API基本功能
        OrderTestData.basicValidation(ORDER_ID_1, "快速验证-订单1"),
        OrderTestData.basicValidation(ORDER_ID_2, "快速验证-订单2"),

        // 分组2: 结构验证 - 验证不同类型订单
        OrderTestData.structureValidation(CROSS_BORDER_ORDER, "结构验证-跨境订单"),
        OrderTestData.structureValidation(DOMESTIC_ORDER, "结构验证-国内订单"),

        // 分组3: 详细验证 - 关键业务订单
        createCriticalOrderDetailed()
    );
}
```

### 3. 性能优化建议
- 基础验证模式执行最快，适合大批量测试
- 结构验证模式平衡了验证深度和执行速度
- 详细验证模式最慢，应谨慎使用

### 4. 错误处理策略
- 基础验证失败通常表示API问题
- 结构验证失败可能表示数据格式变化
- 详细验证失败可能是订单数据更新导致

## ⚠️ 注意事项

### 1. 订单ID要求
- **基础验证**: 只需要订单ID存在且可访问
- **结构验证**: 订单应包含基本的业务数据（商品、物流等）
- **详细验证**: 需要提供精确的预期数据，适用于稳定的已知订单

### 2. 验证模式选择
- 新订单或未知订单：使用基础验证或结构验证
- 已知订单且需要精确验证：使用详细验证
- 批量测试：优先使用基础验证

### 3. 性能考虑
- 基础验证 < 结构验证 < 详细验证（执行时间）
- 建议在CI/CD中主要使用基础验证和结构验证
- 详细验证适合手动测试或关键回归测试

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 订单不存在或无权访问
```
错误: 订单ID验证失败
解决: 确认订单ID正确且当前用户有访问权限
```

#### 2. 结构验证失败
```
错误: 商品项目结构验证失败
解决: 检查订单是否包含商品数据，或降级为基础验证
```

#### 3. 时间逻辑验证失败
```
错误: 支付时间不在创建时间之后
解决: 检查订单状态，某些订单可能时间字段为空
```

### 调试技巧

#### 1. 查看详细日志
```bash
# 启用详细日志
mvn test -Dtest=OrderAPIIntegrationTest -Ptest -Dlogging.level.com.fulfillmen=DEBUG
```

#### 2. 单独测试特定订单
```java
@Test
void debugSpecificOrder() {
    var testData = OrderTestData.structureValidation(YOUR_ORDER_ID, "调试订单");
    // 设置断点进行调试
}
```

#### 3. 验证模式降级
```java
// 如果详细验证失败，尝试结构验证
OrderTestData.structureValidation(orderId, "降级验证")

// 如果结构验证失败，尝试基础验证
OrderTestData.basicValidation(orderId, "基础验证")
```
