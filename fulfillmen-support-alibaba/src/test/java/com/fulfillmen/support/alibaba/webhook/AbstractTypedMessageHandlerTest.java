/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.fulfillmen.starter.core.util.JacksonUtil;
import com.fulfillmen.support.alibaba.enums.CallbackMessageType;
import com.fulfillmen.support.alibaba.enums.GoodsMessageTypeEnums;
import com.fulfillmen.support.alibaba.enums.LogisticsMessageTypeEnums;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.alibaba.webhook.data.GoodsMessage;
import com.fulfillmen.support.alibaba.webhook.data.LogisticsMessage;
import com.fulfillmen.support.alibaba.webhook.data.OrderMessage;

import lombok.extern.slf4j.Slf4j;

/**
 * AbstractTypedMessageHandler 测试类
 * 专门测试类型转换逻辑的修复效果
 * 
 * <AUTHOR>
 * @date 2025/7/26
 * @since 1.0.0
 */
@Slf4j
@DisplayName("AbstractTypedMessageHandler 类型转换测试")
class AbstractTypedMessageHandlerTest {

    private TestOrderMessageHandler testHandler;

    @BeforeEach
    void setUp() {
        testHandler = new TestOrderMessageHandler();
    }

    @Test
    @DisplayName("应该正确处理LinkedHashMap到OrderMessage的类型转换")
    void shouldConvertLinkedHashMapToOrderMessage() {
        // Given - 用户提供的测试数据，模拟MessageDispatcher解析后的LinkedHashMap
        String testJson = """
            {
                "data": {
                    "buyerMemberId": "b2b-665170100",
                    "orderId": 2846530957885540788,
                    "currentStatus": "waitbuyerpay",
                    "sellerMemberId": "b2b-1676547900b7bb3",
                    "msgSendTime": "2018-05-30 19:24:18"
                },
                "gmtBorn": 1753504811243,
                "msgId": "142083412383",
                "type": "ORDER_BUYER_VIEW_BUYER_MAKE",
                "userInfo": "b2b-2207416548807a4d12"
            }
            """;

        // 模拟MessageDispatcher解析后的数据结构（LinkedHashMap）
        Map<String, Object> rawMessageMap = JacksonUtil.convertToBean(testJson, Map.class);
        @SuppressWarnings("unchecked") LinkedHashMap<String, Object> dataMap = (LinkedHashMap<String, Object>) rawMessageMap.get("data");

        // 创建MessageEvent，data字段为LinkedHashMap（这是导致ClassCastException的根本原因）
        MessageEvent<OrderMessage> event = new MessageEvent<>();
        event.setMsgId("142083412383");
        event.setGmtBorn(1753504811243L);
        event.setType(OrderMessageTypeEnums.ORDER_BUYER_VIEW_BUYER_MAKE);
        event.setUserInfo("b2b-2207416548807a4d12");
        // 使用反射设置data字段，避免编译时类型检查
        setEventDataUnsafe(event, dataMap);
        event.setRawData(JacksonUtil.toJsonString(dataMap));
        event.setReceivedAt(LocalDateTime.now());

        log.info("测试数据准备完成，data类型: {}", dataMap.getClass().getSimpleName());

        // When & Then - 修复后应该不抛出ClassCastException
        MessageResult result = assertDoesNotThrow(() -> testHandler.handle(event));

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMsgId()).isEqualTo("142083412383");

        // 验证转换后的数据正确性
        OrderMessage convertedData = testHandler.getLastProcessedData();
        assertNotNull(convertedData);
        assertThat(convertedData.getBuyerMemberId()).isEqualTo("b2b-665170100");
        assertThat(convertedData.getOrderId()).isEqualTo(2846530957885540788L);
        assertThat(convertedData.getCurrentStatus()).isEqualTo("waitbuyerpay");
        assertThat(convertedData.getSellerMemberId()).isEqualTo("b2b-1676547900b7bb3");
        assertThat(convertedData.getMsgSendTime()).isEqualTo("2018-05-30 19:24:18");

        log.info("LinkedHashMap转换测试完成，转换后的OrderMessage: {}", convertedData);
    }

    @Test
    @DisplayName("应该正确处理已经是正确类型的数据")
    void shouldHandleAlreadyTypedData() {
        // Given - 创建正确类型的OrderMessage
        OrderMessage orderMessage = new OrderMessage();
        orderMessage.setBuyerMemberId("b2b-665170100");
        orderMessage.setOrderId(2846530957885540788L);
        orderMessage.setCurrentStatus("waitbuyerpay");
        orderMessage.setSellerMemberId("b2b-1676547900b7bb3");
        orderMessage.setMsgSendTime("2018-05-30 19:24:18");

        MessageEvent<OrderMessage> event = new MessageEvent<>();
        event.setMsgId("test-msg-002");
        event.setType(OrderMessageTypeEnums.ORDER_BUYER_VIEW_BUYER_MAKE);
        event.setData(orderMessage); // 正确类型，应该直接使用
        event.setRawData(JacksonUtil.toJsonString(orderMessage));

        log.info("测试正确类型数据，data类型: {}", orderMessage.getClass().getSimpleName());

        // When
        MessageResult result = assertDoesNotThrow(() -> testHandler.handle(event));

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();

        // 验证数据是直接使用的，没有进行转换
        OrderMessage processedData = testHandler.getLastProcessedData();
        assertThat(processedData).isSameAs(orderMessage); // 应该是同一个对象引用

        log.info("正确类型数据处理测试完成");
    }

    @Test
    @DisplayName("应该正确处理rawData为空的情况")
    void shouldHandleNullRawData() {
        // Given - rawData为空，但data为LinkedHashMap
        LinkedHashMap<String, Object> dataMap = new LinkedHashMap<>();
        dataMap.put("buyerMemberId", "b2b-665170100");
        dataMap.put("orderId", 2846530957885540788L);
        dataMap.put("currentStatus", "waitbuyerpay");

        MessageEvent<OrderMessage> event = new MessageEvent<>();
        event.setMsgId("test-msg-003");
        event.setType(OrderMessageTypeEnums.ORDER_BUYER_VIEW_BUYER_MAKE);
        // 使用反射设置data字段，避免编译时类型检查
        setEventDataUnsafe(event, dataMap);
        event.setRawData(null); // rawData为空

        log.info("测试rawData为空的情况");

        // When
        MessageResult result = assertDoesNotThrow(() -> testHandler.handle(event));

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();

        // 验证通过直接转换eventData得到了正确的结果
        OrderMessage convertedData = testHandler.getLastProcessedData();
        assertNotNull(convertedData);
        assertThat(convertedData.getBuyerMemberId()).isEqualTo("b2b-665170100");

        log.info("rawData为空情况处理测试完成");
    }

    @Test
    @DisplayName("应该正确处理data和rawData都为空的情况")
    void shouldHandleNullDataAndRawData() {
        // Given - data和rawData都为空
        MessageEvent<OrderMessage> event = new MessageEvent<>();
        event.setMsgId("test-msg-004");
        event.setType(OrderMessageTypeEnums.ORDER_BUYER_VIEW_BUYER_MAKE);
        event.setData(null);
        event.setRawData(null);

        log.info("测试data和rawData都为空的情况");

        // When
        MessageResult result = assertDoesNotThrow(() -> testHandler.handle(event));

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();

        // 验证处理了null数据
        OrderMessage processedData = testHandler.getLastProcessedData();
        assertThat(processedData).isNull();

        log.info("空数据情况处理测试完成");
    }

    @Test
    @DisplayName("应该正确处理物流消息的LinkedHashMap到LogisticsMessage转换")
    void shouldConvertLinkedHashMapToLogisticsMessage() {
        // Given - 物流状态变更消息测试数据
        String logisticsJson = """
            {
                "data": {
                    "logisticsId": "12345",
                    "cpCode": "SF",
                    "mailNo": "SF1234567890",
                    "statusChanged": "CONSIGN",
                    "changeTime": "2025-07-26 13:00:00",
                    "orderLogsItems": [{
                        "orderId": 2846530957885540788,
                        "orderEntryId": 12345678
                    }]
                },
                "gmtBorn": 1753504811243,
                "msgId": "logistics-msg-001",
                "type": "LOGISTICS_BUYER_VIEW_TRACE",
                "userInfo": "b2b-2207416548807a4d12"
            }
            """;

        // 创建测试Handler
        TestLogisticsMessageHandler logisticsHandler = new TestLogisticsMessageHandler();

        // 模拟MessageDispatcher解析后的数据结构
        Map<String, Object> rawMessageMap = JacksonUtil.convertToBean(logisticsJson, Map.class);
        @SuppressWarnings("unchecked") LinkedHashMap<String, Object> dataMap = (LinkedHashMap<String, Object>) rawMessageMap.get("data");

        // 创建MessageEvent
        MessageEvent<LogisticsMessage> event = new MessageEvent<>();
        event.setMsgId("logistics-msg-001");
        event.setGmtBorn(1753504811243L);
        event.setType(LogisticsMessageTypeEnums.LOGISTICS_BUYER_VIEW_TRACE);
        event.setUserInfo("b2b-2207416548807a4d12");
        setEventDataUnsafe(event, dataMap);
        event.setRawData(JacksonUtil.toJsonString(dataMap));
        event.setReceivedAt(LocalDateTime.now());

        log.info("物流消息测试数据准备完成，data类型: {}", dataMap.getClass().getSimpleName());

        // When & Then - 应该成功转换
        MessageResult result = assertDoesNotThrow(() -> logisticsHandler.handle(event));

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMsgId()).isEqualTo("logistics-msg-001");

        // 验证转换后的数据正确性
        LogisticsMessage convertedData = logisticsHandler.getLastProcessedData();
        assertNotNull(convertedData);
        assertThat(convertedData.getLogisticsId()).isEqualTo("12345");
        assertThat(convertedData.getCpCode()).isEqualTo("SF");
        assertThat(convertedData.getMailNo()).isEqualTo("SF1234567890");
        assertThat(convertedData.getStatusChanged()).isEqualTo("CONSIGN");
        assertThat(convertedData.getChangeTime()).isEqualTo("2025-07-26 13:00:00");
        assertThat(convertedData.getOrderLogsItems()).isNotNull();
        assertThat(convertedData.getOrderLogsItems()).hasSize(1);
        assertThat(convertedData.getOrderLogsItems().get(0).getOrderId()).isEqualTo(2846530957885540788L);

        log.info("物流消息LinkedHashMap转换测试完成，转换后的LogisticsMessage: {}", convertedData);
    }

    @Test
    @DisplayName("应该正确处理商品消息的LinkedHashMap到GoodsMessage转换")
    void shouldConvertLinkedHashMapToGoodsMessage() {
        // Given - 商品状态变更消息测试数据
        String goodsJson = """
            {
                "data": {
                    "productIds": "547096780502,570872343603",
                    "memberId": "b2b-28308412336ca4e7",
                    "status": "RELATION_VIEW_PRODUCT_NEW_OR_MODIFY",
                    "msgSendTime": "2025-07-26 13:30:00"
                },
                "gmtBorn": 1753504811243,
                "msgId": "goods-msg-001",
                "type": "PRODUCT_RELATION_VIEW_PRODUCT_NEW_OR_MODIFY",
                "userInfo": "b2b-2207416548807a4d12"
            }
            """;

        // 创建测试Handler
        TestGoodsMessageHandler goodsHandler = new TestGoodsMessageHandler();

        // 模拟MessageDispatcher解析后的数据结构
        Map<String, Object> rawMessageMap = JacksonUtil.convertToBean(goodsJson, Map.class);
        @SuppressWarnings("unchecked") LinkedHashMap<String, Object> dataMap = (LinkedHashMap<String, Object>) rawMessageMap.get("data");

        // 创建MessageEvent
        MessageEvent<GoodsMessage> event = new MessageEvent<>();
        event.setMsgId("goods-msg-001");
        event.setGmtBorn(1753504811243L);
        event.setType(GoodsMessageTypeEnums.PRODUCT_RELATION_VIEW_PRODUCT_NEW_OR_MODIFY);
        event.setUserInfo("b2b-2207416548807a4d12");
        setEventDataUnsafe(event, dataMap);
        event.setRawData(JacksonUtil.toJsonString(dataMap));
        event.setReceivedAt(LocalDateTime.now());

        log.info("商品消息测试数据准备完成，data类型: {}", dataMap.getClass().getSimpleName());

        // When & Then - 应该成功转换
        MessageResult result = assertDoesNotThrow(() -> goodsHandler.handle(event));

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMsgId()).isEqualTo("goods-msg-001");

        // 验证转换后的数据正确性
        GoodsMessage convertedData = goodsHandler.getLastProcessedData();
        assertNotNull(convertedData);
        assertThat(convertedData.getProductIds()).isEqualTo("547096780502,570872343603");
        assertThat(convertedData.getMemberId()).isEqualTo("b2b-28308412336ca4e7");
        assertThat(convertedData.getStatus()).isEqualTo("RELATION_VIEW_PRODUCT_NEW_OR_MODIFY");
        assertThat(convertedData.getMsgSendTime()).isEqualTo("2025-07-26 13:30:00");
        assertThat(convertedData.isNewOrModifyStatus()).isTrue();

        log.info("商品消息LinkedHashMap转换测试完成，转换后的GoodsMessage: {}", convertedData);
    }

    @Test
    @DisplayName("应该正确处理多种消息类型的类型转换通用性")
    void shouldHandleMultipleMessageTypesConversion() {
        log.info("开始测试多种消息类型的类型转换通用性");

        // 1. 测试订单消息
        testOrderMessageConversion();

        // 2. 测试物流消息
        testLogisticsMessageConversion();

        // 3. 测试商品消息
        testGoodsMessageConversion();

        log.info("多种消息类型转换测试完成，验证了AbstractTypedMessageHandler的通用性");
    }

    private void testOrderMessageConversion() {
        LinkedHashMap<String, Object> orderData = new LinkedHashMap<>();
        orderData.put("orderId", 2846530957885540788L);
        orderData.put("currentStatus", "waitbuyerpay");
        orderData.put("buyerMemberId", "b2b-665170100");

        TestOrderMessageHandler orderHandler = new TestOrderMessageHandler();
        MessageEvent<OrderMessage> orderEvent = createTestEvent("order-test", orderData);

        MessageResult result = assertDoesNotThrow(() -> orderHandler.handle(orderEvent));
        assertThat(result.isSuccess()).isTrue();
        assertThat(orderHandler.getLastProcessedData()).isNotNull();
        assertThat(orderHandler.getLastProcessedData().getOrderId()).isEqualTo(2846530957885540788L);

        log.info("订单消息转换测试通过");
    }

    private void testLogisticsMessageConversion() {
        LinkedHashMap<String, Object> logisticsData = new LinkedHashMap<>();
        logisticsData.put("logisticsId", "LOG123456");
        logisticsData.put("cpCode", "YTO");
        logisticsData.put("statusChanged", "TRANSPORT");

        TestLogisticsMessageHandler logisticsHandler = new TestLogisticsMessageHandler();
        MessageEvent<LogisticsMessage> logisticsEvent = createTestEvent("logistics-test", logisticsData);

        MessageResult result = assertDoesNotThrow(() -> logisticsHandler.handle(logisticsEvent));
        assertThat(result.isSuccess()).isTrue();
        assertThat(logisticsHandler.getLastProcessedData()).isNotNull();
        assertThat(logisticsHandler.getLastProcessedData().getLogisticsId()).isEqualTo("LOG123456");

        log.info("物流消息转换测试通过");
    }

    private void testGoodsMessageConversion() {
        LinkedHashMap<String, Object> goodsData = new LinkedHashMap<>();
        goodsData.put("productIds", "123456789");
        goodsData.put("memberId", "b2b-test123");
        goodsData.put("status", "RELATION_VIEW_PRODUCT_EXPIRE");

        TestGoodsMessageHandler goodsHandler = new TestGoodsMessageHandler();
        MessageEvent<GoodsMessage> goodsEvent = createTestEvent("goods-test", goodsData);

        MessageResult result = assertDoesNotThrow(() -> goodsHandler.handle(goodsEvent));
        assertThat(result.isSuccess()).isTrue();
        assertThat(goodsHandler.getLastProcessedData()).isNotNull();
        assertThat(goodsHandler.getLastProcessedData().getProductIds()).isEqualTo("123456789");

        log.info("商品消息转换测试通过");
    }

    @SuppressWarnings("unchecked")
    private <T> MessageEvent<T> createTestEvent(String msgId, LinkedHashMap<String, Object> dataMap) {
        MessageEvent<T> event = new MessageEvent<>();
        event.setMsgId(msgId);
        event.setGmtBorn(System.currentTimeMillis());
        event.setUserInfo("test-user");
        setEventDataUnsafe(event, dataMap);
        event.setRawData(JacksonUtil.toJsonString(dataMap));
        event.setReceivedAt(LocalDateTime.now());
        return event;
    }

    @Test
    @DisplayName("应该正确处理不同消息类型的数据结构差异")
    void shouldHandleDifferentMessageStructures() {
        log.info("开始测试不同消息类型的数据结构差异处理");

        // 测试复杂嵌套结构的物流消息
        testComplexLogisticsMessage();

        // 测试包含数组的商品库存变更消息
        testInventoryChangeMessage();

        log.info("不同消息结构差异处理测试完成");
    }

    private void testComplexLogisticsMessage() {
        LinkedHashMap<String, Object> complexLogisticsData = new LinkedHashMap<>();
        complexLogisticsData.put("logisticsId", "COMPLEX123");
        complexLogisticsData.put("oldCpCode", "SF");
        complexLogisticsData.put("newCpCode", "YTO");
        complexLogisticsData.put("oldMailNo", "SF123456");
        complexLogisticsData.put("newMailNo", "YTO789012");
        complexLogisticsData.put("eventTime", "2025-07-26 14:00:00");

        // 创建嵌套的订单物流项
        List<Map<String, Object>> orderLogsItems = new ArrayList<>();
        Map<String, Object> orderLogItem = new LinkedHashMap<>();
        orderLogItem.put("orderId", 1234567890L);
        orderLogItem.put("orderEntryId", 9876543210L);
        orderLogsItems.add(orderLogItem);
        complexLogisticsData.put("orderLogsItems", orderLogsItems);

        TestLogisticsMessageHandler handler = new TestLogisticsMessageHandler();
        MessageEvent<LogisticsMessage> event = createTestEvent("complex-logistics", complexLogisticsData);

        MessageResult result = assertDoesNotThrow(() -> handler.handle(event));
        assertThat(result.isSuccess()).isTrue();

        LogisticsMessage convertedData = handler.getLastProcessedData();
        assertThat(convertedData).isNotNull();
        assertThat(convertedData.getLogisticsId()).isEqualTo("COMPLEX123");
        assertThat(convertedData.getOldCpCode()).isEqualTo("SF");
        assertThat(convertedData.getNewCpCode()).isEqualTo("YTO");
        assertThat(convertedData.getOrderLogsItems()).hasSize(1);
        assertThat(convertedData.getOrderLogsItems().get(0).getOrderId()).isEqualTo(1234567890L);

        log.info("复杂物流消息结构转换测试通过");
    }

    private void testInventoryChangeMessage() {
        LinkedHashMap<String, Object> inventoryData = new LinkedHashMap<>();

        // 创建库存变更列表
        List<Map<String, Object>> inventoryChangeList = new ArrayList<>();
        Map<String, Object> inventoryChange = new LinkedHashMap<>();
        inventoryChange.put("offerId", 1234567890L);
        inventoryChange.put("offerOnSale", 100);
        inventoryChange.put("skuId", 9876543210L);
        inventoryChange.put("skuOnSale", 20);
        inventoryChange.put("quantity", -10);
        inventoryChange.put("bizTime", "1564984329147");
        inventoryChangeList.add(inventoryChange);

        inventoryData.put("offerInventoryChangeList", inventoryChangeList);

        TestGoodsMessageHandler handler = new TestGoodsMessageHandler();
        MessageEvent<GoodsMessage> event = createTestEvent("inventory-change", inventoryData);

        MessageResult result = assertDoesNotThrow(() -> handler.handle(event));
        assertThat(result.isSuccess()).isTrue();

        GoodsMessage convertedData = handler.getLastProcessedData();
        assertThat(convertedData).isNotNull();
        assertThat(convertedData.getOfferInventoryChangeList()).hasSize(1);
        assertThat(convertedData.getOfferInventoryChangeList().get(0).getOfferId()).isEqualTo(1234567890L);
        assertThat(convertedData.getOfferInventoryChangeList().get(0).getQuantity()).isEqualTo(-10);
        assertThat(convertedData.isInventoryChangeMessage()).isTrue();

        log.info("库存变更消息结构转换测试通过");
    }

    /**
     * 使用反射设置MessageEvent的data字段，避免编译时类型检查
     * 这样可以模拟MessageDispatcher实际运行时的情况
     */
    @SuppressWarnings("unchecked")
    private <T> void setEventDataUnsafe(MessageEvent<T> event, Object data) {
        try {
            Field dataField = MessageEvent.class.getDeclaredField("data");
            dataField.setAccessible(true);
            dataField.set(event, data);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set event data via reflection", e);
        }
    }

    /**
     * 测试用的OrderMessage处理器
     */
    private static class TestOrderMessageHandler extends AbstractTypedMessageHandler<OrderMessage> {

        private OrderMessage lastProcessedData;

        public TestOrderMessageHandler() {
            super(OrderMessageTypeEnums.ORDER_BUYER_VIEW_BUYER_MAKE);
        }

        @Override
        protected void doHandle(OrderMessage data, MessageEvent<OrderMessage> event) {
            this.lastProcessedData = data;
            log.info("处理OrderMessage: msgId={}, data={}", event.getMsgId(),
                data != null ? data.getClass().getSimpleName() : "null");
        }

        @Override
        protected Class<OrderMessage> getDataClass() {
            return OrderMessage.class;
        }

        @Override
        public List<CallbackMessageType> getSupportedTypes() {
            return List.of(OrderMessageTypeEnums.ORDER_BUYER_VIEW_BUYER_MAKE);
        }

        @Override
        public int getPriority() {
            return 100;
        }

        public OrderMessage getLastProcessedData() {
            return lastProcessedData;
        }
    }

    /**
     * 测试用的LogisticsMessage处理器
     */
    private static class TestLogisticsMessageHandler extends AbstractTypedMessageHandler<LogisticsMessage> {

        private LogisticsMessage lastProcessedData;

        public TestLogisticsMessageHandler() {
            super(LogisticsMessageTypeEnums.LOGISTICS_BUYER_VIEW_TRACE);
        }

        @Override
        protected void doHandle(LogisticsMessage data, MessageEvent<LogisticsMessage> event) {
            this.lastProcessedData = data;
            log.info("处理LogisticsMessage: msgId={}, data={}", event.getMsgId(),
                data != null ? data.getClass().getSimpleName() : "null");
        }

        @Override
        protected Class<LogisticsMessage> getDataClass() {
            return LogisticsMessage.class;
        }

        @Override
        public List<CallbackMessageType> getSupportedTypes() {
            return List.of(LogisticsMessageTypeEnums.LOGISTICS_BUYER_VIEW_TRACE);
        }

        @Override
        public int getPriority() {
            return 100;
        }

        public LogisticsMessage getLastProcessedData() {
            return lastProcessedData;
        }
    }

    /**
     * 测试用的GoodsMessage处理器
     */
    private static class TestGoodsMessageHandler extends AbstractTypedMessageHandler<GoodsMessage> {

        private GoodsMessage lastProcessedData;

        public TestGoodsMessageHandler() {
            super(GoodsMessageTypeEnums.PRODUCT_RELATION_VIEW_PRODUCT_NEW_OR_MODIFY);
        }

        @Override
        protected void doHandle(GoodsMessage data, MessageEvent<GoodsMessage> event) {
            this.lastProcessedData = data;
            log.info("处理GoodsMessage: msgId={}, data={}", event.getMsgId(),
                data != null ? data.getClass().getSimpleName() : "null");
        }

        @Override
        protected Class<GoodsMessage> getDataClass() {
            return GoodsMessage.class;
        }

        @Override
        public List<CallbackMessageType> getSupportedTypes() {
            return List.of(GoodsMessageTypeEnums.PRODUCT_RELATION_VIEW_PRODUCT_NEW_OR_MODIFY);
        }

        @Override
        public int getPriority() {
            return 100;
        }

        public GoodsMessage getLastProcessedData() {
            return lastProcessedData;
        }
    }
}
