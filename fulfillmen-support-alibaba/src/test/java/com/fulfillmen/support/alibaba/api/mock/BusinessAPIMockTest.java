/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.mock;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.api.BusinessAPI;
import com.fulfillmen.support.alibaba.api.request.business.RankQueryRequestRecord;
import com.fulfillmen.support.alibaba.api.request.business.SellTrendRequestRecord;
import com.fulfillmen.support.alibaba.api.request.business.TopKeywordRequestRecord;
import com.fulfillmen.support.alibaba.api.response.business.RankQueryResponse;
import com.fulfillmen.support.alibaba.api.response.business.SellTrendResponse;
import com.fulfillmen.support.alibaba.api.response.business.TopKeywordResponse;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import com.fulfillmen.support.alibaba.service.IBusinessService;
import java.util.Arrays;
import java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

/**
 * 业务API Mock测试
 * <p>
 * 测试范围： 1. 正常场景测试 2. 参数验证失败测试 3. 业务错误测试 4. 系统异常测试
 *
 * <AUTHOR>
 * @created 2025-01-23
 */
@Slf4j
@Tag("mock")
@Tag("business")
class BusinessAPIMockTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "[业务Mock测试]";

    @Autowired
    private IBusinessService businessService;

    @MockBean
    private BusinessAPI businessAPI;

    @Test
    @Tag("read")
    void shouldQueryRankList() {
        log.info("{} 开始测试榜单查询 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new RankQueryRequestRecord("1111", "complex", 10, "en");
        log.info("{}请求参数: rankId={}, rankType={}, limit={}, language={}", LOG_ITEM, request.rankId(), request
            .rankType(), request.limit(), request.language());

        RankQueryResponse mockResponse = createMockRankQueryResponse();
        when(businessAPI.queryRankList(any(), any())).thenReturn(Mono.just(mockResponse));

        // When
        var response = businessService.queryRankList(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();
            assertThat(result.getResult()).isNotNull();

            // 业务检查
            assertThat(result.getResult().getResult().getRankId()).isEqualTo("1111");
            assertThat(result.getResult().getResult().getRankName()).isEqualTo("Comprehensive List");
            assertThat(result.getResult().getResult().getRankProductModels()).isNotEmpty();

            log.info("{}测试结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}榜单ID: {}", LOG_ITEM, result.getResult().getResult().getRankId());
            log.info("{}榜单名称: {}", LOG_ITEM, result.getResult().getResult().getRankName());
            log.info("{}商品数量: {}", LOG_ITEM, result.getResult().getResult().getRankProductModels().size());
        }).verifyComplete();

        // 验证API调用
        verify(businessAPI).queryRankList(any(), any());

        log.info("{} 测试榜单查询完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleValidationError() {
        log.info("{} 开始测试榜单查询参数校验失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new RankQueryRequestRecord("", // 空榜单ID
            "complex", 10, "en");
        log.info("{}请求参数: rankId={}, rankType={}, limit={}, language={}", LOG_ITEM, request.rankId(), request
            .rankType(), request.limit(), request.language());

        // When & Then
        StepVerifier.create(businessService.queryRankList(request)).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException && throwable.getMessage()
                .contains("榜单ID不能为空");
        }).verify();

        // 验证API未被调用
        verify(businessAPI, never()).queryRankList(any(), any());

        log.info("{} 测试榜单查询参数校验失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleBusinessError() {
        log.info("{} 开始测试榜单查询业务错误 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new RankQueryRequestRecord("invalid_rank_id", // 无效的榜单ID
            "complex", 10, "en");
        log.info("{}请求参数: rankId={}, rankType={}, limit={}, language={}", LOG_ITEM, request.rankId(), request
            .rankType(), request.limit(), request.language());

        RankQueryResponse errorResponse = createErrorRankQueryResponse();
        when(businessAPI.queryRankList(any(), any())).thenReturn(Mono.just(errorResponse));

        // When & Then
        StepVerifier.create(businessService.queryRankList(request)).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getResult()).isNull();

            log.info("{}测试结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}错误响应: {}", LOG_ITEM, result);
        }).verifyComplete();

        // 验证API调用
        verify(businessAPI).queryRankList(any(), any());

        log.info("{} 测试榜单查询业务错误完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldGetTopKeywords() {
        log.info("{} 开始测试商品热搜词查询 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new TopKeywordRequestRecord("en", "1001", "cate");
        log.info("{}请求参数: country={}, sourceId={}, hotKeywordType={}", LOG_ITEM, request.country(), request
            .sourceId(), request.hotKeywordType());

        TopKeywordResponse mockResponse = createMockTopKeywordResponse();
        when(businessAPI.getTopKeywords(any(), any())).thenReturn(Mono.just(mockResponse));

        // When
        var response = businessService.getTopKeywords(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();
            assertThat(result.getResult()).isNotNull();

            // 业务检查
            assertThat(result.getResult().getSuccess()).isTrue();
            assertThat(result.getResult().getResult()).isNotEmpty();
            var firstKeyword = result.getResult().getResult().get(0);
            assertThat(firstKeyword.getSeKeyword()).isEqualTo("大型宠物犬");
            assertThat(firstKeyword.getSeKeywordTranslation()).isEqualTo("Large Pet Dog");

            log.info("{}测试结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}热搜词数量: {}", LOG_ITEM, result.getResult().getResult().size());
            result.getResult()
                .getResult()
                .forEach(keyword -> log.info("{}热搜词: {} -> {}", LOG_ITEM, keyword.getSeKeyword(), keyword
                    .getSeKeywordTranslation()));
        }).verifyComplete();

        // 验证API调用
        verify(businessAPI).getTopKeywords(any(), any());

        log.info("{} 测试商品热搜词查询完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleTopKeywordValidationError() {
        log.info("{} 开始测试商品热搜词参数校验失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new TopKeywordRequestRecord("", // 空语言
            "1001", "cate");
        log.info("{}请求参数: country={}, sourceId={}, hotKeywordType={}", LOG_ITEM, request.country(), request
            .sourceId(), request.hotKeywordType());

        // When & Then
        StepVerifier.create(businessService.getTopKeywords(request)).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException && throwable.getMessage().contains("语言不能为空");
        }).verify();

        // 验证API未被调用
        verify(businessAPI, never()).getTopKeywords(any(), any());

        log.info("{} 测试商品热搜词参数校验失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleTopKeywordBusinessError() {
        log.info("{} 开始测试商品热搜词业务错误 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new TopKeywordRequestRecord("en", "invalid_source_id", // 无效的类目ID
            "cate");
        log.info("{}请求参数: country={}, sourceId={}, hotKeywordType={}", LOG_ITEM, request.country(), request
            .sourceId(), request.hotKeywordType());

        TopKeywordResponse errorResponse = createErrorTopKeywordResponse();
        when(businessAPI.getTopKeywords(any(), any())).thenReturn(Mono.just(errorResponse));

        // When & Then
        StepVerifier.create(businessService.getTopKeywords(request)).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getResult().getSuccess()).isFalse();
            assertThat(result.getResult().getResult()).isEmpty();

            log.info("{}测试结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}错误响应: {}", LOG_ITEM, result);
        }).verifyComplete();

        // 验证API调用
        verify(businessAPI).getTopKeywords(any(), any());

        log.info("{} 测试商品热搜词业务错误完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldGetSellTrend() {
        log.info("{} 开始测试商品销售趋势查询 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new SellTrendRequestRecord(123456789L, "20240101", "20240107");
        log.info("{}请求参数: offerId={}, startDate={}, endDate={}", LOG_ITEM, request.offerId(), request
            .startDate(), request.endDate());

        SellTrendResponse mockResponse = createMockSellTrendResponse();
        when(businessAPI.getSellTrend(any(), any())).thenReturn(Mono.just(mockResponse));

        // When
        var response = businessService.getSellTrend(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();
            assertThat(result.getResult()).isNotNull();

            // 业务检查
            assertThat(result.getResult().getSuccess()).isTrue();
            assertThat(result.getResult().getRetCode()).isEqualTo("S0000");
            assertThat(result.getResult().getResult()).isNotEmpty();
            var firstTrend = result.getResult().getResult().get(0);
            assertThat(firstTrend.getDate()).isEqualTo("20240101");
            assertThat(firstTrend.getValue()).isEqualTo("100");

            log.info("{}测试结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}趋势数据数量: {}", LOG_ITEM, result.getResult().getResult().size());
            result.getResult()
                .getResult()
                .forEach(trend -> log.info("{}日期: {}, 销量: {}", LOG_ITEM, trend.getDate(), trend.getValue()));
        }).verifyComplete();

        // 验证API调用
        verify(businessAPI).getSellTrend(any(), any());

        log.info("{} 测试商品销售趋势查询完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleSellTrendValidationError() {
        log.info("{} 开始测试商品销售趋势参数校验失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new SellTrendRequestRecord(123456789L, "2025-01-23", // 错误的日期格式
            "20240107");
        log.info("{}请求参数: offerId={}, startDate={}, endDate={}", LOG_ITEM, request.offerId(), request
            .startDate(), request.endDate());

        // When & Then
        StepVerifier.create(businessService.getSellTrend(request)).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException && throwable.getMessage()
                .contains("查询起始时间格式错误");
        }).verify();

        // 验证API未被调用
        verify(businessAPI, never()).getSellTrend(any(), any());

        log.info("{} 测试商品销售趋势参数校验失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleSellTrendBusinessError() {
        log.info("{} 开始测试商品销售趋势业务错误 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new SellTrendRequestRecord(999999999L, // 不存在的商品ID
            "20240101", "20240107");
        log.info("{}请求参数: offerId={}, startDate={}, endDate={}", LOG_ITEM, request.offerId(), request
            .startDate(), request.endDate());

        SellTrendResponse errorResponse = createErrorSellTrendResponse();
        when(businessAPI.getSellTrend(any(), any())).thenReturn(Mono.just(errorResponse));

        // When & Then
        StepVerifier.create(businessService.getSellTrend(request)).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getResult()).isNotNull();
            assertThat(result.getResult().getSuccess()).isFalse();
            assertThat(result.getResult().getRetCode()).isEqualTo("E0001");
            assertThat(result.getResult().getResult()).isEmpty();

            log.info("{}测试结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}错误响应: {}", LOG_ITEM, result);
        }).verifyComplete();

        // 验证API调用
        verify(businessAPI).getSellTrend(any(), any());

        log.info("{} 测试商品销售趋势业务错误完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    private RankQueryResponse createMockRankQueryResponse() {
        RankQueryResponse response = new RankQueryResponse();
        RankQueryResponse.RankModel result = new RankQueryResponse.RankModel();
        result.setRankId("1111");
        result.setRankName("Comprehensive List");
        result.setRankType("complex");

        RankQueryResponse.RankProductModel product = new RankQueryResponse.RankProductModel();
        product.setItemId(699490252651L);
        product.setTitle("2023厚底女式拖鞋");
        product.setTranslateTitle("2023 thick-soled ladies sandals");
        product.setImgUrl("https://example.com/image.jpg");
        product.setSort(1);
        product.setServiceList(Arrays.asList("sendGoods48H"));
        product.setBuyerNum(1334);
        product.setSoldOut(433454);
        product.setGoodsScore("5");

        result.setRankProductModels(Arrays.asList(product));
        RankQueryResponse.Result resultWrapper = new RankQueryResponse.Result();
        resultWrapper.setSuccess(true);
        resultWrapper.setCode("200");
        resultWrapper.setMessage("success");
        resultWrapper.setResult(result);
        response.setResult(resultWrapper);
        return response;
    }

    private RankQueryResponse createErrorRankQueryResponse() {
        RankQueryResponse response = new RankQueryResponse();
        response.setResult(null);
        return response;
    }

    private TopKeywordResponse createMockTopKeywordResponse() {
        TopKeywordResponse response = new TopKeywordResponse();
        TopKeywordResponse.Result resultWrapper = new TopKeywordResponse.Result();
        resultWrapper.setSuccess(true);
        resultWrapper.setCode("200");
        resultWrapper.setMessage("success");

        TopKeywordResponse.TopSeKeywordModel keyword = new TopKeywordResponse.TopSeKeywordModel();
        keyword.setSeKeyword("大型宠物犬");
        keyword.setSeKeywordTranslation("Large Pet Dog");

        resultWrapper.setResult(Arrays.asList(keyword));
        response.setResult(resultWrapper);
        return response;
    }

    private TopKeywordResponse createErrorTopKeywordResponse() {
        TopKeywordResponse response = new TopKeywordResponse();
        TopKeywordResponse.Result resultWrapper = new TopKeywordResponse.Result();
        resultWrapper.setSuccess(false);
        resultWrapper.setCode("400");
        resultWrapper.setMessage("Invalid source id");
        resultWrapper.setResult(Collections.emptyList());
        response.setResult(resultWrapper);
        return response;
    }

    private SellTrendResponse createMockSellTrendResponse() {
        SellTrendResponse response = new SellTrendResponse();
        SellTrendResponse.Result resultWrapper = new SellTrendResponse.Result();
        resultWrapper.setSuccess(true);
        resultWrapper.setRetCode("S0000");
        resultWrapper.setRetMsg("成功");

        SellTrendResponse.OfferSellTrendDataModel trend = new SellTrendResponse.OfferSellTrendDataModel();
        trend.setDate("20240101");
        trend.setValue("100");

        resultWrapper.setResult(Arrays.asList(trend));
        response.setResult(resultWrapper);
        return response;
    }

    private SellTrendResponse createErrorSellTrendResponse() {
        SellTrendResponse response = new SellTrendResponse();
        SellTrendResponse.Result resultWrapper = new SellTrendResponse.Result();
        resultWrapper.setSuccess(false);
        resultWrapper.setRetCode("E0001");
        resultWrapper.setRetMsg("商品不存在");
        resultWrapper.setResult(Collections.emptyList());
        response.setResult(resultWrapper);
        return response;
    }
}