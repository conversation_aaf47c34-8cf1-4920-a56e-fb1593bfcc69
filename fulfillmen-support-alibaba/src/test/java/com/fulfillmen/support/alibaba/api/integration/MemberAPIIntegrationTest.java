/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.integration;

import static org.assertj.core.api.Assertions.assertThat;

import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.api.request.member.MemberRegisterRequestRecord;
import com.fulfillmen.support.alibaba.api.request.member.SubAccountAuthAddRequestRecord;
import com.fulfillmen.support.alibaba.api.request.member.SubAccountAuthCancelRequestRecord;
import com.fulfillmen.support.alibaba.api.request.member.SubAccountAuthListRequestRecord;
import com.fulfillmen.support.alibaba.service.IMemberService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.test.StepVerifier;

/**
 * 1688会员API 集成测试 这个测试类会真实调用1688 API，请确保有正确的配置和测试数据
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Slf4j
@Tag("integration")
class MemberAPIIntegrationTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    @Autowired
    private IMemberService memberService;

    @Test
    void shouldRegisterMember() {
        // Given
        String testEmail = "test_" + System.currentTimeMillis() + "@example.com";
        String outLoginId = "********";
        String outMemberId = "test" + System.currentTimeMillis();

        var request = MemberRegisterRequestRecord
            .of("CN", "1688", outLoginId, outMemberId, testEmail, "13800138000", "86", "127.0.0.1");

        log.info("{}开始会员注册测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}登录ID: {}", LOG_ITEM, outLoginId);

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(memberService.register(request)).expectNextMatches(response -> {
            assertThat(response).isNotNull();
            assertThat(response.getResult()).isNotNull();
            assertThat(response.getResult().getSuccess()).isTrue();
            assertThat(response.getResult().getResult()).isTrue();
            return true;
        }).verifyComplete();

        // 记录性能指标
        recordMetrics("RegisterMember", startTime, true);
        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("RegisterMember");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldAddSubAccountAuth() {
        // Given
        var request = SubAccountAuthAddRequestRecord.of(List.of("SUB_001", "SUB_002"));

        log.info("{}开始添加子账号授权测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}子账号列表: {}", LOG_ITEM, String.join(",", request.subUserIdentityList()));

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(memberService.addSubAccountAuth(request)).expectNextMatches(response -> {
            assertThat(response).isNotNull();
            assertThat(response.getResult()).isNotNull();
            assertThat(response.getResult().getSuccess()).isTrue();
            assertThat(response.getResult().getReturnValue()).isNotEmpty();
            return true;
        }).verifyComplete();

        // 记录性能指标
        recordMetrics("AddSubAccountAuth", startTime, true);
        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("AddSubAccountAuth");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldCancelSubAccountAuth() {
        // Given
        var request = SubAccountAuthCancelRequestRecord.of(List.of("SUB_001", "SUB_002"));

        log.info("{}开始取消子账号授权测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}子账号列表: {}", LOG_ITEM, String.join(",", request.subUserIdentityList()));

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(memberService.cancelSubAccountAuth(request)).expectNextMatches(response -> {
            assertThat(response).isNotNull();
            assertThat(response.getResult()).isNotNull();
            assertThat(response.getResult().getSuccess()).isTrue();
            assertThat(response.getResult().getReturnValue()).isTrue();
            return true;
        }).verifyComplete();

        // 记录性能指标
        recordMetrics("CancelSubAccountAuth", startTime, true);
        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("CancelSubAccountAuth");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldListSubAccountAuth() {
        // Given
        var request = SubAccountAuthListRequestRecord.of(List.of("SUB_001", "SUB_002"));

        log.info("{}开始查询子账号授权测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}子账号列表: {}", LOG_ITEM, String.join(",", request.subUserIdentityList()));

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(memberService.listSubAccountAuth(request)).expectNextMatches(response -> {
            assertThat(response).isNotNull();
            assertThat(response.getResult()).isNotNull();
            assertThat(response.getResult().getSuccess()).isTrue();
            assertThat(response.getResult().getReturnValue()).isNotNull();
            return true;
        }).verifyComplete();

        // 记录性能指标
        recordMetrics("ListSubAccountAuth", startTime, true);
        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("ListSubAccountAuth");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }
}