/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.integration;

import static org.assertj.core.api.Assertions.assertThat;

import cn.hutool.json.JSONUtil;
import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.api.request.ai.ImageCutRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ImageElementsRecognitionRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ImageEnlargeRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ImageMattingRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ImageRemoveRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ImageTranslateRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ProductDescGenerateRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ProductTextTranslateRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ProductTitleGenerateRequestRecord;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceException;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import com.fulfillmen.support.alibaba.service.IAiCapabilityService;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.test.StepVerifier;

/**
 * AI能力API集成测试
 * <p>
 * 测试范围： 1. 正常场景测试 2. 性能指标记录 3. 实际API调用验证 4. 错误场景测试
 *
 * <AUTHOR>
 * @created 2025-01-16
 */
@Slf4j
@Tag("integration")
@Tag("ai")
class AiCapabilityAPIIntegrationTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "[AI能力集成测试]";

    @Autowired
    private IAiCapabilityService aiCapabilityService;

    @Test
    @Tag("read")
    void shouldTranslateProductText() {
        log.info("{} 开始测试商品文本翻译 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        List<String> sourceText = Arrays.asList("这是一个高品质的商品，具有优秀的性能和耐用性。", "开发一个高性能的系统");
        var request = new ProductTextTranslateRequestRecord(sourceText, "zh", "en", null);
        log.info("{}请求参数: sourceTextList={}, sourceLanguage={}, targetLanguage={}", LOG_ITEM, request
            .sourceTextList(), request.sourceLanguage(), request.targetLanguage());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = aiCapabilityService.translateProductText(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isTrue();

            // 业务检查
            assertThat(result.getResult()).isNotNull();
            assertThat(result.getResult()).isNotEmpty();

            // 翻译后的文本
            List<String> translatedTextList = JSONUtil.toList(result.getResult(), String.class);
            for (int i = 0; i < translatedTextList.size(); i++) {
                log.info("{}翻译前后的文本: {} -> {}", LOG_ITEM, sourceText.get(i), translatedTextList.get(i));
            }

            // 记录日志
            log.info("{}响应结果: {}", LOG_ITEM, result);

            // 记录性能指标
            recordMetrics("TranslateProductText", startTime, result.getSuccess());
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("TranslateProductText");
        log.info("{} 测试商品文本翻译完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldGenerateProductTitle() {
        log.info("{} 开始测试商品标题生成 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ProductTitleGenerateRequestRecord("高品质蓝牙耳机，采用先进蓝牙5.0技术，具有主动降噪功能，续航时间长达24小时", "en", "电子产品/耳机/蓝牙耳机", "无线,降噪,长续航", "采用先进蓝牙5.0技术，具有主动降噪功能，续航时间长达24小时", null, null);
        log.info("{}请求参数: productName={}, targetLanguage={}, productCategory={}, productKeyword={}", LOG_ITEM, request
            .productName(), request.targetLanguage(), request.productCategory(), request.productKeyword());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = aiCapabilityService.generateProductTitle(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();
            assertThat(result.getResult().getSuccess()).isTrue();

            // 业务检查
            assertThat(result.getResult().getResult()).isNotEmpty();
            assertThat(result.getResult().getCode()).isEqualTo("200");

            // 记录结果
            log.info("{}测试结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, result.getResult().getSuccess());
            log.info("{}生成标题: {}", LOG_ITEM, result.getResult().getResult());

            // 记录性能指标
            recordMetrics("GenerateProductTitle", startTime, result.getResult().getSuccess());
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GenerateProductTitle");
        log.info("{} 测试商品标题生成完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldGenerateProductDesc() {
        log.info("{} 开始测试商品详描生成 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ProductDescGenerateRequestRecord("高品质蓝牙耳机", "en", "电子产品/耳机/蓝牙耳机", "无线,降噪,长续航", "采用先进蓝牙5.0技术，具有主动降噪功能，续航时间长达24小时", null, null);
        log.info("{}请求参数: productName={}, targetLanguage={}, productCategory={}, productKeyword={}", LOG_ITEM, request
            .productName(), request.targetLanguage(), request.productCategory(), request.productKeyword());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = aiCapabilityService.generateProductDesc(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();
            assertThat(result.getResult()).isNotNull();
            assertThat(result.getResult().getSuccess()).isTrue();
            assertThat(result.getResult().getRetCode()).isEqualTo("S0000");

            // 业务检查
            assertThat(result.getResult().getResult()).isNotEmpty();

            // 记录日志
            log.info("{}响应结果: {}", LOG_ITEM, result);
            log.info("{}生成的详描: {}", LOG_ITEM, result.getResult().getResult());

            // 记录性能指标
            recordMetrics("GenerateProductDesc", startTime, result.getResult().getSuccess());
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GenerateProductDesc");
        log.info("{} 测试商品详描生成完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleInvalidLanguage() {
        log.info("{} 开始测试无效的语言代码 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ProductTextTranslateRequestRecord(Collections.singletonList("测试文本"), "zh", "invalid", // 无效的语言代码
            null);
        log.info("{}请求参数: sourceTextList={}, sourceLanguage={}, targetLanguage={}", LOG_ITEM, request
            .sourceTextList(), request.sourceLanguage(), request.targetLanguage());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(aiCapabilityService.translateProductText(request)).expectNextMatches(result -> {
            // 记录日志
            log.info("{}响应结果: {}", LOG_ITEM, result);

            // 记录性能指标
            recordMetrics("TranslateProductTextError", startTime, false);

            return !result.getSuccess() && result.getCode().equals("Z0007");
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("TranslateProductTextError");
        log.info("{} 测试无效的语言代码完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldTranslateImage() {
        log.info("{} 开始测试图片翻译 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ImageTranslateRequestRecord("https://cbu01.alicdn.com/img/ibank/13593437125_1253585053.jpg_.webp", "zh", "en", "true", "false");
        log.info("{}请求参数: imageUrl={}, originalLanguage={}, targetLanguage={}", LOG_ITEM, request.imageUrl(), request
            .originalLanguage(), request.targetLanguage());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = aiCapabilityService.translateImage(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isTrue();

            // 业务检查
            assertThat(result.getResult()).isNotNull();
            assertThat(result.getResult().getTranslatedImageUrl()).isNotEmpty();

            // 记录日志
            log.info("{}响应结果: {}", LOG_ITEM, result);
            log.info("{}翻译后图片URL: {}", LOG_ITEM, result.getResult().getTranslatedImageUrl());

            // 记录性能指标
            recordMetrics("TranslateImage", startTime, result.getSuccess());
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("TranslateImage");
        log.info("{} 测试图片翻译完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleInvalidImageUrl() {
        log.info("{} 开始测试无效的图片URL {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ImageTranslateRequestRecord("invalid_url", // 无效的图片URL
            "zh", "en", "true", "false");
        log.info("{}请求参数: imageUrl={}, originalLanguage={}, targetLanguage={}", LOG_ITEM, request.imageUrl(), request
            .originalLanguage(), request.targetLanguage());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(aiCapabilityService.translateImage(request)).expectNextMatches(result -> {
            // 记录日志
            log.info("{}响应结果: {}", LOG_ITEM, result);

            // 记录性能指标
            recordMetrics("TranslateImageError", startTime, false);

            return !result.getSuccess();
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("TranslateImageError");
        log.info("{} 测试无效的图片URL完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldCutImage() {
        log.info("{} 开始测试图片裁剪 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ImageCutRequestRecord("https://cbu01.alicdn.com/img/ibank/13593437125_1253585053.jpg_.webp", "800", "600");
        log.info("{}请求参数: imageUrl={}, width={}, height={}", LOG_ITEM, request.imageUrl(), request.width(), request
            .height());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = aiCapabilityService.cutImage(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isTrue();
            assertThat(result.getCode()).isEqualTo("S0000");

            // 业务检查
            assertThat(result.getResult()).isNotEmpty();

            // 记录日志
            log.info("{}响应结果: {}", LOG_ITEM, result);
            log.info("{}裁剪后图片URL: {}", LOG_ITEM, result.getResult());

            // 记录性能指标
            recordMetrics("CutImage", startTime, result.getSuccess());
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("CutImage");
        log.info("{} 测试图片裁剪完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleInvalidImageDimensions() {
        log.info("{} 开始测试无效的图片尺寸 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ImageCutRequestRecord("https://cbu01.alicdn.com/img/ibank/13593437125_1253585053.jpg_.webp", "50", // 无效的宽度，小于最小值100
            "600");
        log.info("{}请求参数: imageUrl={}, width={}, height={}", LOG_ITEM, request.imageUrl(), request.width(), request
            .height());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(aiCapabilityService.cutImage(request)).expectErrorMatches(throwable -> {
            // 记录日志
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());

            // 记录性能指标
            recordMetrics("CutImageError", startTime, false);

            return throwable instanceof AlibabaServiceException && throwable.getMessage()
                .contains("width必须在100-5000之间");
        }).verify();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("CutImageError");
        log.info("{} 测试无效的图片尺寸完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldEnlargeImage() {
        log.info("{} 开始测试图片高清放大 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ImageEnlargeRequestRecord("https://cbu01.alicdn.com/img/ibank/13593437125_1253585053.jpg_.webp", 2);
        log.info("{}请求参数: imageUrl={}, upscaleFactor={}", LOG_ITEM, request.imageUrl(), request.upscaleFactor());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = aiCapabilityService.enlargeImage(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isTrue();
            assertThat(result.getCode()).isEqualTo("S0000");

            // 业务检查
            assertThat(result.getResult()).isNotNull();
            assertThat(result.getResult().getEnlargedImageUrl()).isNotEmpty();
            assertThat(result.getResult().getImageWidth()).isNotNull();
            assertThat(result.getResult().getImageHeight()).isNotNull();

            // 记录日志
            log.info("{}响应结果: {}", LOG_ITEM, result);
            log.info("{}放大后图片URL: {}", LOG_ITEM, result.getResult().getEnlargedImageUrl());
            log.info("{}放大后尺寸: {}x{}", LOG_ITEM, result.getResult().getImageWidth(), result.getResult()
                .getImageHeight());

            // 记录性能指标
            recordMetrics("EnlargeImage", startTime, result.getSuccess());
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("EnlargeImage");
        log.info("{} 测试图片高清放大完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleInvalidUpscaleFactor() {
        log.info("{} 开始测试无效的放大倍数 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ImageEnlargeRequestRecord("https://cbu01.alicdn.com/img/ibank/13593437125_1253585053.jpg_.webp", 5); // 无效的放大倍数
        log.info("{}请求参数: imageUrl={}, upscaleFactor={}", LOG_ITEM, request.imageUrl(), request.upscaleFactor());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(aiCapabilityService.enlargeImage(request)).expectErrorMatches(throwable -> {
            // 记录日志
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());

            // 记录性能指标
            recordMetrics("EnlargeImageError", startTime, false);

            return throwable instanceof AlibabaServiceException && throwable.getMessage()
                .contains("upscaleFactor必须在2-4之间");
        }).verify();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("EnlargeImageError");
        log.info("{} 测试无效的放大倍数完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldMattingImage() {
        log.info("{} 开始测试图片智能抠图 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ImageMattingRequestRecord("https://cbu01.alicdn.com/img/ibank/2020/487/906/13509609784_1253585053.jpg", null, 800, 600);
        log.info("{}请求参数: imageUrl={}, backgroundBGR={}, height={}, width={}", LOG_ITEM, request.imageUrl(), request
            .backgroundBGR(), request.height(), request.width());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = aiCapabilityService.mattingImage(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isTrue();
            assertThat(result.getCode()).isEqualTo("S0000");

            // 业务检查
            assertThat(result.getResult()).isNotEmpty();

            // 记录日志
            log.info("{}响应结果: {}", LOG_ITEM, result);
            log.info("{}抠图后图片URL: {}", LOG_ITEM, result.getResult());

            // 记录性能指标
            recordMetrics("MattingImage", startTime, result.getSuccess());
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("MattingImage");
        log.info("{} 测试图片智能抠图完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleInvalidImageDimensionsForMatting() {
        log.info("{} 开始测试无效的图片尺寸 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ImageMattingRequestRecord("https://cbu01.alicdn.com/img/ibank/2020/487/906/13509609784_1253585053.jpg", "[255,255,255]", 50, // 无效的高度，小于最小值100
            600);
        log.info("{}请求参数: imageUrl={}, backgroundBGR={}, height={}, width={}", LOG_ITEM, request.imageUrl(), request
            .backgroundBGR(), request.height(), request.width());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(aiCapabilityService.mattingImage(request)).expectErrorMatches(throwable -> {
            // 记录日志
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());

            // 记录性能指标
            recordMetrics("MattingImageError", startTime, false);

            return throwable instanceof AlibabaServiceValidationException && throwable.getMessage()
                .contains("height必须在100-5000之间");
        }).verify();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("MattingImageError");
        log.info("{} 测试无效的图片尺寸完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldRemoveImage() {
        log.info("{} 开始测试图片智能消除 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ImageRemoveRequestRecord("https://cbu01.alicdn.com/img/ibank/2020/487/906/13509609784_1253585053.jpg", true, true, true, true, true, true, true, true, true, true);
        log.info("{}请求参数: imageUrl={}, noobjRemoveCharacter={}, noobjRemoveLogo={}, noobjRemoveNpx={}", LOG_ITEM, request
            .imageUrl(), request.noobjRemoveCharacter(), request.noobjRemoveLogo(), request.noobjRemoveNpx());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = aiCapabilityService.removeImage(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();
            assertThat(result.getResult().getSuccess()).isTrue();
            assertThat(result.getResult().getCode()).isEqualTo("S0000");

            // 业务检查
            assertThat(result.getResult().getResult()).isNotEmpty();

            // 记录日志
            log.info("{}响应结果: {}", LOG_ITEM, result);
            log.info("{}消除后图片URL: {}", LOG_ITEM, result.getResult().getResult());

            // 记录性能指标
            recordMetrics("RemoveImage", startTime, result.getResult().getSuccess());
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("RemoveImage");
        log.info("{} 测试图片智能消除完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleInvalidImageForRemove() {
        log.info("{} 开始测试无效的图片URL {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ImageRemoveRequestRecord("invalid_url", // 无效的图片URL
            true, true, true, true, true, true, true, true, true, true);
        log.info("{}请求参数: imageUrl={}", LOG_ITEM, request.imageUrl());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(aiCapabilityService.removeImage(request)).expectErrorMatches(throwable -> {
            // 记录日志
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());

            // 记录性能指标
            recordMetrics("RemoveImageError", startTime, false);

            return throwable instanceof AlibabaServiceException && throwable.getMessage().contains("无效的图片URL");
        }).verify();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("RemoveImageError");
        log.info("{} 测试无效的图片URL完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldRecognizeImageElements() {
        log.info("{} 开始测试图像元素识别 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String imageUrl = "https://cbu01.alicdn.com/img/ibank/2020/487/906/13509609784_1253585053.jpg";
        var request = new ImageElementsRecognitionRequestRecord(imageUrl, Arrays.asList("watermark", "logo"), Arrays
            .asList("text", "qrcode"), 1,  // returnCharacter
            1,  // returnBorderPixel
            1,  // returnProductProp
            1,  // returnProductNum
            1); // returnCharacterProp
        log.info("{}请求参数: imageUrl={}, objectDetectElements={}, nonObjectDetectElements={}", LOG_ITEM, request
            .imageUrl(), request.objectDetectElements(), request.nonObjectDetectElements());

        // 性能指标
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(aiCapabilityService.recognizeImageElements(request)).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isTrue();

            // 业务检查
            assertThat(result.getResult()).isNotNull();
            //     assertThat(result.getResult().getRecText()).isNotEmpty();
            //     assertThat(result.getResult().getPdProp()).isGreaterThan(0);

            // 记录性能指标
            recordMetrics("图像元素识别", startTime, result.getSuccess());

            log.info("{}测试结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, result.getSuccess());
            log.info("{}识别结果: 文字={}, 水印={}, Logo={}", LOG_ITEM, result.getResult().getResult().getRecText(), result
                .getResult()
                .getResult()
                .getNoobWatermark(), result.getResult().getResult().getNoobLogo());
        }).verifyComplete();

        log.info("{} 图像元素识别测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    @Tag("error")
    void shouldFailRecognizeImageElements_WhenInvalidRequest() {
        log.info("{} 开始测试图像元素识别参数验证 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ImageElementsRecognitionRequestRecord(null,  // 缺少必填参数
            null, null, 0,  // returnCharacter
            0,  // returnBorderPixel
            0,  // returnProductProp
            0,  // returnProductNum
            0); // returnCharacterProp
        log.info("{}请求参数: imageUrl=null", LOG_ITEM);

        // When & Then
        StepVerifier.create(aiCapabilityService.recognizeImageElements(request))
            .expectError(AlibabaServiceValidationException.class)
            .verify();

        log.info("{} 图像元素识别参数验证测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    @Tag("error")
    void shouldHandleRecognizeImageElements_WhenApiError() {
        log.info("{} 开始测试图像元素识别API错误 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String imageUrl = "https://invalid.example.com/test.jpg";  // 使用无效的图片URL
        var request = new ImageElementsRecognitionRequestRecord(imageUrl, null, null, 0,  // returnCharacter
            0,  // returnBorderPixel
            0,  // returnProductProp
            0,  // returnProductNum
            0); // returnCharacterProp
        log.info("{}请求参数: imageUrl={}", LOG_ITEM, request.imageUrl());

        // 性能指标
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(aiCapabilityService.recognizeImageElements(request)).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isFalse();

            // 记录性能指标
            recordMetrics("图像元素识别-错误场景", startTime, false);

            log.info("{}测试结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}错误信息: code={}, message={}", LOG_ITEM, result.getResult().getCode(), result.getResult()
                .getMessage());
        }).verifyComplete();

        log.info("{} 图像元素识别API错误测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }
}