/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.mock;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import cn.hutool.json.JSONUtil;
import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.api.RefundAPI;
import com.fulfillmen.support.alibaba.api.request.refund.RefundBuyerListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundBuyerOrderViewRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundBuyerSubmitRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundBuyerViewRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundCancelRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundCreateRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundCreateRequestRecord.DisputeRequest;
import com.fulfillmen.support.alibaba.api.request.refund.RefundCreateRequestRecord.GoodsStatus;
import com.fulfillmen.support.alibaba.api.request.refund.RefundOpLogListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundReasonListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundUploadEvidenceRequestRecord;
import com.fulfillmen.support.alibaba.api.response.refund.RefundBuyerListResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundBuyerOrderViewResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundBuyerSubmitResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundBuyerViewResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundCancelResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundCreateResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundOpLogListResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundReasonListResponse;
import com.fulfillmen.support.alibaba.api.response.refund.RefundUploadEvidenceResponse;
import com.fulfillmen.support.alibaba.service.IRefundService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import reactor.test.StepVerifier;

/**
 * 1688退款API Mock测试
 *
 * <AUTHOR>
 * @created 2025-01-15
 */
@Slf4j
class RefundAPIMockTest extends BaseAPITest {

    @Autowired
    private IRefundService refundService;

    @MockBean
    private RefundAPI refundAPI;

    @Test
    void shouldCancelRefund() {
        // Given
        var request = new RefundCancelRequestRecord("RF123456789");

        // Mock 成功响应
        RefundCancelResponse successResponse = JSONUtil.toBean("""
            {
                "success": true,
                "result": {
                    "success": true,
                    "message": "取消退款成功"
                }
            }""", RefundCancelResponse.class);

        when(refundAPI.cancelRefund(any(), any())).thenReturn(reactor.core.publisher.Mono.just(successResponse));

        // When
        var response = refundService.cancelRefund(request);

        // Then
        StepVerifier.create(response).assertNext(cancelResponse -> {
            assertThat(cancelResponse).isNotNull();
            assertThat(cancelResponse.getSuccess()).isTrue();
            assertThat(cancelResponse.getResult()).isNotNull();
            assertThat(cancelResponse.getResult().getSuccess()).isTrue();
        }).verifyComplete();
    }

    @Test
    void shouldQueryRefundBuyerList() {
        // Given
        var request = RefundBuyerListRequestRecord.of(1, 20);

        // Mock 成功响应
        var successResponse = JSONUtil.toBean("""
            {
                "success": true,
                "result": {
                    "totalCount": 10,
                    "currentPageNum": 1,
                    "opOrderRefundModels": [
                        {
                            "refundId": "TQ123456789",
                            "orderId": 123456789,
                            "status": "waitselleragree",
                            "refundPayment": 10000,
                            "applyPayment": 10000,
                            "disputeType": 1,
                            "buyerMemberId": "b2b-123456789",
                            "sellerMemberId": "b2b-987654321",
                            "buyerLoginId": "buyer123",
                            "sellerLoginId": "seller123",
                            "productName": "测试商品"
                        }
                    ]
                }
            }""", RefundBuyerListResponse.class);

        when(refundAPI.queryRefundBuyerList(any(), any())).thenReturn(reactor.core.publisher.Mono
            .just(successResponse));

        // When
        var response = refundService.queryRefundBuyerList(request);

        // Then
        StepVerifier.create(response).assertNext(listResponse -> {
            assertThat(listResponse).isNotNull();
            assertThat(listResponse.getSuccess()).isTrue();
            assertThat(listResponse.getResult()).isNotNull();
            assertThat(listResponse.getResult().getOpOrderRefundModels()).isNotEmpty();
            assertThat(listResponse.getResult().getTotalCount()).isEqualTo(10);
            assertThat(listResponse.getResult().getCurrentPageNum()).isEqualTo(1);

            var detail = listResponse.getResult().getOpOrderRefundModels().get(0);
            assertThat(detail.getRefundId()).isEqualTo("TQ123456789");
            assertThat(detail.getOrderId()).isEqualTo(123456789L);
            assertThat(detail.getStatus()).isEqualTo("waitselleragree");
            assertThat(detail.getRefundPayment()).isEqualTo(10000);
            assertThat(detail.getApplyPayment()).isEqualTo(10000);
            assertThat(detail.getDisputeType()).isEqualTo(1);
            assertThat(detail.getBuyerMemberId()).isEqualTo("b2b-123456789");
            assertThat(detail.getSellerMemberId()).isEqualTo("b2b-987654321");
            assertThat(detail.getBuyerLoginId()).isEqualTo("buyer123");
            assertThat(detail.getSellerLoginId()).isEqualTo("seller123");
            assertThat(detail.getProductName()).isEqualTo("测试商品");
        }).verifyComplete();
    }

    @Test
    void shouldCreateRefund() {
        // Given
        var request = RefundCreateRequestRecord
            .of(123456789L, DisputeRequest.REFUND, 10000L, "买家取消订单", 20021L, GoodsStatus.NOT_RECEIVED);

        // Mock 成功响应
        var successResponse = JSONUtil.toBean("""
            {
                "success": true,
                "result": {
                    "code": "200",
                    "message": "success",
                    "result": {
                        "refundId": "TQ104316274636951198"
                    }
                }
            }""", RefundCreateResponse.class);

        when(refundAPI.createRefund(any(), any())).thenReturn(reactor.core.publisher.Mono.just(successResponse));

        // When
        var response = refundService.createRefund(request);

        // Then
        StepVerifier.create(response).assertNext(createResponse -> {
            assertThat(createResponse).isNotNull();
            assertThat(createResponse.getSuccess()).isTrue();
            assertThat(createResponse.getResult()).isNotNull();
            assertThat(createResponse.getResult().getCode()).isEqualTo("200");
            assertThat(createResponse.getResult().getMessage()).isEqualTo("success");
            assertThat(createResponse.getResult().getResult().getRefundId()).isEqualTo("TQ104316274636951198");
        }).verifyComplete();
    }

    @Test
    void shouldQueryRefundBuyerView() {
        // Given
        var request = RefundBuyerViewRequestRecord.of("RF123456789");

        // Mock 成功响应
        var successResponse = JSONUtil.toBean("""
            {
                "success": true,
                "result": {
                    "opOrderRefundModelDetail": {
                        "refundId": "RF123456789",
                        "orderId": 123456789,
                        "status": "waitselleragree",
                        "refundPayment": 10000,
                        "disputeType": 1,
                        "buyerMemberId": "b2b-123456789",
                        "sellerMemberId": "b2b-987654321",
                        "buyerLoginId": "buyer123",
                        "sellerLoginId": "seller123",
                        "productName": "测试商品",
                        "applyPayment": 10000,
                        "canRefundPayment": 10000,
                        "refundCarriage": 0,
                        "goodsStatus": 1
                    }
                }
            }""", RefundBuyerViewResponse.class);

        when(refundAPI.queryRefundBuyerView(any(), any())).thenReturn(reactor.core.publisher.Mono
            .just(successResponse));

        // When
        var response = refundService.queryRefundBuyerView(request);

        // Then
        StepVerifier.create(response).assertNext(viewResponse -> {
            assertThat(viewResponse).isNotNull();
            assertThat(viewResponse.getSuccess()).isTrue();
            assertThat(viewResponse.getResult()).isNotNull();
            assertThat(viewResponse.getResult().getOpOrderRefundModelDetail()).isNotNull();

            var detail = viewResponse.getResult().getOpOrderRefundModelDetail();
            assertThat(detail.getRefundId()).isEqualTo("RF123456789");
            assertThat(detail.getOrderId()).isEqualTo(123456789L);
            assertThat(detail.getStatus()).isEqualTo("waitselleragree");
            assertThat(detail.getRefundPayment()).isEqualTo(10000L);
            assertThat(detail.getDisputeType()).isEqualTo(1);
            assertThat(detail.getBuyerMemberId()).isEqualTo("b2b-123456789");
            assertThat(detail.getSellerMemberId()).isEqualTo("b2b-987654321");
            assertThat(detail.getBuyerLoginId()).isEqualTo("buyer123");
            assertThat(detail.getSellerLoginId()).isEqualTo("seller123");
            assertThat(detail.getProductName()).isEqualTo("测试商品");
            assertThat(detail.getApplyPayment()).isEqualTo(10000L);
            assertThat(detail.getCanRefundPayment()).isEqualTo(10000L);
            assertThat(detail.getRefundCarriage()).isEqualTo(0L);
            assertThat(detail.getGoodsStatus()).isEqualTo(1);
        }).verifyComplete();
    }

    @Test
    void shouldSubmitRefundBuyerInfo() {
        // Given
        var request = RefundBuyerSubmitRequestRecord.of("TQ123456789", "ZTO", "SF1234567890");

        // Mock 成功响应
        RefundBuyerSubmitResponse successResponse = JSONUtil.toBean("""
            {
                "success": true,
                "result": {
                    "success": true,
                    "refundId": "TQ123456789",
                    "orderId": "123456789",
                    "refundStatus": "waitsellerconfirm",
                    "disputeType": "1",
                    "refundPhase": "returnGoods"
                }
            }""", RefundBuyerSubmitResponse.class);

        // When
        when(refundAPI.submitRefundBuyerInfo(any(), any())).thenReturn(reactor.core.publisher.Mono
            .just(successResponse));

        // Then
        StepVerifier.create(refundService.submitRefundBuyerInfo(request)).assertNext(submitResponse -> {
            assertThat(submitResponse).isNotNull();
            assertThat(submitResponse.getSuccess()).isTrue();
            assertThat(submitResponse.getResult()).isNotNull();
            assertThat(submitResponse.getResult().getSuccess()).isTrue();
            assertThat(submitResponse.getResult().getErrorCode()).isNull();
            assertThat(submitResponse.getResult().getErrorMessage()).isNull();
        }).verifyComplete();
    }

    @Test
    void shouldUploadRefundEvidence() {
        // Given
        var request = RefundUploadEvidenceRequestRecord.of("TQ123456789", "https://example.com/evidence.jpg");

        // Mock 成功响应
        var successResponse = JSONUtil.toBean("""
            {
                "success": true,
                "result": {
                    "success": true,
                    "errorCode": null,
                    "errorMessage": null,
                    "refundId": "TQ123456789",
                    "orderId": "123456789",
                    "refundStatus": "waitsellerconfirm",
                    "disputeType": "1",
                    "refundPhase": "returnGoods"
                }
            }""", RefundUploadEvidenceResponse.class);

        when(refundAPI.uploadRefundEvidence(any(), any())).thenReturn(reactor.core.publisher.Mono
            .just(successResponse));

        // When
        var response = refundService.uploadRefundEvidence(request);

        // Then
        StepVerifier.create(response).assertNext(uploadResponse -> {
            assertThat(uploadResponse).isNotNull();
            assertThat(uploadResponse.getSuccess()).isTrue();
            assertThat(uploadResponse.getResult()).isNotNull();
            assertThat(uploadResponse.getResult().getSuccess()).isTrue();
            assertThat(uploadResponse.getResult().getErrorCode()).isNull();
            assertThat(uploadResponse.getResult().getErrorMessage()).isNull();
            assertThat(uploadResponse.getResult().getRefundId()).isEqualTo("TQ123456789");
            assertThat(uploadResponse.getResult().getOrderId()).isEqualTo("123456789");
            assertThat(uploadResponse.getResult().getRefundStatus()).isEqualTo("waitsellerconfirm");
            assertThat(uploadResponse.getResult().getDisputeType()).isEqualTo("1");
            assertThat(uploadResponse.getResult().getRefundPhase()).isEqualTo("returnGoods");
        }).verifyComplete();
    }

    @Test
    void shouldGetRefundReasonList() {
        // Given
        var request = new RefundReasonListRequestRecord("123456789", List
            .of(123456789L), RefundReasonListRequestRecord.GoodsStatus.REFUND_WAIT_SELLER_SEND);

        // Mock 成功响应
        var successResponse = JSONUtil.toBean("""
            {
                "success": true,
                "result": {
                    "success": true,
                    "result": {
                        "reasons": [
                            {
                                "id": 20021,
                                "name": "不想要了",
                                "needVoucher": false,
                                "noRefundCarriage": false,
                                "tip": "退款说明"
                            }
                        ]
                    }
                }
            }""", RefundReasonListResponse.class);

        when(refundAPI.getRefundReasonList(any(), any())).thenReturn(reactor.core.publisher.Mono.just(successResponse));

        // When
        var response = refundService.getRefundReasonList(request);

        // Then
        StepVerifier.create(response).assertNext(reasonResponse -> {
            assertThat(reasonResponse).isNotNull();
            assertThat(reasonResponse.getSuccess()).isTrue();
            assertThat(reasonResponse.getResult()).isNotNull();
            assertThat(reasonResponse.getResult().getResult()).isNotNull();
            assertThat(reasonResponse.getResult().getResult().getReasons()).isNotEmpty();
            var reason = reasonResponse.getResult().getResult().getReasons().get(0);
            assertThat(reason.getId()).isEqualTo(20021);
            assertThat(reason.getName()).isEqualTo("不想要了");
            assertThat(reason.getNeedVoucher()).isFalse();
            assertThat(reason.getNoRefundCarriage()).isFalse();
            assertThat(reason.getTip()).isEqualTo("退款说明");
        }).verifyComplete();
    }

    @Test
    void shouldQueryRefundOpLogList() {
        // Given
        var request = RefundOpLogListRequestRecord.of("TQ123456789");

        // Mock 成功响应
        var successResponse = JSONUtil.toBean("""
            {
                "success": true,
                "result": {
                    "success": true,
                    "errorCode": null,
                    "errorMessage": null,
                    "opOrderRefundOperationModels": [
                        {
                            "operateRemark": "买家申请退款协议，等待卖家确认",
                            "operateTypeInt": 1,
                            "beforeOperateStatus": "waitbuyermodify",
                            "afterOperateStatus": "waitsellerconfirm"
                        }
                    ]
                }
            }""", RefundOpLogListResponse.class);

        when(refundAPI.queryRefundOpLogList(any(), any())).thenReturn(reactor.core.publisher.Mono
            .just(successResponse));

        // When
        var response = refundService.queryRefundOpLogList(request);

        // Then
        StepVerifier.create(response).assertNext(opLogResponse -> {
            assertThat(opLogResponse).isNotNull();
            assertThat(opLogResponse.getSuccess()).isTrue();
            assertThat(opLogResponse.getResult()).isNotNull();
            assertThat(opLogResponse.getResult().getSuccess()).isTrue();
            assertThat(opLogResponse.getResult().getErrorCode()).isNull();
            assertThat(opLogResponse.getResult().getErrorMessage()).isNull();
            assertThat(opLogResponse.getResult().getOpOrderRefundOperationModels()).isNotEmpty();
            var opLog = opLogResponse.getResult().getOpOrderRefundOperationModels().get(0);
            assertThat(opLog.getOperateRemark()).isEqualTo("买家申请退款协议，等待卖家确认");
            assertThat(opLog.getOperateTypeInt()).isEqualTo(1);
            assertThat(opLog.getBeforeOperateStatus()).isEqualTo("waitbuyermodify");
            assertThat(opLog.getAfterOperateStatus()).isEqualTo("waitsellerconfirm");
        }).verifyComplete();
    }

    @Test
    void shouldQueryRefundBuyerOrderView() {
        // Given
        var request = new RefundBuyerOrderViewRequestRecord("123456789", RefundBuyerOrderViewRequestRecord.QueryType.REFUND_SUCCESS);

        // Mock 成功响应
        var successResponse = JSONUtil.toBean("""
            {
                "success": true,
                "result": {
                    "opOrderRefundModels": [
                        {
                            "refundId": "TQ123456789",
                            "orderId": 123456789,
                            "status": "waitsellerconfirm",
                            "refundPayment": 10000,
                            "disputeType": 1,
                            "productName": "测试商品",
                            "buyerLoginId": "testbuyer",
                            "sellerLoginId": "testseller"
                        }
                    ]
                }
            }""", RefundBuyerOrderViewResponse.class);

        when(refundAPI.queryRefundBuyerOrderView(any(), any())).thenReturn(reactor.core.publisher.Mono
            .just(successResponse));

        // When
        var response = refundService.queryRefundBuyerOrderView(request);

        // Then
        StepVerifier.create(response).assertNext(orderViewResponse -> {
            assertThat(orderViewResponse).isNotNull();
            assertThat(orderViewResponse.getSuccess()).isTrue();
            assertThat(orderViewResponse.getResult()).isNotNull();
            assertThat(orderViewResponse.getResult().getOpOrderRefundModels()).isNotEmpty();
            var refundModel = orderViewResponse.getResult().getOpOrderRefundModels().get(0);
            assertThat(refundModel.getRefundId()).isEqualTo("TQ123456789");
            assertThat(refundModel.getOrderId()).isEqualTo(123456789);
            assertThat(refundModel.getStatus()).isEqualTo("waitsellerconfirm");
            assertThat(refundModel.getRefundPayment()).isEqualTo(10000);
            assertThat(refundModel.getDisputeType()).isEqualTo(1);
            assertThat(refundModel.getProductName()).isEqualTo("测试商品");
            assertThat(refundModel.getBuyerLoginId()).isEqualTo("testbuyer");
            assertThat(refundModel.getSellerLoginId()).isEqualTo("testseller");
        }).verifyComplete();
    }
}