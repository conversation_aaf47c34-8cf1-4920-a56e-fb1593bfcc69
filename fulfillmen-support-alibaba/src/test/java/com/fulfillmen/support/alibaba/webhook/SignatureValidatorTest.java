/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook;

import static org.assertj.core.api.Assertions.assertThat;

import java.lang.reflect.Field;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

/**
 * SignatureValidator 测试类 测试阿里巴巴Webhook消息签名验证器的各种场景，包括有效签名、无效签名、空密钥等
 * <p>
 * 按照官方算法：message + JSON串 -> HMAC-SHA1 -> 十六进制字符串
 *
 * <AUTHOR>
 * @date 2025/7/10
 * @since 1.0.0
 */
@Slf4j
public class SignatureValidatorTest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";
    /**
     * 测试用的密钥和消息
     */
    private static final String TEST_SECRET = "test-secret-key";
    private static final String TEST_MESSAGE = """
        [{
            "bizKey": "2806599398122540788",
            "data": {
                "buyerMemberId": "b2b-2207416548807a4d12",
                "currentStatus": "success",
                "orderId": 2806599398122540788,
                "sellerMemberId": "b2b-221280776451649a09",
                "msgSendTime": "2025-07-10 17:55:46"
            },
            "gmtBorn": 1752141346107,
            "msgId": "139830976934",
            "type": "ORDER_BUYER_VIEW_ORDER_SUCCESS",
            "userInfo": "b2b-2207416548807a4d12"
        }]
        """;
    private SignatureValidator signatureValidator;

    @BeforeEach
    void setUp() {
        signatureValidator = new SignatureValidator();
    }

    @Test
    @Tag("signature")
    void shouldValidateCorrectSignature() throws Exception {
        log.info("{} 开始测试SignatureValidator正确签名验证 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        setWebhookSecret(TEST_SECRET);

        // 计算正确的签名
        String correctSignature = calculateExpectedSignature(TEST_MESSAGE, TEST_SECRET);

        log.info("{}测试密钥: {}", LOG_ITEM, TEST_SECRET);
        log.info("{}消息长度: {}", LOG_ITEM, TEST_MESSAGE.length());
        log.info("{}期望签名: {}", LOG_ITEM, correctSignature);

        // When
        boolean isValid = signatureValidator.validate(TEST_MESSAGE, correctSignature);

        // Then
        assertThat(isValid).isTrue();

        log.info("{}签名验证结果: {}", LOG_ITEM, isValid);

        log.info("{} 测试SignatureValidator正确签名验证完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("signature")
    void shouldRejectIncorrectSignature() throws Exception {
        log.info("{} 开始测试SignatureValidator错误签名验证 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        setWebhookSecret(TEST_SECRET);
        String incorrectSignature = "incorrect-signature";

        log.info("{}测试密钥: {}", LOG_ITEM, TEST_SECRET);
        log.info("{}错误签名: {}", LOG_ITEM, incorrectSignature);

        // When
        boolean isValid = signatureValidator.validate(TEST_MESSAGE, incorrectSignature);

        // Then
        assertThat(isValid).isFalse();

        log.info("{}签名验证结果: {}", LOG_ITEM, isValid);

        log.info("{} 测试SignatureValidator错误签名验证完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("signature")
    void shouldRejectEmptySignature() throws Exception {
        log.info("{} 开始测试SignatureValidator空签名验证 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        setWebhookSecret(TEST_SECRET);

        log.info("{}测试密钥: {}", LOG_ITEM, TEST_SECRET);
        log.info("{}空签名测试", LOG_ITEM);

        // When
        boolean isValid = signatureValidator.validate(TEST_MESSAGE, "");

        // Then
        assertThat(isValid).isFalse();

        log.info("{}签名验证结果: {}", LOG_ITEM, isValid);

        log.info("{} 测试SignatureValidator空签名验证完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("signature")
    void shouldRejectNullSignature() throws Exception {
        log.info("{} 开始测试SignatureValidator null签名验证 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        setWebhookSecret(TEST_SECRET);

        log.info("{}测试密钥: {}", LOG_ITEM, TEST_SECRET);
        log.info("{}null签名测试", LOG_ITEM);

        // When
        boolean isValid = signatureValidator.validate(TEST_MESSAGE, null);

        // Then
        assertThat(isValid).isFalse();

        log.info("{}签名验证结果: {}", LOG_ITEM, isValid);

        log.info("{} 测试SignatureValidator null签名验证完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("signature")
    void shouldSkipValidationWhenSecretIsEmpty() throws Exception {
        log.info("{} 开始测试SignatureValidator空密钥跳过验证 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        setWebhookSecret("");
        String anySignature = "any-signature";

        log.info("{}空密钥测试", LOG_ITEM);
        log.info("{}任意签名: {}", LOG_ITEM, anySignature);

        // When
        boolean isValid = signatureValidator.validate(TEST_MESSAGE, anySignature);

        // Then
        assertThat(isValid).isTrue();

        log.info("{}签名验证结果: {} (跳过验证)", LOG_ITEM, isValid);

        log.info("{} 测试SignatureValidator空密钥跳过验证完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("signature")
    void shouldSkipValidationWhenSecretIsNull() throws Exception {
        log.info("{} 开始测试SignatureValidator null密钥跳过验证 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        setWebhookSecret(null);
        String anySignature = "any-signature";

        log.info("{}null密钥测试", LOG_ITEM);

        // When
        boolean isValid = signatureValidator.validate(TEST_MESSAGE, anySignature);

        // Then
        assertThat(isValid).isTrue();

        log.info("{}签名验证结果: {} (跳过验证)", LOG_ITEM, isValid);

        log.info("{} 测试SignatureValidator null密钥跳过验证完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("signature")
    void shouldHandleDifferentMessageContent() throws Exception {
        log.info("{} 开始测试SignatureValidator不同消息内容 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        setWebhookSecret(TEST_SECRET);
        String message1 = """
            [{"msgId": "001", "type": "TYPE1", "data": {"field": "value1"}}]
            """;
        String message2 = """
            [{"msgId": "002", "type": "TYPE2", "data": {"field": "value2"}}]
            """;

        String signature1 = calculateExpectedSignature(message1, TEST_SECRET);
        String signature2 = calculateExpectedSignature(message2, TEST_SECRET);

        log.info("{}消息1长度: {}", LOG_ITEM, message1.length());
        log.info("{}消息2长度: {}", LOG_ITEM, message2.length());
        log.info("{}签名1: {}", LOG_ITEM, signature1);
        log.info("{}签名2: {}", LOG_ITEM, signature2);

        // When & Then
        assertThat(signatureValidator.validate(message1, signature1)).isTrue();
        assertThat(signatureValidator.validate(message2, signature2)).isTrue();

        // 交叉验证应该失败
        assertThat(signatureValidator.validate(message1, signature2)).isFalse();
        assertThat(signatureValidator.validate(message2, signature1)).isFalse();

        log.info("{}不同消息内容签名验证通过", LOG_ITEM);

        log.info("{} 测试SignatureValidator不同消息内容完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("signature")
    void shouldHandleDifferentSecretKeys() throws Exception {
        log.info("{} 开始测试SignatureValidator不同密钥 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String secret1 = "secret-key-1";
        String secret2 = "secret-key-2";

        String signature1 = calculateExpectedSignature(TEST_MESSAGE, secret1);
        String signature2 = calculateExpectedSignature(TEST_MESSAGE, secret2);

        log.info("{}密钥1: {}", LOG_ITEM, secret1);
        log.info("{}密钥2: {}", LOG_ITEM, secret2);
        log.info("{}签名1: {}", LOG_ITEM, signature1);
        log.info("{}签名2: {}", LOG_ITEM, signature2);

        // When & Then
        setWebhookSecret(secret1);
        assertThat(signatureValidator.validate(TEST_MESSAGE, signature1)).isTrue();
        assertThat(signatureValidator.validate(TEST_MESSAGE, signature2)).isFalse();

        setWebhookSecret(secret2);
        assertThat(signatureValidator.validate(TEST_MESSAGE, signature1)).isFalse();
        assertThat(signatureValidator.validate(TEST_MESSAGE, signature2)).isTrue();

        log.info("{}不同密钥签名验证通过", LOG_ITEM);

        log.info("{} 测试SignatureValidator不同密钥完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    /**
     * 设置webhook密钥（通过反射）
     */
    private void setWebhookSecret(String secret) throws Exception {
        Field field = SignatureValidator.class.getDeclaredField("webhookSecret");
        field.setAccessible(true);
        field.set(signatureValidator, secret);
    }

    /**
     * 计算期望的签名（按照阿里巴巴官方算法）
     * <p>
     * 官方算法： String[] datas = new String[1]; datas[0] = key + value; // key="message", value=JSON串 byte[] signature = SecurityUtil.hmacSha1(datas, toBytes(appSecretKey)); return encodeHexStr(signature);
     */
    private String calculateExpectedSignature(String payload, String secret) {
        return signatureValidator.calculateSignature(payload, secret);
    }
}
