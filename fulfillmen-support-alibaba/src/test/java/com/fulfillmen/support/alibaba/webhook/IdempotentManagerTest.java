/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import lombok.extern.slf4j.Slf4j;

/**
 * IdempotentManager 测试类
 * 测试幂等性管理器的核心功能，包括重复检测、处理状态标记等
 *
 * <AUTHOR>
 * @date 2025/7/10
 * @since 1.0.0
 */
@Slf4j
public class IdempotentManagerTest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    private IdempotentManager idempotentManager;

    @BeforeEach
    void setUp() {
        idempotentManager = new IdempotentManager();
    }

    @Test
    @Tag("idempotent")
    void shouldDetectFirstTimeMessage() {
        log.info("{} 开始测试IdempotentManager首次消息检测 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String msgId = "test-msg-" + System.currentTimeMillis();
        log.info("{}测试消息ID: {}", LOG_ITEM, msgId);

        // When
        boolean isDuplicate = idempotentManager.isDuplicate(msgId);

        // Then
        assertThat(isDuplicate).isFalse();

        log.info("{}首次消息检测结果: 重复={}", LOG_ITEM, isDuplicate);

        log.info("{} 测试IdempotentManager首次消息检测完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("idempotent")
    void shouldDetectDuplicateAfterProcessing() {
        log.info("{} 开始测试IdempotentManager重复消息检测 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String msgId = "test-msg-" + System.currentTimeMillis();
        MessageResult successResult = MessageResult.success(msgId, "test-data");

        log.info("{}测试消息ID: {}", LOG_ITEM, msgId);

        // When - 第一次检测
        boolean firstCheck = idempotentManager.isDuplicate(msgId);

        // 标记为已处理
        idempotentManager.markProcessed(msgId, successResult);

        // 第二次检测
        boolean secondCheck = idempotentManager.isDuplicate(msgId);

        // Then
        assertThat(firstCheck).isFalse();  // 第一次不是重复
        assertThat(secondCheck).isTrue();  // 第二次是重复

        log.info("{}第一次检测结果: 重复={}", LOG_ITEM, firstCheck);
        log.info("{}第二次检测结果: 重复={}", LOG_ITEM, secondCheck);

        log.info("{} 测试IdempotentManager重复消息检测完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("idempotent")
    void shouldMarkSuccessfulProcessing() {
        log.info("{} 开始测试IdempotentManager标记成功处理 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String msgId = "test-msg-" + System.currentTimeMillis();
        MessageResult successResult = MessageResult.success(msgId, "success-data");

        log.info("{}测试消息ID: {}", LOG_ITEM, msgId);
        log.info("{}处理结果: 成功={}", LOG_ITEM, successResult.isSuccess());

        // When
        idempotentManager.markProcessed(msgId, successResult);

        // Then - 验证标记后的重复检测
        boolean isDuplicate = idempotentManager.isDuplicate(msgId);
        assertThat(isDuplicate).isTrue();

        log.info("{}标记后重复检测结果: 重复={}", LOG_ITEM, isDuplicate);

        log.info("{} 测试IdempotentManager标记成功处理完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("idempotent")
    void shouldMarkFailedProcessing() {
        log.info("{} 开始测试IdempotentManager标记失败处理 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String msgId = "test-msg-" + System.currentTimeMillis();
        MessageResult failedResult = MessageResult.error(msgId, "处理失败");

        log.info("{}测试消息ID: {}", LOG_ITEM, msgId);
        log.info("{}处理结果: 成功={}, 错误={}", LOG_ITEM, failedResult.isSuccess(), failedResult.getErrorMessage());

        // When
        idempotentManager.markProcessed(msgId, failedResult);

        // Then - 验证失败结果也会被标记
        boolean isDuplicate = idempotentManager.isDuplicate(msgId);
        assertThat(isDuplicate).isTrue();

        log.info("{}标记后重复检测结果: 重复={}", LOG_ITEM, isDuplicate);

        log.info("{} 测试IdempotentManager标记失败处理完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("idempotent")
    void shouldHandleMultipleMessages() {
        log.info("{} 开始测试IdempotentManager多消息处理 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        long timestamp = System.currentTimeMillis();
        String msgId1 = "test-msg-1-" + timestamp;
        String msgId2 = "test-msg-2-" + timestamp;
        String msgId3 = "test-msg-3-" + timestamp;

        log.info("{}测试消息ID1: {}", LOG_ITEM, msgId1);
        log.info("{}测试消息ID2: {}", LOG_ITEM, msgId2);
        log.info("{}测试消息ID3: {}", LOG_ITEM, msgId3);

        // When & Then
        // 第一次检测都不是重复
        assertThat(idempotentManager.isDuplicate(msgId1)).isFalse();
        assertThat(idempotentManager.isDuplicate(msgId2)).isFalse();
        assertThat(idempotentManager.isDuplicate(msgId3)).isFalse();

        // 标记处理
        idempotentManager.markProcessed(msgId1, MessageResult.success(msgId1));
        idempotentManager.markProcessed(msgId2, MessageResult.error(msgId2, "error"));

        // 再次检测
        assertThat(idempotentManager.isDuplicate(msgId1)).isTrue();  // 已处理
        assertThat(idempotentManager.isDuplicate(msgId2)).isTrue();  // 已处理
        assertThat(idempotentManager.isDuplicate(msgId3)).isFalse(); // 未处理

        log.info("{}多消息幂等性检测通过", LOG_ITEM);

        log.info("{} 测试IdempotentManager多消息处理完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("idempotent")
    void shouldHandleConcurrentAccess() {
        log.info("{} 开始测试IdempotentManager并发访问 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String msgId = "test-msg-concurrent-" + System.currentTimeMillis();
        log.info("{}并发测试消息ID: {}", LOG_ITEM, msgId);

        // When - 模拟并发检测
        boolean check1 = idempotentManager.isDuplicate(msgId);
        boolean check2 = idempotentManager.isDuplicate(msgId);

        // Then - 第一次获取锁成功，第二次应该检测到正在处理
        assertThat(check1).isFalse(); // 第一次获取锁成功
        assertThat(check2).isTrue();  // 第二次检测到正在处理

        log.info("{}第一次并发检测: 重复={}", LOG_ITEM, check1);
        log.info("{}第二次并发检测: 重复={}", LOG_ITEM, check2);

        log.info("{} 测试IdempotentManager并发访问完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("idempotent")
    void shouldHandleNullMessageId() {
        log.info("{} 开始测试IdempotentManager null消息ID {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String nullMsgId = null;
        log.info("{}null消息ID测试", LOG_ITEM);

        // When & Then - 应该能处理null值而不抛异常
        try {
            boolean isDuplicate = idempotentManager.isDuplicate(nullMsgId);
            // 异常情况下应该返回false，允许处理
            assertThat(isDuplicate).isFalse();

            log.info("{}null消息ID处理结果: 重复={}", LOG_ITEM, isDuplicate);
        } catch (Exception e) {
            log.info("{}null消息ID处理异常: {}", LOG_ITEM, e.getMessage());
            // 异常也是可接受的行为
        }

        log.info("{} 测试IdempotentManager null消息ID完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("idempotent")
    void shouldHandleEmptyMessageId() {
        log.info("{} 开始测试IdempotentManager空消息ID {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String emptyMsgId = "";
        log.info("{}空消息ID测试", LOG_ITEM);

        // When
        boolean isDuplicate = idempotentManager.isDuplicate(emptyMsgId);

        // Then
        assertThat(isDuplicate).isFalse(); // 空ID应该被当作新消息

        log.info("{}空消息ID处理结果: 重复={}", LOG_ITEM, isDuplicate);

        log.info("{} 测试IdempotentManager空消息ID完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("cleanup")
    void shouldExecuteCleanupWithoutError() {
        log.info("{} 开始测试IdempotentManager清理功能 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // When & Then - 清理方法应该能正常执行
        try {
            idempotentManager.cleanupExpiredProcessing();
            log.info("{}清理功能执行成功", LOG_ITEM);
        } catch (Exception e) {
            log.error("{}清理功能执行异常: {}", LOG_ITEM, e.getMessage());
            throw e;
        }

        log.info("{} 测试IdempotentManager清理功能完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }
}
