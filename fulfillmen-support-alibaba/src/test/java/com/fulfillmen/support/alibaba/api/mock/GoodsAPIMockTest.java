/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.mock;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.api.GoodsAPI;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsCouponClaimRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsDetailRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsImageSearchRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsImageUploadRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsKeywordNavigationRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsRecommendRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsSearchRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsSellerRequestRecord;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsCouponClaimResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsDetailResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsImageSearchResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsImageUploadResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsKeywordNavigationResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsRecommendResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsSearchResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsSellerResponse;
import com.fulfillmen.support.alibaba.enums.LanguageEnum;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceException;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import com.fulfillmen.support.alibaba.service.IGoodsService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

/**
 * 1688商品API Mock测试 check
 *
 * <AUTHOR>
 * @created 2025-01-10
 */
@Slf4j
@Tag("mock")
@Tag("goods")
class GoodsAPIMockTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    @Autowired
    private IGoodsService goodsService;

    @MockBean
    private GoodsAPI goodsAPI;

    @Test
    @Tag("read")
    void shouldSearchGoods() {
        // Given
        var request = GoodsSearchRequestRecord.of("test", // keyword
            LanguageEnum.EN, // country
            20, // pageSize
            1); // beginPage

        // 创建响应对象
        var result = new GoodsSearchResponse.Result();
        result.setSuccess(true);
        result.setCode("200");

        var searchResult = new GoodsSearchResponse.SearchResult();
        searchResult.setTotalRecords(100);
        searchResult.setPageSize(20);
        searchResult.setCurrentPage(1);

        var goodsInfo = new GoodsSearchResponse.GoodsInfo();
        goodsInfo.setOfferId(123456789L);
        goodsInfo.setSubject("Test Product");
        goodsInfo.setImageUrl("http://example.com/image.jpg");
        goodsInfo.setIsJxhy(true);

        var priceInfo = new GoodsSearchResponse.PriceInfo();
        priceInfo.setPrice("99.99");
        goodsInfo.setPriceInfo(priceInfo);

        searchResult.setData(Collections.singletonList(goodsInfo));
        result.setResult(searchResult);

        var response = new GoodsSearchResponse();
        response.setResult(result);

        when(goodsAPI.searchGoods(any(String.class), any())).thenReturn(Mono.just(response));

        // When
        var responseFlux = goodsService.searchGoods(request);

        // Then
        StepVerifier.create(responseFlux).assertNext(searchResponse -> {
            assertThat(searchResponse).isNotNull();
            assertThat(searchResponse.getResult()).isNotNull();
            assertThat(searchResponse.getResult().getSuccess()).isTrue();
            assertThat(searchResponse.getResult().getCode()).isEqualTo("200");

            var searchResult1 = searchResponse.getResult().getResult();
            assertThat(searchResult1).isNotNull();
            assertThat(searchResult1.getTotalRecords()).isEqualTo(100);
            assertThat(searchResult1.getPageSize()).isEqualTo(20);
            assertThat(searchResult1.getCurrentPage()).isEqualTo(1);
            assertThat(searchResult1.getData()).hasSize(1);

            var firstItem = searchResult1.getData().get(0);
            assertThat(firstItem.getOfferId()).isEqualTo(123456789L);
            assertThat(firstItem.getSubject()).isEqualTo("Test Product");
            assertThat(firstItem.getImageUrl()).isEqualTo("http://example.com/image.jpg");
            assertThat(firstItem.getPriceInfo().getPrice()).isEqualTo("99.99");
        }).verifyComplete();
    }

    @Test
    @Tag("read")
    void shouldGetGoodsDetail() {
        // Given
        var request = GoodsDetailRequestRecord.of(868927657485L, // offerId
            LanguageEnum.EN); // country

        // Mock API response
        var productDetail = new GoodsDetailResponse.ProductDetail();
        productDetail.setOfferId(868927657485L);
        productDetail.setSubject("测试商品");
        productDetail.setSubjectTrans("Test Product");
        productDetail.setDescription("商品描述");
        productDetail.setStatus("published");
        productDetail.setMinOrderQuantity(1);
        productDetail.setBatchNumber(100);
        productDetail.setIsJxhy(true);
        productDetail.setSellerOpenId("TEST_SELLER_ID");
        productDetail.setSoldOut("1000");
        productDetail.setTradeScore("4.8");

        var sellerDataInfo = new GoodsDetailResponse.SellerDataInfo();
        sellerDataInfo.setTradeMedalLevel("5");
        sellerDataInfo.setCompositeServiceScore("4.9");
        sellerDataInfo.setLogisticsExperienceScore("4.8");
        sellerDataInfo.setOfferExperienceScore("4.7");
        sellerDataInfo.setRepeatPurchasePercent("0.85");
        sellerDataInfo.setCollect30DayWithin48HPercent("0.95");
        sellerDataInfo.setQualityRefundWithin30Day("0.01");
        productDetail.setSellerDataInfo(sellerDataInfo);

        var productSaleInfo = new GoodsDetailResponse.ProductSaleInfo();
        productSaleInfo.setAmountOnSale(1000);
        productDetail.setProductSaleInfo(productSaleInfo);

        // 添加渠道价格信息
        var channelSkuPrice = new GoodsDetailResponse.ChannelSkuPrice();
        channelSkuPrice.setSkuId(123456789L);
        channelSkuPrice.setCurrentPrice("99.99");

        var channelPrice = new GoodsDetailResponse.ChannelPrice();
        var channelSkuPriceList = new ArrayList<GoodsDetailResponse.ChannelSkuPrice>();
        channelSkuPriceList.add(channelSkuPrice);
        channelPrice.setChannelSkuPriceList(channelSkuPriceList);
        productDetail.setChannelPrice(channelPrice);

        var result = new GoodsDetailResponse.Result();
        result.setSuccess(true);
        result.setCode("200");
        result.setMessage("success");
        result.setResult(productDetail);

        var response = new GoodsDetailResponse();
        response.setResult(result);

        when(goodsAPI.getGoodsDetail(any(String.class), any())).thenReturn(Mono.just(response));

        // When
        var responseFlux = goodsService.getGoodsDetail(request);

        // Then
        StepVerifier.create(responseFlux).assertNext(detailResponse -> {
            assertThat(detailResponse).isNotNull();
            assertThat(detailResponse.getResult()).isNotNull();
            assertThat(detailResponse.getResult().getSuccess()).isTrue();
            assertThat(detailResponse.getResult().getCode()).isEqualTo("200");

            var detail = detailResponse.getResult().getResult();
            assertThat(detail).isNotNull();
            assertThat(detail.getOfferId()).isEqualTo(868927657485L);
            assertThat(detail.getSubject()).isEqualTo("测试商品");
            assertThat(detail.getSubjectTrans()).isEqualTo("Test Product");
            assertThat(detail.getStatus()).isEqualTo("published");
            assertThat(detail.getMinOrderQuantity()).isEqualTo(1);
            assertThat(detail.getBatchNumber()).isEqualTo(100);
            assertThat(detail.getIsJxhy()).isTrue();
            assertThat(detail.getSellerOpenId()).isEqualTo("TEST_SELLER_ID");
            assertThat(detail.getSoldOut()).isEqualTo("1000");
            assertThat(detail.getTradeScore()).isEqualTo("4.8");

            assertThat(detail.getSellerDataInfo()).isNotNull();
            assertThat(detail.getSellerDataInfo().getTradeMedalLevel()).isEqualTo("5");
            assertThat(detail.getSellerDataInfo().getCompositeServiceScore()).isEqualTo("4.9");
            assertThat(detail.getSellerDataInfo().getCollect30DayWithin48HPercent()).isEqualTo("0.95");

            // 验证渠道价格信息
            assertThat(detail.getChannelPrice()).isNotNull();
            assertThat(detail.getChannelPrice().getChannelSkuPriceList()).isNotNull();
            assertThat(detail.getChannelPrice().getChannelSkuPriceList()).hasSize(1);
            var skuPrice = detail.getChannelPrice().getChannelSkuPriceList().get(0);
            assertThat(skuPrice.getSkuId()).isEqualTo(123456789L);
            assertThat(skuPrice.getCurrentPrice()).isEqualTo("99.99");
        }).verifyComplete();
    }

    @Test
    @Tag("error")
    void shouldHandleDetailError() {
        log.info("{} 开始测试商品详情错误处理 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = GoodsDetailRequestRecord.of(-1L, // offerId
            null); // country
        log.info("{}请求参数: offerId={}, country={}", LOG_ITEM, request.offerId(), request.country());

        when(goodsAPI.getGoodsDetail(any(String.class), any())).thenReturn(Mono
            .error(new AlibabaServiceValidationException("参数校验失败: offerId必须大于0, country不能为空")));

        // When
        var responseFlux = goodsService.getGoodsDetail(request);

        // Then
        StepVerifier.create(responseFlux).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException;
        }).verify();

        log.info("{} 测试商品详情错误处理完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleSearchError() {
        log.info("{} 开始测试商品搜索错误处理 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = GoodsSearchRequestRecord.of("", // keyword
            LanguageEnum.EN, // country
            51, // pageSize
            1); // beginPage
        log.info("{}请求参数: keyword={}, country={}, pageSize={}", LOG_ITEM, request.keyword(), request.country(), request
            .pageSize());

        when(goodsAPI.searchGoods(any(String.class), any())).thenReturn(Mono
            .error(new AlibabaServiceValidationException("参数校验失败: keyword不能为空, country不能为空")));

        // When
        var responseFlux = goodsService.searchGoods(request);

        // Then
        StepVerifier.create(responseFlux).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException;
        }).verify();

        log.info("{} 测试商品搜索错误处理完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldSearchGoodsSinglePage() {
        log.info("{} 开始测试单页商品搜索 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = GoodsSearchRequestRecord.of("test", // keyword
            LanguageEnum.EN, // country
            20, // pageSize
            1); // beginPage
        log.info("{}请求参数: keyword={}, country={}, pageSize={}", LOG_ITEM, request.keyword(), request.country(), request
            .pageSize());

        // Mock API response
        var result = new GoodsSearchResponse.Result();
        result.setSuccess(true);
        result.setCode("200");

        var searchResult = new GoodsSearchResponse.SearchResult();
        searchResult.setTotalRecords(15); // 总记录数小于页大小
        searchResult.setPageSize(20);
        searchResult.setCurrentPage(1);

        var goodsInfo = new GoodsSearchResponse.GoodsInfo();
        goodsInfo.setOfferId(123456789L);
        goodsInfo.setSubject("Test Product");
        goodsInfo.setImageUrl("http://example.com/image.jpg");
        goodsInfo.setIsJxhy(true);

        var priceInfo = new GoodsSearchResponse.PriceInfo();
        priceInfo.setPrice("99.99");
        goodsInfo.setPriceInfo(priceInfo);

        searchResult.setData(Collections.singletonList(goodsInfo));
        result.setResult(searchResult);

        var response = new GoodsSearchResponse();
        response.setResult(result);

        when(goodsAPI.searchGoods(any(String.class), any())).thenReturn(Mono.just(response));

        // When
        var responseFlux = goodsService.searchGoods(request);

        // Then
        StepVerifier.create(responseFlux).assertNext(searchResponse -> {
            // 基本检查
            assertThat(searchResponse).isNotNull();
            assertThat(searchResponse.getResult()).isNotNull();
            assertThat(searchResponse.getResult().getSuccess()).isTrue();
            assertThat(searchResponse.getResult().getCode()).isEqualTo("200");

            // 分页信息检查
            var searchResult1 = searchResponse.getResult().getResult();
            assertThat(searchResult1).isNotNull();
            assertThat(searchResult1.getTotalRecords()).isEqualTo(15);
            assertThat(searchResult1.getPageSize()).isEqualTo(20);
            assertThat(searchResult1.getCurrentPage()).isEqualTo(1);

            // 商品数据检查
            assertThat(searchResult1.getData()).hasSize(1);
            var firstItem = searchResult1.getData().get(0);
            assertThat(firstItem.getOfferId()).isEqualTo(123456789L);
            assertThat(firstItem.getSubject()).isEqualTo("Test Product");
            assertThat(firstItem.getImageUrl()).isEqualTo("http://example.com/image.jpg");
            assertThat(firstItem.getPriceInfo().getPrice()).isEqualTo("99.99");

            // 记录日志
            log.info("{}响应结果: {}", LOG_ITEM, searchResponse);
            log.info("{}商品数据: {}", LOG_ITEM, firstItem);
        }).verifyComplete();

        log.info("{} 测试单页商品搜索完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleSinglePageSearchError() {
        log.info("{} 开始测试单页商品搜索错误处理 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = GoodsSearchRequestRecord.of("", // keyword
            LanguageEnum.EN, // country
            -1, // pageSize
            0); // beginPage
        log.info("{}请求参数: keyword={}, country={}, pageSize={}", LOG_ITEM, request.keyword(), request.country(), request
            .pageSize());

        when(goodsAPI.searchGoods(any(String.class), any())).thenReturn(Mono
            .error(new AlibabaServiceValidationException("Invalid search parameters")));

        // When
        var responseFlux = goodsService.searchGoods(request);

        // Then
        StepVerifier.create(responseFlux).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException || throwable instanceof AlibabaServiceException;
        }).verify();

        log.info("{} 测试单页商品搜索错误处理完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldGetGoodsRecommend() {
        // Given
        var request = GoodsRecommendRequestRecord.of(LanguageEnum.EN, // country
            1, // beginPage
            20, // pageSize
            null); // outMemberId

        // Mock API response
        var priceInfo = new GoodsRecommendResponse.PriceInfo();
        priceInfo.setPrice("99.99");
        priceInfo.setConsignPrice("89.99");

        var productInfo = new GoodsRecommendResponse.ProductInfo();
        productInfo.setOfferId(868927657485L);
        productInfo.setSubject("测试商品");
        productInfo.setSubjectTrans("Test Product");
        productInfo.setImageUrl("http://example.com/image.jpg");
        productInfo.setPriceInfo(priceInfo);
        productInfo.setMonthSold(100);
        productInfo.setRepurchaseRate("0.85");
        productInfo.setTraceInfo("热销商品");
        productInfo.setIsSelect(true);
        productInfo.setPromotionURL("http://example.com/product");

        var result = new GoodsRecommendResponse.Result();
        result.setSuccess(true);
        result.setCode("200");
        result.setResult(new GoodsRecommendResponse.ProductInfo[]{productInfo});

        var response = new GoodsRecommendResponse();
        response.setResult(result);

        when(goodsAPI.recommendGoods(any(String.class), any())).thenReturn(Mono.just(response));

        // When
        var responseFlux = goodsService.recommendGoods(request);

        // Then
        StepVerifier.create(responseFlux).assertNext(recommendResponse -> {
            assertThat(recommendResponse).isNotNull();
            assertThat(recommendResponse.getResult()).isNotNull();
            assertThat(recommendResponse.getResult().getSuccess()).isTrue();
            assertThat(recommendResponse.getResult().getCode()).isEqualTo("200");

            var products = recommendResponse.getResult().getResult();
            assertThat(products).hasSize(1);

            var product = products[0];
            assertThat(product.getOfferId()).isEqualTo(868927657485L);
            assertThat(product.getSubject()).isEqualTo("测试商品");
            assertThat(product.getSubjectTrans()).isEqualTo("Test Product");
            assertThat(product.getImageUrl()).isEqualTo("http://example.com/image.jpg");
            assertThat(product.getPriceInfo().getPrice()).isEqualTo("99.99");
            assertThat(product.getPriceInfo().getConsignPrice()).isEqualTo("89.99");
            assertThat(product.getMonthSold()).isEqualTo(100);
            assertThat(product.getRepurchaseRate()).isEqualTo("0.85");
            assertThat(product.getTraceInfo()).isEqualTo("热销商品");
            assertThat(product.getIsSelect()).isTrue();
            assertThat(product.getPromotionURL()).isEqualTo("http://example.com/product");
        }).verifyComplete();
    }

    @Test
    @Tag("error")
    void shouldHandleRecommendError() {
        log.info("{} 开始测试商品推荐错误处理 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = GoodsRecommendRequestRecord.of(null); // country
        log.info("{}请求参数: country={}", LOG_ITEM, request.country());

        when(goodsAPI.recommendGoods(any(String.class), any())).thenReturn(Mono
            .error(new AlibabaServiceValidationException("参数校验失败: offerId不能为空, country不能为空")));

        // When
        var responseFlux = goodsService.recommendGoods(request);

        // Then
        StepVerifier.create(responseFlux).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException;
        }).verify();

        log.info("{} 测试商品推荐错误处理完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldGetGoodsImageSearch() {
        // Given
        var request = GoodsImageSearchRequestRecord.of("IMAGE_ID_123", // imageId
            LanguageEnum.EN, // country
            1, // beginPage
            20); // pageSize

        // Mock API response
        var priceInfo = new GoodsImageSearchResponse.PriceInfo();
        priceInfo.setPrice("99.99");
        priceInfo.setConsignPrice("89.99");

        var goodsInfo = new GoodsImageSearchResponse.GoodsInfo();
        goodsInfo.setOfferId(868927657485L);
        goodsInfo.setSubject("测试商品");
        goodsInfo.setSubjectTrans("Test Product");
        goodsInfo.setImageUrl("http://example.com/image.jpg");
        goodsInfo.setPriceInfo(priceInfo);
        goodsInfo.setIsJxhy(true);
        goodsInfo.setRepurchaseRate("0.85");
        goodsInfo.setMonthSold(100);
        goodsInfo.setTraceInfo("热销商品");
        goodsInfo.setIsSelect(true);
        goodsInfo.setMinOrderQuantity(1);
        goodsInfo.setPromotionURL("http://example.com/product");

        var searchResult = new GoodsImageSearchResponse.SearchResult();
        searchResult.setTotalRecords(15);
        searchResult.setTotalPage(1);
        searchResult.setPageSize(20);
        searchResult.setCurrentPage(1);
        searchResult.setData(Collections.singletonList(goodsInfo));

        var result = new GoodsImageSearchResponse.Result();
        result.setSuccess("true");
        result.setCode("200");
        result.setMessage("success");
        result.setResult(searchResult);

        var response = new GoodsImageSearchResponse();
        response.setResult(result);

        when(goodsAPI.searchGoodsByImage(any(String.class), any())).thenReturn(Mono.just(response));

        // When
        var responseFlux = goodsService.searchGoodsByImage(request);

        // Then
        StepVerifier.create(responseFlux).assertNext(imageSearchResponse -> {
            assertThat(imageSearchResponse).isNotNull();
            assertThat(imageSearchResponse.getResult()).isNotNull();
            assertThat(imageSearchResponse.getResult().getSuccess()).isEqualTo("true");
            assertThat(imageSearchResponse.getResult().getCode()).isEqualTo("200");
            assertThat(imageSearchResponse.getResult().getMessage()).isEqualTo("success");

            var searchResult1 = imageSearchResponse.getResult().getResult();
            assertThat(searchResult1).isNotNull();
            assertThat(searchResult1.getTotalRecords()).isEqualTo(15);
            assertThat(searchResult1.getTotalPage()).isEqualTo(1);
            assertThat(searchResult1.getPageSize()).isEqualTo(20);
            assertThat(searchResult1.getCurrentPage()).isEqualTo(1);

            var products = searchResult1.getData();
            assertThat(products).hasSize(1);

            var product = products.get(0);
            assertThat(product.getOfferId()).isEqualTo(868927657485L);
            assertThat(product.getSubject()).isEqualTo("测试商品");
            assertThat(product.getSubjectTrans()).isEqualTo("Test Product");
            assertThat(product.getImageUrl()).isEqualTo("http://example.com/image.jpg");
            assertThat(product.getPriceInfo().getPrice()).isEqualTo("99.99");
            assertThat(product.getPriceInfo().getConsignPrice()).isEqualTo("89.99");
            assertThat(product.getIsJxhy()).isTrue();
            assertThat(product.getRepurchaseRate()).isEqualTo("0.85");
            assertThat(product.getMonthSold()).isEqualTo(100);
            assertThat(product.getTraceInfo()).isEqualTo("热销商品");
            assertThat(product.getIsSelect()).isTrue();
            assertThat(product.getMinOrderQuantity()).isEqualTo(1);
            assertThat(product.getPromotionURL()).isEqualTo("http://example.com/product");
        }).verifyComplete();
    }

    @Test
    @Tag("error")
    void shouldHandleImageSearchError() {
        log.info("{} 开始测试商品图片搜索错误处理 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = GoodsImageSearchRequestRecord.of("", // imageId
            null); // country
        log.info("{}请求参数: imageId={}, country={}", LOG_ITEM, request.imageId(), request.country());

        when(goodsAPI.searchGoodsByImage(any(String.class), any())).thenReturn(Mono
            .error(new AlibabaServiceValidationException("参数校验失败: imageId不能为空, country不能为空")));

        // When
        var responseFlux = goodsService.searchGoodsByImage(request);

        // Then
        StepVerifier.create(responseFlux).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException;
        }).verify();

        log.info("{} 测试商品图片搜索错误处理完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("write")
    void shouldUploadGoodsImage() {
        // Given
        var request = GoodsImageUploadRequestRecord.of("base64_image_data", // imageBase64
            "TEST_USER_ID"); // outMemberId

        // Mock API response
        var result = new GoodsImageUploadResponse.Result();
        result.setSuccess("true");
        result.setCode("200");
        result.setMessage("success");
        result.setResult("IMAGE_ID_123");

        var response = new GoodsImageUploadResponse();
        response.setResult(result);

        when(goodsAPI.uploadImage(any(String.class), any())).thenReturn(Mono.just(response));

        // When
        var responseFlux = goodsService.uploadImage(request);

        // Then
        StepVerifier.create(responseFlux).assertNext(uploadResponse -> {
            assertThat(uploadResponse).isNotNull();
            assertThat(uploadResponse.getResult()).isNotNull();
            assertThat(uploadResponse.getResult().getSuccess()).isEqualTo("true");
            assertThat(uploadResponse.getResult().getCode()).isEqualTo("200");
            assertThat(uploadResponse.getResult().getMessage()).isEqualTo("success");
            assertThat(uploadResponse.getResult().getResult()).isEqualTo("IMAGE_ID_123");
        }).verifyComplete();
    }

    @Test
    @Tag("error")
    void shouldHandleImageUploadError() {
        log.info("{} 开始测试商品图片上传错误处理 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = GoodsImageUploadRequestRecord.of("", // imageBase64
            null); // outMemberId
        log.info("{}请求参数: imageBase64={}, outMemberId={}", LOG_ITEM, request.imageBase64(), request.outMemberId());

        when(goodsAPI.uploadImage(any(String.class), any())).thenReturn(Mono
            .error(new AlibabaServiceValidationException("参数校验失败: imageBase64不能为空")));

        // When
        var responseFlux = goodsService.uploadImage(request);

        // Then
        StepVerifier.create(responseFlux).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException;
        }).verify();

        log.info("{} 测试商品图片上传错误处理完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldGetGoodsSeller() {
        // Given
        var request = GoodsSellerRequestRecord.of(LanguageEnum.EN, // country
            "123456789"); // sellerOpenId

        // Mock API response
        var priceInfo = new GoodsSellerResponse.PriceInfo();
        priceInfo.setPrice("99.99");
        priceInfo.setConsignPrice("89.99");
        priceInfo.setJxhyPrice("79.99");
        priceInfo.setPfJxhyPrice("69.99");

        var productInfo = new GoodsSellerResponse.ProductInfo();
        productInfo.setOfferId(868927657485L);
        productInfo.setSubject("测试商品");
        productInfo.setSubjectTrans("Test Product");
        productInfo.setImageUrl("http://example.com/image.jpg");
        productInfo.setPriceInfo(priceInfo);
        productInfo.setIsJxhy(true);
        productInfo.setRepurchaseRate("0.85");
        productInfo.setMonthSold(100);
        productInfo.setTraceInfo("热销商品");
        productInfo.setIsOnePsale(true);
        productInfo.setCreateDate("2025-01-23");
        productInfo.setModifyDate("2025-01-17");
        productInfo.setIsPatentProduct(false);
        productInfo.setOfferIdentities(new String[]{"yx", "select"});
        productInfo.setIsSelect("1");
        productInfo.setToken("TEST_TOKEN");
        productInfo.setPromotionURL("http://example.com/product");

        var pageInfo = new GoodsSellerResponse.PageInfo();
        pageInfo.setTotalRecords(15);
        pageInfo.setTotalPage(1);
        pageInfo.setPageSize(20);
        pageInfo.setCurrentPage(1);
        pageInfo.setData(new GoodsSellerResponse.ProductInfo[]{productInfo});

        var result = new GoodsSellerResponse.Result();
        result.setSuccess(true);
        result.setCode("200");
        result.setMessage("success");
        result.setResult(pageInfo);

        var response = new GoodsSellerResponse();
        response.setResult(result);

        when(goodsAPI.getSellerGoods(any(String.class), any())).thenReturn(Mono.just(response));

        // When
        var responseFlux = goodsService.getSellerGoods(request);

        // Then
        StepVerifier.create(responseFlux).assertNext(sellerResponse -> {
            assertThat(sellerResponse).isNotNull();
            assertThat(sellerResponse.getResult()).isNotNull();
            assertThat(sellerResponse.getResult().getSuccess()).isTrue();
            assertThat(sellerResponse.getResult().getCode()).isEqualTo("200");
            assertThat(sellerResponse.getResult().getMessage()).isEqualTo("success");

            var pageInfo1 = sellerResponse.getResult().getResult();
            assertThat(pageInfo1).isNotNull();
            assertThat(pageInfo1.getTotalRecords()).isEqualTo(15);
            assertThat(pageInfo1.getTotalPage()).isEqualTo(1);
            assertThat(pageInfo1.getPageSize()).isEqualTo(20);
            assertThat(pageInfo1.getCurrentPage()).isEqualTo(1);

            var products = pageInfo1.getData();
            assertThat(products).hasSize(1);

            var product = products[0];
            assertThat(product.getOfferId()).isEqualTo(868927657485L);
            assertThat(product.getSubject()).isEqualTo("测试商品");
            assertThat(product.getSubjectTrans()).isEqualTo("Test Product");
            assertThat(product.getImageUrl()).isEqualTo("http://example.com/image.jpg");
            assertThat(product.getPriceInfo().getPrice()).isEqualTo("99.99");
            assertThat(product.getPriceInfo().getConsignPrice()).isEqualTo("89.99");
            assertThat(product.getPriceInfo().getJxhyPrice()).isEqualTo("79.99");
            assertThat(product.getPriceInfo().getPfJxhyPrice()).isEqualTo("69.99");
            assertThat(product.getIsJxhy()).isTrue();
            assertThat(product.getRepurchaseRate()).isEqualTo("0.85");
            assertThat(product.getMonthSold()).isEqualTo(100);
            assertThat(product.getTraceInfo()).isEqualTo("热销商品");
            assertThat(product.getIsOnePsale()).isTrue();
            assertThat(product.getCreateDate()).isEqualTo("2025-01-23");
            assertThat(product.getModifyDate()).isEqualTo("2025-01-17");
            assertThat(product.getIsPatentProduct()).isFalse();
            assertThat(product.getOfferIdentities()).containsExactly("yx", "select");
            assertThat(product.getIsSelect()).isEqualTo("1");
            assertThat(product.getToken()).isEqualTo("TEST_TOKEN");
            assertThat(product.getPromotionURL()).isEqualTo("http://example.com/product");
        }).verifyComplete();
    }

    @Test
    @Tag("error")
    void shouldHandleSellerError() {
        log.info("{} 开始测试商品店铺错误处理 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = GoodsSellerRequestRecord.of(null, // country
            ""); // sellerOpenId
        log.info("{}请求参数: country={}, sellerOpenId={}", LOG_ITEM, request.country(), request.sellerOpenId());

        when(goodsAPI.getSellerGoods(any(String.class), any())).thenReturn(Mono
            .error(new AlibabaServiceValidationException("参数校验失败: country不能为空, sellerOpenId不能为空")));

        // When
        var responseFlux = goodsService.getSellerGoods(request);

        // Then
        StepVerifier.create(responseFlux).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException;
        }).verify();

        log.info("{} 测试商品店铺错误处理完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldGetGoodsKeywordNavigation() {
        // Given
        var request = GoodsKeywordNavigationRequestRecord.of("test product", // keyword
            LanguageEnum.EN, // language
            "US", // region
            "USD"); // currency

        // Mock API response
        var item = new GoodsKeywordNavigationResponse.NavigationItem();
        item.setId("456");
        item.setName("Test Item");
        item.setTranslateName("测试项目");

        var category = new GoodsKeywordNavigationResponse.NavigationCategory();
        category.setId("123");
        category.setName("Test Category");
        category.setTranslateName("测试类目");
        category.setChildren(Collections.singletonList(item));

        var result = new GoodsKeywordNavigationResponse.NavigationResult();
        result.setSuccess(true);
        result.setRetCode("200");
        result.setRetMsg("success");
        result.setResult(Collections.singletonList(category));

        var response = new GoodsKeywordNavigationResponse();
        response.setResult(result);

        when(goodsAPI.getKeywordNavigation(any(String.class), any())).thenReturn(Mono.just(response));

        // When
        var responseFlux = goodsService.getKeywordNavigation(request);

        // Then
        StepVerifier.create(responseFlux).assertNext(navigationResponse -> {
            assertThat(navigationResponse).isNotNull();
            assertThat(navigationResponse.getResult()).isNotNull();
            assertThat(navigationResponse.getResult().getSuccess()).isTrue();
            assertThat(navigationResponse.getResult().getRetCode()).isEqualTo("200");
            assertThat(navigationResponse.getResult().getRetMsg()).isEqualTo("success");

            var categories = navigationResponse.getResult().getResult();
            assertThat(categories).hasSize(1);

            var firstCategory = categories.get(0);
            assertThat(firstCategory.getId()).isEqualTo("123");
            assertThat(firstCategory.getName()).isEqualTo("Test Category");
            assertThat(firstCategory.getTranslateName()).isEqualTo("测试类目");

            var children = firstCategory.getChildren();
            assertThat(children).hasSize(1);

            var firstItem = children.get(0);
            assertThat(firstItem.getId()).isEqualTo("456");
            assertThat(firstItem.getName()).isEqualTo("Test Item");
            assertThat(firstItem.getTranslateName()).isEqualTo("测试项目");
        }).verifyComplete();
    }

    @Test
    @Tag("error")
    void shouldHandleKeywordNavigationError() {
        log.info("{} 开始测试商品关键词导航错误处理 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = GoodsKeywordNavigationRequestRecord.of("", // keyword
            null, // language
            "", // region
            ""); // currency
        log.info("{}请求参数: keyword={}, language={}, region={}, currency={}", LOG_ITEM, request.keyword(), request
            .language(), request.region(), request.currency());

        when(goodsAPI.getKeywordNavigation(any(String.class), any())).thenReturn(Mono
            .error(new AlibabaServiceValidationException("参数校验失败: keyword不能为空, language不能为空, region不能为空, currency不能为空")));

        // When
        var responseFlux = goodsService.getKeywordNavigation(request);

        // Then
        StepVerifier.create(responseFlux).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException;
        }).verify();

        log.info("{} 测试商品关键词导航错误处理完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("write")
    void shouldClaimCoupon() {
        log.info("{} 开始测试优惠券领取 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = GoodsCouponClaimRequestRecord.of(List.of(123456789L, 987654321L)); // offerIds
        log.info("{}请求参数: offerIds={}", LOG_ITEM, request.offerIds());

        var result = new GoodsCouponClaimResponse.Result();
        result.setSuccess(true);

        var couponResult = new GoodsCouponClaimResponse.CouponResult();
        couponResult.setCouponIds(new String[]{"coupon123", "coupon456"});

        result.setResult(couponResult);

        var response = new GoodsCouponClaimResponse();
        response.setResult(result);

        when(goodsAPI.claimCoupon(any(String.class), any())).thenReturn(Mono.just(response));

        // When
        var responseFlux = goodsService.claimCoupon(request);

        // Then
        StepVerifier.create(responseFlux).assertNext(claimResponse -> {
            // 基本检查
            assertThat(claimResponse).isNotNull();
            assertThat(claimResponse.getResult()).isNotNull();
            assertThat(claimResponse.getResult().getSuccess()).isTrue();

            // 领取结果检查
            var couponIds = claimResponse.getResult().getResult().getCouponIds();
            assertThat(couponIds).hasSize(2);
            assertThat(couponIds[0]).isEqualTo("coupon123");
            assertThat(couponIds[1]).isEqualTo("coupon456");

            // 记录日志
            log.info("{}响应结果: {}", LOG_ITEM, claimResponse);
            log.info("{}优惠券ID列表: {}", LOG_ITEM, Arrays.toString(couponIds));
        }).verifyComplete();

        log.info("{} 测试优惠券领取完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleCouponClaimError() {
        log.info("{} 开始测试优惠券领取错误处理 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = GoodsCouponClaimRequestRecord.of(null); // offerIds
        log.info("{}请求参数: offerIds为空", LOG_ITEM);

        when(goodsAPI.claimCoupon(any(String.class), any())).thenReturn(Mono
            .error(new AlibabaServiceValidationException("参数校验失败: 商品ID列表不能为空")));

        // When
        var responseFlux = goodsService.claimCoupon(request);

        // Then
        StepVerifier.create(responseFlux).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException;
        }).verify();

        log.info("{} 测试优惠券领取错误处理完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleCouponClaimMaxSizeError() {
        log.info("{} 开始测试优惠券领取超过最大数量错误处理 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = GoodsCouponClaimRequestRecord.of(Arrays.asList(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L, 9L, 10L, 11L)); // offerIds
        log.info("{}请求参数: offerIds数量={}", LOG_ITEM, request.offerIds().size());

        when(goodsAPI.claimCoupon(any(String.class), any())).thenReturn(Mono
            .error(new AlibabaServiceException("单次最多支持10个商品")));

        // When
        var responseFlux = goodsService.claimCoupon(request);

        // Then
        StepVerifier.create(responseFlux).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceException && throwable.getMessage().contains("单次最多支持10个商品");
        }).verify();

        log.info("{} 测试优惠券领取超过最大数量错误处理完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldMatchProductDetailStructure() {
        // Given
        var request = GoodsDetailRequestRecord.of(************L, // offerId
            LanguageEnum.EN); // country

        // Mock API response - 使用 goods-************.json 的数据
        var productDetail = new GoodsDetailResponse.ProductDetail();
        productDetail.setOfferId(************L);
        productDetail.setCategoryId(122984003L);
        productDetail.setSubject("2代6件A款哪吒敖丙电影同款魔童模型蛋糕摆件公仔厂家直销");
        productDetail
            .setSubjectTrans("2 generations of 6 pieces of A which Ao C movie with magic child model cake ornaments doll factory outlet");
        productDetail
            .setDescription(
                "<div id=\"offer-template-0\"></div><div style=\"width: 790px;\"><img style=\"display: block; width: 100%; height: auto;\" title=\"\" src=\"https://cbu01.alicdn.com/img/ibank/O1CN01QGvtTV1G8lxMlJYSk_!!*************-0-cib.jpg\" alt=\"\" usemap=\"#_sdmap_0\" />");

        // 设置图片信息
        var productImage = new GoodsDetailResponse.ProductImage();
        productImage.setImages(Arrays
            .asList("https://cbu01.alicdn.com/img/ibank/O1CN01YApDSy1G8lxOF1BxJ_!!*************-0-cib.jpg", "https://cbu01.alicdn.com/img/ibank/O1CN019MXtvs1G8lxOhhUi9_!!*************-0-cib.jpg",
                "https://cbu01.alicdn.com/img/ibank/O1CN01qJPR3z1G8lxMlGnwJ_!!*************-0-cib.jpg", "https://cbu01.alicdn.com/img/ibank/O1CN01RJSGpf1G8lxOF69vM_!!*************-0-cib.jpg",
                "https://cbu01.alicdn.com/img/ibank/O1CN017phueQ1G8lxPlOi8h_!!*************-0-cib.jpg"));
        productDetail.setProductImage(productImage);

        // 设置商品属性
        var attributes = new ArrayList<GoodsDetailResponse.ProductAttribute>();
        var attribute1 = new GoodsDetailResponse.ProductAttribute();
        attribute1.setAttributeId("287");
        attribute1.setAttributeName("材质");
        attribute1.setValue("PVC");
        attribute1.setAttributeNameTrans("Material");
        attribute1.setValueTrans("PVC");
        attributes.add(attribute1);

        var attribute2 = new GoodsDetailResponse.ProductAttribute();
        attribute2.setAttributeId("346");
        attribute2.setAttributeName("产地");
        attribute2.setValue("广东");
        attribute2.setAttributeNameTrans("Origin");
        attribute2.setValueTrans("Guangdong");
        attributes.add(attribute2);
        productDetail.setProductAttribute(attributes);

        // 设置 SKU 信息
        var skuInfos = new ArrayList<GoodsDetailResponse.ProductSkuInfo>();
        var skuInfo = new GoodsDetailResponse.ProductSkuInfo();
        skuInfo.setAmountOnSale(3006);
        skuInfo.setPrice("37.0");
        skuInfo.setSkuId(5724991804055L);
        skuInfo.setSpecId("c08ba9289ad0d00041aca3452df6b219");
        skuInfo.setConsignPrice("37.0");
        skuInfo.setCargoNumber("PVC-**********");

        var fenxiaoPriceInfo = new GoodsDetailResponse.FenxiaoPriceInfo();
        fenxiaoPriceInfo.setOfferPrice("37");
        skuInfo.setFenxiaoPriceInfo(fenxiaoPriceInfo);

        var skuAttributes = new ArrayList<GoodsDetailResponse.SkuAttribute>();
        var skuAttribute = new GoodsDetailResponse.SkuAttribute();
        skuAttribute.setAttributeId(3216L);
        skuAttribute.setAttributeName("颜色");
        skuAttribute.setValue("2代哪吒6件套-A款(PVC)【袋装】");
        skuAttribute.setAttributeNameTrans("Color");
        skuAttribute.setValueTrans("2nd generation nezha 6-piece set-type a (pvc) [bag]");
        skuAttribute
            .setSkuImageUrl("https://cbu01.alicdn.com/img/ibank/O1CN01YApDSy1G8lxOF1BxJ_!!*************-0-cib.jpg");
        skuAttributes.add(skuAttribute);
        skuInfo.setSkuAttributes(skuAttributes);
        skuInfos.add(skuInfo);
        productDetail.setProductSkuInfos(skuInfos);

        // 设置销售信息
        var saleInfo = new GoodsDetailResponse.ProductSaleInfo();
        saleInfo.setAmountOnSale(58821);
        var priceRange = new GoodsDetailResponse.PriceRange();
        priceRange.setStartQuantity(3);
        priceRange.setPrice("0.6");
        saleInfo.setPriceRangeList(Collections.singletonList(priceRange));
        saleInfo.setQuoteType(1);

        var unitInfo = new GoodsDetailResponse.UnitInfo();
        unitInfo.setUnit("套");
        unitInfo.setTransUnit("Sleeve");
        saleInfo.setUnitInfo(unitInfo);

        var fenxiaoSaleInfo = new GoodsDetailResponse.FenxiaoSaleInfo();
        fenxiaoSaleInfo.setOnePieceFreePostage(false);
        fenxiaoSaleInfo.setStartQuantity(3);
        saleInfo.setFenxiaoSaleInfo(fenxiaoSaleInfo);
        productDetail.setProductSaleInfo(saleInfo);

        // 设置物流信息
        var shippingInfo = new GoodsDetailResponse.ProductShippingInfo();
        shippingInfo.setSendGoodsAddressText("广东省汕头市");
        var skuShippingInfo = new GoodsDetailResponse.SkuShippingInfo();
        skuShippingInfo.setSpecId("c08ba9289ad0d00041aca3452df6b219");
        skuShippingInfo.setSkuId(5724991804055L);
        skuShippingInfo.setWeight(550);
        shippingInfo.setSkuShippingInfoList(Collections.singletonList(skuShippingInfo));
        productDetail.setProductShippingInfo(shippingInfo);

        // 设置基本信息
        productDetail.setIsJxhy(false);
        productDetail.setSellerOpenId("BBBmjKf4K_FKsazpQuoIuOgIQ");
        productDetail.setMinOrderQuantity(1);
        productDetail.setStatus("published");
        productDetail.setCreateDate("2025-02-01 14:31:21");
        productDetail.setSoldOut("6559");
        productDetail.setTradeScore("5.0");
        productDetail.setTopCategoryId(1813L);
        productDetail.setSecondCategoryId(181302L);
        productDetail.setThirdCategoryId(122984003L);
        productDetail.setOfferIdentities(Arrays.asList("tp_member"));
        productDetail.setIsSelect("false");
        productDetail.setPromotionUrl("https://detail.1688.com/offer/************.html?tdScene=kj-ai");

        var result = new GoodsDetailResponse.Result();
        result.setSuccess(true);
        result.setCode("200");
        result.setResult(productDetail);

        var response = new GoodsDetailResponse();
        response.setResult(result);

        when(goodsAPI.getGoodsDetail(any(String.class), any())).thenReturn(Mono.just(response));

        // When
        var responseFlux = goodsService.getGoodsDetail(request);

        // Then
        StepVerifier.create(responseFlux).assertNext(detailResponse -> {
            assertThat(detailResponse).isNotNull();
            assertThat(detailResponse.getResult()).isNotNull();
            assertThat(detailResponse.getResult().getSuccess()).isTrue();
            assertThat(detailResponse.getResult().getCode()).isEqualTo("200");

            var detail = detailResponse.getResult().getResult();

            // 验证基本字段
            assertThat(detail.getOfferId()).isEqualTo(************L);
            assertThat(detail.getCategoryId()).isEqualTo(122984003L);
            assertThat(detail.getSubject()).isEqualTo("2代6件A款哪吒敖丙电影同款魔童模型蛋糕摆件公仔厂家直销");
            assertThat(detail.getSubjectTrans()).contains("magic child model");

            // 验证图片信息
            assertThat(detail.getProductImage()).isNotNull();
            assertThat(detail.getProductImage().getImages()).hasSize(5);
            assertThat(detail.getProductImage().getImages().get(0))
                .isEqualTo("https://cbu01.alicdn.com/img/ibank/O1CN01YApDSy1G8lxOF1BxJ_!!*************-0-cib.jpg");

            // 验证商品属性
            assertThat(detail.getProductAttribute()).hasSize(2);
            var attr = detail.getProductAttribute().get(0);
            assertThat(attr.getAttributeId()).isEqualTo("287");
            assertThat(attr.getAttributeName()).isEqualTo("材质");
            assertThat(attr.getValue()).isEqualTo("PVC");

            // 验证 SKU 信息
            assertThat(detail.getProductSkuInfos()).hasSize(1);
            var sku = detail.getProductSkuInfos().get(0);
            assertThat(sku.getAmountOnSale()).isEqualTo(3006);
            assertThat(sku.getPrice()).isEqualTo("37.0");
            assertThat(sku.getSkuId()).isEqualTo(5724991804055L);
            assertThat(sku.getCargoNumber()).isEqualTo("PVC-**********");
            assertThat(sku.getFenxiaoPriceInfo().getOfferPrice()).isEqualTo("37");

            // 验证销售信息
            assertThat(detail.getProductSaleInfo()).isNotNull();
            assertThat(detail.getProductSaleInfo().getAmountOnSale()).isEqualTo(58821);
            assertThat(detail.getProductSaleInfo().getQuoteType()).isEqualTo(1);
            assertThat(detail.getProductSaleInfo().getUnitInfo().getUnit()).isEqualTo("套");
            assertThat(detail.getProductSaleInfo().getFenxiaoSaleInfo().getStartQuantity()).isEqualTo(3);

            // 验证物流信息
            assertThat(detail.getProductShippingInfo()).isNotNull();
            assertThat(detail.getProductShippingInfo().getSendGoodsAddressText()).isEqualTo("广东省汕头市");
            assertThat(detail.getProductShippingInfo().getSkuShippingInfoList().get(0).getWeight()).isEqualTo(550);

            // 验证其他基本信息
            assertThat(detail.getIsJxhy()).isFalse();
            assertThat(detail.getSellerOpenId()).isEqualTo("BBBmjKf4K_FKsazpQuoIuOgIQ");
            assertThat(detail.getMinOrderQuantity()).isEqualTo(1);
            assertThat(detail.getStatus()).isEqualTo("published");
            assertThat(detail.getCreateDate()).isEqualTo("2025-02-01 14:31:21");
            assertThat(detail.getSoldOut()).isEqualTo("6559");
            assertThat(detail.getTradeScore()).isEqualTo("5.0");
            assertThat(detail.getOfferIdentities()).containsExactly("tp_member");
            assertThat(detail.getPromotionUrl())
                .isEqualTo("https://detail.1688.com/offer/************.html?tdScene=kj-ai");
        }).verifyComplete();
    }
}