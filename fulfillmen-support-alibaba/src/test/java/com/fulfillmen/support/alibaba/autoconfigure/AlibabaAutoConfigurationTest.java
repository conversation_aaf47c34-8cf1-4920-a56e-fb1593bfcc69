/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.autoconfigure;

import static org.assertj.core.api.Assertions.assertThat;

import com.fulfillmen.support.alibaba.api.CategoryAPI;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * 阿里巴巴开放平台自动配置测试类
 *
 * <AUTHOR>
 * @date 2024-12-31 09:13
 * @description: 测试阿里巴巴开放平台的自动配置功能
 * @since 1.0.0
 */
@ExtendWith(SpringExtension.class)
class AlibabaAutoConfigurationTest {

    private final ApplicationContextRunner contextRunner = new ApplicationContextRunner()
        .withConfiguration(AutoConfigurations.of(AlibabaAutoConfiguration.class))
        .withPropertyValues("alibaba.open1688.app-key=test-app-key", "alibaba.open1688.secret-key=test-secret-key", "alibaba.open1688.access-token=test-token");

    @Test
    void shouldCreateWebClient() {
        contextRunner.run(context -> {
            assertThat(context).hasSingleBean(WebClient.class);
            WebClient webClient = context.getBean(WebClient.class);
            assertThat(webClient).isNotNull();
        });
    }

    @Test
    void shouldCreateCategoryAPI() {
        contextRunner.run(context -> {
            assertThat(context).hasSingleBean(CategoryAPI.class);
            CategoryAPI categoryAPI = context.getBean(CategoryAPI.class);
            assertThat(categoryAPI).isNotNull();
        });
    }
}