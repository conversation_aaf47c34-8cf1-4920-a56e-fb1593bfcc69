/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.mock;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.api.FeedbackAPI;
import com.fulfillmen.support.alibaba.api.request.feedback.AccountBusinessSaveRequestRecord;
import com.fulfillmen.support.alibaba.api.request.feedback.LogisticsOrderSyncRequestRecord;
import com.fulfillmen.support.alibaba.api.request.feedback.OrderRelationWriteRequestRecord;
import com.fulfillmen.support.alibaba.api.request.feedback.OrderSyncRequestRecord;
import com.fulfillmen.support.alibaba.api.response.feedback.AccountBusinessSaveResponse;
import com.fulfillmen.support.alibaba.api.response.feedback.LogisticsOrderSyncResponse;
import com.fulfillmen.support.alibaba.api.response.feedback.OrderRelationWriteResponse;
import com.fulfillmen.support.alibaba.api.response.feedback.OrderSyncResponse;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceException;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import com.fulfillmen.support.alibaba.service.IFeedbackService;
import java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.util.MultiValueMap;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

/**
 * 1688回传数据相关API Mock测试
 *
 * <AUTHOR>
 * @created 2025-01-14
 */
@Slf4j
@Tag("mock")
@Tag("feedback")
class FeedbackAPIMockTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    @Autowired
    private IFeedbackService feedbackService;

    @MockBean
    private FeedbackAPI feedbackAPI;

    @Test
    @Tag("write")
    void shouldSaveAccountBusiness() {
        log.info("{} 开始测试保存账号所属业务线 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new AccountBusinessSaveRequestRecord("test_account", "sea");
        log.info("{}请求参数: account={}, business={}", LOG_ITEM, request.account(), request.business());

        var result = new AccountBusinessSaveResponse.Result();
        result.setSaveResult(true);

        var response = new AccountBusinessSaveResponse();
        response.setResult(result);

        when(feedbackAPI.saveAccountBusiness(any(String.class), any(MultiValueMap.class))).thenReturn(Mono
            .just(response));

        // When
        var responseFlux = feedbackService.saveAccountBusiness(request);

        // Then
        StepVerifier.create(responseFlux).assertNext(res -> {
            assertThat(res).isNotNull();
            assertThat(res.getResult()).isNotNull();
            assertThat(res.getResult().getSaveResult()).isTrue();
            log.info("{}响应结果: {}", LOG_ITEM, res);
        }).verifyComplete();

        log.info("{} 测试保存账号所属业务线完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleSaveAccountBusinessValidationError() {
        log.info("{} 开始测试保存账号所属业务线参数验证 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new AccountBusinessSaveRequestRecord("", // 账号为空,触发验证失败
            "invalid"); // 无效的业务线
        log.info("{}请求参数: account={}, business={}", LOG_ITEM, request.account(), request.business());

        when(feedbackAPI.saveAccountBusiness(any(String.class), any(MultiValueMap.class))).thenReturn(Mono
            .error(new AlibabaServiceValidationException("参数校验失败")));

        // When
        var responseFlux = feedbackService.saveAccountBusiness(request);

        // Then
        StepVerifier.create(responseFlux).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException;
        }).verify();

        log.info("{} 测试保存账号所属业务线参数验证完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleSaveAccountBusinessError() {
        log.info("{} 开始测试保存账号所属业务线业务错误 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new AccountBusinessSaveRequestRecord("non_exist_account", // 不存在的账号
            "sea");
        log.info("{}请求参数: account={}, business={}", LOG_ITEM, request.account(), request.business());

        var result = new AccountBusinessSaveResponse.Result();
        result.setSaveResult(false); // 业务处理失败

        var response = new AccountBusinessSaveResponse();
        response.setResult(result);

        when(feedbackAPI.saveAccountBusiness(any(String.class), any(MultiValueMap.class))).thenReturn(Mono
            .just(response));

        // When
        var responseFlux = feedbackService.saveAccountBusiness(request);

        // Then
        StepVerifier.create(responseFlux).assertNext(res -> {
            assertThat(res).isNotNull();
            assertThat(res.getResult()).isNotNull();
            assertThat(res.getResult().getSaveResult()).isFalse();
            log.info("{}响应结果: {}", LOG_ITEM, res);
        }).verifyComplete();

        log.info("{} 测试保存账号所属业务线业务错误完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleSaveAccountBusinessSystemError() {
        log.info("{} 开始测试保存账号所属业务线系统异常 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new AccountBusinessSaveRequestRecord("test_account", "sea");
        log.info("{}请求参数: account={}, business={}", LOG_ITEM, request.account(), request.business());

        when(feedbackAPI.saveAccountBusiness(any(String.class), any(MultiValueMap.class))).thenReturn(Mono
            .error(new AlibabaServiceException("系统异常")));

        // When
        var responseFlux = feedbackService.saveAccountBusiness(request);

        // Then
        StepVerifier.create(responseFlux).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceException;
        }).verify();

        log.info("{} 测试保存账号所属业务线系统异常完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("write")
    void shouldSyncLogisticsOrder() {
        log.info("{} 开始测试国家站物流单回传 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var orderLogisticsParam = new LogisticsOrderSyncRequestRecord.OrderLogisticsParam("test_logistics_id", "test_company_code", "test_company_name", 123456789L, "test_bill_no", "2025-01-15 12:00:00",
            "ACCEPT", "[{\"action\":\"已揽收\",\"operateTime\":\"2025-01-15 12:00:00\",\"operator\":\"test_company_name\"}]");

        var request = new LogisticsOrderSyncRequestRecord(orderLogisticsParam);
        log.info("{}请求参数: logisticsId={}, orderId={}, status={}", LOG_ITEM, request.orderLogisticsParam()
            .logisticsId(), request.orderLogisticsParam().orderId(), request.orderLogisticsParam().status());

        var result = new LogisticsOrderSyncResponse.LogisticsOrderSyncResult();
        result.setSuccess(true);

        var response = new LogisticsOrderSyncResponse();
        response.setResult(result);

        when(feedbackAPI.syncLogisticsOrder(any(String.class), any(MultiValueMap.class))).thenReturn(Mono
            .just(response));

        // When
        var responseFlux = feedbackService.syncLogisticsOrder(request);

        // Then
        StepVerifier.create(responseFlux).assertNext(res -> {
            assertThat(res).isNotNull();
            assertThat(res.getResult()).isNotNull();
            assertThat(res.getResult().getSuccess()).isTrue();
            log.info("{}响应结果: {}", LOG_ITEM, res);
        }).verifyComplete();

        log.info("{} 测试国家站物流单回传完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("write")
    void shouldWriteOrderRelation() {
        log.info("{} 开始测试回传订单映射关系 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var orderRelationParam = new OrderRelationWriteRequestRecord.OrderRelationParam("test_order_id", "test_parent_order_id", 123456789L, 987654321L);

        var request = new OrderRelationWriteRequestRecord(orderRelationParam);
        log.info("{}请求参数: orderId={}, parentOrderId={}, purchaseOrderId={}, purchaseParentOrderId={}", LOG_ITEM, request
            .orderRelationParam()
            .orderId(), request.orderRelationParam().parentOrderId(), request.orderRelationParam()
                .purchaseOrderId(), request.orderRelationParam().purchaseParentOrderId());

        var result = new OrderRelationWriteResponse.Result();
        result.setSuccess(true);

        var response = new OrderRelationWriteResponse();
        response.setResult(result);

        when(feedbackAPI.writeOrderRelation(any(String.class), any(MultiValueMap.class))).thenReturn(Mono
            .just(response));

        // When
        var responseFlux = feedbackService.writeOrderRelation(request);

        // Then
        StepVerifier.create(responseFlux).assertNext(res -> {
            assertThat(res).isNotNull();
            assertThat(res.getResult()).isNotNull();
            assertThat(res.getResult().getSuccess()).isTrue();
            log.info("{}响应结果: {}", LOG_ITEM, res);
        }).verifyComplete();

        log.info("{} 测试回传订单映射关系完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("write")
    void shouldSyncOrder() {
        log.info("{} 开始测试同步下游销售订单 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var subOrderParam = new OrderSyncRequestRecord.SubOrderParam("test_sub_order_id", "test_product_id", "test_product_name", "test_skul_id", "test_sku_name", 1L, 100L, "NO_REFUND");

        var orderParam = new OrderSyncRequestRecord.OrderParam("test_order_id", "test_product_id", "test_product_name", "test_skul_id", "test_sku_name", 1L, "2025-01-15 12:00:00", "test_member_id",
            "2025-01-15 12:00:00", "2025-01-15 12:00:00", 100L, "NO_REFUND", "PAID", Collections
                .singletonList(subOrderParam));

        var request = new OrderSyncRequestRecord(orderParam);
        log.info("{}请求参数: orderId={}, productId={}, status={}", LOG_ITEM, request.orderParam().orderId(), request
            .orderParam()
            .productId(), request.orderParam().status());

        var result = new OrderSyncResponse.Result();
        result.setSuccess(true);

        var response = new OrderSyncResponse();
        response.setResult(result);

        when(feedbackAPI.syncOrder(any(String.class), any(MultiValueMap.class))).thenReturn(Mono.just(response));

        // When
        var responseFlux = feedbackService.syncOrder(request);

        // Then
        StepVerifier.create(responseFlux).assertNext(res -> {
            assertThat(res).isNotNull();
            assertThat(res.getResult()).isNotNull();
            assertThat(res.getResult().getSuccess()).isTrue();
            log.info("{}响应结果: {}", LOG_ITEM, res);
        }).verifyComplete();

        log.info("{} 测试同步下游销售订单完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }
}