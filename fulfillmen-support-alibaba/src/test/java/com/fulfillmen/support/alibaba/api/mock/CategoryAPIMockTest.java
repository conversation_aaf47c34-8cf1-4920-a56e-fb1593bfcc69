/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.mock;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.api.CategoryAPI;
import com.fulfillmen.support.alibaba.api.request.category.CategoryAttributeRequestRecord;
import com.fulfillmen.support.alibaba.api.request.category.CategoryRequestRecord;
import com.fulfillmen.support.alibaba.api.request.category.CategoryTranslationByIdRequestRecord;
import com.fulfillmen.support.alibaba.api.request.category.CategoryTranslationRequestRecord;
import com.fulfillmen.support.alibaba.api.response.category.CategoryAttributeResponse;
import com.fulfillmen.support.alibaba.api.response.category.CategoryResponse;
import com.fulfillmen.support.alibaba.api.response.category.CategoryResponse.CategoryInfo;
import com.fulfillmen.support.alibaba.api.response.category.CategoryTranslationByIdResponse;
import com.fulfillmen.support.alibaba.api.response.category.CategoryTranslationResponse;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import com.fulfillmen.support.alibaba.service.ICategoryService;
import java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

/**
 * 类目API Mock测试类
 *
 * <AUTHOR>
 * @created 2025-01-16
 */
@Slf4j
class CategoryAPIMockTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    @Autowired
    private ICategoryService categoryService;

    @MockBean
    private CategoryAPI categoryAPI;

    @Test
    void shouldGetCategory() {
        log.info("{} 开始测试获取类目信息 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new CategoryRequestRecord("126506005");
        log.info("{}请求参数: categoryId={}", LOG_ITEM, request.categoryId());

        var mockResponse = new CategoryResponse();
        mockResponse.setSucces("true");
        mockResponse.setCategoryInfo(Collections.singletonList(new CategoryInfo()));
        when(categoryAPI.getCategory(any(), any())).thenReturn(Mono.just(mockResponse));

        // When
        var response = categoryService.getCategory(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getSucces()).isEqualTo("true");
            assertThat(result.getCategoryInfo()).isNotEmpty();

            log.info("{}响应结果: {}", LOG_ITEM, result);
        }).verifyComplete();

        log.info("{} 测试获取类目信息完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetTranslationById() {
        log.info("{} 开始测试获取类目翻译信息(通过ID) {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new CategoryTranslationByIdRequestRecord(126506005L, "en", "test_member_id", null);
        log.info("{}请求参数: categoryId={}, language={}, outMemberId={}", LOG_ITEM, request.categoryId(), request
            .language(), request.outMemberId());

        var mockResponse = new CategoryTranslationByIdResponse();
        mockResponse.setSucces("true");
        when(categoryAPI.getTranslationById(any(), any())).thenReturn(Mono.just(mockResponse));

        // When
        var response = categoryService.getTranslationById(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getSucces()).isEqualTo("true");

            log.info("{}响应结果: {}", LOG_ITEM, result);
        }).verifyComplete();

        log.info("{} 测试获取类目翻译信息(通过ID)完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetTranslation() {
        log.info("{} 开始测试获取类目翻译信息(通过关键词) {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new CategoryTranslationRequestRecord("电子产品", "en", null);
        log.info("{}请求参数: cateName={}, language={}", LOG_ITEM, request.cateName(), request.language());

        var mockResponse = new CategoryTranslationResponse();
        var resultWrapper = new CategoryTranslationResponse.ResultWrapper();
        resultWrapper.setSuccess(true);
        mockResponse.setResult(resultWrapper);
        when(categoryAPI.getTranslation(any(), any())).thenReturn(Mono.just(mockResponse));

        // When
        var response = categoryService.getTranslation(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getResult()).isNotNull();
            assertThat(result.getResult().getSuccess()).isTrue();

            log.info("{}响应结果: {}", LOG_ITEM, result);
        }).verifyComplete();

        log.info("{} 测试获取类目翻译信息(通过关键词)完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetAttributes() {
        log.info("{} 开始测试获取类目属性信息 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new CategoryAttributeRequestRecord(126506005L, "1688", null);
        log.info("{}请求参数: categoryId={}, webSite={}", LOG_ITEM, request.categoryId(), request.webSite());

        var mockResponse = new CategoryAttributeResponse();
        mockResponse.setSuccess(true);
        when(categoryAPI.getAttributes(any(), any())).thenReturn(Mono.just(mockResponse));

        // When
        var response = categoryService.getAttributes(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isTrue();

            log.info("{}响应结果: {}", LOG_ITEM, result);
        }).verifyComplete();

        log.info("{} 测试获取类目属性信息完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleCategoryValidationError() {
        log.info("{} 开始测试类目查询参数校验失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new CategoryRequestRecord("");
        log.info("{}请求参数: categoryId={}", LOG_ITEM, request.categoryId());

        // When & Then
        StepVerifier.create(categoryService.getCategory(request)).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException && throwable.getMessage()
                .contains("类目ID不能为空");
        }).verify();

        verify(categoryAPI, never()).getCategory(any(), any());
        log.info("{} 测试类目查询参数校验失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleTranslationByIdValidationError() {
        log.info("{} 开始测试类目翻译查询(通过ID)参数校验失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new CategoryTranslationByIdRequestRecord(null, "", "", null);
        log.info("{}请求参数: categoryId={}, language={}, outMemberId={}", LOG_ITEM, request.categoryId(), request
            .language(), request.outMemberId());

        // When & Then
        StepVerifier.create(categoryService.getTranslationById(request)).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException && throwable.getMessage()
                .contains("类目ID不能为空");
        }).verify();

        verify(categoryAPI, never()).getTranslationById(any(), any());
        log.info("{} 测试类目翻译查询(通过ID)参数校验失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleTranslationValidationError() {
        log.info("{} 开始测试类目翻译查询(通过关键词)参数校验失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new CategoryTranslationRequestRecord("", "", null);
        log.info("{}请求参数: cateName={}, language={}", LOG_ITEM, request.cateName(), request.language());

        // When & Then
        StepVerifier.create(categoryService.getTranslation(request)).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException && throwable.getMessage()
                .contains("类目名称不能为空");
        }).verify();

        verify(categoryAPI, never()).getTranslation(any(), any());
        log.info("{} 测试类目翻译查询(通过关键词)参数校验失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleAttributesValidationError() {
        log.info("{} 开始测试类目属性查询参数校验失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new CategoryAttributeRequestRecord(null, "", null);
        log.info("{}请求参数: categoryId={}, webSite={}", LOG_ITEM, request.categoryId(), request.webSite());

        // When & Then
        StepVerifier.create(categoryService.getAttributes(request)).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException && throwable.getMessage()
                .contains("类目ID不能为空");
        }).verify();

        verify(categoryAPI, never()).getAttributes(any(), any());
        log.info("{} 测试类目属性查询参数校验失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }
}