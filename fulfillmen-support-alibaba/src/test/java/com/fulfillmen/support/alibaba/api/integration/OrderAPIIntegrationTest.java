/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.integration;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.fail;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.api.integration.data.BaseInfoExpected;
import com.fulfillmen.support.alibaba.api.integration.data.LogisticsExpected;
import com.fulfillmen.support.alibaba.api.integration.data.OrderBizInfoExpected;
import com.fulfillmen.support.alibaba.api.integration.data.OrderRateInfoExpected;
import com.fulfillmen.support.alibaba.api.integration.data.OrderTestData;
import com.fulfillmen.support.alibaba.api.integration.data.ProductItemsExpected;
import com.fulfillmen.support.alibaba.api.integration.data.TradeTermsExpected;
import com.fulfillmen.support.alibaba.api.request.model.TradeFastAddress;
import com.fulfillmen.support.alibaba.api.request.model.TradeFastCargo;
import com.fulfillmen.support.alibaba.api.request.order.OrderBuyerListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.order.OrderBuyerListRequestRecord.OrderStatusEnum;
import com.fulfillmen.support.alibaba.api.request.order.OrderCancelRequestRecord;
import com.fulfillmen.support.alibaba.api.request.order.OrderCreateRequestRecord;
import com.fulfillmen.support.alibaba.api.request.order.OrderDetailRequestRecord;
import com.fulfillmen.support.alibaba.api.request.order.OrderPreviewRequestRecord;
import com.fulfillmen.support.alibaba.api.request.order.TradeFeedbackRequestRecord;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeProductItem;
import com.fulfillmen.support.alibaba.api.response.order.OrderCreateResponse;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse;
import com.fulfillmen.support.alibaba.service.IOrderService;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.test.StepVerifier;

/**
 * 1688订单API 集成测试 这个测试类会真实调用1688 API，请确保有正确的配置和测试数据
 *
 * <AUTHOR>
 * @created 2025-01-10
 */
@Slf4j
@Tag("integration")
class OrderAPIIntegrationTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    /**
     * 收货地址 默认定义在 中田仓库
     */
    private static final TradeFastAddress address = TradeFastAddress.builder().fullName("中田 10002")
        .mobile("18575207550")
        .phone("0517-88990077")
        .postCode("516000")
        .provinceText("广东省")
        .cityText("惠州市")
        .areaText("惠城区")
        .address("广东省 惠州市 惠城区 汝湖镇 金泽物流园二期一号楼四楼-14101")
        .districtCode("441302")
        .build();

    // 使用默认收货地址
    // 这里使用的应该是 租户设定的仓库中转收货地址
    private static final OrderCreateRequestRecord.AddressParamRecord addressParam = OrderCreateRequestRecord.AddressParamRecord
        .builder()
        .fullName("中田 10002")
        .mobile("18575207550")
        .phone("0517-88990077")
        .postCode("516000")
        .cityText("惠州市")
        .provinceText("广东省")
        .areaText("惠城区")
        .address("广东省 惠州市 惠城区 汝湖镇 金泽物流园二期一号楼四楼-14101")
        .districtCode("441302")
        .build();
    @Autowired
    private IOrderService orderService;

    public static void main(String[] args) {
        OrderCreateRequestRecord.CargoParamRecord cargoParamRecord = OrderCreateRequestRecord.CargoParamRecord.builder()
            .offerId(914405447049L)
            .specId("ee70f58dd71fdb67a6c3d1c3b3f8a97f")
            .quantity(100.0)
            .build();

        // 使用 Hutool JSONUtil
        System.out.println("Hutool JSONUtil: " + JSONUtil.toJsonStr(cargoParamRecord));

        // 使用 Jackson ObjectMapper
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String jacksonJson = objectMapper.writeValueAsString(cargoParamRecord);
            System.out.println("Jackson ObjectMapper: " + jacksonJson);
        } catch (Exception e) {
            System.err.println("Jackson 序列化失败: " + e.getMessage());
        }
    }

    /**
     * 生成商品列表
     *
     * @param cargoList 商品列表
     */
    private static void generateCargoList(List<TradeFastCargo> cargoList) {
        // 1688 offer Id
        long offerId = 914405447049L;
        // List<String> specIds = List.of("ee70f58dd71fdb67a6c3d1c3b3f8a97f",
        // "a493142e11a7c0f76e842249665dca0c");
        // 商品 订单预览
        cargoList.add(TradeFastCargo.builder().offerId(offerId).specId("ee70f58dd71fdb67a6c3d1c3b3f8a97f")
            .quantity(100.0).build());
        cargoList.add(TradeFastCargo.builder().offerId(offerId).specId("a493142e11a7c0f76e842249665dca0c")
            .quantity(100.0).build());
    }

    /**
     * 生成创建订单的商品列表
     *
     */
    private List<OrderCreateRequestRecord.CargoParamRecord> generateCreateOrderCargoList() {
        // 1688 offer Id
        long offerId = 914405447049L;
        return Lists.newArrayList(OrderCreateRequestRecord.CargoParamRecord.builder()
            .offerId(offerId)
            .specId("ee70f58dd71fdb67a6c3d1c3b3f8a97f")
            .quantity(100.0)
            .build(),
            OrderCreateRequestRecord.CargoParamRecord.builder()
                .offerId(offerId)
                .specId("a493142e11a7c0f76e842249665dca0c")
                .quantity(100.0)
                .build());
    }

    @Test
    void shouldPreviewOrder() {

        List<TradeFastCargo> cargoList = new ArrayList<>();
        generateCargoList(cargoList);
        var request = OrderPreviewRequestRecord.of(address, cargoList, "general");

        log.info("{}开始预览订单集成测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}流程类型: {}", LOG_ITEM, request.flow());
        log.info("{}收货人: {}", LOG_ITEM, request.addressParam().getFullName());
        log.info("{}商品ID: {}", LOG_ITEM, request.cargoParamList().get(0).getOfferId());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = orderService.previewOrder(request);

        // Then
        StepVerifier.create(response).assertNext(previewResponse -> {
            // 记录API调用指标
            recordMetrics("PreviewOrder", startTime, previewResponse != null && previewResponse.getSuccess());

            assertThat(previewResponse).isNotNull();
            if (previewResponse.getSuccess()) {
                assertThat(previewResponse.getOrderPreviewResult()).isNotEmpty();

                var resultList = previewResponse.getOrderPreviewResult();
                resultList.forEach(result -> {
                    assertThat(result.getStatus()).isTrue();
                    assertThat(result.getSumPayment()).isPositive();
                    assertThat(result.getCargoList()).isNotEmpty();

                    // 打印预览结果
                    log.info("{}预览订单结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
                    log.info("{}是否成功: {}", LOG_ITEM, previewResponse.getSuccess());
                    log.info("{}订单总金额: {} 元", LOG_ITEM, result.getSumPayment() / 100);
                    log.info("{}商品总金额: {} 元", LOG_ITEM, result.getSumPaymentNoCarriage() / 100);
                    log.info("{}附加费金额: {} 元", LOG_ITEM,
                        result.getAdditionalFee() != null ? result.getAdditionalFee().intValue() / 100 : 0);
                    log.info("{}优惠金额: {} 元", LOG_ITEM,
                        result.getDiscountFee() != null ? result.getDiscountFee().intValue() / 100 : 0);
                    log.info("{}可用交易方式: {}", LOG_ITEM, result.getTradeModeNameList());
                    log.info("{}运费: {}", LOG_ITEM, result.getSumCarriage() != null ? result.getSumCarriage() / 100 : 0);

                    if (CollectionUtil.isNotEmpty(result.getCargoList())) {
                        result.getCargoList().forEach(cargo -> {
                            log.info("{}商品信息{}", LOG_SEPARATOR, LOG_SEPARATOR);
                            log.info("{}offerId: {}", LOG_ITEM, cargo.getOfferId());
                            log.info("{}specId: {}", LOG_ITEM, cargo.getSpecId());
                            log.info("{}skuId: {}", LOG_ITEM, cargo.getSkuId());
                            log.info("{}OpenOfferId: {}", LOG_ITEM, cargo.getOpenOfferId());
                            log.info("{}商品数量: {}", LOG_ITEM, cargo.getAmount());
                            log.info("{}商品单价: {}", LOG_ITEM, cargo.getFinalUnitPrice());
                        });

                    }
                });

            } else {
                // 如果API调用失败，打印错误信息
                log.info("{}预览订单错误结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
                log.info("{}是否成功: {}", LOG_ITEM, previewResponse.getSuccess());
                log.info("{}错误码: {}", LOG_ITEM, previewResponse.getErrorCode());
                log.info("{}错误信息: {}", LOG_ITEM, previewResponse.getErrorMessage());
                assertThat(previewResponse.getOrderPreviewResult()).isNotEmpty();
                var resultList = previewResponse.getOrderPreviewResult();
                resultList.forEach(result -> {
                    log.info("{}预览结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
                    log.info("{}预览结果状态: {}", LOG_ITEM, result.getStatus());
                    log.info("{}预览结果消息: {}", LOG_ITEM, result.getMessage());
                    log.info("{}预览结果代码: {}", LOG_ITEM, result.getResultCode());
                });
            }
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("PreviewOrder");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldCreateCrossBorderOrder() {
        var cargoList = generateCreateOrderCargoList();
        // 使用 saleproxy 交易方式，需要确保与供应商有代销关系
        // var request = OrderCreateRequestRecord.of("general", "cross", addressParam,
        // cargoList);
        var request = OrderCreateRequestRecord.builder()
            .flow("general")
            // .isvBizType("cross")
            .addressParam(addressParam)
            .cargoParamList(cargoList)
            .outOrderId("test1234567")
            .build();
        log.info("{}开始创建跨境订单集成测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}流程类型: {}", LOG_ITEM, request.flow());
        log.info("{}收货人: {}", LOG_ITEM, request.addressParam().fullName());
        log.info("{}商品ID: {}", LOG_ITEM, request.cargoParamList().get(0).offerId());

        resetMetrics();
        long startTime = System.currentTimeMillis();

        var response = orderService.createCrossOrder(request);

        StepVerifier.create(response).assertNext(createResponse -> {
            recordMetrics("CreateCrossBorderOrder", startTime, createResponse != null && createResponse.getSuccess());

            assertThat(createResponse).isNotNull();

            if (createResponse.getSuccess()) {
                assertThat(createResponse.getResult()).isNotNull();
                var result = createResponse.getResult();
                assertThat(result.getOrderId()).isNotNull();
                assertThat(result.getTotalSuccessAmount()).isPositive();

                log.info("{}创建订单成功{}", LOG_SEPARATOR, LOG_SEPARATOR);
                log.info("{}订单ID: {}", LOG_ITEM, result.getOrderId());
                log.info("{}总金额: {} 元", LOG_ITEM, result.getTotalSuccessAmount() / 100);
            } else {
                log.info("{}创建订单失败{}", LOG_SEPARATOR, LOG_SEPARATOR);
                log.info("{}错误码: {}", LOG_ITEM, createResponse.getErrorCode());
                log.info("{}错误信息: {}", LOG_ITEM, createResponse.getErrorMessage());
                assertThat(createResponse.getErrorCode()).isNotNull();
            }
        }).verifyComplete();

        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("CreateCrossBorderOrder");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetOrderDetail() {
        // Setup: 使用真实的订单ID进行测试
//        Long orderId = 2626652931211540788L;
        Long orderId = 2829032257200540788L;
        assertThat(orderId).isNotNull();
        log.info("{}开始获取订单详情集成测试，订单ID: {}{}", LOG_SEPARATOR, orderId, LOG_SEPARATOR);

        var request = OrderDetailRequestRecord.of("1688", orderId, true, true, true);
        log.info("{}请求参数{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}订单ID: {}", LOG_ITEM, request.orderId());
        log.info("{}包含物流信息: {}", LOG_ITEM, true);
        log.info("{}包含发票信息: {}", LOG_ITEM, true);
        log.info("{}包含评价信息: {}", LOG_ITEM, true);

        resetMetrics();
        long startTime = System.currentTimeMillis();

        var response = orderService.getOrderDetail(request);

        StepVerifier.create(response).assertNext(detailResponse -> {
            recordMetrics("GetOrderDetail", startTime, detailResponse != null && detailResponse.getResult() != null);

            // 基础响应验证
            assertThat(detailResponse).isNotNull();
            assertThat(detailResponse.getResult()).isNotNull();
            assertThat(detailResponse.getSuccess()).isTrue();

            var result = detailResponse.getResult();

            // ==================== 基础信息验证 ====================
            log.info("{}基础信息验证{}", LOG_SEPARATOR, LOG_SEPARATOR);
            assertThat(result.getBaseInfo()).isNotNull();
            assertThat(result.getBaseInfo().getId()).isEqualTo(orderId);
            assertThat(result.getBaseInfo().getStatus()).isEqualTo("success");
            assertThat(result.getBaseInfo().getTotalAmount()).isEqualTo(new BigDecimal("36800"));
            assertThat(result.getBaseInfo().getBuyerId()).isEqualTo("b2b-2207416548807a4d12");
            assertThat(result.getBaseInfo().getSellerId()).isEqualTo("b2b-22069085846688ab1b");
            assertThat(result.getBaseInfo().getBusinessType()).isEqualTo("cb");
            assertThat(result.getBaseInfo().getOutOrderId()).isEqualTo("12727");

            log.info("{}订单ID: {}", LOG_ITEM, result.getBaseInfo().getId());
            log.info("{}订单状态: {}", LOG_ITEM, result.getBaseInfo().getStatus());
            log.info("{}订单总金额: {} 元", LOG_ITEM, result.getBaseInfo().getTotalAmount().divide(new BigDecimal("100")));
            log.info("{}买家ID: {}", LOG_ITEM, result.getBaseInfo().getBuyerId());
            log.info("{}卖家ID: {}", LOG_ITEM, result.getBaseInfo().getSellerId());
            log.info("{}业务类型: {}", LOG_ITEM, result.getBaseInfo().getBusinessType());
            log.info("{}外部订单号: {}", LOG_ITEM, result.getBaseInfo().getOutOrderId());

            // 日期字段验证
            assertThat(result.getBaseInfo().getCreateTime()).isNotNull();
            assertThat(result.getBaseInfo().getPayTime()).isNotNull();
            assertThat(result.getBaseInfo().getCompleteTime()).isNotNull();
            log.info("{}创建时间: {}", LOG_ITEM, result.getBaseInfo().getCreateTime());
            log.info("{}支付时间: {}", LOG_ITEM, result.getBaseInfo().getPayTime());
            log.info("{}完成时间: {}", LOG_ITEM, result.getBaseInfo().getCompleteTime());

            // 联系人信息验证
            if (result.getBaseInfo().getBuyerContact() != null) {
                log.info("{}买家联系人: {}", LOG_ITEM, result.getBaseInfo().getBuyerContact().getName());
                log.info("{}买家公司: {}", LOG_ITEM, result.getBaseInfo().getBuyerContact().getCompanyName());
            }
            if (result.getBaseInfo().getSellerContact() != null) {
                log.info("{}卖家联系人: {}", LOG_ITEM, result.getBaseInfo().getSellerContact().getName());
                log.info("{}卖家公司: {}", LOG_ITEM, result.getBaseInfo().getSellerContact().getCompanyName());
            }

            // ==================== 商品项目验证 ====================
            log.info("{}商品项目验证{}", LOG_SEPARATOR, LOG_SEPARATOR);
            assertThat(result.getProductItems()).isNotNull();
            assertThat(result.getProductItems()).hasSize(12); // 根据实际API响应，有12个商品项目

            // 验证所有商品状态都是成功
            assertThat(result.getProductItems()).allMatch(item -> "success".equals(item.getStatus()));

            // 计算商品总金额验证
            var totalItemAmount = result.getProductItems().stream()
                .map(TradeProductItem::getItemAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            log.info("{}商品项目总数: {}", LOG_ITEM, result.getProductItems().size());
            log.info("{}商品总金额: {} 元", LOG_ITEM, totalItemAmount);

            // 验证主要商品类型
            var productNames = result.getProductItems().stream()
                .map(TradeProductItem::getName)
                .distinct()
                .toList();
            log.info("{}商品类型: {}", LOG_ITEM, productNames);

            // 验证包含预期的商品类型
            assertThat(productNames).anyMatch(name -> name.contains("配件链接"));
            assertThat(productNames).anyMatch(name -> name.contains("接地床笠"));
            assertThat(productNames).anyMatch(name -> name.contains("接地床单"));

            // 详细验证前几个商品项目
            var firstItem = result.getProductItems().get(0);
            assertThat(firstItem.getProductId()).isEqualTo(879003654249L);
            assertThat(firstItem.getSkuId()).isEqualTo(5719623040970L);
            assertThat(firstItem.getQuantity()).isEqualTo(20);
            assertThat(firstItem.getPrice()).isEqualTo(new BigDecimal("20"));

            log.info("{}第一个商品详情{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}商品名称: {}", LOG_ITEM, firstItem.getName());
            log.info("{}商品ID: {}", LOG_ITEM, firstItem.getProductId());
            log.info("{}SKU ID: {}", LOG_ITEM, firstItem.getSkuId());
            log.info("{}数量: {}", LOG_ITEM, firstItem.getQuantity());
            log.info("{}单价: {} 元", LOG_ITEM, firstItem.getPrice());
            log.info("{}商品金额: {} 元", LOG_ITEM, firstItem.getItemAmount());

            // ==================== 物流信息验证 ====================
            log.info("{}物流信息验证{}", LOG_SEPARATOR, LOG_SEPARATOR);
            assertThat(result.getNativeLogistics()).isNotNull();
            assertThat(result.getNativeLogistics().getContactPerson()).isEqualTo("中田 12727");
            assertThat(result.getNativeLogistics().getMobile()).isEqualTo("18124000751");
            assertThat(result.getNativeLogistics().getProvince()).isEqualTo("广东省");
            assertThat(result.getNativeLogistics().getCity()).isEqualTo("惠州市");
            assertThat(result.getNativeLogistics().getArea()).isEqualTo("惠城区");

            log.info("{}收货人: {}", LOG_ITEM, result.getNativeLogistics().getContactPerson());
            log.info("{}收货手机: {}", LOG_ITEM, result.getNativeLogistics().getMobile());
            log.info("{}收货地址: {} {} {}", LOG_ITEM,
                result.getNativeLogistics().getProvince(),
                result.getNativeLogistics().getCity(),
                result.getNativeLogistics().getArea());
            log.info("{}详细地址: {}", LOG_ITEM, result.getNativeLogistics().getAddress());

            // 物流明细验证
            assertThat(result.getNativeLogistics().getLogisticsItems()).isNotNull();
            assertThat(result.getNativeLogistics().getLogisticsItems()).hasSize(1);

            var logisticsItem = result.getNativeLogistics().getLogisticsItems().get(0);
            assertThat(logisticsItem.getLogisticsCompanyName()).isEqualTo("跨越速运");
            assertThat(logisticsItem.getLogisticsBillNo()).isEqualTo("KY4000784755080");
            assertThat(logisticsItem.getStatus()).isEqualTo("alreadysend");
            assertThat(logisticsItem.getDeliveredTime()).isNotNull();

            log.info("{}物流公司: {}", LOG_ITEM, logisticsItem.getLogisticsCompanyName());
            log.info("{}运单号: {}", LOG_ITEM, logisticsItem.getLogisticsBillNo());
            log.info("{}物流状态: {}", LOG_ITEM, logisticsItem.getStatus());
            log.info("{}发货时间: {}", LOG_ITEM, logisticsItem.getDeliveredTime());
            log.info("{}发货地址: {} {} {}", LOG_ITEM,
                logisticsItem.getFromProvince(),
                logisticsItem.getFromCity(),
                logisticsItem.getFromArea());

            // ==================== 交易条款验证 ====================
            log.info("{}交易条款验证{}", LOG_SEPARATOR, LOG_SEPARATOR);
            assertThat(result.getTradeTerms()).isNotNull();
            assertThat(result.getTradeTerms()).hasSize(1);

            var tradeTerm = result.getTradeTerms().get(0);
            assertThat(tradeTerm.getPayStatus()).isEqualTo("6"); // 已付款
            assertThat(tradeTerm.getPhasAmount()).isEqualTo(new BigDecimal("36800"));
            assertThat(tradeTerm.getPayTime()).isNotNull();
            assertThat(tradeTerm.getPayWayDesc()).isEqualTo("支付平台");

            log.info("{}支付状态: {}", LOG_ITEM, tradeTerm.getPayStatus());
            log.info("{}支付金额: {} 元", LOG_ITEM, tradeTerm.getPhasAmount().divide(new BigDecimal("100")));
            log.info("{}支付时间: {}", LOG_ITEM, tradeTerm.getPayTime());
            log.info("{}支付方式: {}", LOG_ITEM, tradeTerm.getPayWayDesc());

            // ==================== 订单业务信息验证 ====================
            log.info("{}订单业务信息验证{}", LOG_SEPARATOR, LOG_SEPARATOR);
            assertThat(result.getOrderBizInfo()).isNotNull();
            assertThat(result.getOrderBizInfo().getOdsCyd()).isFalse(); // 不是采源宝订单
            assertThat(result.getOrderBizInfo().getCreditOrder()).isFalse(); // 不是诚e赊订单
            assertThat(result.getOrderBizInfo().getDropShipping()).isFalse(); // 不是dropshipping订单
            assertThat(result.getOrderBizInfo().getShippingInsurance()).isEqualTo("givenByAnXinGou");

            log.info("{}是否采源宝订单: {}", LOG_ITEM, result.getOrderBizInfo().getOdsCyd());
            log.info("{}是否诚e赊订单: {}", LOG_ITEM, result.getOrderBizInfo().getCreditOrder());
            log.info("{}是否dropshipping: {}", LOG_ITEM, result.getOrderBizInfo().getDropShipping());
            log.info("{}运费险信息: {}", LOG_ITEM, result.getOrderBizInfo().getShippingInsurance());

            // ==================== 订单评价信息验证 ====================
            if (result.getOrderRateInfo() != null) {
                log.info("{}订单评价信息{}", LOG_SEPARATOR, LOG_SEPARATOR);
                log.info("{}买家评价状态: {}", LOG_ITEM, result.getOrderRateInfo().getBuyerRateStatus());
                log.info("{}卖家评价状态: {}", LOG_ITEM, result.getOrderRateInfo().getSellerRateStatus());
            }

            // ==================== 数据一致性验证 ====================
            log.info("{}数据一致性验证{}", LOG_SEPARATOR, LOG_SEPARATOR);

            // 验证支付金额与订单总金额一致
            assertThat(tradeTerm.getPhasAmount().longValue()).isEqualTo(result.getBaseInfo().getTotalAmount().longValue());
            log.info("{}支付金额与订单总金额一致性验证通过", LOG_ITEM);

            // 验证日期逻辑合理性
            assertThat(result.getBaseInfo().getPayTime()).isAfter(result.getBaseInfo().getCreateTime());
            assertThat(result.getBaseInfo().getCompleteTime()).isAfter(result.getBaseInfo().getPayTime());
            log.info("{}订单时间逻辑验证通过: 创建 < 支付 < 完成", LOG_ITEM);

            // 验证物流发货时间在支付之后
            assertThat(logisticsItem.getDeliveredTime()).isAfter(result.getBaseInfo().getPayTime());
            log.info("{}物流时间逻辑验证通过: 发货时间在支付之后", LOG_ITEM);

            log.info("{}订单详情验证全部通过{}", LOG_SEPARATOR, LOG_SEPARATOR);

        }).verifyComplete();

        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GetOrderDetail");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    /**
     * 提供订单详情测试数据
     * <p>
     * 支持多种验证模式：
     * 1. BASIC_ONLY - 仅验证API调用成功和基础响应结构
     * 2. STRUCTURE_VALIDATION - 验证数据结构完整性和业务逻辑合理性
     * 3. DETAILED_VALIDATION - 进行精确的数值匹配验证（适用于已知订单）
     * <p>
     * 🔧 使用说明：
     * - 要测试您的订单ID，请修改下面的 testOrderIds 列表
     * - 基础验证和结构验证适用于任意有效订单ID
     * - 详细验证需要提供具体的预期数据
     * <p>
     * 📝 示例：
     * 将您的订单ID添加到 testOrderIds 列表中，例如：
     * List<Long> testOrderIds = Arrays.asList(
     * 1234567890123456789L, // 您的订单ID1
     * 9876543210987654321L // 您的订单ID2
     * );
     */
    static Stream<OrderTestData> orderDetailTestDataProvider() {
        // ==================== 配置您的订单ID ====================
        // 在这里添加您要测试的订单ID
        List<Long> testOrderIds = Arrays.asList(
            2829032257200540788L,  // 示例订单1
            2626652931211540788L   // 示例订单2
        // 添加您的订单ID:
        // YOUR_ORDER_ID_1,
        // YOUR_ORDER_ID_2
        );

        // ==================== 生成测试数据 ====================
        List<OrderTestData> testDataList = new ArrayList<>();

        // 为每个订单ID生成基础验证测试
        testOrderIds.forEach(orderId -> testDataList.add(OrderTestData.basicValidation(orderId,
            "基础验证 - 订单ID: " + orderId)));

        // 可选：为特定订单添加结构验证
        if (!testOrderIds.isEmpty()) {
            Long firstOrderId = testOrderIds.get(0);
            testDataList.add(OrderTestData.structureValidation(firstOrderId,
                "结构验证 - 订单ID: " + firstOrderId));
        }

        // 可选：添加详细验证（需要已知订单的具体数据）
        // testDataList.add(createKnownOrderDetailed());

        return testDataList.stream();
    }

    /**
     * 创建单一商品类型订单测试数据（示例）
     * 注意：这是示例数据，实际订单可能不存在
     */
    @SuppressWarnings("unused")
    private static OrderTestData createSingleProductTypeOrder() {
        return OrderTestData.builder()
            .orderId(1234567890123456789L)
            .description("示例订单 - 单一商品类型订单")
            .baseInfo(BaseInfoExpected.builder()
                .status("success")
                .totalAmount("15000")
                .buyerID("b2b-example-buyer")
                .sellerID("b2b-example-seller")
                .businessType("cb")
                .outOrderId("EX001")
                .buyerContact("张三", "示例买家公司")
                .sellerContact("李四", "示例卖家公司")
                .validateTimestamps(true)
                .build())
            .productItems(ProductItemsExpected.builder()
                .expectedCount(5)
                .expectedProductTypes("示例商品")
                .firstItem(123456789L, 987654321L, 10, new BigDecimal("150"))
                .validateTotalAmount(true)
                .build())
            .logistics(LogisticsExpected.builder()
                .contactPerson("张三")
                .mobile("***********")
                .province("北京市")
                .city("北京市")
                .area("朝阳区")
                .address("示例地址123号")
                .logisticsCompanyName("顺丰速运")
                .logisticsBillNo("SF1234567890")
                .status("alreadysend")
                .fromProvince("广东省")
                .fromCity("深圳市")
                .fromArea("南山区")
                .build())
            .tradeTerms(TradeTermsExpected.builder()
                .payStatus("6")
                .phasAmount(15000L)
                .payWayDesc("支付宝")
                .build())
            .orderBizInfo(OrderBizInfoExpected.builder()
                .odsCyd(false)
                .creditOrder(false)
                .dropshipping(false)
                .shippingInsurance("none")
                .build())
            .orderRateInfo(OrderRateInfoExpected.builder()
                .buyerRateStatus(3)
                .sellerRateStatus(4)
                .build())
            .build();
    }

    /**
     * 创建跨境订单测试数据（示例）
     * 注意：这是示例数据，实际订单可能不存在
     */
    @SuppressWarnings("unused")
    private static OrderTestData createCrossBorderOrder() {
        return OrderTestData.builder()
            .orderId(9876543210987654L)
            .description("示例跨境订单 - 包含多种规格的商品")
            .baseInfo(BaseInfoExpected.builder()
                .status("success")
                .totalAmount("58900")
                .buyerID("b2b-cross-border-buyer")
                .sellerID("b2b-cross-border-seller")
                .businessType("cb")
                .outOrderId("CB2024001")
                .buyerContact("王五", "跨境贸易有限公司")
                .sellerContact("赵六", "出口制造有限公司")
                .validateTimestamps(true)
                .build())
            .productItems(ProductItemsExpected.builder()
                .expectedCount(8)
                .expectedProductTypes("电子产品", "配件")
                .firstItem(555666777L, 111222333L, 50, new BigDecimal("118"))
                .validateTotalAmount(true)
                .build())
            .logistics(LogisticsExpected.builder()
                .contactPerson("王五")
                .mobile("***********")
                .province("上海市")
                .city("上海市")
                .area("浦东新区")
                .address("自贸区示例路456号")
                .logisticsCompanyName("中通快递")
                .logisticsBillNo("ZT9876543210")
                .status("alreadysend")
                .fromProvince("江苏省")
                .fromCity("苏州市")
                .fromArea("工业园区")
                .build())
            .tradeTerms(TradeTermsExpected.builder()
                .payStatus("6")
                .phasAmount(58900L)
                .payWayDesc("银行转账")
                .build())
            .orderBizInfo(OrderBizInfoExpected.builder()
                .odsCyd(true)
                .creditOrder(false)
                .dropshipping(true)
                .shippingInsurance("givenBySeller")
                .build())
            .orderRateInfo(OrderRateInfoExpected.builder()
                .buyerRateStatus(5)
                .sellerRateStatus(5)
                .build())
            .build();
    }

    /**
     * 参数化测试：获取订单详情
     * 支持多个不同订单的数据验证，使用数据驱动的方式进行测试
     */
    @ParameterizedTest(name = "订单详情测试 - {1}")
    @MethodSource("orderDetailTestDataProvider")
    void shouldGetOrderDetailParameterized(OrderTestData testData) {
        log.info("{}开始获取订单详情集成测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}测试描述: {}", LOG_ITEM, testData.description());
        log.info("{}订单ID: {}", LOG_ITEM, testData.orderId());

        var request = OrderDetailRequestRecord.of("1688", testData.orderId(), true, true, true);
        log.info("{}请求参数{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}订单ID: {}", LOG_ITEM, request.orderId());
        log.info("{}包含物流信息: {}", LOG_ITEM, true);
        log.info("{}包含发票信息: {}", LOG_ITEM, true);
        log.info("{}包含评价信息: {}", LOG_ITEM, true);

        resetMetrics();
        long startTime = System.currentTimeMillis();

        var response = orderService.getOrderDetail(request);

        StepVerifier.create(response).assertNext(detailResponse -> {
            recordMetrics("GetOrderDetailParameterized", startTime, detailResponse != null && detailResponse.getResult() != null);

            // 基础响应验证
            assertThat(detailResponse).isNotNull();
            assertThat(detailResponse.getResult()).isNotNull();
            assertThat(detailResponse.getSuccess()).isTrue();

            var result = detailResponse.getResult();

            // 使用测试数据进行验证
            validateOrderDetail(result, testData);

        }).verifyComplete();

        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GetOrderDetailParameterized");
        log.info("{}测试完成: {}{}", LOG_SEPARATOR, testData.description(), LOG_SEPARATOR);
    }

    @Test
    void shouldGetBuyerOrderList() {
        // Setup: Create an order to ensure the list is not empty
        // createOrderForTest();

        var request = OrderBuyerListRequestRecord.builder()
            .page(1)
            .pageSize(10)
            .orderStatus(OrderStatusEnum.WAIT_SELLER_SEND.getValue())
            .build();
        log.info("{}开始获取订单列表集成测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}页码: {}", LOG_ITEM, request.page());
        log.info("{}每页数量: {}", LOG_ITEM, request.pageSize());

        resetMetrics();
        long startTime = System.currentTimeMillis();

        var response = orderService.getBuyerOrderList(request);

        StepVerifier.create(response).assertNext(listResponse -> {
            recordMetrics("GetBuyerOrderList", startTime, listResponse != null && listResponse.getErrorCode() == null);
            assertThat(listResponse).isNotNull();
            assertThat(listResponse.getErrorCode()).isNull();
            assertThat(listResponse.getTotalRecord()).isGreaterThan(0);
            assertThat(listResponse.getResult()).isNotEmpty();

            log.info("{}获取订单列表成功{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}订单总数: {}", LOG_ITEM, listResponse.getTotalRecord());
            log.info("{}返回订单数: {}", LOG_ITEM, listResponse.getResult().size());
            log.info("{}第一个订单ID: {}", LOG_ITEM, listResponse.getResult().get(0).getBaseInfo().getId());

        }).verifyComplete();

        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GetBuyerOrderList");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldAddFeedbackToOrder() {
        // Setup: Create an order to add feedback to
        String orderId = createOrderForTest();
        assertThat(orderId).isNotNull();

        String feedbackMessage = "This is a test feedback from integration test.";
        var request = TradeFeedbackRequestRecord.of(orderId, feedbackMessage);

        log.info("{}开始为订单添加留言集成测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}订单ID: {}", LOG_ITEM, request.orderId());
        log.info("{}留言内容: {}", LOG_ITEM, request.feedback());

        resetMetrics();
        long startTime = System.currentTimeMillis();

        var response = orderService.addFeedback(request);

        StepVerifier.create(response).assertNext(feedbackResponse -> {
            recordMetrics("AddFeedback", startTime, feedbackResponse != null && feedbackResponse.getSuccess());
            assertThat(feedbackResponse).isNotNull();
            assertThat(feedbackResponse.getSuccess()).isTrue();
            assertThat(feedbackResponse.getResult()).isNotNull();
            assertThat(feedbackResponse.getResult().getSuccess()).isTrue();

            log.info("{}添加留言成功{}", LOG_SEPARATOR, LOG_SEPARATOR);
        }).verifyComplete();

        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("AddFeedback");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldCancelOrder() {
        // Setup: Create an order to cancel
        String orderId = createOrderForTest();
        assertThat(orderId).isNotNull();

        var request = OrderCancelRequestRecord.of("1688", Long.valueOf(orderId), "buyerCancel");

        log.info("{}开始取消订单集成测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}订单ID: {}", LOG_ITEM, request.tradeId());
        log.info("{}取消原因: {}", LOG_ITEM, request.cancelReason());

        resetMetrics();
        long startTime = System.currentTimeMillis();

        var response = orderService.cancelOrder(request);

        StepVerifier.create(response).assertNext(cancelResponse -> {
            recordMetrics("CancelOrder", startTime, cancelResponse != null && cancelResponse.getSuccess());
            assertThat(cancelResponse).isNotNull();
            assertThat(cancelResponse.getSuccess()).isTrue();
            assertThat(cancelResponse.getErrorCode()).isNull();

            log.info("{}取消订单成功{}", LOG_SEPARATOR, LOG_SEPARATOR);
        }).verifyComplete();

        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("CancelOrder");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    /**
     * 为测试创建一个订单，并返回订单ID
     *
     * @return 订单ID
     */
    private String createOrderForTest() {
        var cargoList = generateCreateOrderCargoList();
        var request = OrderCreateRequestRecord.of("saleproxy", "cross", addressParam, cargoList);

        // 在测试中，可以使用block()来简化异步代码的处理
        OrderCreateResponse response = orderService.createCrossOrder(request).block();

        if (response != null && response.getSuccess() && response.getResult() != null
            && response.getResult().getOrderId() != null) {
            return response.getResult().getOrderId();
        }

        // 如果订单创建失败，则测试失败
        fail("为测试创建订单失败. Response: " + response);
        return null; // 不可达
    }

    /**
     * 验证订单详情数据
     * 根据验证模式进行不同级别的验证
     *
     * @param result   实际的订单详情响应
     * @param testData 预期的测试数据
     */
    private void validateOrderDetail(OrderDetailResponse.OrderDetail result, OrderTestData testData) {
        log.info("{}开始验证订单详情 - 验证模式: {}{}", LOG_SEPARATOR, testData.validationMode(), LOG_SEPARATOR);

        // 所有模式都进行基础验证
        validateBasicStructure(result, testData);

        // 根据验证模式进行不同级别的验证
        switch (testData.validationMode()) {
            case BASIC_ONLY -> {
                log.info("{}基础验证模式 - 仅验证API响应结构{}", LOG_ITEM, LOG_SEPARATOR);
                displayOrderSummary(result);
            }
            case STRUCTURE_VALIDATION -> {
                log.info("{}结构验证模式 - 验证数据完整性和业务逻辑{}", LOG_ITEM, LOG_SEPARATOR);
                validateStructureAndLogic(result, testData);
                displayDetailedOrderInfo(result);
            }
            case DETAILED_VALIDATION -> {
                log.info("{}详细验证模式 - 进行精确数值匹配{}", LOG_ITEM, LOG_SEPARATOR);
                validateStructureAndLogic(result, testData);
                validateDetailedMatching(result, testData);
                displayDetailedOrderInfo(result);
            }
        }

        log.info("{}订单详情验证完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    /**
     * 基础结构验证 - 所有模式都需要通过
     */
    private void validateBasicStructure(OrderDetailResponse.OrderDetail result, OrderTestData testData) {
        log.info("{}基础结构验证{}", LOG_SEPARATOR, LOG_SEPARATOR);

        // 验证基础响应结构
        assertThat(result).isNotNull();
        assertThat(result.getBaseInfo()).isNotNull();
        assertThat(result.getBaseInfo().getId()).isEqualTo(testData.orderId());

        log.info("{}订单ID验证通过: {}", LOG_ITEM, result.getBaseInfo().getId());

        // 验证必要字段存在
        assertThat(result.getBaseInfo().getStatus()).isNotNull();
        assertThat(result.getBaseInfo().getTotalAmount()).isNotNull();
        assertThat(result.getBaseInfo().getBuyerId()).isNotNull();
        assertThat(result.getBaseInfo().getSellerId()).isNotNull();

        log.info("{}基础字段验证通过", LOG_ITEM);
    }

    /**
     * 结构和业务逻辑验证
     */
    private void validateStructureAndLogic(OrderDetailResponse.OrderDetail result, OrderTestData testData) {
        log.info("{}结构和业务逻辑验证{}", LOG_SEPARATOR, LOG_SEPARATOR);

        // 验证数据类型和合理性
        assertThat(result.getBaseInfo().getTotalAmount()).isPositive();
        assertThat(result.getBaseInfo().getStatus()).isNotBlank();

        // 验证时间逻辑（如果时间字段存在）
        if (result.getBaseInfo().getCreateTime() != null && result.getBaseInfo().getPayTime() != null) {
            assertThat(result.getBaseInfo().getPayTime()).isAfterOrEqualTo(result.getBaseInfo().getCreateTime());
            log.info("{}时间逻辑验证通过: 支付时间在创建时间之后", LOG_ITEM);
        }

        if (result.getBaseInfo().getPayTime() != null && result.getBaseInfo().getCompleteTime() != null) {
            assertThat(result.getBaseInfo().getCompleteTime()).isAfterOrEqualTo(result.getBaseInfo().getPayTime());
            log.info("{}时间逻辑验证通过: 完成时间在支付时间之后", LOG_ITEM);
        }

        // 验证商品项目结构
        if (result.getProductItems() != null) {
            assertThat(result.getProductItems()).isNotEmpty();
            result.getProductItems().forEach(item -> {
                assertThat(item.getProductId()).isNotNull();
                assertThat(item.getQuantity()).isPositive();
                if (item.getPrice() != null) {
                    assertThat(item.getPrice()).isPositive();
                }
            });
            log.info("{}商品项目结构验证通过: {} 个商品", LOG_ITEM, result.getProductItems().size());
        }

        // 验证交易条款结构
        if (result.getTradeTerms() != null && !result.getTradeTerms().isEmpty()) {
            result.getTradeTerms().forEach(term -> {
                if (term.getPhasAmount() != null) {
                    assertThat(term.getPhasAmount()).isPositive();
                }
            });
            log.info("{}交易条款结构验证通过", LOG_ITEM);
        }

        log.info("{}结构和业务逻辑验证完成", LOG_ITEM);
    }

    /**
     * 显示订单概要信息 - 用于基础验证模式
     */
    private void displayOrderSummary(OrderDetailResponse.OrderDetail result) {
        log.info("{}订单概要信息{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}订单ID: {}", LOG_ITEM, result.getBaseInfo().getId());
        log.info("{}订单状态: {}", LOG_ITEM, result.getBaseInfo().getStatus());
        log.info("{}订单总金额: {} 元", LOG_ITEM,
            result.getBaseInfo().getTotalAmount().divide(new BigDecimal("100")));
        log.info("{}买家ID: {}", LOG_ITEM, result.getBaseInfo().getBuyerId());
        log.info("{}卖家ID: {}", LOG_ITEM, result.getBaseInfo().getSellerId());
        log.info("{}业务类型: {}", LOG_ITEM, result.getBaseInfo().getBusinessType());

        if (result.getProductItems() != null) {
            log.info("{}商品项目数量: {}", LOG_ITEM, result.getProductItems().size());
        }

        if (result.getNativeLogistics() != null) {
            log.info("{}收货人: {}", LOG_ITEM, result.getNativeLogistics().getContactPerson());
            log.info("{}收货地址: {} {} {}", LOG_ITEM,
                result.getNativeLogistics().getProvince(),
                result.getNativeLogistics().getCity(),
                result.getNativeLogistics().getArea());
        }
    }

    /**
     * 显示详细订单信息 - 用于结构验证和详细验证模式
     */
    private void displayDetailedOrderInfo(OrderDetailResponse.OrderDetail result) {
        displayOrderSummary(result);

        // 显示时间信息
        if (result.getBaseInfo().getCreateTime() != null) {
            log.info("{}创建时间: {}", LOG_ITEM, result.getBaseInfo().getCreateTime());
        }
        if (result.getBaseInfo().getPayTime() != null) {
            log.info("{}支付时间: {}", LOG_ITEM, result.getBaseInfo().getPayTime());
        }
        if (result.getBaseInfo().getCompleteTime() != null) {
            log.info("{}完成时间: {}", LOG_ITEM, result.getBaseInfo().getCompleteTime());
        }

        // 显示联系人信息
        if (result.getBaseInfo().getBuyerContact() != null) {
            log.info("{}买家联系人: {}", LOG_ITEM, result.getBaseInfo().getBuyerContact().getName());
            log.info("{}买家公司: {}", LOG_ITEM, result.getBaseInfo().getBuyerContact().getCompanyName());
        }
        if (result.getBaseInfo().getSellerContact() != null) {
            log.info("{}卖家联系人: {}", LOG_ITEM, result.getBaseInfo().getSellerContact().getName());
            log.info("{}卖家公司: {}", LOG_ITEM, result.getBaseInfo().getSellerContact().getCompanyName());
        }

        // 显示商品详情
        if (result.getProductItems() != null && !result.getProductItems().isEmpty()) {
            log.info("{}商品详情{}", LOG_SEPARATOR, LOG_SEPARATOR);
            var productNames = result.getProductItems().stream()
                .map(TradeProductItem::getName)
                .distinct()
                .toList();
            log.info("{}商品类型: {}", LOG_ITEM, productNames);

            var totalItemAmount = result.getProductItems().stream()
                .map(TradeProductItem::getItemAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            log.info("{}商品总金额: {} 元", LOG_ITEM, totalItemAmount);

            // 显示第一个商品的详细信息
            var firstItem = result.getProductItems().get(0);
            log.info("{}第一个商品详情{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}商品名称: {}", LOG_ITEM, firstItem.getName());
            log.info("{}商品ID: {}", LOG_ITEM, firstItem.getProductId());
            log.info("{}SKU ID: {}", LOG_ITEM, firstItem.getSkuId());
            log.info("{}数量: {}", LOG_ITEM, firstItem.getQuantity());
            log.info("{}单价: {} 元", LOG_ITEM, firstItem.getPrice());
            log.info("{}商品金额: {} 元", LOG_ITEM, firstItem.getItemAmount());
        }

        // 显示物流信息
        if (result.getNativeLogistics() != null) {
            log.info("{}物流详情{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}收货手机: {}", LOG_ITEM, result.getNativeLogistics().getMobile());
            log.info("{}详细地址: {}", LOG_ITEM, result.getNativeLogistics().getAddress());

            if (result.getNativeLogistics().getLogisticsItems() != null &&
                !result.getNativeLogistics().getLogisticsItems().isEmpty()) {
                var logisticsItem = result.getNativeLogistics().getLogisticsItems().get(0);
                log.info("{}物流公司: {}", LOG_ITEM, logisticsItem.getLogisticsCompanyName());
                log.info("{}运单号: {}", LOG_ITEM, logisticsItem.getLogisticsBillNo());
                log.info("{}物流状态: {}", LOG_ITEM, logisticsItem.getStatus());
                if (logisticsItem.getDeliveredTime() != null) {
                    log.info("{}发货时间: {}", LOG_ITEM, logisticsItem.getDeliveredTime());
                }
            }
        }

        // 显示交易条款
        if (result.getTradeTerms() != null && !result.getTradeTerms().isEmpty()) {
            log.info("{}交易条款{}", LOG_SEPARATOR, LOG_SEPARATOR);
            var tradeTerm = result.getTradeTerms().get(0);
            log.info("{}支付状态: {}", LOG_ITEM, tradeTerm.getPayStatus());
            if (tradeTerm.getPhasAmount() != null) {
                log.info("{}支付金额: {} 元", LOG_ITEM, tradeTerm.getPhasAmount().divide(new BigDecimal("100")));
            }
            if (tradeTerm.getPayTime() != null) {
                log.info("{}支付时间: {}", LOG_ITEM, tradeTerm.getPayTime());
            }
            log.info("{}支付方式: {}", LOG_ITEM, tradeTerm.getPayWayDesc());
        }

        // 显示业务信息
        if (result.getOrderBizInfo() != null) {
            log.info("{}业务信息{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否采源宝订单: {}", LOG_ITEM, result.getOrderBizInfo().getOdsCyd());
            log.info("{}是否诚e赊订单: {}", LOG_ITEM, result.getOrderBizInfo().getCreditOrder());
            log.info("{}是否dropshipping: {}", LOG_ITEM, result.getOrderBizInfo().getDropShipping());
            log.info("{}运费险信息: {}", LOG_ITEM, result.getOrderBizInfo().getShippingInsurance());
        }

        // 显示评价信息
        if (result.getOrderRateInfo() != null) {
            log.info("{}评价信息{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}买家评价状态: {}", LOG_ITEM, result.getOrderRateInfo().getBuyerRateStatus());
            log.info("{}卖家评价状态: {}", LOG_ITEM, result.getOrderRateInfo().getSellerRateStatus());
        }
    }

    /**
     * 详细匹配验证 - 仅用于详细验证模式
     */
    private void validateDetailedMatching(OrderDetailResponse.OrderDetail result, OrderTestData testData) {
        log.info("{}详细匹配验证{}", LOG_SEPARATOR, LOG_SEPARATOR);

        // 只有在提供了详细验证数据时才进行精确匹配
        if (testData.baseInfo() != null) {
            validateBaseInfoDetailed(result, testData.baseInfo());
        }

        if (testData.productItems() != null) {
            validateProductItemsDetailed(result, testData.productItems());
        }

        if (testData.logistics() != null) {
            validateLogisticsDetailed(result, testData.logistics());
        }

        if (testData.tradeTerms() != null) {
            validateTradeTermsDetailed(result, testData.tradeTerms());
        }

        if (testData.orderBizInfo() != null) {
            validateOrderBizInfoDetailed(result, testData.orderBizInfo());
        }

        if (testData.orderRateInfo() != null) {
            validateOrderRateInfoDetailed(result, testData.orderRateInfo());
        }

        log.info("{}详细匹配验证完成", LOG_ITEM);
    }

    /**
     * 基础信息详细验证
     */
    private void validateBaseInfoDetailed(OrderDetailResponse.OrderDetail result, BaseInfoExpected baseInfo) {
        log.info("{}基础信息详细验证{}", LOG_SEPARATOR, LOG_SEPARATOR);

        if (baseInfo.status() != null) {
            assertThat(result.getBaseInfo().getStatus()).isEqualTo(baseInfo.status());
        }
        if (baseInfo.totalAmount() != null) {
            assertThat(result.getBaseInfo().getTotalAmount()).isEqualTo(baseInfo.totalAmount());
        }
        if (baseInfo.buyerID() != null) {
            assertThat(result.getBaseInfo().getBuyerId()).isEqualTo(baseInfo.buyerID());
        }
        if (baseInfo.sellerID() != null) {
            assertThat(result.getBaseInfo().getSellerId()).isEqualTo(baseInfo.sellerID());
        }
        if (baseInfo.businessType() != null) {
            assertThat(result.getBaseInfo().getBusinessType()).isEqualTo(baseInfo.businessType());
        }
        if (baseInfo.outOrderId() != null) {
            assertThat(result.getBaseInfo().getOutOrderId()).isEqualTo(baseInfo.outOrderId());
        }

        // 时间字段验证
        if (baseInfo.validateTimestamps()) {
            assertThat(result.getBaseInfo().getCreateTime()).isNotNull();
            assertThat(result.getBaseInfo().getPayTime()).isNotNull();
            assertThat(result.getBaseInfo().getCompleteTime()).isNotNull();
        }

        // 联系人信息验证
        if (baseInfo.buyerContact() != null && result.getBaseInfo().getBuyerContact() != null) {
            assertThat(result.getBaseInfo().getBuyerContact().getName()).isEqualTo(baseInfo.buyerContact().name());
            assertThat(result.getBaseInfo().getBuyerContact().getCompanyName()).isEqualTo(baseInfo.buyerContact().companyName());
        }

        if (baseInfo.sellerContact() != null && result.getBaseInfo().getSellerContact() != null) {
            assertThat(result.getBaseInfo().getSellerContact().getName()).isEqualTo(baseInfo.sellerContact().name());
            assertThat(result.getBaseInfo().getSellerContact().getCompanyName()).isEqualTo(baseInfo.sellerContact().companyName());
        }

        log.info("{}基础信息详细验证通过", LOG_ITEM);
    }

    /**
     * 商品项目详细验证
     */
    private void validateProductItemsDetailed(OrderDetailResponse.OrderDetail result, ProductItemsExpected productItems) {
        log.info("{}商品项目详细验证{}", LOG_SEPARATOR, LOG_SEPARATOR);

        if (result.getProductItems() != null) {
            if (productItems.expectedCount() > 0) {
                assertThat(result.getProductItems()).hasSize(productItems.expectedCount());
            }

            // 验证所有商品状态都是成功
            assertThat(result.getProductItems()).allMatch(item -> "success".equals(item.getStatus()));

            // 验证商品类型
            if (productItems.expectedProductTypes() != null && !productItems.expectedProductTypes().isEmpty()) {
                var productNames = result.getProductItems().stream()
                    .map(TradeProductItem::getName)
                    .distinct()
                    .toList();

                for (String expectedType : productItems.expectedProductTypes()) {
                    assertThat(productNames).anyMatch(name -> name.contains(expectedType));
                }
            }

            // 验证第一个商品的详细信息
            if (productItems.firstItem() != null && !result.getProductItems().isEmpty()) {
                var firstItem = result.getProductItems().get(0);
                var expectedFirstItem = productItems.firstItem();

                assertThat(firstItem.getProductId()).isEqualTo(expectedFirstItem.productID());
                assertThat(firstItem.getSkuId()).isEqualTo(expectedFirstItem.skuID());
                assertThat(firstItem.getQuantity()).isEqualTo(expectedFirstItem.quantity());
                assertThat(firstItem.getPrice()).isEqualTo(expectedFirstItem.price());
            }

            // 计算商品总金额验证
            if (productItems.validateTotalAmount()) {
                var totalItemAmount = result.getProductItems().stream()
                    .map(TradeProductItem::getItemAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                assertThat(totalItemAmount).isPositive();
            }
        }

        log.info("{}商品项目详细验证通过", LOG_ITEM);
    }

    /**
     * 物流信息详细验证
     */
    private void validateLogisticsDetailed(OrderDetailResponse.OrderDetail result, LogisticsExpected logistics) {
        log.info("{}物流信息详细验证{}", LOG_SEPARATOR, LOG_SEPARATOR);

        if (result.getNativeLogistics() != null) {
            if (logistics.contactPerson() != null) {
                assertThat(result.getNativeLogistics().getContactPerson()).isEqualTo(logistics.contactPerson());
            }
            if (logistics.mobile() != null) {
                assertThat(result.getNativeLogistics().getMobile()).isEqualTo(logistics.mobile());
            }
            if (logistics.province() != null) {
                assertThat(result.getNativeLogistics().getProvince()).isEqualTo(logistics.province());
            }
            if (logistics.city() != null) {
                assertThat(result.getNativeLogistics().getCity()).isEqualTo(logistics.city());
            }
            if (logistics.area() != null) {
                assertThat(result.getNativeLogistics().getArea()).isEqualTo(logistics.area());
            }

            // 物流明细验证
            if (result.getNativeLogistics().getLogisticsItems() != null &&
                !result.getNativeLogistics().getLogisticsItems().isEmpty()) {
                var logisticsItem = result.getNativeLogistics().getLogisticsItems().get(0);

                if (logistics.logisticsCompanyName() != null) {
                    assertThat(logisticsItem.getLogisticsCompanyName()).isEqualTo(logistics.logisticsCompanyName());
                }
                if (logistics.logisticsBillNo() != null) {
                    assertThat(logisticsItem.getLogisticsBillNo()).isEqualTo(logistics.logisticsBillNo());
                }
                if (logistics.status() != null) {
                    assertThat(logisticsItem.getStatus()).isEqualTo(logistics.status());
                }
                if (logistics.fromProvince() != null) {
                    assertThat(logisticsItem.getFromProvince()).isEqualTo(logistics.fromProvince());
                }
                if (logistics.fromCity() != null) {
                    assertThat(logisticsItem.getFromCity()).isEqualTo(logistics.fromCity());
                }
                if (logistics.fromArea() != null) {
                    assertThat(logisticsItem.getFromArea()).isEqualTo(logistics.fromArea());
                }
            }
        }

        log.info("{}物流信息详细验证通过", LOG_ITEM);
    }

    /**
     * 交易条款详细验证
     */
    private void validateTradeTermsDetailed(OrderDetailResponse.OrderDetail result, TradeTermsExpected tradeTerms) {
        log.info("{}交易条款详细验证{}", LOG_SEPARATOR, LOG_SEPARATOR);

        if (result.getTradeTerms() != null && !result.getTradeTerms().isEmpty()) {
            var tradeTerm = result.getTradeTerms().get(0);

            if (tradeTerms.payStatus() != null) {
                assertThat(tradeTerm.getPayStatus()).isEqualTo(tradeTerms.payStatus());
            }
            if (tradeTerms.phasAmount() != null) {
                assertThat(tradeTerm.getPhasAmount()).isEqualTo(tradeTerms.phasAmount());
            }
            if (tradeTerms.payWayDesc() != null) {
                assertThat(tradeTerm.getPayWayDesc()).isEqualTo(tradeTerms.payWayDesc());
            }
        }

        log.info("{}交易条款详细验证通过", LOG_ITEM);
    }

    /**
     * 订单业务信息详细验证
     */
    private void validateOrderBizInfoDetailed(OrderDetailResponse.OrderDetail result, OrderBizInfoExpected orderBizInfo) {
        log.info("{}订单业务信息详细验证{}", LOG_SEPARATOR, LOG_SEPARATOR);

        if (result.getOrderBizInfo() != null) {
            assertThat(result.getOrderBizInfo().getOdsCyd()).isEqualTo(orderBizInfo.odsCyd());
            assertThat(result.getOrderBizInfo().getCreditOrder()).isEqualTo(orderBizInfo.creditOrder());
            assertThat(result.getOrderBizInfo().getDropShipping()).isEqualTo(orderBizInfo.dropshipping());
            if (orderBizInfo.shippingInsurance() != null) {
                assertThat(result.getOrderBizInfo().getShippingInsurance()).isEqualTo(orderBizInfo.shippingInsurance());
            }
        }

        log.info("{}订单业务信息详细验证通过", LOG_ITEM);
    }

    /**
     * 订单评价信息详细验证
     */
    private void validateOrderRateInfoDetailed(OrderDetailResponse.OrderDetail result, OrderRateInfoExpected orderRateInfo) {
        log.info("{}订单评价信息详细验证{}", LOG_SEPARATOR, LOG_SEPARATOR);

        if (result.getOrderRateInfo() != null) {
            if (orderRateInfo.buyerRateStatus() != null) {
                assertThat(result.getOrderRateInfo().getBuyerRateStatus()).isEqualTo(orderRateInfo.buyerRateStatus());
            }
            if (orderRateInfo.sellerRateStatus() != null) {
                assertThat(result.getOrderRateInfo().getSellerRateStatus()).isEqualTo(orderRateInfo.sellerRateStatus());
            }
        }

        log.info("{}订单评价信息详细验证通过", LOG_ITEM);
    }
}
