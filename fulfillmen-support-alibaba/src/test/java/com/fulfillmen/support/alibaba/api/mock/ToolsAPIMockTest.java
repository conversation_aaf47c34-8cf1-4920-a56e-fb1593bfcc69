/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.mock;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import cn.hutool.json.JSONUtil;
import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.api.ToolsAPI;
import com.fulfillmen.support.alibaba.api.request.tools.LoginIdEncryptRequestRecord;
import com.fulfillmen.support.alibaba.api.request.tools.PoolProductPullRequestRecord;
import com.fulfillmen.support.alibaba.api.request.tools.RelationAddRequestRecord;
import com.fulfillmen.support.alibaba.api.request.tools.WangwangNickDecryptRequestRecord;
import com.fulfillmen.support.alibaba.api.request.tools.WangwangUrlRequestRecord;
import com.fulfillmen.support.alibaba.api.response.tools.LoginIdEncryptResponse;
import com.fulfillmen.support.alibaba.api.response.tools.PoolProductPullResponse;
import com.fulfillmen.support.alibaba.api.response.tools.RelationAddResponse;
import com.fulfillmen.support.alibaba.api.response.tools.WangwangNickDecryptResponse;
import com.fulfillmen.support.alibaba.api.response.tools.WangwangUrlResponse;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceException;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import com.fulfillmen.support.alibaba.service.IToolsService;
import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.util.MultiValueMap;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

/**
 * 工具API Mock测试
 *
 * <AUTHOR>
 * @created 2025-01-16
 */
@Slf4j
@Tag("mock")
@Tag("tools")
class ToolsAPIMockTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    @Autowired
    private IToolsService toolsService;

    @MockBean
    private ToolsAPI toolsAPI;

    @Test
    @Tag("read")
    void shouldGetWangwangUrl() {
        log.info("{} 开始测试获取旺旺聊天链接 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = WangwangUrlRequestRecord.of("test_open_uid");
        log.info("{}请求参数: toOpenUid={}", LOG_ITEM, request.toOpenUid());

        var result = new WangwangUrlResponse.Result();
        result.setSuccess(true);
        result.setCode("200");
        result.setResult("https://wangwang.chat.link");

        var response = new WangwangUrlResponse();
        response.setResult(result);

        when(toolsAPI.getWangwangUrl(any(String.class), any(MultiValueMap.class))).thenReturn(Mono.just(response));

        // When
        var responseFlux = toolsService.getWangwangUrl(request);

        // Then
        StepVerifier.create(responseFlux).assertNext(res -> {
            assertThat(res).isNotNull();
            assertThat(res.getResult()).isNotNull();
            assertThat(res.getResult().getSuccess()).isTrue();
            assertThat(res.getResult().getCode()).isEqualTo("200");
            assertThat(res.getResult().getResult()).isEqualTo("https://wangwang.chat.link");
            log.info("{}响应结果: {}", LOG_ITEM, res);
        }).verifyComplete();

        log.info("{} 测试获取旺旺聊天链接完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleGetWangwangUrlValidationError() {
        log.info("{} 开始测试获取旺旺聊天链接参数验证 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = WangwangUrlRequestRecord.of(""); // openUid为空,触发验证失败
        log.info("{}请求参数: toOpenUid={}", LOG_ITEM, request.toOpenUid());

        // When
        var responseFlux = toolsService.getWangwangUrl(request);

        // Then
        StepVerifier.create(responseFlux).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException;
        }).verify();

        log.info("{} 测试获取旺旺聊天链接参数验证完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleGetWangwangUrlSystemError() {
        log.info("{} 开始测试获取旺旺聊天链接系统异常 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = WangwangUrlRequestRecord.of("test_open_uid");
        log.info("{}请求参数: toOpenUid={}", LOG_ITEM, request.toOpenUid());

        when(toolsAPI.getWangwangUrl(any(String.class), any())).thenReturn(Mono
            .error(new AlibabaServiceException("系统异常")));

        // When
        var responseFlux = toolsService.getWangwangUrl(request);

        // Then
        StepVerifier.create(responseFlux).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceException;
        }).verify();

        log.info("{} 测试获取旺旺聊天链接系统异常完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldPullPoolProducts() {
        log.info("{} 开始测试拉取商品池数据 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new PoolProductPullRequestRecord(123L, // offerPoolId
            456L, // cateId
            "test_task_id", // taskId
            "en", // language
            1, // pageNo
            10, // pageSize
            "order1m", // sortField
            "DESC" // sortType
        );
        log.info("{}请求参数: {}", LOG_ITEM, request);

        var productPool = new PoolProductPullResponse.ProductPool();
        productPool.setOfferId(789L);
        productPool.setBizCategoryId("123456");
        productPool.setOfferPoolTotal(100);

        var result = new PoolProductPullResponse.Result();
        result.setSuccess(true);
        result.setCode("S0000");
        result.setMessage("成功");
        result.setResult(Arrays.asList(productPool));

        var response = new PoolProductPullResponse();
        response.setResult(result);

        when(toolsAPI.pullPoolProducts(any(String.class), any(MultiValueMap.class))).thenReturn(Mono.just(response));

        // When
        var responseFlux = toolsService.pullPoolProducts(request);

        // Then
        StepVerifier.create(responseFlux).assertNext(res -> {
            assertThat(res).isNotNull();
            assertThat(res.getResult()).isNotNull();
            assertThat(res.getResult().getSuccess()).isTrue();
            assertThat(res.getResult().getCode()).isEqualTo("S0000");
            assertThat(res.getResult().getResult()).hasSize(1);
            assertThat(res.getResult().getResult().get(0).getOfferId()).isEqualTo(789L);
            log.info("{}响应结果: {}", LOG_ITEM, res);
        }).verifyComplete();

        log.info("{} 测试拉取商品池数据完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandlePullPoolProductsValidationError() {
        log.info("{} 开始测试拉取商品池数据参数验证 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new PoolProductPullRequestRecord(null, // offerPoolId为空,触发验证失败
            null, "", // taskId为空,触发验证失败
            null, 0, // pageNo无效,触发验证失败
            0, // pageSize无效,触发验证失败
            null, null);
        log.info("{}请求参数: {}", LOG_ITEM, request);

        // When
        var responseFlux = toolsService.pullPoolProducts(request);

        // Then
        StepVerifier.create(responseFlux).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException;
        }).verify();

        log.info("{} 测试拉取商品池数据参数验证完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandlePullPoolProductsSystemError() {
        log.info("{} 开始测试拉取商品池数据系统异常 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new PoolProductPullRequestRecord(123L, 456L, "test_task_id", "en", 1, 10, "order1m", "DESC");
        log.info("{}请求参数: {}", LOG_ITEM, request);

        when(toolsAPI.pullPoolProducts(any(String.class), any())).thenReturn(Mono
            .error(new AlibabaServiceException("系统异常")));

        // When
        var responseFlux = toolsService.pullPoolProducts(request);

        // Then
        StepVerifier.create(responseFlux).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceException;
        }).verify();

        log.info("{} 测试拉取商品池数据系统异常完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("write")
    void shouldAddRelation() {
        log.info("{} 开始测试添加买卖家分销关系 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new RelationAddRequestRecord(123456L);
        log.info("{}请求参数: {}", LOG_ITEM, request);

        var result = new RelationAddResponse.Result();
        result.setSuccess(true);
        result.setCode("200");
        result.setMessage("操作成功");
        result.setResult(true);

        var response = new RelationAddResponse();
        response.setResult(result);

        when(toolsAPI.addRelation(any(String.class), any(MultiValueMap.class))).thenReturn(Mono.just(response));

        // When
        var responseFlux = toolsService.addRelation(request);

        // Then
        StepVerifier.create(responseFlux).assertNext(res -> {
            assertThat(res).isNotNull();
            assertThat(res.getResult()).isNotNull();
            assertThat(res.getResult().getSuccess()).isTrue();
            assertThat(res.getResult().getCode()).isEqualTo("200");
            assertThat(res.getResult().getResult()).isTrue();
            log.info("{}响应结果: {}", LOG_ITEM, res);
        }).verifyComplete();

        log.info("{} 测试添加买卖家分销关系完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleAddRelationValidationError() {
        log.info("{} 开始测试添加买卖家分销关系参数验证 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new RelationAddRequestRecord(null); // offerId为空,触发验证失败
        log.info("{}请求参数: {}", LOG_ITEM, request);

        // When
        var responseFlux = toolsService.addRelation(request);

        // Then
        StepVerifier.create(responseFlux).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException;
        }).verify();

        log.info("{} 测试添加买卖家分销关系参数验证完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleAddRelationSystemError() {
        log.info("{} 开始测试添加买卖家分销关系系统异常 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new RelationAddRequestRecord(123456L);
        log.info("{}请求参数: {}", LOG_ITEM, request);

        when(toolsAPI.addRelation(any(String.class), any())).thenReturn(Mono
            .error(new AlibabaServiceException("系统异常")));

        // When
        var responseFlux = toolsService.addRelation(request);

        // Then
        StepVerifier.create(responseFlux).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceException;
        }).verify();

        log.info("{} 测试添加买卖家分销关系系统异常完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldEncryptLoginId() {
        // Given
        var request = LoginIdEncryptRequestRecord.of("test_user");

        log.info("{}开始加密用户loginId测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}用户登录名: {}", LOG_ITEM, request.loginId());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // Mock 成功响应
        LoginIdEncryptResponse successResponse = JSONUtil.toBean("""
            {
                "openUid": "ABCDEF123456"
            }""", LoginIdEncryptResponse.class);

        when(toolsAPI.encryptLoginId(any(), any())).thenReturn(Mono.just(successResponse));

        // When
        var response = toolsService.encryptLoginId(request);

        // Then
        StepVerifier.create(response).assertNext(encryptResponse -> {
            // 记录API调用指标
            recordMetrics("EncryptLoginId", startTime, encryptResponse != null && encryptResponse.getOpenUid() != null);

            assertThat(encryptResponse).isNotNull();
            assertThat(encryptResponse.getOpenUid()).isEqualTo("ABCDEF123456");

            // 打印加密结果
            log.info("{}加密用户loginId结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}OpenUid: {}", LOG_ITEM, encryptResponse.getOpenUid());
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("EncryptLoginId");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleEncryptLoginIdError() {
        // Given
        var request = LoginIdEncryptRequestRecord.of("test_user");

        log.info("{}开始加密用户loginId错误测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}用户登录名: {}", LOG_ITEM, request.loginId());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // Mock API错误
        when(toolsAPI.encryptLoginId(any(), any())).thenReturn(Mono.error(new AlibabaServiceException("加密失败")));

        // When
        var response = toolsService.encryptLoginId(request);

        // Then
        StepVerifier.create(response).expectErrorMatches(throwable -> {
            // 记录API调用指标
            recordMetrics("EncryptLoginIdError", startTime, false);

            // 验证错误类型
            assertThat(throwable).isInstanceOf(AlibabaServiceException.class);
            assertThat(throwable.getMessage()).contains("加密失败");

            // 打印错误信息
            log.info("{}加密用户loginId错误信息{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}错误: {}", LOG_ITEM, throwable.getMessage());
            return true;
        }).verify();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("EncryptLoginIdError");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldDecryptWangwangNick() {
        // Given
        var request = WangwangNickDecryptRequestRecord.of("test_open_uid");

        log.info("{}开始解密旺旺昵称测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}OpenUid: {}", LOG_ITEM, request.openUid());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // Mock 成功响应
        WangwangNickDecryptResponse successResponse = JSONUtil.toBean("""
            {
                "wangwangNick": "test_wangwang_nick"
            }""", WangwangNickDecryptResponse.class);

        when(toolsAPI.decryptWangwangNick(any(), any())).thenReturn(Mono.just(successResponse));

        // When
        var response = toolsService.decryptWangwangNick(request);

        // Then
        StepVerifier.create(response).assertNext(decryptResponse -> {
            // 记录API调用指标
            recordMetrics("DecryptWangwangNick", startTime, decryptResponse != null && decryptResponse
                .getWangwangNick() != null);

            assertThat(decryptResponse).isNotNull();
            assertThat(decryptResponse.getWangwangNick()).isEqualTo("test_wangwang_nick");

            // 打印解密结果
            log.info("{}解密旺旺昵称结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}旺旺昵称: {}", LOG_ITEM, decryptResponse.getWangwangNick());
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("DecryptWangwangNick");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleDecryptWangwangNickValidationError() {
        // Given
        var request = WangwangNickDecryptRequestRecord.of("");

        log.info("{}开始解密旺旺昵称验证错误测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}OpenUid: {}", LOG_ITEM, request.openUid());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = toolsService.decryptWangwangNick(request);

        // Then
        StepVerifier.create(response).expectErrorMatches(throwable -> {
            // 记录API调用指标
            recordMetrics("DecryptWangwangNickValidationError", startTime, false);

            // 验证错误类型
            assertThat(throwable).isInstanceOf(AlibabaServiceValidationException.class);
            assertThat(throwable.getMessage()).contains("待解密的openUid不能为空");

            // 打印错误信息
            log.info("{}解密旺旺昵称错误信息{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}错误: {}", LOG_ITEM, throwable.getMessage());
            return true;
        }).verify();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("DecryptWangwangNickValidationError");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleDecryptWangwangNickError() {
        // Given
        var request = WangwangNickDecryptRequestRecord.of("test_open_uid");

        log.info("{}开始解密旺旺昵称错误测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}OpenUid: {}", LOG_ITEM, request.openUid());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // Mock API错误
        when(toolsAPI.decryptWangwangNick(any(), any())).thenReturn(Mono.error(new AlibabaServiceException("解密失败")));

        // When
        var response = toolsService.decryptWangwangNick(request);

        // Then
        StepVerifier.create(response).expectErrorMatches(throwable -> {
            // 记录API调用指标
            recordMetrics("DecryptWangwangNickError", startTime, false);

            // 验证错误类型
            assertThat(throwable).isInstanceOf(AlibabaServiceException.class);
            assertThat(throwable.getMessage()).contains("解密失败");

            // 打印错误信息
            log.info("{}解密旺旺昵称错误信息{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}错误: {}", LOG_ITEM, throwable.getMessage());
            return true;
        }).verify();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("DecryptWangwangNickError");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }
}