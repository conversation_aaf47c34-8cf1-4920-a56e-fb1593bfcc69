/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.integration.data;

/**
 * 订单详情测试数据容器
 * 支持多种验证模式：基础验证、结构验证、详细验证
 *
 * <AUTHOR>
 * @created 2025-07-12
 */
public record OrderTestData(
    Long orderId,
    String description,
    ValidationMode validationMode,
    BaseInfoExpected baseInfo,
    ProductItemsExpected productItems,
    LogisticsExpected logistics,
    TradeTermsExpected tradeTerms,
    OrderBizInfoExpected orderBizInfo,
    OrderRateInfoExpected orderRateInfo
) {

    /**
     * 验证模式枚举
     */
    public enum ValidationMode {
        /**
         * 基础验证：仅验证API调用成功和响应结构基本完整性
         * 适用于任意有效订单ID，不进行具体数值验证
         */
        BASIC_ONLY,

        /**
         * 结构验证：验证数据结构完整性和业务逻辑合理性
         * 验证字段存在性、数据类型、时间顺序等，但不验证具体数值
         */
        STRUCTURE_VALIDATION,

        /**
         * 详细验证：进行精确的数值匹配验证
         * 适用于已知的特定订单，验证所有预期值
         */
        DETAILED_VALIDATION
    }

    /**
     * 创建基础验证模式的测试数据
     * 适用于任意有效订单ID，仅进行基础验证
     */
    public static OrderTestData basicValidation(Long orderId, String description) {
        return new OrderTestData(orderId, description, ValidationMode.BASIC_ONLY,
            null, null, null, null, null, null);
    }

    /**
     * 创建结构验证模式的测试数据
     * 验证数据结构完整性，但不验证具体数值
     */
    public static OrderTestData structureValidation(Long orderId, String description) {
        return new OrderTestData(orderId, description, ValidationMode.STRUCTURE_VALIDATION,
            null, null, null, null, null, null);
    }

    /**
     * 创建测试数据的构建器
     */
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {

        private Long orderId;
        private String description;
        private ValidationMode validationMode = ValidationMode.DETAILED_VALIDATION; // 默认详细验证保持向后兼容
        private BaseInfoExpected baseInfo;
        private ProductItemsExpected productItems;
        private LogisticsExpected logistics;
        private TradeTermsExpected tradeTerms;
        private OrderBizInfoExpected orderBizInfo;
        private OrderRateInfoExpected orderRateInfo;

        public Builder orderId(Long orderId) {
            this.orderId = orderId;
            return this;
        }

        public Builder description(String description) {
            this.description = description;
            return this;
        }

        public Builder validationMode(ValidationMode validationMode) {
            this.validationMode = validationMode;
            return this;
        }

        public Builder baseInfo(BaseInfoExpected baseInfo) {
            this.baseInfo = baseInfo;
            return this;
        }

        public Builder productItems(ProductItemsExpected productItems) {
            this.productItems = productItems;
            return this;
        }

        public Builder logistics(LogisticsExpected logistics) {
            this.logistics = logistics;
            return this;
        }

        public Builder tradeTerms(TradeTermsExpected tradeTerms) {
            this.tradeTerms = tradeTerms;
            return this;
        }

        public Builder orderBizInfo(OrderBizInfoExpected orderBizInfo) {
            this.orderBizInfo = orderBizInfo;
            return this;
        }

        public Builder orderRateInfo(OrderRateInfoExpected orderRateInfo) {
            this.orderRateInfo = orderRateInfo;
            return this;
        }

        public OrderTestData build() {
            return new OrderTestData(
                orderId, description, validationMode, baseInfo, productItems,
                logistics, tradeTerms, orderBizInfo, orderRateInfo
            );
        }
    }
}
