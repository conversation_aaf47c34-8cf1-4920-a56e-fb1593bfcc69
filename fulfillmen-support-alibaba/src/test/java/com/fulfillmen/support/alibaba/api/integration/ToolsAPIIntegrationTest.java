/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.integration;

import static org.assertj.core.api.Assertions.assertThat;

import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.api.request.tools.LoginIdEncryptRequestRecord;
import com.fulfillmen.support.alibaba.api.request.tools.PoolProductPullRequestRecord;
import com.fulfillmen.support.alibaba.api.request.tools.RelationAddRequestRecord;
import com.fulfillmen.support.alibaba.api.request.tools.WangwangNickDecryptRequestRecord;
import com.fulfillmen.support.alibaba.api.request.tools.WangwangUrlRequestRecord;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import com.fulfillmen.support.alibaba.service.IToolsService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.test.StepVerifier;

/**
 * 工具API集成测试
 *
 * <AUTHOR>
 * @created 2025-01-16
 */
@Slf4j
@Tag("integration")
@Tag("tools")
class ToolsAPIIntegrationTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    @Autowired
    private IToolsService toolsService;

    @Test
    @Tag("read")
    void shouldGetWangwangUrl() {
        log.info("{} 开始测试获取旺旺聊天链接 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
//        var request = WangwangUrlRequestRecord.of("BBB8A9Qxi1_j8N9Xacx-AHAPA");
        var request = WangwangUrlRequestRecord.of("BBBdvTJEG7CP6flCK7LfMwhBw");
        log.info("{}请求参数: toOpenUid={}", LOG_ITEM, request.toOpenUid());

        // When
        var responseFlux = toolsService.getWangwangUrl(request);

        // Then
        StepVerifier.create(responseFlux).assertNext(response -> {
            assertThat(response).isNotNull();
            assertThat(response.getResult()).isNotNull();
            assertThat(response.getResult().getSuccess()).isTrue();
            assertThat(response.getResult().getResult()).isNotEmpty();
            log.info("{}响应结果: {}", LOG_ITEM, response);
        }).verifyComplete();

        log.info("{} 测试获取旺旺聊天链接完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleGetWangwangUrlValidationError() {
        log.info("{} 开始测试获取旺旺聊天链接参数验证 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = WangwangUrlRequestRecord.of("");
        log.info("{}请求参数: toOpenUid={}", LOG_ITEM, request.toOpenUid());

        // When
        var responseFlux = toolsService.getWangwangUrl(request);

        // Then
        StepVerifier.create(responseFlux).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException;
        }).verify();

        log.info("{} 测试获取旺旺聊天链接参数验证完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldPullPoolProducts() {
        log.info("{} 开始测试拉取商品池数据 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new PoolProductPullRequestRecord(123L, 456L, "test_task_id", "en", 1, 10, "order1m", "DESC");
        log.info("{}请求参数: {}", LOG_ITEM, request);

        // When
        var responseFlux = toolsService.pullPoolProducts(request);

        // Then
        StepVerifier.create(responseFlux).assertNext(response -> {
            assertThat(response).isNotNull();
            assertThat(response.getResult()).isNotNull();
            assertThat(response.getResult().getSuccess()).isTrue();
            assertThat(response.getResult().getCode()).isEqualTo("S0000");
            log.info("{}响应结果: {}", LOG_ITEM, response);
        }).verifyComplete();

        log.info("{} 测试拉取商品池数据完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandlePullPoolProductsValidationError() {
        log.info("{} 开始测试拉取商品池数据参数验证 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new PoolProductPullRequestRecord(null, null, "", null, 0, 0, null, null);
        log.info("{}请求参数: {}", LOG_ITEM, request);

        // When
        var responseFlux = toolsService.pullPoolProducts(request);

        // Then
        StepVerifier.create(responseFlux).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException;
        }).verify();

        log.info("{} 测试拉取商品池数据参数验证完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("write")
    void shouldAddRelation() {
        log.info("{} 开始测试添加买卖家分销关系 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new RelationAddRequestRecord(123456L);
        log.info("{}请求参数: {}", LOG_ITEM, request);

        // When
        var responseFlux = toolsService.addRelation(request);

        // Then
        StepVerifier.create(responseFlux).assertNext(response -> {
            assertThat(response).isNotNull();
            assertThat(response.getResult()).isNotNull();
            assertThat(response.getResult().getSuccess()).isTrue();
            assertThat(response.getResult().getCode()).isEqualTo("200");
            log.info("{}响应结果: {}", LOG_ITEM, response);
        }).verifyComplete();

        log.info("{} 测试添加买卖家分销关系完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleAddRelationValidationError() {
        log.info("{} 开始测试添加买卖家分销关系参数验证 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new RelationAddRequestRecord(null);
        log.info("{}请求参数: {}", LOG_ITEM, request);

        // When
        var responseFlux = toolsService.addRelation(request);

        // Then
        StepVerifier.create(responseFlux).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException;
        }).verify();

        log.info("{} 测试添加买卖家分销关系参数验证完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldEncryptLoginId() {
        // Given
        var request = LoginIdEncryptRequestRecord.of("test_user");

        log.info("{}开始加密用户loginId测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}用户登录名: {}", LOG_ITEM, request.loginId());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = toolsService.encryptLoginId(request);

        // Then
        StepVerifier.create(response).assertNext(encryptResponse -> {
            // 记录API调用指标
            recordMetrics("EncryptLoginId", startTime, encryptResponse != null && encryptResponse.getOpenUid() != null);

            assertThat(encryptResponse).isNotNull();
            assertThat(encryptResponse.getOpenUid()).isNotEmpty();

            // 打印加密结果
            log.info("{}加密用户loginId结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}OpenUid: {}", LOG_ITEM, encryptResponse.getOpenUid());
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("EncryptLoginId");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleEncryptLoginIdValidationError() {
        // Given
        var request = LoginIdEncryptRequestRecord.of("");

        log.info("{}开始加密用户loginId验证错误测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}用户登录名: {}", LOG_ITEM, request.loginId());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = toolsService.encryptLoginId(request);

        // Then
        StepVerifier.create(response).expectErrorMatches(throwable -> {
            // 记录API调用指标
            recordMetrics("EncryptLoginIdValidationError", startTime, false);

            // 验证错误类型
            assertThat(throwable).isInstanceOf(AlibabaServiceValidationException.class);
            assertThat(throwable.getMessage()).contains("用户登录名不能为空");

            // 打印错误信息
            log.info("{}加密用户loginId错误信息{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}错误: {}", LOG_ITEM, throwable.getMessage());
            return true;
        }).verify();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("EncryptLoginIdValidationError");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldDecryptWangwangNick() {
        // Given
        var request = WangwangNickDecryptRequestRecord.of("BBB8A9Qxi1_j8N9Xacx-AHAPA");

        log.info("{}开始解密旺旺昵称测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}OpenUid: {}", LOG_ITEM, request.openUid());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = toolsService.decryptWangwangNick(request);

        // Then
        StepVerifier.create(response).assertNext(decryptResponse -> {
            // 记录API调用指标
            recordMetrics("DecryptWangwangNick", startTime, decryptResponse != null && decryptResponse
                .getWangwangNick() != null);

            assertThat(decryptResponse).isNotNull();
            assertThat(decryptResponse.getWangwangNick()).isNotEmpty();

            // 打印解密结果
            log.info("{}解密旺旺昵称结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}旺旺昵称: {}", LOG_ITEM, decryptResponse.getWangwangNick());
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("DecryptWangwangNick");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleDecryptWangwangNickValidationError() {
        // Given
        var request = WangwangNickDecryptRequestRecord.of("");

        log.info("{}开始解密旺旺昵称验证错误测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}OpenUid: {}", LOG_ITEM, request.openUid());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = toolsService.decryptWangwangNick(request);

        // Then
        StepVerifier.create(response).expectErrorMatches(throwable -> {
            // 记录API调用指标
            recordMetrics("DecryptWangwangNickValidationError", startTime, false);

            // 验证错误类型
            assertThat(throwable).isInstanceOf(AlibabaServiceValidationException.class);
            assertThat(throwable.getMessage()).contains("待解密的openUid不能为空");

            // 打印错误信息
            log.info("{}解密旺旺昵称错误信息{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}错误: {}", LOG_ITEM, throwable.getMessage());
            return true;
        }).verify();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("DecryptWangwangNickValidationError");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }
}