/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.mock;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.api.MessageAPI;
import com.fulfillmen.support.alibaba.api.request.message.MessageConfirmRequestRecord;
import com.fulfillmen.support.alibaba.api.request.message.MessageCursorRequestRecord;
import com.fulfillmen.support.alibaba.api.request.message.MessageQueryFailedListRequestRecord;
import com.fulfillmen.support.alibaba.api.response.message.MessageConfirmResponse;
import com.fulfillmen.support.alibaba.api.response.message.MessageCursorResponse;
import com.fulfillmen.support.alibaba.api.response.message.MessageQueryFailedListResponse;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceException;
import com.fulfillmen.support.alibaba.service.IMessageService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

/**
 * 1688消息API Mock测试
 *
 * <AUTHOR>
 * @created 2025-01-15
 */
@Slf4j
class MessageAPIMockTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    @Autowired
    private IMessageService messageService;

    @MockBean
    private MessageAPI messageAPI;

    @Test
    void shouldQueryFailedMessageList() {
        log.info("{} 开始测试查询失败消息列表 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new MessageQueryFailedListRequestRecord(new Date(), // createStartTime
            new Date(), // createEndTime
            1, // page
            20, // pageSize
            null, // type
            null // userInfo
        );
        log.info("{}请求参数: createStartTime={}, createEndTime={}, page={}, pageSize={}", LOG_ITEM, request
            .createStartTime(), request.createEndTime(), request.page(), request.pageSize());

        MessageQueryFailedListResponse mockResponse = createMockResponse();
        when(messageAPI.queryFailedMessageList(any(), any())).thenReturn(Mono.just(mockResponse));

        // When
        var response = messageService.queryFailedMessageList(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isTrue();

            // 业务检查
            assertThat(result.getPushMessagePage()).isNotNull();
            assertThat(result.getPushMessagePage().getDatas()).isNotNull();
            assertThat(result.getPushMessagePage().getTotalCount()).isEqualTo(1);

            // 记录日志
            log.info("{}响应结果: {}", LOG_ITEM, result);
        }).verifyComplete();

        log.info("{} 测试查询失败消息列表完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleValidationError() {
        log.info("{} 开始测试参数校验失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new MessageQueryFailedListRequestRecord(null, // createStartTime
            null, // createEndTime
            1, // page
            10, // pageSize - 小于最小值20
            null, // type
            null // userInfo
        );
        log.info("{}请求参数: pageSize={}", LOG_ITEM, request.pageSize());

        // When & Then
        StepVerifier.create(messageService.queryFailedMessageList(request))
            .expectErrorMatches(throwable -> throwable instanceof com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException && throwable
                .getMessage()
                .contains("每页数据量必须在20-50之间"))
            .verify();

        log.info("{} 测试参数校验失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleTimeRangeValidationError() {
        log.info("{} 开始测试时间范围校验失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        Date endTime = new Date();
        Date startTime = new Date(endTime.getTime() + 24 * 60 * 60 * 1000); // 开始时间比结束时间晚一天
        var request = new MessageQueryFailedListRequestRecord(startTime, // createStartTime
            endTime, // createEndTime
            1, // page
            20, // pageSize
            null, // type
            null // userInfo
        );
        log.info("{}请求参数: createStartTime={}, createEndTime={}", LOG_ITEM, request.createStartTime(), request
            .createEndTime());

        // When & Then
        StepVerifier.create(messageService.queryFailedMessageList(request))
            .expectErrorMatches(throwable -> throwable instanceof com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException && throwable
                .getMessage()
                .contains("开始时间不能晚于结束时间"))
            .verify();

        log.info("{} 测试时间范围校验失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleApiError() {
        log.info("{} 开始测试API调用失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new MessageQueryFailedListRequestRecord(null, // createStartTime
            null, // createEndTime
            1, // page
            20, // pageSize
            null, // type
            null // userInfo
        );
        log.info("{}请求参数: page={}, pageSize={}", LOG_ITEM, request.page(), request.pageSize());

        MessageQueryFailedListResponse errorResponse = new MessageQueryFailedListResponse();
        errorResponse.setSuccess(false);
        errorResponse.setErrorCode("SYSTEM_ERROR");
        errorResponse.setErrorMessage("系统错误");

        when(messageAPI.queryFailedMessageList(any(), any())).thenReturn(Mono.just(errorResponse));

        // When & Then
        StepVerifier.create(messageService.queryFailedMessageList(request)).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isFalse();
            assertThat(result.getErrorCode()).isEqualTo("SYSTEM_ERROR");
            assertThat(result.getErrorMessage()).isEqualTo("系统错误");
            log.info("{}错误响应: code={}, message={}", LOG_ITEM, result.getErrorCode(), result.getErrorMessage());
        }).verifyComplete();

        log.info("{} 测试API调用失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleNetworkError() {
        log.info("{} 开始测试网络异常 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new MessageQueryFailedListRequestRecord(null, // createStartTime
            null, // createEndTime
            1, // page
            20, // pageSize
            null, // type
            null // userInfo
        );
        log.info("{}请求参数: page={}, pageSize={}", LOG_ITEM, request.page(), request.pageSize());

        when(messageAPI.queryFailedMessageList(any(), any())).thenReturn(Mono
            .error(new WebClientResponseException(503, "Service Unavailable", null, null, null)));

        // When & Then
        StepVerifier.create(messageService.queryFailedMessageList(request))
            .expectErrorMatches(throwable -> throwable instanceof AlibabaServiceException && throwable.getMessage()
                .contains("Service Unavailable"))
            .verify();

        log.info("{} 测试网络异常完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    private MessageQueryFailedListResponse createMockResponse() {
        MessageQueryFailedListResponse response = new MessageQueryFailedListResponse();
        response.setSuccess(true);

        MessageQueryFailedListResponse.PushMessagePage page = new MessageQueryFailedListResponse.PushMessagePage();
        page.setDatas(new ArrayList<>());
        page.setTotalCount(1);

        MessageQueryFailedListResponse.PushMessage message = new MessageQueryFailedListResponse.PushMessage();
        message.setMsgId(1L);
        message.setType("TEST_TYPE");
        message.setUserInfo("TEST_USER");
        message.setData(new HashMap<>());
        message.setGmtBorn(System.currentTimeMillis());

        page.getDatas().add(message);
        response.setPushMessagePage(page);

        return response;
    }

    // 新增的失败消息批量确认测试用例
    @Test
    void shouldConfirmFailedMessage() {
        log.info("{} 开始测试失败消息批量确认 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new MessageConfirmRequestRecord(Arrays.asList(123L, 456L));
        log.info("{}请求参数: msgIdList={}", LOG_ITEM, request.msgIdList());

        MessageConfirmResponse mockResponse = new MessageConfirmResponse();
        mockResponse.setSuccess(true);
        mockResponse.setIsSuccess(true);
        when(messageAPI.confirmFailedMessage(any(), any())).thenReturn(Mono.just(mockResponse));

        // When
        var response = messageService.confirmFailedMessage(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isTrue();
            assertThat(result.getIsSuccess()).isTrue();
            log.info("{}响应结果: success={}, isSuccess={}", LOG_ITEM, result.getSuccess(), result.getIsSuccess());
        }).verifyComplete();

        log.info("{} 测试失败消息批量确认完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleEmptyMsgIdListError() {
        log.info("{} 开始测试空消息ID列表错误处理 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given - 测试空列表
        var request = new MessageConfirmRequestRecord(new ArrayList<>());
        MessageConfirmRequestRecord spyRequest = spy(request);
        doNothing().when(spyRequest).requireParams();
        log.info("{}请求参数: msgIdList=empty list", LOG_ITEM);

        // Mock 错误响应
        MessageConfirmResponse mockResponse = new MessageConfirmResponse();
        mockResponse.setSuccess(false);
        mockResponse.setErrorCode("PARAMETER_ERROR");
        mockResponse.setErrorMessage("待确认的消息id列表不能为空");
        when(messageAPI.confirmFailedMessage(any(), any())).thenReturn(Mono.just(mockResponse));

        // When
        var response = messageService.confirmFailedMessage(spyRequest);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isFalse();
            assertThat(result.getErrorCode()).isEqualTo("PARAMETER_ERROR");
            assertThat(result.getErrorMessage()).isEqualTo("待确认的消息id列表不能为空");
            log.info("{}错误响应: code={}, message={}", LOG_ITEM, result.getErrorCode(), result.getErrorMessage());
        }).verifyComplete();

        log.info("{} 测试空消息ID列表错误处理完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleNullMsgIdListError() {
        log.info("{} 开始测试null消息ID列表错误处理 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given - 测试null
        var request = new MessageConfirmRequestRecord(null);
        log.info("{}请求参数: msgIdList=null", LOG_ITEM);

        // When & Then
        StepVerifier.create(messageService.confirmFailedMessage(request))
            .expectErrorMatches(throwable -> throwable instanceof com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException && throwable
                .getMessage()
                .contains("待确认的消息id列表不能为空"))
            .verify();

        log.info("{} 测试null消息ID列表错误处理完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleConfirmFailedMessageError() {
        log.info("{} 开始测试失败消息批量确认失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new MessageConfirmRequestRecord(Arrays.asList(123L, 456L));
        log.info("{}请求参数: msgIdList={}", LOG_ITEM, request.msgIdList());

        MessageConfirmResponse mockResponse = new MessageConfirmResponse();
        mockResponse.setSuccess(false);
        mockResponse.setErrorCode("SYSTEM_ERROR");
        mockResponse.setErrorMessage("系统错误");
        when(messageAPI.confirmFailedMessage(any(), any())).thenReturn(Mono.just(mockResponse));

        // When
        var response = messageService.confirmFailedMessage(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isFalse();
            assertThat(result.getErrorCode()).isEqualTo("SYSTEM_ERROR");
            assertThat(result.getErrorMessage()).isEqualTo("系统错误");
            log.info("{}错误响应: code={}, message={}", LOG_ITEM, result.getErrorCode(), result.getErrorMessage());
        }).verifyComplete();

        log.info("{} 测试失败消息批量确认失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetMessageCursorList() {
        log.info("{} 开始测试获取消息游标列表 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new MessageCursorRequestRecord(new Date(), // createStartTime
            new Date(), // createEndTime
            20, // quantity
            null, // type
            null // userInfo
        );
        MessageCursorRequestRecord spyRequest = spy(request);
        doNothing().when(spyRequest).requireParams();
        log.info("{}请求参数: createStartTime={}, createEndTime={}, quantity={}", LOG_ITEM, spyRequest
            .createStartTime(), spyRequest.createEndTime(), spyRequest.quantity());

        MessageCursorResponse mockResponse = new MessageCursorResponse();
        mockResponse.setSuccess(true);
        mockResponse.setPushMessageList(new ArrayList<>());
        when(messageAPI.getMessageCursorList(any(), any())).thenReturn(Mono.just(mockResponse));

        // When
        var response = messageService.getMessageCursorList(spyRequest);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isTrue();
            assertThat(result.getPushMessageList()).isNotNull();
            log.info("{}响应结果: {}", LOG_ITEM, result);
        }).verifyComplete();

        log.info("{} 测试获取消息游标列表完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleGetMessageCursorListError() {
        log.info("{} 开始测试获取消息游标列表参数校验失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new MessageCursorRequestRecord(null, // createStartTime
            null, // createEndTime
            10, // quantity - 小于最小值20
            null, // type
            null // userInfo
        );
        log.info("{}请求参数: quantity={}", LOG_ITEM, request.quantity());

        // When & Then
        StepVerifier.create(messageService.getMessageCursorList(request))
            .expectErrorMatches(throwable -> throwable instanceof com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException && throwable
                .getMessage()
                .contains("每次取的数据量必须在20-200之间"))
            .verify();

        log.info("{} 测试获取消息游标列表参数校验失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleTimeRangeValidationErrorForCursor() {
        log.info("{} 开始测试获取消息游标列表时间范围校验失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        Date endTime = new Date();
        Date startTime = new Date(endTime.getTime() + 24 * 60 * 60 * 1000); // 开始时间比结束时间晚一天
        var request = new MessageCursorRequestRecord(startTime, // createStartTime
            endTime, // createEndTime
            20, // quantity
            null, // type
            null // userInfo
        );
        log.info("{}请求参数: createStartTime={}, createEndTime={}", LOG_ITEM, request.createStartTime(), request
            .createEndTime());

        // When & Then
        StepVerifier.create(messageService.getMessageCursorList(request))
            .expectErrorMatches(throwable -> throwable instanceof com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException && throwable
                .getMessage()
                .contains("开始时间不能晚于结束时间"))
            .verify();

        log.info("{} 测试获取消息游标列表时间范围校验失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleGetMessageCursorListApiError() {
        log.info("{} 开始测试获取消息游标列表API调用失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new MessageCursorRequestRecord(null, // createStartTime
            null, // createEndTime
            20, // quantity
            null, // type
            null // userInfo
        );
        MessageCursorRequestRecord spyRequest = spy(request);
        doNothing().when(spyRequest).requireParams();
        log.info("{}请求参数: quantity={}", LOG_ITEM, request.quantity());

        // Mock API错误响应
        MessageCursorResponse errorResponse = new MessageCursorResponse();
        errorResponse.setSuccess(false);
        errorResponse.setErrorCode("SYSTEM_ERROR");
        errorResponse.setErrorMessage("系统错误");

        when(messageAPI.getMessageCursorList(any(), any())).thenReturn(Mono.just(errorResponse));

        // When & Then
        StepVerifier.create(messageService.getMessageCursorList(spyRequest)).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isFalse();
            assertThat(result.getErrorCode()).isEqualTo("SYSTEM_ERROR");
            assertThat(result.getErrorMessage()).isEqualTo("系统错误");
            log.info("{}错误响应: code={}, message={}", LOG_ITEM, result.getErrorCode(), result.getErrorMessage());
        }).verifyComplete();

        log.info("{} 测试获取消息游标列表API调用失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleGetMessageCursorListNetworkError() {
        log.info("{} 开始测试获取消息游标列表网络异常 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new MessageCursorRequestRecord(null, // createStartTime
            null, // createEndTime
            20, // quantity
            null, // type
            null // userInfo
        );
        MessageCursorRequestRecord spyRequest = spy(request);
        doNothing().when(spyRequest).requireParams();
        log.info("{}请求参数: quantity={}", LOG_ITEM, request.quantity());

        // Mock 网络异常
        when(messageAPI.getMessageCursorList(any(), any())).thenReturn(Mono
            .error(new WebClientResponseException(503, "Service Unavailable", null, null, null)));

        // When & Then
        StepVerifier.create(messageService.getMessageCursorList(spyRequest))
            .expectErrorMatches(throwable -> throwable instanceof AlibabaServiceException && throwable.getMessage()
                .contains("Service Unavailable"))
            .verify();

        log.info("{} 测试获取消息游标列表网络异常完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }
}