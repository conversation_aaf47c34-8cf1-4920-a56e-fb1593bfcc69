/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.integration.data;

/**
 * 物流信息预期数据
 * 
 * <AUTHOR>
 * @created 2025-07-12
 */
public record LogisticsExpected(
    String contactPerson,
    String mobile,
    String province,
    String city,
    String area,
    String address,
    String logisticsCompanyName,
    String logisticsBillNo,
    String status,
    String fromProvince,
    String fromCity,
    String fromArea
) {

    /**
     * 创建物流信息预期数据的构建器
     */
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {

        private String contactPerson;
        private String mobile;
        private String province;
        private String city;
        private String area;
        private String address;
        private String logisticsCompanyName;
        private String logisticsBillNo;
        private String status;
        private String fromProvince;
        private String fromCity;
        private String fromArea;

        public Builder contactPerson(String contactPerson) {
            this.contactPerson = contactPerson;
            return this;
        }

        public Builder mobile(String mobile) {
            this.mobile = mobile;
            return this;
        }

        public Builder province(String province) {
            this.province = province;
            return this;
        }

        public Builder city(String city) {
            this.city = city;
            return this;
        }

        public Builder area(String area) {
            this.area = area;
            return this;
        }

        public Builder address(String address) {
            this.address = address;
            return this;
        }

        public Builder logisticsCompanyName(String logisticsCompanyName) {
            this.logisticsCompanyName = logisticsCompanyName;
            return this;
        }

        public Builder logisticsBillNo(String logisticsBillNo) {
            this.logisticsBillNo = logisticsBillNo;
            return this;
        }

        public Builder status(String status) {
            this.status = status;
            return this;
        }

        public Builder fromProvince(String fromProvince) {
            this.fromProvince = fromProvince;
            return this;
        }

        public Builder fromCity(String fromCity) {
            this.fromCity = fromCity;
            return this;
        }

        public Builder fromArea(String fromArea) {
            this.fromArea = fromArea;
            return this;
        }

        public LogisticsExpected build() {
            return new LogisticsExpected(
                contactPerson, mobile, province, city, area, address,
                logisticsCompanyName, logisticsBillNo, status,
                fromProvince, fromCity, fromArea
            );
        }
    }
}
