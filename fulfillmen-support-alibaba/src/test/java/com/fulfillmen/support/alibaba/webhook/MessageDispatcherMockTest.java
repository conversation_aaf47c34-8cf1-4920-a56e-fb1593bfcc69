/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fulfillmen.support.alibaba.webhook.exception.WebhookProcessingException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * MessageDispatcher Mock测试类
 * 测试MessageDispatcher的核心功能，包括消息分发、路由、签名验证、幂等性检查等
 *
 * <AUTHOR>
 * @date 2025/7/10
 * @since 1.0.0
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
public class MessageDispatcherMockTest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    @Mock
    private MessageRouter messageRouter;

    @Mock
    private IdempotentManager idempotentManager;

    @Mock
    private SignatureValidator signatureValidator;

    private MessageDispatcher messageDispatcher;

    /**
     * 测试用例的真实数据 - 基于用户提供的消息
     */
    private static final String VALID_MESSAGE_BODY = """
        [{
            "bizKey": "2806599398122540788",
            "data": {
                "buyerMemberId": "b2b-2207416548807a4d12",
                "currentStatus": "success",
                "orderId": 2806599398122540788,
                "sellerMemberId": "b2b-221280776451649a09",
                "msgSendTime": "2025-07-10 17:55:46"
            },
            "gmtBorn": 1752141346107,
            "msgId": "139830976934",
            "type": "ORDER_BUYER_VIEW_ORDER_SUCCESS",
            "userInfo": "b2b-2207416548807a4d12"
        }]
        """;

    private static final String VALID_SIGNATURE = "38DF23A2911926BD925AA1D89DC2D282777BA572";

    @BeforeEach
    void setUp() {
        messageDispatcher = new MessageDispatcher(messageRouter, idempotentManager, signatureValidator);
    }

    @Test
    @Tag("success")
    void shouldDispatchValidMessage() {
        log.info("{} 开始测试MessageDispatcher处理有效消息 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        when(signatureValidator.validate(anyString(), anyString())).thenReturn(true);
        when(idempotentManager.isDuplicate(anyString())).thenReturn(false);

        MessageResult successResult = MessageResult.success("139830976934");
        when(messageRouter.route(any(MessageEvent.class))).thenReturn(successResult);

        log.info("{}消息长度: {}", LOG_ITEM, VALID_MESSAGE_BODY.length());
        log.info("{}签名: {}", LOG_ITEM, VALID_SIGNATURE);

        // When
        List<MessageResult> results = messageDispatcher.dispatch(VALID_MESSAGE_BODY, VALID_SIGNATURE);

        // Then
        assertThat(results).isNotNull();
        assertThat(results).hasSize(1);
        assertThat(results.get(0).isSuccess()).isTrue();
        assertThat(results.get(0).getMsgId()).isEqualTo("139830976934");

        // 验证调用顺序
        verify(signatureValidator).validate(VALID_MESSAGE_BODY, VALID_SIGNATURE);
        verify(idempotentManager).isDuplicate("139830976934");
        verify(messageRouter).route(any(MessageEvent.class));
        verify(idempotentManager).markProcessed(eq("139830976934"), any(MessageResult.class));

        log.info("{}处理结果数量: {}", LOG_ITEM, results.size());
        log.info("{}处理结果: 成功={}, 消息ID={}", LOG_ITEM, results.get(0).isSuccess(), results.get(0).getMsgId());

        log.info("{} 测试MessageDispatcher处理有效消息完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldRejectInvalidSignature() {
        log.info("{} 开始测试MessageDispatcher签名验证失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        when(signatureValidator.validate(anyString(), anyString())).thenReturn(false);

        log.info("{}模拟签名验证失败", LOG_ITEM);

        // When & Then
        assertThatThrownBy(() -> messageDispatcher.dispatch(VALID_MESSAGE_BODY, "INVALID_SIGNATURE"))
            .isInstanceOf(WebhookProcessingException.class)
            .hasMessageContaining("消息签名验证失败");

        // 验证只调用了签名验证，其他组件未被调用
        verify(signatureValidator).validate(VALID_MESSAGE_BODY, "INVALID_SIGNATURE");
        verify(idempotentManager, never()).isDuplicate(anyString());
        verify(messageRouter, never()).route(any(MessageEvent.class));

        log.info("{}签名验证失败，正确抛出异常", LOG_ITEM);

        log.info("{} 测试MessageDispatcher签名验证失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("success")
    void shouldHandleDuplicateMessage() {
        log.info("{} 开始测试MessageDispatcher处理重复消息 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        when(signatureValidator.validate(anyString(), anyString())).thenReturn(true);
        when(idempotentManager.isDuplicate("139830976934")).thenReturn(true);

        log.info("{}模拟重复消息检测", LOG_ITEM);

        // When
        List<MessageResult> results = messageDispatcher.dispatch(VALID_MESSAGE_BODY, VALID_SIGNATURE);

        // Then
        assertThat(results).isNotNull();
        assertThat(results).hasSize(1);
        assertThat(results.get(0).isSuccess()).isTrue();
        assertThat(results.get(0).getMsgId()).isEqualTo("139830976934");
        assertThat(results.get(0).getErrorMessage()).contains("消息重复");

        // 验证重复消息不会路由到处理器
        verify(signatureValidator).validate(VALID_MESSAGE_BODY, VALID_SIGNATURE);
        verify(idempotentManager).isDuplicate("139830976934");
        verify(messageRouter, never()).route(any(MessageEvent.class));
        verify(idempotentManager, never()).markProcessed(anyString(), any(MessageResult.class));

        log.info("{}重复消息处理结果: {}", LOG_ITEM, results.get(0).getErrorMessage());

        log.info("{} 测试MessageDispatcher处理重复消息完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleInvalidJsonMessage() {
        log.info("{} 开始测试MessageDispatcher处理无效JSON消息 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String invalidJson = "{ invalid json }";
        when(signatureValidator.validate(anyString(), anyString())).thenReturn(true);

        log.info("{}无效JSON: {}", LOG_ITEM, invalidJson);

        // When & Then
        assertThatThrownBy(() -> messageDispatcher.dispatch(invalidJson, VALID_SIGNATURE))
            .isInstanceOf(WebhookProcessingException.class)
            .hasMessageContaining("处理webhook消息异常");

        // 验证签名验证通过，但JSON解析失败
        verify(signatureValidator).validate(invalidJson, VALID_SIGNATURE);
        verify(idempotentManager, never()).isDuplicate(anyString());

        log.info("{}无效JSON正确抛出异常", LOG_ITEM);

        log.info("{} 测试MessageDispatcher处理无效JSON消息完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleRouterException() {
        log.info("{} 开始测试MessageDispatcher处理路由异常 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        when(signatureValidator.validate(anyString(), anyString())).thenReturn(true);
        when(idempotentManager.isDuplicate(anyString())).thenReturn(false);
        when(messageRouter.route(any(MessageEvent.class)))
            .thenThrow(new RuntimeException("路由处理异常"));

        log.info("{}模拟路由处理异常", LOG_ITEM);

        // When
        List<MessageResult> results = messageDispatcher.dispatch(VALID_MESSAGE_BODY, VALID_SIGNATURE);

        // Then
        assertThat(results).isNotNull();
        assertThat(results).hasSize(1);
        assertThat(results.get(0).isSuccess()).isFalse();
        assertThat(results.get(0).getMsgId()).isEqualTo("139830976934");
        assertThat(results.get(0).getErrorMessage()).contains("路由处理异常");

        // 验证异常情况下不会标记为已处理
        verify(idempotentManager, never()).markProcessed(anyString(), any(MessageResult.class));

        log.info("{}路由异常处理结果: {}", LOG_ITEM, results.get(0).getErrorMessage());

        log.info("{} 测试MessageDispatcher处理路由异常完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("success")
    void shouldHandleMultipleMessages() {
        log.info("{} 开始测试MessageDispatcher处理多条消息 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String multipleMessages = """
            [{
                "bizKey": "2806599398122540788",
                "data": {
                    "buyerMemberId": "b2b-2207416548807a4d12",
                    "currentStatus": "success",
                    "orderId": 2806599398122540788,
                    "sellerMemberId": "b2b-221280776451649a09",
                    "msgSendTime": "2025-07-10 17:55:46"
                },
                "gmtBorn": 1752141346107,
                "msgId": "139830976934",
                "type": "ORDER_BUYER_VIEW_ORDER_SUCCESS",
                "userInfo": "b2b-2207416548807a4d12"
            },
            {
                "bizKey": "2806599398122540789",
                "data": {
                    "buyerMemberId": "b2b-2207416548807a4d13",
                    "currentStatus": "pending",
                    "orderId": 2806599398122540789,
                    "sellerMemberId": "b2b-221280776451649a10",
                    "msgSendTime": "2025-07-10 17:56:46"
                },
                "gmtBorn": 1752141346108,
                "msgId": "139830976935",
                "type": "ORDER_BUYER_VIEW_ORDER_SUCCESS",
                "userInfo": "b2b-2207416548807a4d13"
            }]
            """;

        when(signatureValidator.validate(anyString(), anyString())).thenReturn(true);
        when(idempotentManager.isDuplicate(anyString())).thenReturn(false);

        MessageResult successResult1 = MessageResult.success("139830976934");
        MessageResult successResult2 = MessageResult.success("139830976935");
        when(messageRouter.route(any(MessageEvent.class)))
            .thenReturn(successResult1)
            .thenReturn(successResult2);

        log.info("{}多条消息测试", LOG_ITEM);

        // When
        List<MessageResult> results = messageDispatcher.dispatch(multipleMessages, VALID_SIGNATURE);

        // Then
        assertThat(results).isNotNull();
        assertThat(results).hasSize(2);
        assertThat(results.get(0).isSuccess()).isTrue();
        assertThat(results.get(0).getMsgId()).isEqualTo("139830976934");
        assertThat(results.get(1).isSuccess()).isTrue();
        assertThat(results.get(1).getMsgId()).isEqualTo("139830976935");

        // 验证每条消息都被处理
        verify(idempotentManager, times(2)).isDuplicate(anyString());
        verify(messageRouter, times(2)).route(any(MessageEvent.class));
        verify(idempotentManager, times(2)).markProcessed(anyString(), any(MessageResult.class));

        log.info("{}处理结果数量: {}", LOG_ITEM, results.size());

        log.info("{} 测试MessageDispatcher处理多条消息完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleIdempotentManagerException() {
        log.info("{} 开始测试MessageDispatcher处理幂等性管理器异常 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        when(signatureValidator.validate(anyString(), anyString())).thenReturn(true);
        when(idempotentManager.isDuplicate(anyString()))
            .thenThrow(new RuntimeException("Redis连接异常"));

        log.info("{}模拟幂等性管理器异常", LOG_ITEM);

        // When
        List<MessageResult> results = messageDispatcher.dispatch(VALID_MESSAGE_BODY, VALID_SIGNATURE);

        // Then
        assertThat(results).isNotNull();
        assertThat(results).hasSize(1);
        assertThat(results.get(0).isSuccess()).isFalse();
        assertThat(results.get(0).getMsgId()).isEqualTo("139830976934");
        assertThat(results.get(0).getErrorMessage()).contains("Redis连接异常");

        log.info("{}幂等性管理器异常处理结果: {}", LOG_ITEM, results.get(0).getErrorMessage());

        log.info("{} 测试MessageDispatcher处理幂等性管理器异常完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("success")
    void shouldHandleNoHandlerFound() {
        log.info("{} 开始测试MessageDispatcher处理无处理器情况 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        when(signatureValidator.validate(anyString(), anyString())).thenReturn(true);
        when(idempotentManager.isDuplicate(anyString())).thenReturn(false);

        MessageResult noHandlerResult = MessageResult.noHandler("139830976934");
        when(messageRouter.route(any(MessageEvent.class))).thenReturn(noHandlerResult);

        log.info("{}模拟无处理器情况", LOG_ITEM);

        // When
        List<MessageResult> results = messageDispatcher.dispatch(VALID_MESSAGE_BODY, VALID_SIGNATURE);

        // Then
        assertThat(results).isNotNull();
        assertThat(results).hasSize(1);
        assertThat(results.get(0).isSuccess()).isFalse();
        assertThat(results.get(0).getMsgId()).isEqualTo("139830976934");
        assertThat(results.get(0).getErrorMessage()).contains("未找到适合的消息处理器");

        // 验证失败情况下不会标记为已处理
        verify(idempotentManager, never()).markProcessed(anyString(), any(MessageResult.class));

        log.info("{}无处理器处理结果: {}", LOG_ITEM, results.get(0).getErrorMessage());

        log.info("{} 测试MessageDispatcher处理无处理器情况完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }
}
