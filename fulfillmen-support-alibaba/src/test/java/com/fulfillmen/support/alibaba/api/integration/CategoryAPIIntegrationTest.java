/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.integration;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.fail;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.api.request.category.CategoryAttributeRequestRecord;
import com.fulfillmen.support.alibaba.api.request.category.CategoryRequestRecord;
import com.fulfillmen.support.alibaba.api.request.category.CategoryTranslationByIdRequestRecord;
import com.fulfillmen.support.alibaba.api.request.category.CategoryTranslationRequestRecord;
import com.fulfillmen.support.alibaba.api.response.category.CategoryResponse;
import com.fulfillmen.support.alibaba.api.response.category.CategoryResponse.CategoryInfo;
import com.fulfillmen.support.alibaba.enums.LanguageEnum;
import com.fulfillmen.support.alibaba.service.ICategoryService;
import java.io.File;
import java.io.IOException;
import java.time.Duration;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import reactor.util.retry.Retry;

/**
 * 类目API集成测试类
 *
 * <AUTHOR>
 * @created 2025-01-16
 */
@Slf4j
class CategoryAPIIntegrationTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";
    private static final String CATEGORY_DIR = "src/test/resources/category";
    private static final int MAX_RETRY = 3;
    private static final Duration RETRY_DELAY = Duration.ofSeconds(2);
    private static final Duration REQUEST_INTERVAL = Duration.ofSeconds(1);

    @Autowired
    private ICategoryService categoryService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void shouldGetCategory() {
        // Given
        log.info("{} 开始测试获取类目信息 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        var request = new CategoryRequestRecord("126506005");
        log.info("{}请求参数: categoryId={}", LOG_ITEM, request.categoryId());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = categoryService.getCategory(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getSucces()).isEqualTo("true");
            assertThat(result.getCategoryInfo()).isNotEmpty();

            log.info("{}响应结果: {}", LOG_ITEM, result);
            result.getCategoryInfo().forEach(category -> {
                log.info("{}类目ID: {}", LOG_ITEM, category.getCategoryId());
                log.info("{}类目名称: {}", LOG_ITEM, category.getName());
                log.info("{}是否叶子节点: {}", LOG_ITEM, category.getIsLeaf());
            });

            recordMetrics("GetCategory", startTime, true);
        }).verifyComplete();

        logMetrics("GetCategory");
        log.info("{} 测试获取类目信息完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetTranslationById() {
        log.info("{} 开始测试获取类目翻译信息(通过ID) {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new CategoryTranslationByIdRequestRecord(125372002L, "en", "test_member_id", null);
        log.info("{}请求参数: categoryId={}, language={}, outMemberId={}", LOG_ITEM, request.categoryId(), request
            .language(), request.outMemberId());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = categoryService.getTranslationById(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getResult().getSuccess()).isTrue();

            log.info("{}响应结果: {}", LOG_ITEM, result);

            recordMetrics("GetTranslationById", startTime, true);
        }).verifyComplete();

        logMetrics("GetTranslationById");
        log.info("{} 测试获取类目翻译信息(通过ID)完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetTranslation() {
        log.info("{} 开始测试获取类目翻译信息(通过关键词) {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new CategoryTranslationRequestRecord("电子", LanguageEnum.ZH.getLanguage(), null);
        log.info("{}请求参数: cateName={}, language={}", LOG_ITEM, request.cateName(), request.language());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = categoryService.getTranslation(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getResult()).isNotNull();
            assertThat(result.getResult().getSuccess()).isTrue();

            log.info("{}响应结果: {}", LOG_ITEM, result);

            recordMetrics("GetTranslation", startTime, true);
        }).verifyComplete();

        logMetrics("GetTranslation");
        log.info("{} 测试获取类目翻译信息(通过关键词)完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetAttributes() {
        log.info("{} 开始测试获取类目属性信息 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new CategoryAttributeRequestRecord(126506005L, "1688", null);
        log.info("{}请求参数: categoryId={}, webSite={}", LOG_ITEM, request.categoryId(), request.webSite());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = categoryService.getAttributes(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isTrue();

            log.info("{}响应结果: {}", LOG_ITEM, result);

            recordMetrics("GetAttributes", startTime, true);
        }).verifyComplete();

        logMetrics("GetAttributes");
        log.info("{} 测试获取类目属性信息完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGenerateAllCategories() {
        log.info("{} 开始生成所有类目信息 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // 创建类目目录
        createCategoryDirectory();

        // 获取一级类目
        var firstLevelResponse = generateFirstLevelCategories();
        assertThat(firstLevelResponse).isNotNull();
        assertThat(firstLevelResponse.getCategoryInfo()).isNotEmpty();

        // 遍历一级类目，获取二级类目
        firstLevelResponse.getCategoryInfo().forEach(firstLevel -> {
            if (!isLeafCategory(firstLevel)) {
                try {
                    Thread.sleep(REQUEST_INTERVAL.toMillis());
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return;
                }

                var secondLevelResponse = generateSecondLevelCategories(firstLevel);
                assertThat(secondLevelResponse).isNotNull();
                assertThat(secondLevelResponse.getCategoryInfo()).isNotEmpty();
            }
        });

        log.info("{} 生成所有类目信息完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldReadAllCategories() {
        log.info("{} 开始读取所有类目信息 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        try {
            // 读取一级类目
            var firstLevelFile = new File(CATEGORY_DIR, "first_level_categories.json");
            var firstLevelResponse = objectMapper.readValue(firstLevelFile, CategoryResponse.class);

            // 统计信息
            int totalCategories = 0;
            int leafCategories = 0;

            // 遍历一级类目
            for (CategoryInfo category : firstLevelResponse.getCategoryInfo()) {
                totalCategories++;
                if (isLeafCategory(category)) {
                    leafCategories++;
                }

                // 输出类目信息
                log.info("{}一级类目: {}", LOG_ITEM, category.getName());
                log.info("{}   |- ID: {}", LOG_ITEM, category.getCategoryId());
                log.info("{}   |- 是否叶子节点: {}", LOG_ITEM, category.getIsLeaf());
                log.info("{}   |- 类目类型: {}", LOG_ITEM, category.getCategoryType());
                if (category.getMinOrderQuantity() != null) {
                    log.info("{}   |- 最小订购量: {}", LOG_ITEM, category.getMinOrderQuantity());
                }

            }

            // 输出统计信息
            log.info("{}统计信息:", LOG_ITEM);
            log.info("{}|- 总类目数: {}", LOG_ITEM, totalCategories);
            log.info("{}|- 叶子类目数: {}", LOG_ITEM, leafCategories);
            log.info("{}|- 非叶子类目数: {}", LOG_ITEM, totalCategories - leafCategories);

        } catch (IOException e) {
            log.error("读取类目文件失败", e);
            fail("读取类目文件失败: " + e.getMessage());
        }

        log.info("{} 读取所有类目信息完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    private void createCategoryDirectory() {
        var categoryDir = new File(CATEGORY_DIR);
        if (!categoryDir.exists()) {
            categoryDir.mkdirs();
        }
    }

    private CategoryResponse generateFirstLevelCategories() {
        log.info("{}开始获取一级类目", LOG_ITEM);

        var request = new CategoryRequestRecord("0");

        var response = executeWithRetry(categoryService.getCategory(request), "GetFirstLevelCategory").block();

        saveToFile(response, "first_level_categories.json");
        return response;
    }

    private CategoryResponse generateSecondLevelCategories(CategoryInfo firstLevel) {
        log.info("{}开始获取一级类目 {} 的二级类目", LOG_ITEM, firstLevel.getName());

        var request = new CategoryRequestRecord(String.valueOf(firstLevel.getCategoryId()));

        var response = executeWithRetry(categoryService.getCategory(request), "GetSecondLevelCategory").block();

        saveToFile(response, String.format("second_level_%s.json", firstLevel.getCategoryId()));
        return response;
    }

    private <T> Mono<T> executeWithRetry(Mono<T> mono, String operationName) {
        AtomicInteger retryCount = new AtomicInteger(0);

        return mono.retryWhen(Retry.backoff(MAX_RETRY, RETRY_DELAY).doBeforeRetry(retrySignal -> {
            int attempt = retryCount.incrementAndGet();
            log.warn("{}第{}次重试 {}: {}", LOG_ITEM, attempt, operationName, retrySignal.failure().getMessage());
        }));
    }

    private void saveToFile(Object content, String filename) {
        try {
            var file = new File(CATEGORY_DIR, filename);
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(file, content);
            log.info("{}已保存到文件: {}", LOG_ITEM, file.getName());
        } catch (IOException e) {
            log.error("保存文件失败: {}", filename, e);
        }
    }

    private boolean isLeafCategory(CategoryInfo category) {
        return category.getIsLeaf() != null && category.getIsLeaf();
    }
}