/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.integration;

import static org.assertj.core.api.Assertions.assertThat;

import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.api.request.logistics.AddressCodeParseRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsCompanyListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsFreightTemplateRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsInfoRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsInfoRequestRecord.LogisticsInfoField;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsInsuranceRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsOutOrderIdRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsReceiveAddressRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsTraceRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.ProductFreightEstimateRequestRecord;
import com.fulfillmen.support.alibaba.api.response.model.LogisticsTraceInfo;
import com.fulfillmen.support.alibaba.api.response.model.LogisticsTraceStep;
import com.fulfillmen.support.alibaba.service.ILogisticsService;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.test.StepVerifier;

/**
 * 1688物流API集成测试
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Slf4j
class LogisticsAPIIntegrationTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    @Autowired
    private ILogisticsService logisticsService;

    @Test
    void shouldGetFreightTemplate() {
        log.info("{} 开始测试获取物流模板详情 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = LogisticsFreightTemplateRequestRecord.ofWithDetails(1234567L);
        log.info("{}请求参数: templateId={}, querySubTemplate={}, queryRate={}", LOG_ITEM, request.templateId(), request
            .querySubTemplate(), request.queryRate());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = logisticsService.getFreightTemplate(request);

        // Then
        StepVerifier.create(response).assertNext(resp -> {
            if (resp.getResult() != null && !resp.getResult().isEmpty()) {
                var template = resp.getResult().get(0);
                log.info("{}模板ID: {}", LOG_ITEM, template.getId());
                log.info("{}模板名称: {}", LOG_ITEM, template.getName());
                log.info("{}地址: {}", LOG_ITEM, template.getAddressCodeText());
                log.info("{}备注: {}", LOG_ITEM, template.getRemark());
                log.info("{}状态: {}", LOG_ITEM, template.getStatus());
                log.info("{}类型: {}", LOG_ITEM, template.getType());

                if (template.getExpressSubTemplate() != null) {
                    log.info("{}快递子模板: {}", LOG_ITEM, template.getExpressSubTemplate());
                }
                if (template.getLogisticsSubTemplate() != null) {
                    log.info("{}货运子模板: {}", LOG_ITEM, template.getLogisticsSubTemplate());
                }
                if (template.getCodSubTemplate() != null) {
                    log.info("{}货到付款子模板: {}", LOG_ITEM, template.getCodSubTemplate());
                }
            } else {
                log.info("{}错误码: {}", LOG_ITEM, resp.getErrorCode());
                log.info("{}错误信息: {}", LOG_ITEM, resp.getErrorMessage());
            }

            recordMetrics("GetFreightTemplate", startTime, true);
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GetFreightTemplate");
        log.info("{} 测试获取物流模板详情完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldParseAddressCode() {
        log.info("{} 开始测试解析地址码 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = AddressCodeParseRequestRecord.of("广东省 惠州市 惠城区 汝湖镇 金泽物流园二期一号楼");
        log.info("{}请求参数: addressInfo={}", LOG_ITEM, request.addressInfo());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = logisticsService.parseAddressCode(request);

        // Then
        StepVerifier.create(response).assertNext(resp -> {
            var success = resp.getResult() != null;
            log.info("{}是否成功: {}", LOG_ITEM, success);
            if (success) {
                var address = resp.getResult();
                log.info("{}详细地址: {}", LOG_ITEM, address.getAddress());
                log.info("{}地区码: {}", LOG_ITEM, address.getAddressCode());
                log.info("{}完整地址: {}", LOG_ITEM, address.getFullName());
                log.info("{}收货人: {}", LOG_ITEM, address.getFullName());
                log.info("{}手机号: {}", LOG_ITEM, address.getMobile());
                log.info("{}邮编: {}", LOG_ITEM, address.getPostCode());
            } else {
                log.info("{}错误码: {}", LOG_ITEM, resp.getErrorCode());
                log.info("{}错误信息: {}", LOG_ITEM, resp.getErrorMessage());
            }

            recordMetrics("ParseAddressCode", startTime, success);
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("ParseAddressCode");
        log.info("{} 测试解析地址码完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetOutOrderId() {
        log.info("{} 开始测试查询外部订单ID {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = LogisticsOutOrderIdRequestRecord.ofShipmentId("888048437036");
        log.info("{}请求参数: shipmentId={}", LOG_ITEM, request.shipmentId());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = logisticsService.getOutOrderId(request);

        // Then
        StepVerifier.create(response).assertNext(resp -> {
            log.info("{}是否成功: {}", LOG_ITEM, resp.getSuccess());
            if (resp.getSuccess() && resp.getResult() != null) {
                var result = resp.getResult();
                log.info("{}外部订单ID: {}", LOG_ITEM, result.getOutOrderId());
                log.info("{}订单ID: {}", LOG_ITEM, result.getOrderId());
            } else {
                log.info("{}错误码: {}", LOG_ITEM, resp.getErrorCode());
                log.info("{}错误信息: {}", LOG_ITEM, resp.getErrorMessage());
            }

            recordMetrics("GetOutOrderId", startTime, resp.getSuccess());
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GetOutOrderId");
        log.info("{} 测试查询外部订单ID完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetLogisticsInfo() {
        log.info("{} 开始测试获取物流信息 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = LogisticsInfoRequestRecord.of("2438608801049540788", Arrays
            .asList(LogisticsInfoField.LOGISTICS_ID, LogisticsInfoField.LOGISTICS_BILL_NO, LogisticsInfoField.STATUS, LogisticsInfoField.LOGISTICS_COMPANY_ID, LogisticsInfoField.LOGISTICS_COMPANY_NAME,
                LogisticsInfoField.SEND_GOODS, LogisticsInfoField.RECEIVER, LogisticsInfoField.SENDER, LogisticsInfoField.LOGISTICS_ORDER_GOODS));
        log.info("{}请求参数: orderId={}, fields={}, webSite={}", LOG_ITEM, request.orderId(), request.fields(), request
            .webSite());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = logisticsService.getLogisticsInfo(request);

        // Then
        StepVerifier.create(response).assertNext(resp -> {
            log.info("{}是否成功: {}", LOG_ITEM, resp.getSuccess());
            if (resp.getSuccess() && resp.getResult() != null && !resp.getResult().isEmpty()) {
                var order = resp.getResult().get(0);
                log.info("{}物流单号: {}", LOG_ITEM, order.getLogisticsId());
                log.info("{}运单号: {}", LOG_ITEM, order.getLogisticsBillNo());
                log.info("{}状态: {}", LOG_ITEM, order.getStatus());
                log.info("{}物流公司ID: {}", LOG_ITEM, order.getLogisticsCompanyId());
                log.info("{}物流公司名称: {}", LOG_ITEM, order.getLogisticsCompanyName());
                log.info("{}备注: {}", LOG_ITEM, order.getRemarks());
                log.info("{}服务特性: {}", LOG_ITEM, order.getServiceFeature());
                log.info("{}系统发货时间: {}", LOG_ITEM, order.getGmtSystemSend());

                if (order.getReceiver() != null) {
                    var receiver = order.getReceiver();
                    log.info("{}收货人信息 {}", LOG_SEPARATOR, LOG_SEPARATOR);
                    log.info("{}姓名: {}", LOG_ITEM, receiver.getReceiverName());
                    log.info("{}电话: {}", LOG_ITEM, receiver.getReceiverPhone());
                    log.info("{}手机: {}", LOG_ITEM, receiver.getReceiverMobile());
                    log.info("{}地址: {}", LOG_ITEM, receiver.getReceiverAddress());
                    log.info("{}省份: {}", LOG_ITEM, receiver.getReceiverProvince());
                    log.info("{}城市: {}", LOG_ITEM, receiver.getReceiverCity());
                    log.info("{}区县: {}", LOG_ITEM, receiver.getReceiverCounty());
                }

                if (order.getSender() != null) {
                    var sender = order.getSender();
                    log.info("{}发货人信息 {}", LOG_SEPARATOR, LOG_SEPARATOR);
                    log.info("{}姓名: {}", LOG_ITEM, sender.getSenderName());
                    log.info("{}电话: {}", LOG_ITEM, sender.getSenderPhone());
                    log.info("{}手机: {}", LOG_ITEM, sender.getSenderMobile());
                    log.info("{}地址: {}", LOG_ITEM, sender.getSenderAddress());
                    log.info("{}省份: {}", LOG_ITEM, sender.getSenderProvince());
                    log.info("{}城市: {}", LOG_ITEM, sender.getSenderCity());
                    log.info("{}区县: {}", LOG_ITEM, sender.getSenderCounty());
                }

                if (order.getLogisticsOrderGoods() != null && !order.getLogisticsOrderGoods().isEmpty()) {
                    log.info("{}商品信息 {}", LOG_SEPARATOR, LOG_SEPARATOR);
                    order.getLogisticsOrderGoods().forEach(goods -> {
                        log.info("{}商品名称: {}", LOG_ITEM, goods.getGoodName());
                        log.info("{}数量: {}", LOG_ITEM, goods.getQuantity());
                        log.info("{}单位: {}", LOG_ITEM, goods.getUnit());
                        log.info("{}描述: {}", LOG_ITEM, goods.getDescription());
                    });
                }
            } else {
                log.info("{}错误码: {}", LOG_ITEM, resp.getErrorCode());
                log.info("{}错误信息: {}", LOG_ITEM, resp.getErrorMessage());
            }

            recordMetrics("GetLogisticsInfo", startTime, resp.getSuccess());
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GetLogisticsInfo");
        log.info("{} 测试获取物流信息完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetLogisticsTrace() {
        log.info("{} 开始测试获取物流跟踪信息 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = LogisticsTraceRequestRecord.of(2438608801049540788L, "1688");
        log.info("{}请求参数: orderId={}, webSite={}", LOG_ITEM, request.orderId(), request.webSite());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = logisticsService.getLogisticsTrace(request);

        // Then
        StepVerifier.create(response).assertNext(resp -> {
            log.info("{}响应结果: {}", LOG_ITEM, resp);

            // 检查基本响应字段
            assertThat(resp).isNotNull();
            if (!resp.getSuccess()) {
                assertThat(resp.getErrorCode()).isNotEmpty();
                assertThat(resp.getErrorMessage()).isNotEmpty();
                log.info("{}错误码: {}", LOG_ITEM, resp.getErrorCode());
                log.info("{}错误信息: {}", LOG_ITEM, resp.getErrorMessage());
                recordMetrics("GetLogisticsTrace", startTime, false);
                return;
            }

            // 检查物流跟踪信息
            assertThat(resp.getLogisticsTrace()).isNotNull();
            if (!resp.getLogisticsTrace().isEmpty()) {
                LogisticsTraceInfo traceInfo = resp.getLogisticsTrace().get(0);
                assertThat(traceInfo.getLogisticsId()).isNotEmpty();
                assertThat(traceInfo.getLogisticsBillNo()).isNotEmpty();
                assertThat(traceInfo.getOrderId()).isNotNull();

                log.info("{}物流单号: {}", LOG_ITEM, traceInfo.getLogisticsId());
                log.info("{}运单号: {}", LOG_ITEM, traceInfo.getLogisticsBillNo());
                log.info("{}订单号: {}", LOG_ITEM, traceInfo.getOrderId());

                // 检查物流步骤
                List<LogisticsTraceStep> steps = traceInfo.getLogisticsSteps();
                if (steps != null && !steps.isEmpty()) {
                    LogisticsTraceStep step = steps.get(0);
                    assertThat(step.getAcceptTime()).isNotEmpty();
                    assertThat(step.getRemark()).isNotEmpty();

                    log.info("{}最新物流时间: {}", LOG_ITEM, step.getAcceptTime());
                    log.info("{}最新物流状态: {}", LOG_ITEM, step.getRemark());
                    log.info("{}物流步骤数: {}", LOG_ITEM, steps.size());
                }
            }

            recordMetrics("GetLogisticsTrace", startTime, true);
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GetLogisticsTrace");
        log.info("{} 测试获取物流跟踪信息完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetReceiveAddress() {
        log.info("{} 开始测试获取收货地址列表 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = LogisticsReceiveAddressRequestRecord.of();
        log.info("{}请求参数: 无需参数", LOG_ITEM);

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = logisticsService.getReceiveAddress(request);

        // Then
        StepVerifier.create(response).assertNext(resp -> {
            assertThat(resp).isNotNull();
            log.info("{}是否成功: {}", LOG_ITEM, resp.getSuccess());

            if (resp.getSuccess() && resp.getResult() != null && resp.getResult().getReceiveAddressItems() != null) {
                log.info("{}地址数量: {}", LOG_ITEM, resp.getResult().getReceiveAddressItems().size());

                resp.getResult().getReceiveAddressItems().forEach(item -> {
                    log.info("{}{} 地址详情 {}", LOG_SEPARATOR, item.getId(), LOG_SEPARATOR);
                    log.info("{}地址ID: {}", LOG_ITEM, item.getId());
                    log.info("{}收货人: {}", LOG_ITEM, item.getFullName());
                    log.info("{}详细地址: {}", LOG_ITEM, item.getAddress());
                    log.info("{}地区: {}", LOG_ITEM, item.getAddressCodeText());
                    log.info("{}邮编: {}", LOG_ITEM, item.getPost());
                    log.info("{}电话: {}", LOG_ITEM, item.getPhone());
                    log.info("{}手机: {}", LOG_ITEM, item.getMobilePhone());
                    log.info("{}是否默认: {}", LOG_ITEM, item.getIsDefault());
                    log.info("{}镇编码: {}", LOG_ITEM, item.getTownCode());
                    log.info("{}镇名称: {}", LOG_ITEM, item.getTownName());
                });
            } else {
                log.info("{}错误码: {}", LOG_ITEM, resp.getErrorCode());
                log.info("{}错误信息: {}", LOG_ITEM, resp.getErrorMessage());
            }

            recordMetrics("GetReceiveAddress", startTime, resp.getSuccess());
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GetReceiveAddress");
        log.info("{} 测试获取收货地址列表完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldEstimateFreight() {
        log.info("{} 开始测试商品中国国内运费预估 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = ProductFreightEstimateRequestRecord
            .of(853999508500L, "440000", "441300", "441302", 1L, Collections
                .singletonList(ProductFreightEstimateRequestRecord.LogisticsSkuNumModelRecord.of("5821010886708", 1L)));
        log.info("{}请求参数: offerId={}, toProvinceCode={}, toCityCode={}, toCountryCode={}, totalNum={}", LOG_ITEM, request
            .offerId(), request.toProvinceCode(), request.toCityCode(), request.toCountryCode(), request.totalNum());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = logisticsService.estimateFreight(request);

        // Then
        StepVerifier.create(response).assertNext(resp -> {
            assertThat(resp).isNotNull();
            if (resp.getResult() != null) {
                assertThat(resp.getResult()).isNotNull();
                var result = resp.getResult().getResult();
                log.info("{}运费预估结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
                log.info("{}商品ID: {}", LOG_ITEM, result.getOfferId());
                log.info("{}预估运费: {}", LOG_ITEM, result.getFreight());
                log.info("{}运费模板ID: {}", LOG_ITEM, result.getTemplateId());
                log.info("{}是否包邮: {}", LOG_ITEM, result.getFreePostage());
                log.info("{}计费类型: {}", LOG_ITEM, result.getChargeType());
                log.info("{}首重/件费用: {}", LOG_ITEM, result.getFirstFee());
                log.info("{}续重/件费用: {}", LOG_ITEM, result.getNextFee());
            } else {
                log.info("{}错误码: {}", LOG_ITEM, resp.getErrorCode());
                log.info("{}错误信息: {}", LOG_ITEM, resp.getErrorMessage());
            }

            recordMetrics("EstimateFreight", startTime, resp.getResult().getSuccess());
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("EstimateFreight");
        log.info("{} 测试商品中国国内运费预估完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetLogisticsCompanyList() {
        log.info("{} 开始测试获取物流公司列表 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = LogisticsCompanyListRequestRecord.of();
        log.info("{}请求参数: 无需参数", LOG_ITEM);

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = logisticsService.getLogisticsCompanyList(request);

        // Then
        StepVerifier.create(response).assertNext(resp -> {
            log.info("{}是否成功: {}", LOG_ITEM, resp.getSuccess());
            if (resp.getSuccess() && resp.getResult() != null && !resp.getResult().isEmpty()) {
                var company = resp.getResult().get(0);
                log.info("{}物流公司ID: {}", LOG_ITEM, company.getId());
                log.info("{}物流公司名称: {}", LOG_ITEM, company.getCompanyName());
                log.info("{}物流公司编号: {}", LOG_ITEM, company.getCompanyNo());
                log.info("{}服务电话: {}", LOG_ITEM, company.getCompanyPhone());
                log.info("{}是否支持打印: {}", LOG_ITEM, company.getSupportPrint());
                log.info("{}全拼: {}", LOG_ITEM, company.getSpelling());
                log.info("{}物流公司总数: {}", LOG_ITEM, resp.getResult().size());
            } else {
                log.info("{}错误码: {}", LOG_ITEM, resp.getErrorCode());
                log.info("{}错误信息: {}", LOG_ITEM, resp.getErrorMessage());
            }

            recordMetrics("GetLogisticsCompanyList", startTime, resp.getSuccess());
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GetLogisticsCompanyList");
        log.info("{} 测试获取物流公司列表完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetShippingInsurance() {
        log.info("{} 开始测试运费险信息查询 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = LogisticsInsuranceRequestRecord.of(2441868278075540788L, "givenByMerchant");
        log.info("{}请求参数: orderId={}, type={}", LOG_ITEM, request.orderId(), request.type());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = logisticsService.getShippingInsurance(request);

        // Then
        StepVerifier.create(response).assertNext(resp -> {
            log.info("{}是否成功: {}", LOG_ITEM, resp.getResult().getSuccess());
            if (resp.getResult() != null && resp.getResult().getSuccess()) {
                var result = resp.getResult();
                log.info("{}响应码: {}", LOG_ITEM, result.getCode());
                log.info("{}响应信息: {}", LOG_ITEM, result.getMessage());

                if (result.getResult() != null) {
                    var policyResult = result.getResult();
                    log.info("{}保单ID: {}", LOG_ITEM, policyResult.getInsuranceId());
                    log.info("{}订单ID: {}", LOG_ITEM, policyResult.getOrderId());

                    if (policyResult.getTradeClaimList() != null && !policyResult.getTradeClaimList().isEmpty()) {
                        var claim = policyResult.getTradeClaimList().get(0);
                        log.info("{}理赔信息 {}", LOG_SEPARATOR, LOG_SEPARATOR);
                        log.info("{}理赔单ID: {}", LOG_ITEM, claim.getClaimId());
                        log.info("{}理赔金额: {}", LOG_ITEM, claim.getClaimAmount());
                        log.info("{}理赔状态: {}", LOG_ITEM, claim.getStatus());
                        log.info("{}支付宝交易流水号: {}", LOG_ITEM, claim.getTradeNO());
                        log.info("{}申请时间: {}", LOG_ITEM, claim.getApplicationTime());
                        log.info("{}打款时间: {}", LOG_ITEM, claim.getPayTime());
                    }
                }
            } else {
                log.info("{}错误码: {}", LOG_ITEM, resp.getErrorCode());
                log.info("{}错误信息: {}", LOG_ITEM, resp.getErrorMessage());
            }

            recordMetrics("GetShippingInsurance", startTime, resp.getResult().getSuccess());
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GetShippingInsurance");
        log.info("{} 测试运费险信息查询完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }
}