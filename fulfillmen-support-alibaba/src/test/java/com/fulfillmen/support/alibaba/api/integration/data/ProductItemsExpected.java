/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.integration.data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品项目预期数据
 * 
 * <AUTHOR>
 * @created 2025-07-12
 */
public record ProductItemsExpected(
    int expectedCount,
    List<String> expectedProductTypes,
    FirstItemExpected firstItem,
    boolean validateTotalAmount
) {

    /**
     * 创建商品项目预期数据的构建器
     */
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {

        private int expectedCount;
        private List<String> expectedProductTypes;
        private FirstItemExpected firstItem;
        private boolean validateTotalAmount = true;

        public Builder expectedCount(int expectedCount) {
            this.expectedCount = expectedCount;
            return this;
        }

        public Builder expectedProductTypes(List<String> expectedProductTypes) {
            this.expectedProductTypes = expectedProductTypes;
            return this;
        }

        public Builder expectedProductTypes(String... types) {
            this.expectedProductTypes = List.of(types);
            return this;
        }

        public Builder firstItem(FirstItemExpected firstItem) {
            this.firstItem = firstItem;
            return this;
        }

        public Builder firstItem(Long productID, Long skuID, Integer quantity, BigDecimal price) {
            this.firstItem = new FirstItemExpected(productID, skuID, quantity, price);
            return this;
        }

        public Builder validateTotalAmount(boolean validateTotalAmount) {
            this.validateTotalAmount = validateTotalAmount;
            return this;
        }

        public ProductItemsExpected build() {
            return new ProductItemsExpected(
                expectedCount, expectedProductTypes, firstItem, validateTotalAmount
            );
        }
    }
}
