/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.webhook.handler.OrderSuccessMessageHandler;

import lombok.extern.slf4j.Slf4j;

/**
 * MessageDispatcher 集成测试
 * 测试完整的消息处理流程，包括真实的组件交互
 *
 * <AUTHOR>
 * @date 2025/7/10
 * @since 1.0.0
 */
@Slf4j
@SpringBootTest
@TestPropertySource(properties = {
    "alibaba.open1688.secret-key=",  // 空密钥，跳过签名验证
    "spring.redis.host=localhost",
    "spring.redis.port=6379"
})
public class MessageDispatcherIntegrationTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    private MessageDispatcher messageDispatcher;
    private MessageRouter messageRouter;
    private IdempotentManager idempotentManager;
    private SignatureValidator signatureValidator;

    /**
     * 测试用例的真实数据 - 基于用户提供的消息
     */
    private static final String VALID_MESSAGE_BODY = """
        [{
            "bizKey": "2806599398122540788",
            "data": {
                "buyerMemberId": "b2b-2207416548807a4d12",
                "currentStatus": "success",
                "orderId": 2806599398122540788,
                "sellerMemberId": "b2b-221280776451649a09",
                "msgSendTime": "2025-07-10 17:55:46"
            },
            "gmtBorn": 1752141346107,
            "msgId": "139830976934",
            "type": "ORDER_BUYER_VIEW_ORDER_SUCCESS",
            "userInfo": "b2b-2207416548807a4d12"
        }]
        """;

    @BeforeEach
    void setUp() {
        // 创建真实的组件实例
        signatureValidator = new SignatureValidator();
        idempotentManager = new IdempotentManager();

        // 创建消息路由器并注册处理器
        messageRouter = new MessageRouter(List.of(new OrderSuccessMessageHandler()));
        messageRouter.registerHandlers();

        // 创建消息分发器
        messageDispatcher = new MessageDispatcher(messageRouter, idempotentManager, signatureValidator);
    }

    @Test
    @Tag("integration")
    void shouldProcessCompleteMessageFlow() {
        log.info("{} 开始端到端消息处理测试 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String signature = ""; // 空签名，会跳过验证
        log.info("{}消息长度: {}", LOG_ITEM, VALID_MESSAGE_BODY.length());

        long startTime = System.currentTimeMillis();

        // When
        List<MessageResult> results = messageDispatcher.dispatch(VALID_MESSAGE_BODY, signature);

        long duration = System.currentTimeMillis() - startTime;

        // Then
        assertThat(results).isNotNull();
        assertThat(results).hasSize(1);

        MessageResult result = results.get(0);
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMsgId()).isEqualTo("139830976934");
        assertThat(result.getProcessedAt()).isNotNull();
        assertThat(result.getData()).isNotNull();

        log.info("{}处理结果: 成功={}, 消息ID={}", LOG_ITEM, result.isSuccess(), result.getMsgId());
        log.info("{}处理时间: {}ms", LOG_ITEM, duration);
        log.info("{}处理数据: {}", LOG_ITEM, result.getData());

        log.info("{} 端到端消息处理测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("integration")
    void shouldHandleIdempotencyCorrectly() {
        log.info("{} 开始幂等性集成测试 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String signature = "";
        log.info("{}测试消息幂等性处理", LOG_ITEM);

        // When - 第一次处理
        List<MessageResult> firstResults = messageDispatcher.dispatch(VALID_MESSAGE_BODY, signature);

        // When - 第二次处理相同消息
        List<MessageResult> secondResults = messageDispatcher.dispatch(VALID_MESSAGE_BODY, signature);

        // Then
        assertThat(firstResults).hasSize(1);
        assertThat(secondResults).hasSize(1);

        MessageResult firstResult = firstResults.get(0);
        MessageResult secondResult = secondResults.get(0);

        // 第一次应该成功处理
        assertThat(firstResult.isSuccess()).isTrue();
        assertThat(firstResult.getMsgId()).isEqualTo("139830976934");

        // 第二次应该检测到重复
        assertThat(secondResult.isSuccess()).isTrue();
        assertThat(secondResult.getMsgId()).isEqualTo("139830976934");
        assertThat(secondResult.getErrorMessage()).contains("消息重复");

        log.info("{}第一次处理结果: {}", LOG_ITEM, firstResult.isSuccess());
        log.info("{}第二次处理结果: {}", LOG_ITEM, secondResult.getErrorMessage());

        log.info("{} 幂等性集成测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("integration")
    void shouldHandleUnknownMessageType() {
        log.info("{} 开始未知消息类型集成测试 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String unknownTypeMessage = """
            [{
                "bizKey": "2806599398122540788",
                "data": {
                    "someField": "someValue"
                },
                "gmtBorn": 1752141346107,
                "msgId": "139830976999",
                "type": "UNKNOWN_MESSAGE_TYPE",
                "userInfo": "b2b-2207416548807a4d12"
            }]
            """;

        log.info("{}未知消息类型测试", LOG_ITEM);

        // When
        List<MessageResult> results = messageDispatcher.dispatch(unknownTypeMessage, "");

        // Then
        assertThat(results).isNotNull();
        assertThat(results).hasSize(1);

        MessageResult result = results.get(0);
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMsgId()).isEqualTo("139830976999");
        assertThat(result.getErrorMessage()).contains("未找到适合的消息处理器");

        log.info("{}未知类型处理结果: {}", LOG_ITEM, result.getErrorMessage());

        log.info("{} 未知消息类型集成测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("integration")
    void shouldHandleMultipleMessagesWithMixedResults() {
        log.info("{} 开始混合结果多消息集成测试 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String mixedMessages = """
            [{
                "bizKey": "2806599398122540788",
                "data": {
                    "buyerMemberId": "b2b-2207416548807a4d12",
                    "currentStatus": "success",
                    "orderId": 2806599398122540788,
                    "sellerMemberId": "b2b-221280776451649a09",
                    "msgSendTime": "2025-07-10 17:55:46"
                },
                "gmtBorn": 1752141346107,
                "msgId": "139830976950",
                "type": "ORDER_BUYER_VIEW_ORDER_SUCCESS",
                "userInfo": "b2b-2207416548807a4d12"
            },
            {
                "bizKey": "2806599398122540789",
                "data": {
                    "someField": "someValue"
                },
                "gmtBorn": 1752141346108,
                "msgId": "139830976951",
                "type": "UNKNOWN_MESSAGE_TYPE",
                "userInfo": "b2b-2207416548807a4d13"
            }]
            """;

        log.info("{}混合消息类型测试", LOG_ITEM);

        // When
        List<MessageResult> results = messageDispatcher.dispatch(mixedMessages, "");

        // Then
        assertThat(results).isNotNull();
        assertThat(results).hasSize(2);

        // 第一个消息应该成功
        MessageResult result1 = results.get(0);
        assertThat(result1.isSuccess()).isTrue();
        assertThat(result1.getMsgId()).isEqualTo("139830976950");

        // 第二个消息应该失败（未知类型）
        MessageResult result2 = results.get(1);
        assertThat(result2.isSuccess()).isFalse();
        assertThat(result2.getMsgId()).isEqualTo("139830976951");
        assertThat(result2.getErrorMessage()).contains("未找到适合的消息处理器");

        log.info("{}第一个消息结果: 成功={}", LOG_ITEM, result1.isSuccess());
        log.info("{}第二个消息结果: 成功={}, 错误={}", LOG_ITEM, result2.isSuccess(), result2.getErrorMessage());

        log.info("{} 混合结果多消息集成测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("performance")
    void shouldProcessMessageWithinReasonableTime() {
        log.info("{} 开始消息处理性能测试 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String signature = "";
        int iterations = 10;

        log.info("{}性能测试迭代次数: {}", LOG_ITEM, iterations);

        // When
        long totalTime = 0;
        for (int i = 0; i < iterations; i++) {
            // 使用不同的msgId避免幂等性影响
            String testMessage = VALID_MESSAGE_BODY.replace("139830976934", "139830976934" + i);

            long startTime = System.currentTimeMillis();
            List<MessageResult> results = messageDispatcher.dispatch(testMessage, signature);
            long duration = System.currentTimeMillis() - startTime;

            totalTime += duration;

            assertThat(results).hasSize(1);
            assertThat(results.get(0).isSuccess()).isTrue();
        }

        // Then
        long averageTime = totalTime / iterations;
        assertThat(averageTime).isLessThan(100); // 平均处理时间应该小于100ms

        log.info("{}总处理时间: {}ms", LOG_ITEM, totalTime);
        log.info("{}平均处理时间: {}ms", LOG_ITEM, averageTime);
        log.info("{}处理迭代次数: {}", LOG_ITEM, iterations);

        log.info("{} 消息处理性能测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }
}
