/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.integration.data;

/**
 * 订单评价信息预期数据
 * 
 * <AUTHOR>
 * @created 2025-07-12
 */
public record OrderRateInfoExpected(
    Integer buyerRateStatus,
    Integer sellerRateStatus
) {

    /**
     * 创建订单评价信息预期数据的构建器
     */
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {

        private Integer buyerRateStatus;
        private Integer sellerRateStatus;

        public Builder buyerRateStatus(Integer buyerRateStatus) {
            this.buyerRateStatus = buyerRateStatus;
            return this;
        }

        public Builder sellerRateStatus(Integer sellerRateStatus) {
            this.sellerRateStatus = sellerRateStatus;
            return this;
        }

        public OrderRateInfoExpected build() {
            return new OrderRateInfoExpected(buyerRateStatus, sellerRateStatus);
        }
    }
}
