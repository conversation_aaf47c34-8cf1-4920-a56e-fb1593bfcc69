/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.integration.data;

/**
 * 订单业务信息预期数据
 * 
 * <AUTHOR>
 * @created 2025-07-12
 */
public record OrderBizInfoExpected(
    Boolean odsCyd,
    Boolean creditOrder,
    Boolean dropshipping,
    String shippingInsurance
) {

    /**
     * 创建订单业务信息预期数据的构建器
     */
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {

        private Boolean odsCyd;
        private Boolean creditOrder;
        private Boolean dropshipping;
        private String shippingInsurance;

        public Builder odsCyd(Boolean odsCyd) {
            this.odsCyd = odsCyd;
            return this;
        }

        public Builder creditOrder(Boolean creditOrder) {
            this.creditOrder = creditOrder;
            return this;
        }

        public Builder dropshipping(Boolean dropshipping) {
            this.dropshipping = dropshipping;
            return this;
        }

        public Builder shippingInsurance(String shippingInsurance) {
            this.shippingInsurance = shippingInsurance;
            return this;
        }

        public OrderBizInfoExpected build() {
            return new OrderBizInfoExpected(odsCyd, creditOrder, dropshipping, shippingInsurance);
        }
    }
}
