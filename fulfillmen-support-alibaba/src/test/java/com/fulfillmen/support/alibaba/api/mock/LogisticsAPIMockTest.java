/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.mock;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.api.LogisticsAPI;
import com.fulfillmen.support.alibaba.api.request.logistics.AddressCodeParseRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsCompanyListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsFreightTemplateRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsInfoRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsInsuranceRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsOutOrderIdRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsReceiveAddressRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.LogisticsTraceRequestRecord;
import com.fulfillmen.support.alibaba.api.request.logistics.ProductFreightEstimateRequestRecord;
import com.fulfillmen.support.alibaba.api.response.logistics.AddressCodeParseResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsCompanyListResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsFreightTemplateResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsInfoResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsInsuranceResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsOutOrderIdResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsReceiveAddressResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.LogisticsTraceResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.ProductFreightEstimateResponse;
import com.fulfillmen.support.alibaba.api.response.logistics.ProductFreightEstimateResponse.ApiResult;
import com.fulfillmen.support.alibaba.api.response.model.FreightTemplate;
import com.fulfillmen.support.alibaba.api.response.model.LogisticsTraceInfo;
import com.fulfillmen.support.alibaba.api.response.model.LogisticsTraceStep;
import com.fulfillmen.support.alibaba.api.response.model.OpenPlatformLogisticsOrder;
import com.fulfillmen.support.alibaba.api.response.model.OutOrderIdResult;
import com.fulfillmen.support.alibaba.api.response.model.ReceiveAddress;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import com.fulfillmen.support.alibaba.service.ILogisticsService;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

/**
 * 1688物流API Mock测试
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Slf4j
class LogisticsAPIMockTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    @Autowired
    private ILogisticsService logisticsService;

    @MockBean
    private LogisticsAPI logisticsAPI;

    @Test
    void shouldGetFreightTemplate() {
        log.info("{} 开始测试获取物流模板详情 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = LogisticsFreightTemplateRequestRecord.of(1234567L, true, true);
        log.info("{}请求参数: templateId={}, querySubTemplate={}, queryRate={}", LOG_ITEM, request.templateId(), request
            .querySubTemplate(), request.queryRate());

        // Mock 成功响应
        LogisticsFreightTemplateResponse successResponse = new LogisticsFreightTemplateResponse();
        List<FreightTemplate> templates = new ArrayList<>();
        FreightTemplate template = new FreightTemplate();
        template.setId(1234567L);
        template.setName("测试模板");
        template.setAddressCodeText("浙江省 杭州市");
        template.setRemark("测试备注");
        templates.add(template);
        successResponse.setResult(templates);
        successResponse.setSuccess(true);

        when(logisticsAPI.getFreightTemplate(any(), any())).thenReturn(Mono.just(successResponse));

        // When
        long startTime = System.currentTimeMillis();
        var response = logisticsService.getFreightTemplate(request);

        // Then
        StepVerifier.create(response).assertNext(resp -> {
            assertThat(resp).isNotNull();
            assertThat(resp.getSuccess()).isTrue();
            assertThat(resp.getResult()).isNotEmpty();

            var resultTemplate = resp.getResult().get(0);
            assertThat(resultTemplate.getId()).isEqualTo(1234567L);
            assertThat(resultTemplate.getName()).isEqualTo("测试模板");
            assertThat(resultTemplate.getAddressCodeText()).isEqualTo("浙江省 杭州市");
            assertThat(resultTemplate.getRemark()).isEqualTo("测试备注");

            log.info("{}是否成功: {}", LOG_ITEM, resp.getSuccess());
            log.info("{}模板ID: {}", LOG_ITEM, resultTemplate.getId());
            log.info("{}模板名称: {}", LOG_ITEM, resultTemplate.getName());
            log.info("{}地址: {}", LOG_ITEM, resultTemplate.getAddressCodeText());
            log.info("{}备注: {}", LOG_ITEM, resultTemplate.getRemark());

            recordMetrics("GetFreightTemplate", startTime, resp.getSuccess());
        }).verifyComplete();

        log.info("{} 测试获取物流模板详情完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleGetFreightTemplateError() {
        log.info("{} 开始测试获取物流模板详情-错误场景 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = LogisticsFreightTemplateRequestRecord.of(9999999L, false, false);
        log.info("{}请求参数: templateId={}", LOG_ITEM, request.templateId());

        // Mock 错误响应
        LogisticsFreightTemplateResponse errorResponse = new LogisticsFreightTemplateResponse();
        errorResponse.setSuccess(false);
        errorResponse.setErrorCode("400");
        errorResponse.setErrorMessage("模板不存在");

        when(logisticsAPI.getFreightTemplate(any(), any())).thenReturn(Mono.just(errorResponse));

        // When
        long startTime = System.currentTimeMillis();
        var response = logisticsService.getFreightTemplate(request);

        // Then
        StepVerifier.create(response).assertNext(resp -> {
            assertThat(resp).isNotNull();
            assertThat(resp.getSuccess()).isFalse();
            assertThat(resp.getErrorCode()).isEqualTo("400");
            assertThat(resp.getErrorMessage()).isEqualTo("模板不存在");

            log.info("{}是否成功: {}", LOG_ITEM, resp.getSuccess());
            log.info("{}错误码: {}", LOG_ITEM, resp.getErrorCode());
            log.info("{}错误信息: {}", LOG_ITEM, resp.getErrorMessage());

            recordMetrics("GetFreightTemplate", startTime, resp.getSuccess());
        }).verifyComplete();

        log.info("{} 测试获取物流模板详情-错误场景完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldParseAddressCode() {
        log.info("{} 开始测试解析地址码 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = AddressCodeParseRequestRecord.of("浙江省杭州市余杭区网商路699号");
        log.info("{}请求参数: addressInfo={}", LOG_ITEM, request.addressInfo());

        // Mock 成功响应
        AddressCodeParseResponse successResponse = new AddressCodeParseResponse();
        ReceiveAddress address = new ReceiveAddress();
        address.setAddress("网商路699号");
        address.setAddressCode("330110");
        address.setFullName("浙江省杭州市余杭区");
        successResponse.setResult(address);
        successResponse.setSuccess(true);

        when(logisticsAPI.parseAddressCode(any(), any())).thenReturn(Mono.just(successResponse));

        // When
        long startTime = System.currentTimeMillis();
        var response = logisticsService.parseAddressCode(request);

        // Then
        StepVerifier.create(response).assertNext(resp -> {
            assertThat(resp).isNotNull();
            assertThat(resp.getSuccess()).isTrue();
            assertThat(resp.getResult()).isNotNull();

            var resultAddress = resp.getResult();
            assertThat(resultAddress.getAddress()).isEqualTo("网商路699号");
            assertThat(resultAddress.getAddressCode()).isEqualTo("330110");
            assertThat(resultAddress.getFullName()).isEqualTo("浙江省杭州市余杭区");

            log.info("{}是否成功: {}", LOG_ITEM, resp.getSuccess());
            log.info("{}详细地址: {}", LOG_ITEM, resultAddress.getAddress());
            log.info("{}地区码: {}", LOG_ITEM, resultAddress.getAddressCode());
            log.info("{}完整地址: {}", LOG_ITEM, resultAddress.getFullName());

            recordMetrics("ParseAddressCode", startTime, resp.getSuccess());
        }).verifyComplete();

        log.info("{} 测试解析地址码完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetOutOrderId() {
        log.info("{} 开始测试查询外部订单ID {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = LogisticsOutOrderIdRequestRecord.of("SF1234567890", null);
        log.info("{}请求参数: shipmentId={}", LOG_ITEM, request.shipmentId());

        // Mock 成功响应
        LogisticsOutOrderIdResponse successResponse = new LogisticsOutOrderIdResponse();
        OutOrderIdResult outOrderIdResult = new OutOrderIdResult();
        outOrderIdResult.setOutOrderId("OUT123456");
        outOrderIdResult.setOrderId("ORDER123456");
        successResponse.setResult(outOrderIdResult);
        successResponse.setSuccess(true);

        when(logisticsAPI.getOutOrderId(any(), any())).thenReturn(Mono.just(successResponse));

        // When
        long startTime = System.currentTimeMillis();
        var response = logisticsService.getOutOrderId(request);

        // Then
        StepVerifier.create(response).assertNext(resp -> {
            assertThat(resp).isNotNull();
            assertThat(resp.getSuccess()).isTrue();
            assertThat(resp.getResult()).isNotNull();

            var result = resp.getResult();
            assertThat(result.getOutOrderId()).isEqualTo("OUT123456");
            assertThat(result.getOrderId()).isEqualTo("ORDER123456");

            log.info("{}是否成功: {}", LOG_ITEM, resp.getSuccess());
            log.info("{}外部订单ID: {}", LOG_ITEM, result.getOutOrderId());
            log.info("{}订单ID: {}", LOG_ITEM, result.getOrderId());

            recordMetrics("GetOutOrderId", startTime, resp.getSuccess());
        }).verifyComplete();

        log.info("{} 测试查询外部订单ID完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetLogisticsInfo() {
        log.info("{} 开始测试获取物流信息 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = LogisticsInfoRequestRecord.of("ORDER123456", List
            .of(LogisticsInfoRequestRecord.LogisticsInfoField.LOGISTICS_ID, LogisticsInfoRequestRecord.LogisticsInfoField.LOGISTICS_BILL_NO, LogisticsInfoRequestRecord.LogisticsInfoField.STATUS), "china");

        log.info("{}请求参数: orderId={}, fields={}, webSite={}", LOG_ITEM, request.orderId(), request.fields(), request
            .webSite());

        // Mock 成功响应
        LogisticsInfoResponse successResponse = new LogisticsInfoResponse();
        OpenPlatformLogisticsOrder logisticsOrder = new OpenPlatformLogisticsOrder();
        logisticsOrder.setLogisticsId("LP123456");
        logisticsOrder.setLogisticsBillNo("SF123456");
        logisticsOrder.setStatus("ACCEPT");
        List<OpenPlatformLogisticsOrder> orders = new ArrayList<>();
        orders.add(logisticsOrder);
        successResponse.setResult(orders);
        successResponse.setSuccess(true);

        when(logisticsAPI.getLogisticsInfo(any(), any())).thenReturn(Mono.just(successResponse));

        // When
        long startTime = System.currentTimeMillis();
        var response = logisticsService.getLogisticsInfo(request);

        // Then
        StepVerifier.create(response).assertNext(resp -> {
            assertThat(resp).isNotNull();
            assertThat(resp.getSuccess()).isTrue();
            assertThat(resp.getResult()).isNotNull();

            var result = resp.getResult();
            assertThat(result.get(0).getLogisticsId()).isEqualTo("LP123456");
            assertThat(result.get(0).getLogisticsBillNo()).isEqualTo("SF123456");
            assertThat(result.get(0).getStatus()).isEqualTo("ACCEPT");

            log.info("{}是否成功: {}", LOG_ITEM, resp.getSuccess());
            log.info("{}物流ID: {}", LOG_ITEM, result.get(0).getLogisticsId());
            log.info("{}物流单号: {}", LOG_ITEM, result.get(0).getLogisticsBillNo());
            log.info("{}物流状态: {}", LOG_ITEM, result.get(0).getStatus());

            recordMetrics("GetLogisticsInfo", startTime, resp.getSuccess());
        }).verifyComplete();

        log.info("{} 测试获取物流信息完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleGetLogisticsInfoError() {
        log.info("{} 开始测试获取物流信息-错误场景 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = LogisticsInfoRequestRecord.of("", Collections.emptyList(), null);
        log.info("{}请求参数: orderId=null", LOG_ITEM);

        // Then
        StepVerifier.create(logisticsService.getLogisticsInfo(request)).expectErrorMatches(throwable -> {
            assertThat(throwable).isInstanceOf(AlibabaServiceValidationException.class).hasMessage("订单ID不能为空");

            log.info("{}错误类型: {}", LOG_ITEM, throwable.getClass().getSimpleName());
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());

            return true;
        }).verify();

        log.info("{} 测试获取物流信息-错误场景完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetLogisticsTrace() {
        log.info("{} 开始测试获取物流跟踪信息 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = LogisticsTraceRequestRecord.of(188983797838441800L, "1688", "LP00106397027178");
        log.info("{}请求参数: orderId={}, webSite={}, logisticsId={}", LOG_ITEM, request.orderId(), request
            .webSite(), request.logisticsId());

        // Mock 成功响应
        LogisticsTraceResponse successResponse = new LogisticsTraceResponse();
        successResponse.setSuccess(true);

        List<LogisticsTraceInfo> traceInfoList = new ArrayList<>();
        LogisticsTraceInfo traceInfo = new LogisticsTraceInfo();
        traceInfo.setLogisticsId("LP00106397027178");
        traceInfo.setLogisticsBillNo("3832890717253");
        traceInfo.setOrderId(188983797838441800L);

        List<LogisticsTraceStep> steps = new ArrayList<>();
        LogisticsTraceStep step1 = new LogisticsTraceStep();
        step1.setAcceptTime("2018-07-24 21:55:33");
        step1.setRemark("在广东广州天河区天平架一公司进行揽件扫描");
        steps.add(step1);

        LogisticsTraceStep step2 = new LogisticsTraceStep();
        step2.setAcceptTime("2018-07-24 22:10:50");
        step2.setRemark("在广东广州天河区天平架一公司进行下级地点扫描，即将发往：浙江宁波分拨中心");
        steps.add(step2);

        traceInfo.setLogisticsSteps(steps);
        traceInfoList.add(traceInfo);
        successResponse.setLogisticsTrace(traceInfoList);

        when(logisticsAPI.getLogisticsTrace(any(), any())).thenReturn(Mono.just(successResponse));

        // When
        long startTime = System.currentTimeMillis();
        var response = logisticsService.getLogisticsTrace(request);

        // Then
        StepVerifier.create(response).assertNext(resp -> {
            assertThat(resp).isNotNull();
            assertThat(resp.getSuccess()).isTrue();
            assertThat(resp.getLogisticsTrace()).hasSize(1);

            LogisticsTraceInfo info = resp.getLogisticsTrace().get(0);
            assertThat(info.getLogisticsId()).isEqualTo("LP00106397027178");
            assertThat(info.getLogisticsBillNo()).isEqualTo("3832890717253");
            assertThat(info.getOrderId()).isEqualTo(188983797838441800L);

            List<LogisticsTraceStep> logisticsSteps = info.getLogisticsSteps();
            assertThat(logisticsSteps).hasSize(2);

            LogisticsTraceStep firstStep = logisticsSteps.get(0);
            assertThat(firstStep.getAcceptTime()).isEqualTo("2018-07-24 21:55:33");
            assertThat(firstStep.getRemark()).isEqualTo("在广东广州天河区天平架一公司进行揽件扫描");

            log.info("{}是否成功: {}", LOG_ITEM, resp.getSuccess());
            log.info("{}物流单号: {}", LOG_ITEM, info.getLogisticsId());
            log.info("{}运单号: {}", LOG_ITEM, info.getLogisticsBillNo());
            log.info("{}订单号: {}", LOG_ITEM, info.getOrderId());
            log.info("{}物流步骤数: {}", LOG_ITEM, logisticsSteps.size());

            recordMetrics("GetLogisticsTrace", startTime, resp.getSuccess());
        }).verifyComplete();

        log.info("{} 测试获取物流跟踪信息完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleGetLogisticsTraceError() {
        log.info("{} 开始测试获取物流跟踪信息-错误场景 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = LogisticsTraceRequestRecord.of(999999999999L, "1688");
        log.info("{}请求参数: orderId={}, webSite={}", LOG_ITEM, request.orderId(), request.webSite());

        // Mock 错误响应
        LogisticsTraceResponse errorResponse = new LogisticsTraceResponse();
        errorResponse.setSuccess(false);
        errorResponse.setErrorCode("404");
        errorResponse.setErrorMessage("无法找到相对应的物流单跟踪信息。");

        when(logisticsAPI.getLogisticsTrace(any(), any())).thenReturn(Mono.just(errorResponse));

        // When
        long startTime = System.currentTimeMillis();
        var response = logisticsService.getLogisticsTrace(request);

        // Then
        StepVerifier.create(response).assertNext(resp -> {
            assertThat(resp).isNotNull();
            assertThat(resp.getSuccess()).isFalse();
            assertThat(resp.getErrorCode()).isEqualTo("404");
            assertThat(resp.getErrorMessage()).isEqualTo("无法找到相对应的物流单跟踪信息。");
            assertThat(resp.getLogisticsTrace()).isNull();

            log.info("{}是否成功: {}", LOG_ITEM, resp.getSuccess());
            log.info("{}错误码: {}", LOG_ITEM, resp.getErrorCode());
            log.info("{}错误信息: {}", LOG_ITEM, resp.getErrorMessage());

            recordMetrics("GetLogisticsTrace", startTime, resp.getSuccess());
        }).verifyComplete();

        log.info("{} 测试获取物流跟踪信息-错误场景完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetReceiveAddress() {
        log.info("{} 开始测试获取收货地址列表 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = LogisticsReceiveAddressRequestRecord.of();
        log.info("{}请求参数: 无需参数", LOG_ITEM);

        // Mock 成功响应
        LogisticsReceiveAddressResponse successResponse = new LogisticsReceiveAddressResponse();
        LogisticsReceiveAddressResponse.Result result = new LogisticsReceiveAddressResponse.Result();
        List<LogisticsReceiveAddressResponse.ReceiveAddressItem> items = new ArrayList<>();

        LogisticsReceiveAddressResponse.ReceiveAddressItem item = new LogisticsReceiveAddressResponse.ReceiveAddressItem();
        item.setId(322683081L);
        item.setFullName("张三");
        item.setAddress("网商路699号");
        item.setPost("310000");
        item.setPhone("0571-88888888");
        item.setMobilePhone("13800138000");
        item.setAddressCode("330110");
        item.setAddressCodeText("浙江省 杭州市 滨江区");
        item.setIsDefault(true);
        item.setTownCode("123");
        item.setTownName("长河镇");

        items.add(item);
        result.setReceiveAddressItems(items);
        successResponse.setResult(result);
        successResponse.setSuccess(true);

        when(logisticsAPI.getReceiveAddress(any(), any())).thenReturn(Mono.just(successResponse));

        // When
        long startTime = System.currentTimeMillis();
        var response = logisticsService.getReceiveAddress(request);

        // Then
        StepVerifier.create(response).assertNext(resp -> {
            assertThat(resp).isNotNull();
            assertThat(resp.getSuccess()).isTrue();
            assertThat(resp.getResult()).isNotNull();
            assertThat(resp.getResult().getReceiveAddressItems()).hasSize(1);

            var addressItem = resp.getResult().getReceiveAddressItems().get(0);
            assertThat(addressItem.getId()).isEqualTo(322683081L);
            assertThat(addressItem.getFullName()).isEqualTo("张三");
            assertThat(addressItem.getAddress()).isEqualTo("网商路699号");
            assertThat(addressItem.getPost()).isEqualTo("310000");
            assertThat(addressItem.getPhone()).isEqualTo("0571-88888888");
            assertThat(addressItem.getMobilePhone()).isEqualTo("13800138000");
            assertThat(addressItem.getAddressCode()).isEqualTo("330110");
            assertThat(addressItem.getAddressCodeText()).isEqualTo("浙江省 杭州市 滨江区");
            assertThat(addressItem.getIsDefault()).isTrue();
            assertThat(addressItem.getTownCode()).isEqualTo("123");
            assertThat(addressItem.getTownName()).isEqualTo("长河镇");

            log.info("{}是否成功: {}", LOG_ITEM, resp.getSuccess());
            log.info("{}地址数量: {}", LOG_ITEM, resp.getResult().getReceiveAddressItems().size());
            log.info("{}地址ID: {}", LOG_ITEM, addressItem.getId());
            log.info("{}收货人: {}", LOG_ITEM, addressItem.getFullName());
            log.info("{}详细地址: {}", LOG_ITEM, addressItem.getAddress());
            log.info("{}地区: {}", LOG_ITEM, addressItem.getAddressCodeText());
            log.info("{}邮编: {}", LOG_ITEM, addressItem.getPost());
            log.info("{}电话: {}", LOG_ITEM, addressItem.getPhone());
            log.info("{}手机: {}", LOG_ITEM, addressItem.getMobilePhone());
            log.info("{}是否默认: {}", LOG_ITEM, addressItem.getIsDefault());
            log.info("{}镇编码: {}", LOG_ITEM, addressItem.getTownCode());
            log.info("{}镇名称: {}", LOG_ITEM, addressItem.getTownName());

            recordMetrics("GetReceiveAddress", startTime, resp.getSuccess());
        }).verifyComplete();

        log.info("{} 测试获取收货地址列表完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleGetReceiveAddressError() {
        log.info("{} 开始测试获取收货地址列表-错误场景 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = LogisticsReceiveAddressRequestRecord.of();
        log.info("{}请求参数: 无需参数", LOG_ITEM);

        // Mock 错误响应
        LogisticsReceiveAddressResponse errorResponse = new LogisticsReceiveAddressResponse();
        errorResponse.setSuccess(false);
        errorResponse.setErrorCode("400");
        errorResponse.setErrorMessage("获取收货地址失败");

        when(logisticsAPI.getReceiveAddress(any(), any())).thenReturn(Mono.just(errorResponse));

        // When
        long startTime = System.currentTimeMillis();
        var response = logisticsService.getReceiveAddress(request);

        // Then
        StepVerifier.create(response).assertNext(resp -> {
            assertThat(resp).isNotNull();
            assertThat(resp.getSuccess()).isFalse();
            assertThat(resp.getErrorCode()).isEqualTo("400");
            assertThat(resp.getErrorMessage()).isEqualTo("获取收货地址失败");

            log.info("{}是否成功: {}", LOG_ITEM, resp.getSuccess());
            log.info("{}错误码: {}", LOG_ITEM, resp.getErrorCode());
            log.info("{}错误信息: {}", LOG_ITEM, resp.getErrorMessage());

            recordMetrics("GetReceiveAddress", startTime, resp.getSuccess());
        }).verifyComplete();

        log.info("{} 测试获取收货地址列表-错误场景完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldEstimateFreight() {
        log.info("{} 开始测试商品中国国内运费预估 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = ProductFreightEstimateRequestRecord.of(123456789L, "330000", "330100", "330108", 1L, Collections
            .singletonList(ProductFreightEstimateRequestRecord.LogisticsSkuNumModelRecord.of("123456", 1L)));
        log.info("{}请求参数: offerId={}, toProvinceCode={}, toCityCode={}, toCountryCode={}, totalNum={}", LOG_ITEM, request
            .offerId(), request.toProvinceCode(), request.toCityCode(), request.toCountryCode(), request.totalNum());

        var mockResponse = createMockFreightEstimateResponse();
        when(logisticsAPI.estimateFreight(any(), any())).thenReturn(Mono.just(mockResponse));

        // When
        var response = logisticsService.estimateFreight(request);

        // Then
        StepVerifier.create(response).assertNext(resp -> {
            assertThat(resp).isNotNull();
            assertThat(resp.getSuccess()).isTrue();
            assertThat(resp.getResult()).isNotNull();

            var result = resp.getResult().getResult();
            log.info("{}运费预估结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}商品ID: {}", LOG_ITEM, result.getOfferId());
            log.info("{}预估运费: {}", LOG_ITEM, result.getFreight());
            log.info("{}运费模板ID: {}", LOG_ITEM, result.getTemplateId());
            log.info("{}是否包邮: {}", LOG_ITEM, result.getFreePostage());
            log.info("{}计费类型: {}", LOG_ITEM, result.getChargeType());
            log.info("{}首重/件费用: {}", LOG_ITEM, result.getFirstFee());
            log.info("{}续重/件费用: {}", LOG_ITEM, result.getNextFee());
        }).verifyComplete();

        log.info("{} 测试商品中国国内运费预估完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    private ProductFreightEstimateResponse createMockFreightEstimateResponse() {
        ProductFreightEstimateResponse response = new ProductFreightEstimateResponse();
        response.setSuccess(true);

        ProductFreightEstimateResponse.ProductFreightModel result = new ProductFreightEstimateResponse.ProductFreightModel();
        result.setOfferId(123456789L);
        result.setFreight("12.5");
        result.setTemplateId(987654L);
        result.setFreePostage(false);
        result.setChargeType("0");
        result.setFirstFee("8");
        result.setNextFee("4");
        ApiResult apiResult = new ApiResult();
        apiResult.setSuccess(true);
        apiResult.setResult(result);
        response.setResult(apiResult);
        return response;
    }

    @Test
    void shouldGetLogisticsCompanyList() {
        log.info("{} 开始测试获取物流公司列表 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = LogisticsCompanyListRequestRecord.of();
        log.info("{}请求参数: 无需参数", LOG_ITEM);

        // Mock 成功响应
        LogisticsCompanyListResponse successResponse = new LogisticsCompanyListResponse();
        List<LogisticsCompanyListResponse.OpLogisticsCompanyModel> companies = new ArrayList<>();
        LogisticsCompanyListResponse.OpLogisticsCompanyModel company = new LogisticsCompanyListResponse.OpLogisticsCompanyModel();
        company.setId(1L);
        company.setCompanyName("顺丰速运");
        company.setCompanyNo("SF");
        company.setCompanyPhone("95338");
        company.setSupportPrint(true);
        company.setSpelling("shunfeng");
        companies.add(company);
        successResponse.setResult(companies);
        successResponse.setSuccess(true);

        when(logisticsAPI.getLogisticsCompanyList(any(), any())).thenReturn(Mono.just(successResponse));

        // When
        long startTime = System.currentTimeMillis();
        var response = logisticsService.getLogisticsCompanyList(request);

        // Then
        StepVerifier.create(response).assertNext(resp -> {
            assertThat(resp).isNotNull();
            assertThat(resp.getSuccess()).isTrue();
            assertThat(resp.getResult()).isNotEmpty();

            var resultCompany = resp.getResult().get(0);
            assertThat(resultCompany.getId()).isEqualTo(1L);
            assertThat(resultCompany.getCompanyName()).isEqualTo("顺丰速运");
            assertThat(resultCompany.getCompanyNo()).isEqualTo("SF");
            assertThat(resultCompany.getCompanyPhone()).isEqualTo("95338");
            assertThat(resultCompany.getSupportPrint()).isTrue();
            assertThat(resultCompany.getSpelling()).isEqualTo("shunfeng");

            log.info("{}是否成功: {}", LOG_ITEM, resp.getSuccess());
            log.info("{}物流公司ID: {}", LOG_ITEM, resultCompany.getId());
            log.info("{}物流公司名称: {}", LOG_ITEM, resultCompany.getCompanyName());
            log.info("{}物流公司编号: {}", LOG_ITEM, resultCompany.getCompanyNo());
            log.info("{}服务电话: {}", LOG_ITEM, resultCompany.getCompanyPhone());
            log.info("{}是否支持打印: {}", LOG_ITEM, resultCompany.getSupportPrint());
            log.info("{}全拼: {}", LOG_ITEM, resultCompany.getSpelling());

            recordMetrics("GetLogisticsCompanyList", startTime, resp.getSuccess());
        }).verifyComplete();

        log.info("{} 测试获取物流公司列表完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleGetLogisticsCompanyListError() {
        log.info("{} 开始测试获取物流公司列表-错误场景 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = LogisticsCompanyListRequestRecord.of();
        log.info("{}请求参数: 无需参数", LOG_ITEM);

        // Mock 错误响应
        LogisticsCompanyListResponse errorResponse = new LogisticsCompanyListResponse();
        errorResponse.setSuccess(false);
        errorResponse.setErrorCode("500");
        errorResponse.setErrorMessage("服务器错误");

        when(logisticsAPI.getLogisticsCompanyList(any(), any())).thenReturn(Mono.just(errorResponse));

        // When
        long startTime = System.currentTimeMillis();
        var response = logisticsService.getLogisticsCompanyList(request);

        // Then
        StepVerifier.create(response).assertNext(resp -> {
            assertThat(resp).isNotNull();
            assertThat(resp.getSuccess()).isFalse();
            assertThat(resp.getErrorCode()).isEqualTo("500");
            assertThat(resp.getErrorMessage()).isEqualTo("服务器错误");

            log.info("{}是否成功: {}", LOG_ITEM, resp.getSuccess());
            log.info("{}错误码: {}", LOG_ITEM, resp.getErrorCode());
            log.info("{}错误信息: {}", LOG_ITEM, resp.getErrorMessage());

            recordMetrics("GetLogisticsCompanyList", startTime, resp.getSuccess());
        }).verifyComplete();

        log.info("{} 测试获取物流公司列表-错误场景完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetShippingInsurance() {
        log.info("{} 开始测试运费险信息查询 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = LogisticsInsuranceRequestRecord.of(123456789L, "givenByPlatform");
        log.info("{}请求参数: orderId={}, type={}", LOG_ITEM, request.orderId(), request.type());

        // Mock 成功响应
        LogisticsInsuranceResponse successResponse = new LogisticsInsuranceResponse();
        LogisticsInsuranceResponse.ResultModel resultModel = new LogisticsInsuranceResponse.ResultModel();
        resultModel.setSuccess(true);
        resultModel.setCode("200");
        resultModel.setMessage("成功");

        LogisticsInsuranceResponse.TradeFreightPolicyResult policyResult = new LogisticsInsuranceResponse.TradeFreightPolicyResult();
        policyResult.setInsuranceId(987654321L);
        policyResult.setOrderId(123456789L);

        List<LogisticsInsuranceResponse.TradeClaimResult> claims = new ArrayList<>();
        LogisticsInsuranceResponse.TradeClaimResult claim = new LogisticsInsuranceResponse.TradeClaimResult();
        claim.setClaimId("CLAIM123456");
        claim.setClaimAmount(1000L);
        claim.setStatus("SUCCESS");
        claim.setTradeNO("TRADE123456");
        claim.setApplicationTime(new Date());
        claim.setPayTime(new Date());
        claims.add(claim);

        policyResult.setTradeClaimList(claims);
        resultModel.setResult(policyResult);
        successResponse.setResult(resultModel);
        successResponse.setSuccess(true);

        when(logisticsAPI.getShippingInsurance(any(), any())).thenReturn(Mono.just(successResponse));

        // When
        long startTime = System.currentTimeMillis();
        var response = logisticsService.getShippingInsurance(request);

        // Then
        StepVerifier.create(response).assertNext(resp -> {
            assertThat(resp).isNotNull();
            assertThat(resp.getSuccess()).isTrue();
            assertThat(resp.getResult()).isNotNull();
            assertThat(resp.getResult().getSuccess()).isTrue();
            assertThat(resp.getResult().getCode()).isEqualTo("200");

            var policyRes = resp.getResult().getResult();
            assertThat(policyRes.getInsuranceId()).isEqualTo(987654321L);
            assertThat(policyRes.getOrderId()).isEqualTo(123456789L);
            assertThat(policyRes.getTradeClaimList()).hasSize(1);

            var claimResult = policyRes.getTradeClaimList().get(0);
            assertThat(claimResult.getClaimId()).isEqualTo("CLAIM123456");
            assertThat(claimResult.getClaimAmount()).isEqualTo(1000L);
            assertThat(claimResult.getStatus()).isEqualTo("SUCCESS");
            assertThat(claimResult.getTradeNO()).isEqualTo("TRADE123456");

            log.info("{}是否成功: {}", LOG_ITEM, resp.getSuccess());
            log.info("{}响应码: {}", LOG_ITEM, resp.getResult().getCode());
            log.info("{}响应信息: {}", LOG_ITEM, resp.getResult().getMessage());
            log.info("{}保单ID: {}", LOG_ITEM, policyRes.getInsuranceId());
            log.info("{}订单ID: {}", LOG_ITEM, policyRes.getOrderId());
            log.info("{}理赔单数量: {}", LOG_ITEM, policyRes.getTradeClaimList().size());
            log.info("{}理赔单ID: {}", LOG_ITEM, claimResult.getClaimId());
            log.info("{}理赔金额: {}", LOG_ITEM, claimResult.getClaimAmount());
            log.info("{}理赔状态: {}", LOG_ITEM, claimResult.getStatus());
            log.info("{}支付宝交易流水号: {}", LOG_ITEM, claimResult.getTradeNO());
            log.info("{}申请时间: {}", LOG_ITEM, claimResult.getApplicationTime());
            log.info("{}打款时间: {}", LOG_ITEM, claimResult.getPayTime());

            recordMetrics("GetShippingInsurance", startTime, resp.getSuccess());
        }).verifyComplete();

        log.info("{} 测试运费险信息查询完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleGetShippingInsuranceError() {
        log.info("{} 开始测试运费险信息查询-错误场景 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = LogisticsInsuranceRequestRecord.of(999999999L, "invalidType");
        log.info("{}请求参数: orderId={}, type={}", LOG_ITEM, request.orderId(), request.type());

        // Mock 错误响应
        LogisticsInsuranceResponse errorResponse = new LogisticsInsuranceResponse();
        LogisticsInsuranceResponse.ResultModel resultModel = new LogisticsInsuranceResponse.ResultModel();
        resultModel.setSuccess(false);
        resultModel.setCode("400");
        resultModel.setMessage("无效的运费险类型");
        errorResponse.setResult(resultModel);
        errorResponse.setSuccess(false);

        when(logisticsAPI.getShippingInsurance(any(), any())).thenReturn(Mono.just(errorResponse));

        // When
        long startTime = System.currentTimeMillis();
        var response = logisticsService.getShippingInsurance(request);

        // Then
        StepVerifier.create(response).expectErrorMatches(throwable -> {
            assertThat(throwable).isInstanceOf(AlibabaServiceValidationException.class).hasMessage("运费险类型必须是平台赠送或商家赠送");

            log.info("{}错误类型: {}", LOG_ITEM, throwable.getClass().getSimpleName());
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());

            return true;
        }).verify();

        log.info("{} 测试运费险信息查询-错误场景完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }
}