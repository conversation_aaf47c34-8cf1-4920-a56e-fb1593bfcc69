/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook;

import static org.assertj.core.api.Assertions.assertThat;

import java.time.LocalDateTime;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.alibaba.webhook.data.OrderSuccessData;
import com.fulfillmen.support.alibaba.webhook.handler.OrderSuccessMessageHandler;

import lombok.extern.slf4j.Slf4j;

/**
 * MessageRouter 测试类
 * 测试消息路由器的核心功能，包括处理器注册、消息路由、优先级处理等
 *
 * <AUTHOR>
 * @date 2025/7/10
 * @since 1.0.0
 */
@Slf4j
public class MessageRouterTest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    private MessageRouter messageRouter;
    private OrderSuccessMessageHandler orderSuccessHandler;

    @BeforeEach
    void setUp() {
        orderSuccessHandler = new OrderSuccessMessageHandler();
        messageRouter = new MessageRouter(List.of(orderSuccessHandler));
        messageRouter.registerHandlers();
    }

    @Test
    @Tag("routing")
    void shouldRouteMessageToCorrectHandler() {
        log.info("{} 开始测试MessageRouter消息路由 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        MessageEvent<OrderSuccessData> event = createOrderSuccessEvent();
        log.info("{}消息ID: {}", LOG_ITEM, event.getMsgId());
        log.info("{}消息类型: {}", LOG_ITEM, event.getType());

        // When
        MessageResult result = messageRouter.route(event);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMsgId()).isEqualTo("test-msg-001");
        assertThat(result.getData()).isNotNull();

        log.info("{}路由结果: 成功={}, 消息ID={}", LOG_ITEM, result.isSuccess(), result.getMsgId());

        log.info("{} 测试MessageRouter消息路由完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("routing")
    void shouldReturnNoHandlerForUnknownType() {
        log.info("{} 开始测试MessageRouter处理未知消息类型 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        MessageEvent<Object> event = new MessageEvent<>();
        event.setMsgId("test-msg-002");
        event.setType(new UnknownMessageType("UNKNOWN_TYPE"));
        event.setData(new Object());
        event.setReceivedAt(LocalDateTime.now());

        log.info("{}未知消息类型: {}", LOG_ITEM, event.getType());

        // When
        MessageResult result = messageRouter.route(event);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMsgId()).isEqualTo("test-msg-002");
        assertThat(result.getErrorMessage()).contains("未找到适合的消息处理器");

        log.info("{}未知类型处理结果: {}", LOG_ITEM, result.getErrorMessage());

        log.info("{} 测试MessageRouter处理未知消息类型完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("routing")
    void shouldHandleMultipleHandlersWithPriority() {
        log.info("{} 开始测试MessageRouter处理器优先级 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given - 创建两个不同优先级的处理器
        TestMessageHandler highPriorityHandler = new TestMessageHandler(50);
        TestMessageHandler lowPriorityHandler = new TestMessageHandler(200);

        MessageRouter testRouter = new MessageRouter(List.of(
            orderSuccessHandler,  // 优先级100
            highPriorityHandler,  // 优先级50
            lowPriorityHandler    // 优先级200
        ));
        testRouter.registerHandlers();

        MessageEvent<OrderSuccessData> event = createOrderSuccessEvent();

        log.info("{}注册处理器数量: 3", LOG_ITEM);
        log.info("{}优先级: 50, 100, 200", LOG_ITEM);

        // When
        MessageResult result = testRouter.route(event);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMsgId()).isEqualTo("test-msg-001");
        // 应该使用优先级最高的处理器（数字最小）
        assertThat(result.getData()).isEqualTo("high-priority-handler");

        log.info("{}优先级处理结果: {}", LOG_ITEM, result.getData());

        log.info("{} 测试MessageRouter处理器优先级完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("routing")
    void shouldHandleEmptyHandlerList() {
        log.info("{} 开始测试MessageRouter空处理器列表 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        MessageRouter emptyRouter = new MessageRouter(List.of());
        emptyRouter.registerHandlers();

        MessageEvent<OrderSuccessData> event = createOrderSuccessEvent();

        log.info("{}空处理器列表测试", LOG_ITEM);

        // When
        MessageResult result = emptyRouter.route(event);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMsgId()).isEqualTo("test-msg-001");
        assertThat(result.getErrorMessage()).contains("未找到适合的消息处理器");

        log.info("{}空处理器处理结果: {}", LOG_ITEM, result.getErrorMessage());

        log.info("{} 测试MessageRouter空处理器列表完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("routing")
    void shouldRegisterHandlersCorrectly() {
        log.info("{} 开始测试MessageRouter处理器注册 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        TestMessageHandler testHandler1 = new TestMessageHandler(100);
        TestMessageHandler testHandler2 = new TestMessageHandler(200);

        MessageRouter testRouter = new MessageRouter(List.of(testHandler1, testHandler2));

        log.info("{}注册前处理器映射为空", LOG_ITEM);

        // When
        testRouter.registerHandlers();

        // Then - 通过路由测试验证注册是否成功
        MessageEvent<OrderSuccessData> event = createOrderSuccessEvent();
        MessageResult result = testRouter.route(event);

        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isEqualTo("test-handler");

        log.info("{}注册后处理器可正常路由", LOG_ITEM);

        log.info("{} 测试MessageRouter处理器注册完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    /**
     * 创建订单成功事件
     */
    private MessageEvent<OrderSuccessData> createOrderSuccessEvent() {
        OrderSuccessData data = new OrderSuccessData();
        data.setBuyerMemberId("b2b-2207416548807a4d12");
        data.setCurrentStatus("success");
        data.setOrderId(2806599398122540788L);
        data.setSellerMemberId("b2b-221280776451649a09");
        data.setMsgSendTime("2025-07-10 17:55:46");

        MessageEvent<OrderSuccessData> event = new MessageEvent<>();
        event.setMsgId("test-msg-001");
        event.setGmtBorn(1752141346107L);
        event.setUserInfo("b2b-2207416548807a4d12");
        event.setType(OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_SUCCESS);
        event.setData(data);
        event.setReceivedAt(LocalDateTime.now());

        return event;
    }

    /**
     * 测试用的消息处理器
     */
    private static class TestMessageHandler implements MessageHandler<OrderSuccessData> {

        private final int priority;

        public TestMessageHandler(int priority) {
            this.priority = priority;
        }

        @Override
        public boolean canHandle(com.fulfillmen.support.alibaba.enums.CallbackMessageType messageType) {
            return OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_SUCCESS.equals(messageType);
        }

        @Override
        public MessageResult handle(MessageEvent<OrderSuccessData> event) {
            String data = priority == 50 ? "high-priority-handler" : "test-handler";
            return MessageResult.success(event.getMsgId(), data);
        }

        @Override
        public int getPriority() {
            return priority;
        }

        @Override
        public List<com.fulfillmen.support.alibaba.enums.CallbackMessageType> getSupportedTypes() {
            return List.of(OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_SUCCESS);
        }
    }

    /**
     * 未知消息类型实现
     */
    private static class UnknownMessageType implements com.fulfillmen.support.alibaba.enums.CallbackMessageType {

        private final String type;

        public UnknownMessageType(String type) {
            this.type = type;
        }

        @Override
        public String getMessageType() {
            return type;
        }

        @Override
        public String getMessageDesc() {
            return "未知消息类型: " + type;
        }
    }
}
