/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.order;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.LocalDateTime;
import org.junit.jupiter.api.Test;

/**
 * 订单详情响应测试
 * 
 * <AUTHOR>
 * @created 2025-01-12
 */
class OrderDetailResponseTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    void testBasicDeserialization() throws Exception {
        // 简单的JSON测试
        String simpleJson = """
            {
                "result": {
                    "baseInfo": {
                        "id": 123456789,
                        "createTime": "20250625153309000+0800",
                        "payTime": "20250625171936000+0800",
                        "completeTime": "20250705174806000+0800"
                    },
                    "tradeTerms": [{
                        "payTime": "20250625171937000+0800",
                        "payStatus": "6"
                    }]
                },
                "success": "true"
            }
            """;

        // 反序列化
        OrderDetailResponse response = objectMapper.readValue(simpleJson, OrderDetailResponse.class);

        // 验证基本结构
        assertNotNull(response);
        assertNotNull(response.getResult());
        assertEquals(Boolean.TRUE, response.getSuccess());

        // 验证日期转换
        LocalDateTime createTime = response.getResult().getBaseInfo().getCreateTime();
        LocalDateTime payTime = response.getResult().getBaseInfo().getPayTime();
        LocalDateTime completeTime = response.getResult().getBaseInfo().getCompleteTime();

        assertNotNull(createTime);
        assertNotNull(payTime);
        assertNotNull(completeTime);

        System.out.println("基础日期转换测试通过！");
        System.out.println("创建时间: " + createTime);
        System.out.println("支付时间: " + payTime);
        System.out.println("完成时间: " + completeTime);
    }

    @Test
    void testDateFormats() throws Exception {
        // 测试不同的日期格式
        String jsonWithDifferentDates = """
            {
                "result": {
                    "baseInfo": {
                        "id": 123456789,
                        "createTime": "20250625153309000+0800"
                    },
                "tradeProductItems": [{
                        "gmtPayExpireTime": "2025-06-25 17:34:28",
                        "gmtCreate": "20250625153309000+0800"
                    }]
                },
                "success": "true"
            }
            """;

        OrderDetailResponse response = objectMapper.readValue(jsonWithDifferentDates, OrderDetailResponse.class);

        // 验证不同格式的日期都能正确解析
        assertNotNull(response.getResult().getBaseInfo().getCreateTime());
        assertNotNull(response.getResult().getProductItems().get(0).getGmtCreate());
        assertNotNull(response.getResult().getProductItems().get(0).getGmtPayExpireTime());

        System.out.println("多种日期格式测试通过！");
    }

    @Test
    void testLocalDateTimeFeatures() throws Exception {
        // 测试 LocalDateTime 的特性
        String jsonWithDates = """
            {
                "result": {
                    "baseInfo": {
                        "id": 123456789,
                        "createTime": "20250625153309000+0800",
                        "payTime": "20250625171936000+0800"
                    },
                    "tradeTerms": [{
                        "payTime": "20250625171937000+0800"
                    }]
                },
                "success": "true"
            }
            """;

        OrderDetailResponse response = objectMapper.readValue(jsonWithDates, OrderDetailResponse.class);

        LocalDateTime createTime = response.getResult().getBaseInfo().getCreateTime();
        LocalDateTime payTime = response.getResult().getTradeTerms().get(0).getPayTime();

        // 验证 LocalDateTime 的功能
        assertNotNull(createTime);
        assertNotNull(payTime);

        // 验证日期比较
        assertTrue(payTime.isAfter(createTime), "支付时间应该在创建时间之后");

        // 验证日期格式化
        assertNotNull(createTime.toString());
        assertTrue(createTime.toString().contains("2025-06-25"));

        // 验证日期操作
        LocalDateTime nextDay = createTime.plusDays(1);
        assertTrue(nextDay.isAfter(createTime));

        System.out.println("LocalDateTime 功能测试通过！");
        System.out.println("创建时间: " + createTime);
        System.out.println("支付时间: " + payTime);
        System.out.println("时间差: " + java.time.Duration.between(createTime, payTime).toMinutes() + " 分钟");
    }
}
