/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.integration.data;

import java.math.BigDecimal;

/**
 * 交易条款预期数据
 *
 * <AUTHOR>
 * @created 2025-07-12
 */
public record TradeTermsExpected(
    String payStatus,
    BigDecimal phasAmount,
    String payWayDesc
) {

    /**
     * 创建交易条款预期数据的构建器
     */
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {

        private String payStatus;
        private BigDecimal phasAmount;
        private String payWayDesc;

        public Builder payStatus(String payStatus) {
            this.payStatus = payStatus;
            return this;
        }

        public Builder phasAmount(BigDecimal phasAmount) {
            this.phasAmount = phasAmount;
            return this;
        }

        public Builder phasAmount(String amount) {
            this.phasAmount = new BigDecimal(amount);
            return this;
        }

        public Builder phasAmount(long amount) {
            this.phasAmount = BigDecimal.valueOf(amount);
            return this;
        }

        public Builder payWayDesc(String payWayDesc) {
            this.payWayDesc = payWayDesc;
            return this;
        }

        public TradeTermsExpected build() {
            return new TradeTermsExpected(payStatus, phasAmount, payWayDesc);
        }
    }
}
