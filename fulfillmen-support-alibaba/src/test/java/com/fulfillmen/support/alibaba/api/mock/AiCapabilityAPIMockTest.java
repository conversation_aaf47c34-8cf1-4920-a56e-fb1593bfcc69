/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.mock;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fulfillmen.support.alibaba.api.AiCapabilityAPI;
import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.api.request.ai.ImageCutRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ImageElementsRecognitionRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ImageEnlargeRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ImageMattingRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ImageRemoveRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ImageTranslateRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ProductDescGenerateRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ProductTextTranslateRequestRecord;
import com.fulfillmen.support.alibaba.api.request.ai.ProductTitleGenerateRequestRecord;
import com.fulfillmen.support.alibaba.api.response.ai.ImageCutResponse;
import com.fulfillmen.support.alibaba.api.response.ai.ImageElementsRecognitionResponse;
import com.fulfillmen.support.alibaba.api.response.ai.ImageEnlargeResponse;
import com.fulfillmen.support.alibaba.api.response.ai.ImageMattingResponse;
import com.fulfillmen.support.alibaba.api.response.ai.ImageRemoveResponse;
import com.fulfillmen.support.alibaba.api.response.ai.ImageTranslateResponse;
import com.fulfillmen.support.alibaba.api.response.ai.ProductDescGenerateResponse;
import com.fulfillmen.support.alibaba.api.response.ai.ProductTextTranslateResponse;
import com.fulfillmen.support.alibaba.api.response.ai.ProductTitleGenerateResponse;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import com.fulfillmen.support.alibaba.service.IAiCapabilityService;
import java.util.Arrays;
import java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

/**
 * AI能力API Mock测试
 * <p>
 * 测试范围： 1. 正常场景测试 2. 参数验证失败测试 3. 业务错误测试 4. 系统异常测试
 *
 * <AUTHOR>
 * @created 2025-01-16
 */
@Slf4j
@Tag("mock")
@Tag("ai")
class AiCapabilityAPIMockTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "[AI能力Mock测试]";

    @Autowired
    private IAiCapabilityService aiCapabilityService;

    @MockBean
    private AiCapabilityAPI aiCapabilityAPI;

    @Test
    @Tag("read")
    void shouldTranslateProductText() {
        log.info("{} 开始测试商品文本翻译 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String sourceText = "测试商品文本";
        var request = new ProductTextTranslateRequestRecord(Collections.singletonList(sourceText), "zh", "en", null);
        log.info("{}请求参数: sourceTextList={}, sourceLanguage={}, targetLanguage={}", LOG_ITEM, request
            .sourceTextList(), request.sourceLanguage(), request.targetLanguage());

        ProductTextTranslateResponse mockResponse = createMockTranslateResponse("Test product text");
        when(aiCapabilityAPI.translateProductText(any(), any())).thenReturn(Mono.just(mockResponse));

        // When
        var response = aiCapabilityService.translateProductText(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isTrue();

            // 业务检查
            assertThat(result.getResult()).isNotEmpty();
            assertThat(result.getResult()).contains("Test product text");

            log.info("{}测试结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, result.getSuccess());
            log.info("{}翻译结果: {}", LOG_ITEM, result.getResult());
        }).verifyComplete();

        // 验证API调用
        verify(aiCapabilityAPI).translateProductText(any(), any());

        log.info("{} 测试商品文本翻译完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldGenerateProductTitle() {
        log.info("{} 开始测试商品标题生成 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ProductTitleGenerateRequestRecord("测试商品", "en", "测试类目", "测试关键词", "测试描述", null, null);
        log.info("{}请求参数: productName={}, targetLanguage={}, productCategory={}, productKeyword={}", LOG_ITEM, request
            .productName(), request.targetLanguage(), request.productCategory(), request.productKeyword());

        ProductTitleGenerateResponse mockResponse = createMockTitleResponse();
        when(aiCapabilityAPI.generateProductTitle(any(), any())).thenReturn(Mono.just(mockResponse));

        // When
        var response = aiCapabilityService.generateProductTitle(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();
            assertThat(result.getResult()).isNotNull();
            assertThat(result.getResult().getSuccess()).isTrue();

            // 业务检查
            assertThat(result.getResult().getCode()).isEqualTo("200");
            assertThat(result.getResult().getResult()).isNotEmpty();

            log.info("{}测试结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, result.getResult().getSuccess());
            log.info("{}生成标题: {}", LOG_ITEM, result.getResult().getResult());
        }).verifyComplete();

        // 验证API调用
        verify(aiCapabilityAPI).generateProductTitle(any(), any());

        log.info("{} 测试商品标题生成完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldGenerateProductDesc() {
        log.info("{} 开始测试商品详描生成 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String expectedDesc = "Test product description with detailed features and benefits.";
        var request = new ProductDescGenerateRequestRecord("测试商品", "en", "测试类目", "测试关键词", "测试描述", null, null);
        log.info("{}请求参数: productName={}, targetLanguage={}, productCategory={}, productKeyword={}", LOG_ITEM, request
            .productName(), request.targetLanguage(), request.productCategory(), request.productKeyword());

        ProductDescGenerateResponse mockResponse = createMockDescResponse(expectedDesc);
        when(aiCapabilityAPI.generateProductDesc(any(), any())).thenReturn(Mono.just(mockResponse));

        // When
        var response = aiCapabilityService.generateProductDesc(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();
            assertThat(result.getResult()).isNotNull();
            assertThat(result.getResult().getSuccess()).isTrue();

            // 业务检查
            assertThat(result.getResult().getRetCode()).isEqualTo("S0000");
            assertThat(result.getResult().getResult()).isEqualTo(expectedDesc);

            log.info("{}测试结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, result.getResult().getSuccess());
            log.info("{}错误码: {}", LOG_ITEM, result.getResult().getRetCode());
            log.info("{}生成详描: {}", LOG_ITEM, result.getResult().getResult());
        }).verifyComplete();

        // 验证API调用
        verify(aiCapabilityAPI).generateProductDesc(any(), any());

        log.info("{} 测试商品详描生成完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleTranslateValidationError() {
        log.info("{} 开始测试商品文本翻译参数校验失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ProductTextTranslateRequestRecord(Collections.emptyList(), // 空文本列表
            "zh", "en", null);

        log.info("{}请求参数: sourceTextList={}, sourceLanguage={}, targetLanguage={}", LOG_ITEM, request
            .sourceTextList(), request.sourceLanguage(), request.targetLanguage());

        // When & Then
        StepVerifier.create(aiCapabilityService.translateProductText(request)).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException && throwable.getMessage()
                .contains("sourceTextList不能为空");
        }).verify();

        // 验证API未被调用
        verify(aiCapabilityAPI, never()).translateProductText(any(), any());
        log.info("{} 测试商品文本翻译参数校验失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleTitleGenerateValidationError() {
        log.info("{} 开始测试商品标题生成参数校验失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ProductTitleGenerateRequestRecord("", // 空商品名称
            "en", null, null, null, null, null);
        log.info("{}请求参数: productName={}, targetLanguage={}", LOG_ITEM, request.productName(), request
            .targetLanguage());

        // When & Then
        StepVerifier.create(aiCapabilityService.generateProductTitle(request)).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException && throwable.getMessage()
                .contains("productName不能为空");
        }).verify();

        // 验证API未被调用
        verify(aiCapabilityAPI, never()).generateProductTitle(any(), any());
        log.info("{} 测试商品标题生成参数校验失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleDescGenerateValidationError() {
        log.info("{} 开始测试商品详描生成参数校验失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ProductDescGenerateRequestRecord("", // 空商品名称
            "en", null, null, null, null, null);

        log.info("{}请求参数: productName={}, targetLanguage={}", LOG_ITEM, request.productName(), request
            .targetLanguage());

        // When & Then
        StepVerifier.create(aiCapabilityService.generateProductDesc(request)).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException && throwable.getMessage()
                .contains("productName不能为空");
        }).verify();

        // 验证API未被调用
        verify(aiCapabilityAPI, never()).generateProductDesc(any(), any());
        log.info("{} 测试商品详描生成参数校验失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldTranslateImage() {
        log.info("{} 开始测试图片翻译 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ImageTranslateRequestRecord("http://example.com/test.jpg", "zh", "en", "true", "false");
        log.info("{}请求参数: imageUrl={}, originalLanguage={}, targetLanguage={}", LOG_ITEM, request.imageUrl(), request
            .originalLanguage(), request.targetLanguage());

        var mockResponse = createMockImageTranslateResponse();
        when(aiCapabilityAPI.translateImage(any(), any())).thenReturn(Mono.just(mockResponse));

        // When & Then
        StepVerifier.create(aiCapabilityService.translateImage(request)).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isTrue();
            assertThat(result.getResult()).isNotNull();
            assertThat(result.getResult().getTranslatedImageUrl()).isNotEmpty();

            log.info("{}测试结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, result.getSuccess());
            log.info("{}翻译后图片URL: {}", LOG_ITEM, result.getResult().getTranslatedImageUrl());
        }).verifyComplete();

        // 验证API调用
        verify(aiCapabilityAPI).translateImage(any(), any());

        log.info("{} 测试图片翻译完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleImageTranslateValidationError() {
        log.info("{} 开始测试图片翻译参数校验失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ImageTranslateRequestRecord("", // 空图片URL
            "zh", "en", "true", "false");
        log.info("{}请求参数: imageUrl={}, originalLanguage={}, targetLanguage={}", LOG_ITEM, request.imageUrl(), request
            .originalLanguage(), request.targetLanguage());

        // When & Then
        StepVerifier.create(aiCapabilityService.translateImage(request)).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException;
        }).verify();

        // 验证API未被调用
        verify(aiCapabilityAPI, never()).translateImage(any(), any());

        log.info("{} 测试图片翻译参数校验失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldCutImage() {
        log.info("{} 开始测试图片裁剪 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ImageCutRequestRecord("http://example.com/test.jpg", "800", "600");
        log.info("{}请求参数: imageUrl={}, width={}, height={}", LOG_ITEM, request.imageUrl(), request.width(), request
            .height());

        var mockResponse = createMockImageCutResponse();
        when(aiCapabilityAPI.cutImage(any(), any())).thenReturn(Mono.just(mockResponse));

        // When & Then
        StepVerifier.create(aiCapabilityService.cutImage(request)).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isTrue();
            assertThat(result.getCode()).isEqualTo("200");
            assertThat(result.getResult()).isNotEmpty();

            log.info("{}测试结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, result.getSuccess());
            log.info("{}裁剪后图片URL: {}", LOG_ITEM, result.getResult());
        }).verifyComplete();

        // 验证API调用
        verify(aiCapabilityAPI).cutImage(any(), any());

        log.info("{} 测试图片裁剪完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleImageCutValidationError() {
        log.info("{} 开始测试图片裁剪参数校验失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ImageCutRequestRecord("", // 空图片URL
            "800", "600");
        log.info("{}请求参数: imageUrl={}, width={}, height={}", LOG_ITEM, request.imageUrl(), request.width(), request
            .height());

        // When & Then
        StepVerifier.create(aiCapabilityService.cutImage(request)).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException;
        }).verify();

        // 验证API未被调用
        verify(aiCapabilityAPI, never()).cutImage(any(), any());

        log.info("{} 测试图片裁剪参数校验失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldEnlargeImage() {
        log.info("{} 开始测试图片高清放大 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ImageEnlargeRequestRecord("http://example.com/test.jpg", 2);
        log.info("{}请求参数: imageUrl={}, upscaleFactor={}", LOG_ITEM, request.imageUrl(), request.upscaleFactor());

        var mockResponse = createMockImageEnlargeResponse();
        when(aiCapabilityAPI.enlargeImage(any(), any())).thenReturn(Mono.just(mockResponse));

        // When & Then
        StepVerifier.create(aiCapabilityService.enlargeImage(request)).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isTrue();
            assertThat(result.getCode()).isEqualTo("200");
            assertThat(result.getResult()).isNotNull();
            assertThat(result.getResult().getEnlargedImageUrl()).isNotEmpty();
            assertThat(result.getResult().getImageWidth()).isEqualTo(1600);
            assertThat(result.getResult().getImageHeight()).isEqualTo(1200);

            log.info("{}测试结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, result.getSuccess());
            log.info("{}放大后图片URL: {}", LOG_ITEM, result.getResult().getEnlargedImageUrl());
            log.info("{}放大后尺寸: {}x{}", LOG_ITEM, result.getResult().getImageWidth(), result.getResult()
                .getImageHeight());
        }).verifyComplete();

        // 验证API调用
        verify(aiCapabilityAPI).enlargeImage(any(), any());

        log.info("{} 测试图片高清放大完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleImageEnlargeValidationError() {
        log.info("{} 开始测试图片高清放大参数校验失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ImageEnlargeRequestRecord("http://example.com/test.jpg", 5); // 无效的放大倍数
        log.info("{}请求参数: imageUrl={}, upscaleFactor={}", LOG_ITEM, request.imageUrl(), request.upscaleFactor());

        // When & Then
        StepVerifier.create(aiCapabilityService.enlargeImage(request)).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof IllegalArgumentException && throwable.getMessage()
                .contains("upscaleFactor必须在2-4之间");
        }).verify();

        // 验证API未被调用
        verify(aiCapabilityAPI, never()).enlargeImage(any(), any());

        log.info("{} 测试图片高清放大参数校验失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldMattingImage() {
        log.info("{} 开始测试图片智能抠图 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ImageMattingRequestRecord("http://example.com/test.jpg", "[255,255,255]", 800, 600);
        log.info("{}请求参数: imageUrl={}, backgroundBGR={}, height={}, width={}", LOG_ITEM, request.imageUrl(), request
            .backgroundBGR(), request.height(), request.width());

        var mockResponse = createMockImageMattingResponse();
        when(aiCapabilityAPI.mattingImage(any(), any())).thenReturn(Mono.just(mockResponse));

        // When & Then
        StepVerifier.create(aiCapabilityService.mattingImage(request)).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isTrue();
            assertThat(result.getCode()).isEqualTo("200");
            assertThat(result.getResult()).isNotEmpty();

            log.info("{}测试结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, result.getSuccess());
            log.info("{}抠图后图片URL: {}", LOG_ITEM, result.getResult());
        }).verifyComplete();

        // 验证API调用
        verify(aiCapabilityAPI).mattingImage(any(), any());

        log.info("{} 测试图片智能抠图完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleImageMattingValidationError() {
        log.info("{} 开始测试图片智能抠图参数校验失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ImageMattingRequestRecord("", // 空图片URL
            "[255,255,255]", 800, 600);
        log.info("{}请求参数: imageUrl={}, backgroundBGR={}, height={}, width={}", LOG_ITEM, request.imageUrl(), request
            .backgroundBGR(), request.height(), request.width());

        // When & Then
        StepVerifier.create(aiCapabilityService.mattingImage(request)).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException && throwable.getMessage()
                .contains("imageUrl不能为空");
        }).verify();

        // 验证API未被调用
        verify(aiCapabilityAPI, never()).mattingImage(any(), any());

        log.info("{} 测试图片智能抠图参数校验失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldRemoveImage() {
        log.info("{} 开始测试图片智能消除 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ImageRemoveRequestRecord("http://example.com/test.jpg", true, true, true, true, true, true, true, true, true, true);
        log.info("{}请求参数: imageUrl={}, noobjRemoveCharacter={}, noobjRemoveLogo={}, noobjRemoveNpx={}", LOG_ITEM, request
            .imageUrl(), request.noobjRemoveCharacter(), request.noobjRemoveLogo(), request.noobjRemoveNpx());

        var mockResponse = createMockImageRemoveResponse();
        when(aiCapabilityAPI.removeImage(any(), any())).thenReturn(Mono.just(mockResponse));

        // When & Then
        StepVerifier.create(aiCapabilityService.removeImage(request)).assertNext(result -> {
            assertThat(result).isNotNull();
            assertThat(result.getResult().getSuccess()).isTrue();
            assertThat(result.getResult().getCode()).isEqualTo("S0000");
            assertThat(result.getResult().getResult()).isNotEmpty();

            log.info("{}测试结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, result.getResult().getSuccess());
            log.info("{}消除后图片URL: {}", LOG_ITEM, result.getResult().getResult());
        }).verifyComplete();

        // 验证API调用
        verify(aiCapabilityAPI).removeImage(any(), any());

        log.info("{} 测试图片智能消除完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleImageRemoveValidationError() {
        log.info("{} 开始测试图片智能消除参数校验失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ImageRemoveRequestRecord("", // 空图片URL
            true, true, true, true, true, true, true, true, true, true);
        log.info("{}请求参数: imageUrl={}", LOG_ITEM, request.imageUrl());

        // When & Then
        StepVerifier.create(aiCapabilityService.removeImage(request)).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
            return throwable instanceof AlibabaServiceValidationException && throwable.getMessage()
                .contains("imageUrl不能为空");
        }).verify();

        // 验证API未被调用
        verify(aiCapabilityAPI, never()).removeImage(any(), any());

        log.info("{} 测试图片智能消除参数校验失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldRecognizeImageElements() {
        log.info("{} 开始测试图像元素识别 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String imageUrl = "https://example.com/test.jpg";
        var request = new ImageElementsRecognitionRequestRecord(imageUrl, Arrays.asList("watermark", "logo"), Arrays
            .asList("text", "qrcode"), 1, // returnCharacter
            1, // returnBorderPixel
            1, // returnProductProp
            1, // returnProductNum
            1); // returnCharacterProp
        log.info("{}请求参数: imageUrl={}, objectDetectElements={}, nonObjectDetectElements={}", LOG_ITEM, request
            .imageUrl(), request.objectDetectElements(), request.nonObjectDetectElements());

        ImageElementsRecognitionResponse mockResponse = createMockImageElementsRecognitionResponse();
        when(aiCapabilityAPI.recognizeImageElements(any(), any())).thenReturn(Mono.just(mockResponse));

        // When
        var response = aiCapabilityService.recognizeImageElements(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isTrue();

            // 业务检查
            assertThat(result.getResult()).isNotNull();
            assertThat(result.getResult().getResult()).isNotNull();
            assertThat(result.getResult().getResult().getRecText()).isNotEmpty();
            assertThat(result.getResult().getResult().getPdProp()).isNotNull();
            assertThat(result.getResult().getResult().getNoobWatermark()).isTrue();
            assertThat(result.getResult().getResult().getNoobLogo()).isTrue();

            log.info("{}测试结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, result.getSuccess());
            log.info("{}识别结果: 文字={}, 水印={}, Logo={}", LOG_ITEM, result.getResult().getResult().getRecText(), result
                .getResult()
                .getResult()
                .getNoobWatermark(), result.getResult().getResult().getNoobLogo());
        }).verifyComplete();

        log.info("{} 图像元素识别测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    @Tag("error")
    void shouldFailRecognizeImageElements_WhenInvalidRequest() {
        log.info("{} 开始测试图像元素识别参数验证 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new ImageElementsRecognitionRequestRecord(null, // 缺少必填参数
            null, null, 0, // returnCharacter
            0, // returnBorderPixel
            0, // returnProductProp
            0, // returnProductNum
            0); // returnCharacterProp
        log.info("{}请求参数: imageUrl=null", LOG_ITEM);

        // When & Then
        StepVerifier.create(aiCapabilityService.recognizeImageElements(request))
            .expectError(AlibabaServiceValidationException.class)
            .verify();

        // 验证API未被调用
        verify(aiCapabilityAPI, never()).recognizeImageElements(any(), any());

        log.info("{} 图像元素识别参数验证测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    @Tag("error")
    void shouldHandleRecognizeImageElements_WhenApiError() {
        log.info("{} 开始测试图像元素识别API错误 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String imageUrl = "https://invalid.example.com/test.jpg"; // 使用无效的图片URL
        var request = new ImageElementsRecognitionRequestRecord(imageUrl, null, null, 0, // returnCharacter
            0, // returnBorderPixel
            0, // returnProductProp
            0, // returnProductNum
            0); // returnCharacterProp
        log.info("{}请求参数: imageUrl={}", LOG_ITEM, request.imageUrl());

        ImageElementsRecognitionResponse errorResponse = new ImageElementsRecognitionResponse();
        errorResponse.setSuccess(false);
        ImageElementsRecognitionResponse.Result result = new ImageElementsRecognitionResponse.Result();
        result.setCode("500");
        result.setMessage("API Error");
        errorResponse.setResult(result);

        when(aiCapabilityAPI.recognizeImageElements(any(), any())).thenReturn(Mono.just(errorResponse));

        // When & Then
        StepVerifier.create(aiCapabilityService.recognizeImageElements(request)).assertNext(response -> {
            assertThat(response).isNotNull();
            assertThat(response.getSuccess()).isFalse();
            assertThat(response.getResult().getCode()).isEqualTo("500");
            assertThat(response.getResult().getMessage()).isEqualTo("API Error");

            log.info("{}测试结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}错误信息: code={}, message={}", LOG_ITEM, response.getResult().getCode(), response.getResult()
                .getMessage());
        }).verifyComplete();

        log.info("{} 图像元素识别API错误测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    private ProductTextTranslateResponse createMockTranslateResponse(String translatedText) {
        ProductTextTranslateResponse response = new ProductTextTranslateResponse();
        response.setSuccess(true);
        response.setResult(translatedText);
        return response;
    }

    private ProductTitleGenerateResponse createMockTitleResponse() {
        ProductTitleGenerateResponse response = new ProductTitleGenerateResponse();
        ProductTitleGenerateResponse.GenerateResult result = new ProductTitleGenerateResponse.GenerateResult();
        result.setSuccess(true);
        result.setCode("200");
        result.setMessage("成功");
        result.setResult("Generated Title");
        response.setResult(result);
        return response;
    }

    private ProductDescGenerateResponse createMockDescResponse(String description) {
        ProductDescGenerateResponse response = new ProductDescGenerateResponse();
        ProductDescGenerateResponse.ResultWrapper result = new ProductDescGenerateResponse.ResultWrapper();
        result.setSuccess(true);
        result.setRetCode("S0000");
        result.setRetMsg("成功");
        result.setResult(description);
        response.setResult(result);
        return response;
    }

    private ImageTranslateResponse createMockImageTranslateResponse() {
        ImageTranslateResponse response = new ImageTranslateResponse();
        response.setSuccess(true);
        response.setCode("S0000");
        response.setMessage("成功");

        ImageTranslateResponse.TranslatedImageModel result = new ImageTranslateResponse.TranslatedImageModel();
        result.setOriginalImageUrl("http://example.com/test.jpg");
        result.setOriginalLanguage("zh");
        result.setTranslatedImageUrl("http://example.com/test_translated.jpg");
        result.setTranslatedLanguage("en");
        result.setTranslatedImagePixelData("base64encodeddata");

        response.setResult(result);
        return response;
    }

    private ImageCutResponse createMockImageCutResponse() {
        ImageCutResponse response = new ImageCutResponse();
        response.setSuccess(true);
        response.setCode("200");
        response.setMessage("成功");
        response.setResult("http://example.com/test_cut.jpg");
        return response;
    }

    private ImageEnlargeResponse createMockImageEnlargeResponse() {
        ImageEnlargeResponse response = new ImageEnlargeResponse();
        response.setSuccess(true);
        response.setCode("200");
        response.setMessage("成功");

        ImageEnlargeResponse.EnlargeImageModel result = new ImageEnlargeResponse.EnlargeImageModel();
        result.setEnlargedImageUrl("http://example.com/test_enlarged.jpg");
        result.setImageWidth(1600);
        result.setImageHeight(1200);

        response.setResult(result);
        return response;
    }

    private ImageMattingResponse createMockImageMattingResponse() {
        ImageMattingResponse response = new ImageMattingResponse();
        response.setSuccess(true);
        response.setCode("200");
        response.setMessage("成功");
        response.setResult("http://example.com/test_matting.jpg");
        return response;
    }

    private ImageRemoveResponse createMockImageRemoveResponse() {
        ImageRemoveResponse response = new ImageRemoveResponse();
        ImageRemoveResponse.Result result = new ImageRemoveResponse.Result();
        result.setSuccess(true);
        result.setCode("S0000");
        result.setMessage("成功");
        result.setResult("http://example.com/test_removed.jpg");
        response.setResult(result);
        return response;
    }

    private ImageElementsRecognitionResponse createMockImageElementsRecognitionResponse() {
        ImageElementsRecognitionResponse response = new ImageElementsRecognitionResponse();
        response.setSuccess(true);

        ImageElementsRecognitionResponse.Result result = new ImageElementsRecognitionResponse.Result();
        result.setSuccess(true);
        result.setCode("200");
        result.setMessage("success");

        ImageElementsRecognitionResponse.RecognitionResult recognitionResult = new ImageElementsRecognitionResponse.RecognitionResult();
        recognitionResult.setRecText(Collections.singletonList("Sample text"));
        recognitionResult.setPdProp("0.85");
        recognitionResult.setNoobWatermark(true);
        recognitionResult.setNoobLogo(true);
        recognitionResult.setNoobQrcode(false);
        recognitionResult.setNoobCharacter(true);
        recognitionResult.setBorderPixel("[255,255,255]");
        recognitionResult.setObjNgx(false);

        result.setResult(recognitionResult);
        response.setResult(result);
        return response;
    }
}