/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.integration;

import static org.assertj.core.api.Assertions.assertThat;

import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.api.request.business.RankQueryRequestRecord;
import com.fulfillmen.support.alibaba.api.request.business.SellTrendRequestRecord;
import com.fulfillmen.support.alibaba.api.request.business.TopKeywordRequestRecord;
import com.fulfillmen.support.alibaba.exception.AlibabaServiceValidationException;
import com.fulfillmen.support.alibaba.service.IBusinessService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.test.StepVerifier;

/**
 * 业务API集成测试
 * <p>
 * 测试范围： 1. 正常场景测试 2. 性能指标记录 3. 实际API调用验证 4. 错误场景测试
 *
 * <AUTHOR>
 * @created 2025-01-23
 */
@Slf4j
@Tag("integration")
@Tag("business")
class BusinessAPIIntegrationTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "[业务集成测试]";

    @Autowired
    private IBusinessService businessService;

    @Test
    @Tag("read")
    void shouldQueryRankList() {
        log.info("{} 开始测试榜单查询 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new RankQueryRequestRecord("311", "complex", 10, "en");
        log.info("{}请求参数: rankId={}, rankType={}, limit={}, language={}", LOG_ITEM, request.rankId(), request
            .rankType(), request.limit(), request.language());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = businessService.queryRankList(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();
            assertThat(result.getResult()).isNotNull();

            // 业务检查
            assertThat(result.getResult().getSuccess()).isTrue();
            assertThat(result.getResult().getResult().getRankProductModels()).isNotEmpty();

            // 记录日志
            log.info("{}测试结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}榜单ID: {}", LOG_ITEM, result.getResult().getResult().getRankId());
            log.info("{}榜单名称: {}", LOG_ITEM, result.getResult().getResult().getRankName());
            log.info("{}商品数量: {}", LOG_ITEM, result.getResult().getResult().getRankProductModels().size());

            // 记录性能指标
            recordMetrics("QueryRankList", startTime, true);
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("QueryRankList");
        log.info("{} 测试榜单查询完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleValidationError() {
        log.info("{} 开始测试榜单查询参数校验失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new RankQueryRequestRecord("", // 空榜单ID
            "complex", 10, "en");
        log.info("{}请求参数: rankId={}, rankType={}, limit={}, language={}", LOG_ITEM, request.rankId(), request
            .rankType(), request.limit(), request.language());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(businessService.queryRankList(request)).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());

            // 记录性能指标
            recordMetrics("QueryRankListValidationError", startTime, false);

            return throwable instanceof AlibabaServiceValidationException && throwable.getMessage()
                .contains("榜单ID不能为空");
        }).verify();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("QueryRankListValidationError");
        log.info("{} 测试榜单查询参数校验失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldGetTopKeywords() {
        log.info("{} 开始测试商品热搜词查询 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new TopKeywordRequestRecord("en", "311", "cate");
        log.info("{}请求参数: country={}, sourceId={}, hotKeywordType={}", LOG_ITEM, request.country(), request
            .sourceId(), request.hotKeywordType());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = businessService.getTopKeywords(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();
            assertThat(result.getResult()).isNotNull();

            // 业务检查
            assertThat(result.getResult().getSuccess()).isTrue();
            assertThat(result.getResult().getResult()).isNotEmpty();

            // 记录日志
            log.info("{}测试结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}热搜词数量: {}", LOG_ITEM, result.getResult().getResult().size());
            result.getResult()
                .getResult()
                .forEach(keyword -> log.info("{}热搜词: {} -> {}", LOG_ITEM, keyword.getSeKeyword(), keyword
                    .getSeKeywordTranslation()));

            // 记录性能指标
            recordMetrics("GetTopKeywords", startTime, true);
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GetTopKeywords");
        log.info("{} 测试商品热搜词查询完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleTopKeywordValidationError() {
        log.info("{} 开始测试商品热搜词参数校验失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new TopKeywordRequestRecord("", // 空语言
            "311", "cate");
        log.info("{}请求参数: country={}, sourceId={}, hotKeywordType={}", LOG_ITEM, request.country(), request
            .sourceId(), request.hotKeywordType());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(businessService.getTopKeywords(request)).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());

            // 记录性能指标
            recordMetrics("GetTopKeywordsValidationError", startTime, false);

            return throwable instanceof AlibabaServiceValidationException && throwable.getMessage().contains("语言不能为空");
        }).verify();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GetTopKeywordsValidationError");
        log.info("{} 测试商品热搜词参数校验失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldGetSellTrend() {
        log.info("{} 开始测试商品销售趋势查询 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new SellTrendRequestRecord(734399568819L, "20250101", "20250107");
        log.info("{}请求参数: offerId={}, startDate={}, endDate={}", LOG_ITEM, request.offerId(), request
            .startDate(), request.endDate());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = businessService.getSellTrend(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();
            assertThat(result.getResult()).isNotNull();

            // 业务检查
            assertThat(result.getResult().getSuccess()).isTrue();
            assertThat(result.getResult().getResult()).isNotEmpty();

            // 记录日志
            log.info("{}测试结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}趋势数据数量: {}", LOG_ITEM, result.getResult().getResult().size());
            result.getResult()
                .getResult()
                .forEach(trend -> log.info("{}日期: {}, 销量: {}", LOG_ITEM, trend.getDate(), trend.getValue()));

            // 记录性能指标
            recordMetrics("GetSellTrend", startTime, true);
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GetSellTrend");
        log.info("{} 测试商品销售趋势查询完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleSellTrendValidationError() {
        log.info("{} 开始测试商品销售趋势参数校验失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        var request = new SellTrendRequestRecord(734399568819L, "2025-01-23", // 错误的日期格式
            "20240107");
        log.info("{}请求参数: offerId={}, startDate={}, endDate={}", LOG_ITEM, request.offerId(), request
            .startDate(), request.endDate());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(businessService.getSellTrend(request)).expectErrorMatches(throwable -> {
            log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());

            // 记录性能指标
            recordMetrics("GetSellTrendValidationError", startTime, false);

            return throwable instanceof AlibabaServiceValidationException && throwable.getMessage()
                .contains("查询起始时间格式错误");
        }).verify();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GetSellTrendValidationError");
        log.info("{} 测试商品销售趋势参数校验失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

}