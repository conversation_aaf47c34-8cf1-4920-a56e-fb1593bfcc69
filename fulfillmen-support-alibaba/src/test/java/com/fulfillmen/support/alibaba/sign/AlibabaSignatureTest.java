/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.sign;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

import cn.hutool.core.map.MapUtil;
import java.util.Map;
import java.util.stream.Stream;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

/**
 * 阿里巴巴开放平台签名工具测试类
 *
 * <AUTHOR>
 * @date 2024-12-31 09:13
 * @description: 测试阿里巴巴开放平台API签名的生成和验证功能
 * @since 1.0.0
 */
class AlibabaSignatureTest {

    @Test
    void shouldSignWithValidParams() {
        // Given
        String urlPath = "param2/1/system/currentTime/1688";
        Map<String, String> params = MapUtil.builder("access_token", "test-token")
            .put("_aop_timestamp", "1577203200000")
            .build();
        String secretKey = "test-secret";

        // When
        String signature = AlibabaSignature.sign(urlPath, params, secretKey);

        // Then
        assertThat(signature).isNotEmpty();
    }

    @Test
    void shouldThrowExceptionWhenUrlPathIsEmpty() {
        // Given
        String urlPath = "";
        Map<String, String> params = MapUtil.newHashMap();
        String secretKey = "test-secret";

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> AlibabaSignature.sign(urlPath, params, secretKey));
    }

    @ParameterizedTest
    @MethodSource("provideUrlsForExtraction")
    void shouldExtractUrlPathCorrectly(String url, String expectedPath) {
        String actualPath = AlibabaSignature.extractUrlPath(url);
        assertThat(actualPath).isEqualTo(expectedPath);
    }

    private static Stream<Arguments> provideUrlsForExtraction() {
        return Stream.of(Arguments
            .of("http://gw.open.1688.com/openapi/http/1/system/currentTime/1688", "http/1/system/currentTime/1688"), Arguments
                .of("http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.get/8390330", "param2/1/com.alibaba.product/alibaba.product.get/8390330"), Arguments
                    .of("http://gw.open.1688.com/openapi/http/1/system/currentTime/1688?b=1&a=2", "http/1/system/currentTime/1688"));
    }

    @Test
    void shouldVerifyValidSignature() {
        // Given
        String urlPath = "param2/1/system/currentTime/1688";
        Map<String, String> params = MapUtil.builder("param1", "value1").build();
        String secretKey = "test-secret";
        String signature = AlibabaSignature.sign(urlPath, params, secretKey);

        // When
        boolean isValid = AlibabaSignature.verify(urlPath, params, secretKey, signature);

        // Then
        assertThat(isValid).isTrue();
    }

    @Test
    void shouldGenerateCorrectSignatureForSpecificExample() {
        // Given
        String urlPath = "param/1/dev.test/currentTime/1";
        Map<String, String> params = MapUtil.builder("key1", "value1").put("key2", "value2").build();
        String secretKey = "test123";
        String expectedSignature = "522D1D877DFB35F5E9C16E2178CB678469945C6E";
        String baseUrl = "http://gw.open.1688.com/openapi";

        // When
        String actualSignature = AlibabaSignature.sign(urlPath, params, secretKey);
        System.out.println("计算后签名:" + actualSignature);
        // Then
        // 1. 验证签名是否正确
        assertThat(actualSignature).isEqualTo(expectedSignature);

        // 2. 验证完整的URL
        String fullUrl = baseUrl + "/" + urlPath;
        String signedUrl = AlibabaSignature.signUrl(fullUrl, params, secretKey);
        String expectedUrl = fullUrl + "?key1=value1&key2=value2&_aop_signature=" + expectedSignature;
        System.out.println("计算后签名URL:" + signedUrl);
        assertThat(signedUrl).isEqualTo(expectedUrl);

        // 3. 验证签名是否可以通过验证
        assertThat(AlibabaSignature.verify(urlPath, params, secretKey, actualSignature)).isTrue();
    }
}