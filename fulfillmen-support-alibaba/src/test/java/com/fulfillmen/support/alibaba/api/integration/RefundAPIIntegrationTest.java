/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.integration;

import static org.assertj.core.api.Assertions.assertThat;

import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.api.request.refund.RefundBuyerListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundBuyerOrderViewRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundBuyerSubmitRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundBuyerViewRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundCreateRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundOpLogListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundReasonListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.refund.RefundUploadEvidenceRequestRecord;
import com.fulfillmen.support.alibaba.service.IRefundService;
import java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.test.StepVerifier;

/**
 * 1688退款API集成测试
 *
 * <AUTHOR>
 * @created 2025-01-15
 */
@Slf4j
class RefundAPIIntegrationTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    @Autowired
    private IRefundService refundService;

    @Test
    void shouldQueryRefundBuyerList() {
        // checked 没问题
        // Given
        var request = RefundBuyerListRequestRecord.of(1, 5);

        log.info("{}开始查询退款单列表测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}页码: {}", LOG_ITEM, request.page());
        log.info("{}每页数量: {}", LOG_ITEM, request.pageSize());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = refundService.queryRefundBuyerList(request);

        // Then
        StepVerifier.create(response).assertNext(listResponse -> {
            // 记录API调用指标
            recordMetrics("QueryRefundBuyerList", startTime, listResponse != null && listResponse.getSuccess());

            // 验证基本响应
            assertThat(listResponse).isNotNull();
            assertThat(listResponse.getSuccess()).isTrue();
            assertThat(listResponse.getErrorCode()).isNull();
            assertThat(listResponse.getErrorMessage()).isNull();
            assertThat(listResponse.getResult()).isNotNull();
            assertThat(listResponse.getResult().getOpOrderRefundModels()).isNotNull();

            // 验证分页信息
            assertThat(listResponse.getResult().getTotalCount()).isNotNull();
            assertThat(listResponse.getResult().getCurrentPageNum()).isNotNull();

            // 打印查询结果
            log.info("{}查询退款单列表结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, listResponse.getSuccess());
            log.info("{}总记录数: {}", LOG_ITEM, listResponse.getResult().getTotalCount());
            log.info("{}当前页码: {}", LOG_ITEM, listResponse.getResult().getCurrentPageNum());

            // 验证退款单列表
            assertThat(listResponse.getResult().getOpOrderRefundModels()).isNotEmpty();
            var detail = listResponse.getResult().getOpOrderRefundModels().get(0);

            // 验证退款单详情
            assertThat(detail.getRefundId()).isNotNull().startsWith("TQ");
            assertThat(detail.getOrderId()).isNotNull();
            assertThat(detail.getStatus()).isNotNull();
            assertThat(detail.getRefundPayment()).isNotNull();
            assertThat(detail.getApplyPayment()).isNotNull();
            assertThat(detail.getProductName()).isNotNull();
            assertThat(detail.getBuyerMemberId()).isNotNull();
            assertThat(detail.getSellerMemberId()).isNotNull();

            // 打印第一条退款单详情
            log.info("{}退款单详情{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}退款单号(refundId): {}", LOG_ITEM, detail.getRefundId());
            log.info("{}订单号(orderId): {}", LOG_ITEM, detail.getOrderId());
            log.info("{}退款状态(status): {}", LOG_ITEM, detail.getStatus());
            log.info("{}退款金额(refundPayment): {} 分", LOG_ITEM, detail.getRefundPayment());
            log.info("{}申请退款金额(applyPayment): {} 分", LOG_ITEM, detail.getApplyPayment());

            // 商品信息
            log.info("{}商品名称: {}", LOG_ITEM, detail.getProductName());

            // 用户信息
            log.info("{}买家会员ID: {}", LOG_ITEM, detail.getBuyerMemberId());
            log.info("{}买家登录ID: {}", LOG_ITEM, detail.getBuyerLoginId());
            log.info("{}卖家会员ID: {}", LOG_ITEM, detail.getSellerMemberId());
            log.info("{}卖家登录ID: {}", LOG_ITEM, detail.getSellerLoginId());

            // 时间信息
            log.info("{}创建时间: {}", LOG_ITEM, detail.getGmtCreate());
            log.info("{}修改时间: {}", LOG_ITEM, detail.getGmtModified());
            log.info("{}申请时间: {}", LOG_ITEM, detail.getGmtApply());
            log.info("{}完成时间: {}", LOG_ITEM, detail.getGmtCompleted());

            // 退款原因
            log.info("{}申请原因: {}", LOG_ITEM, detail.getApplyReason());
            log.info("{}申请原因ID: {}", LOG_ITEM, detail.getApplyReasonId());

            // 扩展信息
            if (detail.getExtInfo() != null) {
                log.info("{}扩展信息: {}", LOG_ITEM, detail.getExtInfo());
            }
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("QueryRefundBuyerList");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    /**
     * TODO： 暂时无测试数据，需要测试创建退款
     */
    @Test
    void shouldCreateRefund() {
        // TODO: 暂时无法测试
        // Given
        var request = RefundCreateRequestRecord
            .of(123456789L, RefundCreateRequestRecord.DisputeRequest.REFUND, 10000L, "不想要了", 20028L, RefundCreateRequestRecord.GoodsStatus.RECEIVED);

        log.info("{}开始创建退款申请测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}订单号: {}", LOG_ITEM, request.orderId());
        log.info("{}退款金额: {} 分", LOG_ITEM, request.applyPayment());
        log.info("{}货物状态: {}", LOG_ITEM, request.goodsStatus());
        log.info("{}退款原因ID: {}", LOG_ITEM, request.applyReasonId());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = refundService.createRefund(request);

        // Then
        StepVerifier.create(response).assertNext(createResponse -> {
            // 记录API调用指标
            recordMetrics("CreateRefund", startTime, createResponse != null && createResponse.getSuccess());

            assertThat(createResponse).isNotNull();
            assertThat(createResponse.getSuccess()).isTrue();
            assertThat(createResponse.getResult()).isNotNull();

            // 打印创建结果
            log.info("{}创建退款申请结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, createResponse.getSuccess());
            log.info("{}退款单号: {}", LOG_ITEM, createResponse.getResult().getResult().getRefundId());
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("CreateRefund");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldQueryRefundBuyerView() {
        // checked 没问题
        // Given
        var request = RefundBuyerViewRequestRecord.of("191504821274548807");

        log.info("{}开始查询退款单详情测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}退款单号: {}", LOG_ITEM, request.refundId());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = refundService.queryRefundBuyerView(request);

        // Then
        StepVerifier.create(response).assertNext(viewResponse -> {
            // 记录API调用指标
            recordMetrics("QueryRefundBuyerView", startTime, viewResponse != null);

            assertThat(viewResponse).isNotNull();
            assertThat(viewResponse.getResult()).isNotNull();
            assertThat(viewResponse.getResult().getOpOrderRefundModelDetail()).isNotNull();

            var detail = viewResponse.getResult().getOpOrderRefundModelDetail();

            // 打印查询结果
            log.info("{}查询退款单详情结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}退款单号: {}", LOG_ITEM, detail.getRefundId());
            log.info("{}订单号: {}", LOG_ITEM, detail.getOrderId());
            log.info("{}退款状态: {}", LOG_ITEM, detail.getStatus());
            log.info("{}退款金额: {} 分", LOG_ITEM, detail.getRefundPayment());
            log.info("{}申请退款金额: {} 分", LOG_ITEM, detail.getApplyPayment());
            log.info("{}商品名称: {}", LOG_ITEM, detail.getProductName());
            log.info("{}买家会员ID: {}", LOG_ITEM, detail.getBuyerMemberId());
            log.info("{}卖家会员ID: {}", LOG_ITEM, detail.getSellerMemberId());
            log.info("{}买家登录ID: {}", LOG_ITEM, detail.getBuyerLoginId());
            log.info("{}卖家登录ID: {}", LOG_ITEM, detail.getSellerLoginId());

            // 时间信息
            log.info("{}创建时间: {}", LOG_ITEM, detail.getGmtCreate());
            log.info("{}修改时间: {}", LOG_ITEM, detail.getGmtModified());
            log.info("{}申请时间: {}", LOG_ITEM, detail.getGmtApply());
            log.info("{}完成时间: {}", LOG_ITEM, detail.getGmtCompleted());

            // 退款原因
            log.info("{}申请原因: {}", LOG_ITEM, detail.getApplyReason());
            log.info("{}申请原因ID: {}", LOG_ITEM, detail.getApplyReasonId());

            // 扩展信息
            if (detail.getExtInfo() != null) {
                log.info("{}扩展信息: {}", LOG_ITEM, detail.getExtInfo());
            }
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("QueryRefundBuyerView");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetRefundReasonList() {
        // checked 没问题
        // Given
        var request = new RefundReasonListRequestRecord("2410380770244540788", Collections
            .singletonList(2410380770244540788L), RefundReasonListRequestRecord.GoodsStatus.REFUND_WAIT_SELLER_SEND);

        log.info("{}开始查询退款原因列表测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}订单号: {}", LOG_ITEM, request.orderId());
        log.info("{}货物状态: {}", LOG_ITEM, request.goodsStatus());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = refundService.getRefundReasonList(request);

        // Then
        StepVerifier.create(response).assertNext(reasonResponse -> {
            // 记录API调用指标
            recordMetrics("GetRefundReasonList", startTime, reasonResponse != null && reasonResponse
                .getResult() != null);

            // 验证基本响应
            assertThat(reasonResponse).isNotNull();
            assertThat(reasonResponse.getResult()).isNotNull();

            // 打印查询结果
            log.info("{}查询退款原因列表结果{}", LOG_SEPARATOR, LOG_SEPARATOR);

            if (!reasonResponse.getResult().getSuccess()) {
                log.info("{}查询失败: {}", LOG_ITEM, reasonResponse.getResult().getSuccess());
                log.info("{}错误码: {}", LOG_ITEM, reasonResponse.getResult().getResult().getCode());
                log.info("{}错误信息: {}", LOG_ITEM, reasonResponse.getResult().getResult().getMessage());
                return;
            }

            assertThat(reasonResponse.getResult().getResult()).isNotNull();
            assertThat(reasonResponse.getResult().getResult().getReasons()).isNotEmpty();

            log.info("{}是否成功: {}", LOG_ITEM, reasonResponse.getResult().getSuccess());
            log.info("{}原因数量: {}", LOG_ITEM, reasonResponse.getResult().getResult().getReasons().size());

            // 验证原因列表
            assertThat(reasonResponse.getResult().getResult().getReasons()).isNotEmpty();

            // 打印第一个退款原因
            var reason = reasonResponse.getResult().getResult().getReasons().get(0);
            log.info("{}原因ID: {}", LOG_ITEM, reason.getId());
            log.info("{}原因名称: {}", LOG_ITEM, reason.getName());
            log.info("{}是否需要凭证: {}", LOG_ITEM, reason.getNeedVoucher());
            log.info("{}是否不支持退运费: {}", LOG_ITEM, reason.getNoRefundCarriage());
            log.info("{}提示信息: {}", LOG_ITEM, reason.getTip());
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GetRefundReasonList");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldQueryRefundOpLogList() {
        // checked
        // Given
        var request = RefundOpLogListRequestRecord.of("TQ191504821274548807");

        log.info("{}开始查询退款操作记录列表测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}退款单号: {}", LOG_ITEM, request.refundId());
        log.info("{}页码: {}", LOG_ITEM, request.pageNo());
        log.info("{}每页条数: {}", LOG_ITEM, request.pageSize());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = refundService.queryRefundOpLogList(request);

        // Then
        StepVerifier.create(response).assertNext(opLogResponse -> {
            // 记录API调用指标
            recordMetrics("QueryRefundOpLogList", startTime, opLogResponse != null);

            // 验证基本响应
            assertThat(opLogResponse).isNotNull();
            assertThat(opLogResponse.getErrorCode()).isNull();
            assertThat(opLogResponse.getErrorMessage()).isNull();
            assertThat(opLogResponse.getResult()).isNotNull();
            assertThat(opLogResponse.getResult().getOpOrderRefundOperationModels()).isNotNull();

            // 打印查询结果
            log.info("{}查询退款操作记录列表结果{}", LOG_SEPARATOR, LOG_SEPARATOR);

            // 打印操作记录
            log.info("{}退款操作记录{}", LOG_SEPARATOR, LOG_SEPARATOR);
            opLogResponse.getResult().getOpOrderRefundOperationModels().forEach(opLog -> {
                log.info("{}操作时间: {}", LOG_ITEM, opLog.getGmtCreate());
                log.info("{}操作人ID: {}", LOG_ITEM, opLog.getOperatorId());
                log.info("{}操作人登录ID: {}", LOG_ITEM, opLog.getOperatorLoginId());
                log.info("{}操作人角色: {}", LOG_ITEM, opLog.getOperatorRoleId());
                log.info("{}操作类型: {}", LOG_ITEM, opLog.getOperateTypeInt());
                log.info("{}操作备注: {}", LOG_ITEM, opLog.getOperateRemark());
                log.info("{}操作前状态: {}", LOG_ITEM, opLog.getBeforeOperateStatus());
                log.info("{}操作后状态: {}", LOG_ITEM, opLog.getAfterOperateStatus());
                log.info("{}描述说明: {}", LOG_ITEM, opLog.getDescription());

                if (opLog.getLogisticsCompany() != null) {
                    var logistics = opLog.getLogisticsCompany();
                    log.info("{}物流公司: {}", LOG_ITEM, logistics.getCompanyName());
                    log.info("{}物流编号: {}", LOG_ITEM, logistics.getCompanyNo());
                    log.info("{}物流电话: {}", LOG_ITEM, logistics.getCompanyPhone());
                }
            });
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("QueryRefundOpLogList");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldQueryRefundBuyerOrderView() {
        // checked
        // Given
        var request = new RefundBuyerOrderViewRequestRecord("2410380770244540788", RefundBuyerOrderViewRequestRecord.QueryType.REFUND_SUCCESS);

        log.info("{}开始查询订单退款详情测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}订单号: {}", LOG_ITEM, request.orderId());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = refundService.queryRefundBuyerOrderView(request);

        // Then
        StepVerifier.create(response).assertNext(orderViewResponse -> {
            // 记录API调用指标
            recordMetrics("QueryRefundBuyerOrderView", startTime, orderViewResponse != null);

            // 验证基本响应
            assertThat(orderViewResponse).isNotNull();
            assertThat(orderViewResponse.getErrorCode()).isNull();
            assertThat(orderViewResponse.getErrorMessage()).isNull();
            assertThat(orderViewResponse.getResult()).isNotNull();
            assertThat(orderViewResponse.getResult().getOpOrderRefundModels()).isNotNull();

            // 打印查询结果
            log.info("{}查询订单退款详情结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, orderViewResponse.getSuccess());

            // 打印退款单列表
            if (!orderViewResponse.getResult().getOpOrderRefundModels().isEmpty()) {
                var refund = orderViewResponse.getResult().getOpOrderRefundModels().get(0);
                log.info("{}退款单号: {}", LOG_ITEM, refund.getRefundId());
                log.info("{}订单号: {}", LOG_ITEM, refund.getOrderId());
                log.info("{}退款状态: {}", LOG_ITEM, refund.getStatus());
                log.info("{}退款金额: {} 分", LOG_ITEM, refund.getRefundPayment());
                log.info("{}退款类型: {}", LOG_ITEM, refund.getDisputeType());
            }
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("QueryRefundBuyerOrderView");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldSubmitRefundBuyerInfo() {
        // 暂时无法测试
        // Given
        var request = RefundBuyerSubmitRequestRecord.of("TQ123456789", "ZTO", "SF1234567890");

        log.info("{}开始提交退款货物信息测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}退款单号: {}", LOG_ITEM, request.refundId());
        log.info("{}物流公司编号: {}", LOG_ITEM, request.logisticsCompanyNo());
        log.info("{}物流单号: {}", LOG_ITEM, request.freightBill());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = refundService.submitRefundBuyerInfo(request);

        // Then
        StepVerifier.create(response).assertNext(submitResponse -> {
            // 记录API调用指标
            recordMetrics("SubmitRefundBuyerInfo", startTime, submitResponse != null);

            // 验证基本响应
            assertThat(submitResponse).isNotNull();

            // 打印提交结果
            log.info("{}提交退款货物信息结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, submitResponse.getResult().getSuccess());

            if (!submitResponse.getResult().getSuccess()) {
                log.info("{}错误码: {}", LOG_ITEM, submitResponse.getResult().getErrorCode());
                log.info("{}错误信息: {}", LOG_ITEM, submitResponse.getResult().getErrorInfo());
                return;
            }

        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("SubmitRefundBuyerInfo");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldUploadRefundEvidence() {
        // Given
        var request = RefundUploadEvidenceRequestRecord.of("TQ123456789", "https://example.com/evidence.jpg");

        log.info("{}开始上传退款凭证测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}退款单号: {}", LOG_ITEM, request.refundId());
        log.info("{}凭证描述: {}", LOG_ITEM, request.description());
        log.info("{}凭证URL: {}", LOG_ITEM, request.evidenceUrl());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = refundService.uploadRefundEvidence(request);

        // Then
        StepVerifier.create(response).assertNext(uploadResponse -> {
            // 记录API调用指标
            recordMetrics("UploadRefundEvidence", startTime, uploadResponse != null && uploadResponse.getSuccess());

            // 验证基本响应
            assertThat(uploadResponse).isNotNull();
            assertThat(uploadResponse.getSuccess()).isTrue();
            assertThat(uploadResponse.getErrorCode()).isNull();
            assertThat(uploadResponse.getErrorMessage()).isNull();
            assertThat(uploadResponse.getResult()).isNotNull();

            // 打印上传结果
            log.info("{}上传退款凭证结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, uploadResponse.getSuccess());
            log.info("{}退款单号: {}", LOG_ITEM, uploadResponse.getResult().getRefundId());
            log.info("{}退款状态: {}", LOG_ITEM, uploadResponse.getResult().getRefundStatus());
            log.info("{}退款阶段: {}", LOG_ITEM, uploadResponse.getResult().getRefundPhase());
            log.info("{}退款类型: {}", LOG_ITEM, uploadResponse.getResult().getDisputeType());
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("UploadRefundEvidence");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }
}