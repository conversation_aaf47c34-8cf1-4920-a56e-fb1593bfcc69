/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.integration;

import static org.assertj.core.api.Assertions.assertThat;

import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.api.request.feedback.AccountBusinessSaveRequestRecord;
import com.fulfillmen.support.alibaba.api.request.feedback.LogisticsOrderSyncRequestRecord;
import com.fulfillmen.support.alibaba.api.request.feedback.OrderRelationWriteRequestRecord;
import com.fulfillmen.support.alibaba.api.request.feedback.OrderSyncRequestRecord;
import com.fulfillmen.support.alibaba.service.IFeedbackService;
import java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.test.StepVerifier;

/**
 * 1688回传数据相关API集成测试
 *
 * <AUTHOR>
 * @created 2025-01-15
 */
@Slf4j
@Tag("integration")
class FeedbackAPIIntegrationTest extends BaseAPITest {

    private static final String LOG_ITEM = "[1688回传数据]";

    @Autowired
    private IFeedbackService feedbackService;

    @Test
    void shouldSaveAccountBusiness() {
        // Given
        var request = new AccountBusinessSaveRequestRecord("testAccount", "sea");
        log.info("{}请求参数: account={}, business={}", LOG_ITEM, request.account(), request.business());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = feedbackService.saveAccountBusiness(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isTrue();

            // 业务检查
            assertThat(result.getResult()).isNotNull();
            assertThat(result.getResult().getSaveResult()).isTrue();

            // 记录日志
            log.info("{}响应结果: {}", LOG_ITEM, result);

            // 记录性能指标
            recordMetrics("SaveAccountBusiness", startTime, result.getSuccess());
        }).verifyComplete();
    }

    @Test
    void shouldSyncLogisticsOrder() {
        // Given
        var orderLogisticsParam = new LogisticsOrderSyncRequestRecord.OrderLogisticsParam("test_logistics_id", "test_company_code", "test_company_name", 123456789L, "test_bill_no", "2025-01-15 12:00:00",
            "ACCEPT", "[{\"action\":\"已揽收\",\"operateTime\":\"2025-01-15 12:00:00\",\"operator\":\"test_company_name\"}]");

        var request = new LogisticsOrderSyncRequestRecord(orderLogisticsParam);
        log.info("{}请求参数: logisticsId={}, orderId={}, status={}", LOG_ITEM, request.orderLogisticsParam()
            .logisticsId(), request.orderLogisticsParam().orderId(), request.orderLogisticsParam().status());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = feedbackService.syncLogisticsOrder(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isTrue();

            // 业务检查
            assertThat(result.getResult()).isNotNull();
            assertThat(result.getResult().getSuccess()).isTrue();

            // 记录日志
            log.info("{}响应结果: {}", LOG_ITEM, result);

            // 记录性能指标
            recordMetrics("SyncLogisticsOrder", startTime, result.getSuccess());
        }).verifyComplete();
    }

    @Test
    void shouldWriteOrderRelation() {
        // Given
        var orderRelationParam = new OrderRelationWriteRequestRecord.OrderRelationParam("test_order_id", "test_parent_order_id", 123456789L, 987654321L);

        var request = new OrderRelationWriteRequestRecord(orderRelationParam);
        log.info("{}请求参数: orderId={}, parentOrderId={}, purchaseOrderId={}, purchaseParentOrderId={}", LOG_ITEM, request
            .orderRelationParam()
            .orderId(), request.orderRelationParam().parentOrderId(), request.orderRelationParam()
                .purchaseOrderId(), request.orderRelationParam().purchaseParentOrderId());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = feedbackService.writeOrderRelation(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isTrue();

            // 业务检查
            assertThat(result.getResult()).isNotNull();
            assertThat(result.getResult().getSuccess()).isTrue();

            // 记录日志
            log.info("{}响应结果: {}", LOG_ITEM, result);

            // 记录性能指标
            recordMetrics("WriteOrderRelation", startTime, result.getSuccess());
        }).verifyComplete();
    }

    @Test
    void shouldSyncOrder() {
        // Given
        var subOrderParam = new OrderSyncRequestRecord.SubOrderParam("test_sub_order_id", "test_product_id", "test_product_name", "test_skul_id", "test_sku_name", 1L, 100L, "NO_REFUND");

        var orderParam = new OrderSyncRequestRecord.OrderParam("test_order_id", "test_product_id", "test_product_name", "test_skul_id", "test_sku_name", 1L, "2025-01-16 10:10:10", "test_member_id",
            "2025-01-16 10:10:10", "2025-01-16 10:10:10", 100L, "NO_REFUND", "success", Collections
                .singletonList(subOrderParam));

        var request = new OrderSyncRequestRecord(orderParam);
        log.info("{}请求参数: orderId={}, productId={}, status={}", LOG_ITEM, request.orderParam().orderId(), request
            .orderParam()
            .productId(), request.orderParam().status());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = feedbackService.syncOrder(request);

        // Then
        StepVerifier.create(response).assertNext(result -> {
            // 基本检查
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isTrue();

            // 业务检查
            assertThat(result.getResult()).isNotNull();
            assertThat(result.getResult().getSuccess()).isTrue();
            assertThat(result.getResult().getCode()).isEqualTo("200");

            // 记录日志
            log.info("{}响应结果: {}", LOG_ITEM, result);

            // 记录性能指标
            recordMetrics("SyncOrder", startTime, result.getSuccess());
        }).verifyComplete();
    }
}