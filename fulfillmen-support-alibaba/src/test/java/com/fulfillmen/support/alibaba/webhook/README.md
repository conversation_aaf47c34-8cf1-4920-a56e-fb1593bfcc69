# Webhook 测试用例

本目录包含了针对阿里巴巴Webhook回调处理的完整测试用例，重点测试MessageDispatcher的核心功能。

**重要说明：** 本目录中的所有类仅用于测试目的，不应在生产代码中使用。

## 测试文件说明

### 1. 测试用业务类

#### data/OrderSuccessData.java
测试用的订单成功数据模型，用于验证数据序列化和反序列化。

#### handler/OrderSuccessMessageHandler.java
测试用的订单成功消息处理器，用于验证消息处理逻辑。

### 2. 核心组件测试用例

#### MessageDispatcherMockTest.java
MessageDispatcher的Mock测试类，测试消息分发的核心逻辑：

**测试用例：**
- `shouldDispatchValidMessage()` - 测试有效消息分发
- `shouldRejectInvalidSignature()` - 测试签名验证失败
- `shouldHandleDuplicateMessage()` - 测试重复消息处理
- `shouldHandleInvalidJsonMessage()` - 测试无效JSON处理
- `shouldHandleRouterException()` - 测试路由异常处理
- `shouldHandleMultipleMessages()` - 测试多条消息处理
- `shouldHandleIdempotentManagerException()` - 测试幂等性管理器异常
- `shouldHandleNoHandlerFound()` - 测试无处理器情况

#### MessageDispatcherIntegrationTest.java
MessageDispatcher的集成测试类，测试完整的消息处理流程：

**测试用例：**
- `shouldProcessCompleteMessageFlow()` - 测试完整消息处理流程
- `shouldHandleIdempotencyCorrectly()` - 测试幂等性处理
- `shouldHandleUnknownMessageType()` - 测试未知消息类型
- `shouldHandleMultipleMessagesWithMixedResults()` - 测试混合结果处理
- `shouldProcessMessageWithinReasonableTime()` - 测试处理性能

#### MessageRouterTest.java
MessageRouter的测试类，测试消息路由功能：

**测试用例：**
- `shouldRouteMessageToCorrectHandler()` - 测试消息路由
- `shouldReturnNoHandlerForUnknownType()` - 测试未知类型路由
- `shouldHandleMultipleHandlersWithPriority()` - 测试处理器优先级
- `shouldHandleEmptyHandlerList()` - 测试空处理器列表
- `shouldRegisterHandlersCorrectly()` - 测试处理器注册

#### SignatureValidatorTest.java
SignatureValidator的测试类，测试签名验证功能：

**测试用例：**
- `shouldValidateCorrectSignature()` - 测试正确签名验证
- `shouldRejectIncorrectSignature()` - 测试错误签名拒绝
- `shouldSkipValidationWhenSecretIsEmpty()` - 测试空密钥跳过验证
- `shouldSkipValidationWhenSecretIsNull()` - 测试null密钥跳过验证
- `shouldRejectNullSignature()` - 测试null签名拒绝
- `shouldRejectEmptySignature()` - 测试空签名拒绝
- `shouldHandleDifferentMessageContent()` - 测试不同消息内容
- `shouldHandleDifferentSecretKeys()` - 测试不同密钥

#### IdempotentManagerTest.java
IdempotentManager的测试类，测试幂等性管理功能：

**测试用例：**
- `shouldDetectFirstTimeMessage()` - 测试首次消息检测
- `shouldDetectDuplicateAfterProcessing()` - 测试重复消息检测
- `shouldMarkSuccessfulProcessing()` - 测试成功处理标记
- `shouldMarkFailedProcessing()` - 测试失败处理标记
- `shouldHandleMultipleMessages()` - 测试多消息处理
- `shouldHandleConcurrentAccess()` - 测试并发访问
- `shouldHandleNullMessageId()` - 测试null消息ID
- `shouldHandleEmptyMessageId()` - 测试空消息ID
- `shouldExecuteCleanupWithoutError()` - 测试清理功能

### 3. 数据模型测试用例

#### OrderSuccessDataTest.java
数据模型测试类，测试OrderSuccessData的各种功能：

**测试用例：**
- `shouldCreateOrderSuccessDataWithAllFields()` - 测试字段设置
- `shouldSerializeToJson()` - 测试JSON序列化
- `shouldDeserializeFromJson()` - 测试JSON反序列化
- `shouldSerializeAndDeserializeCorrectly()` - 测试序列化一致性
- `shouldHandleNullValues()` - 测试空值处理
- `shouldHandlePartialData()` - 测试部分数据处理
- `shouldValidateOrderIdFormat()` - 测试订单ID格式验证
- `shouldValidateStatusValues()` - 测试状态值验证

## 测试数据

测试使用的消息数据基于真实的阿里巴巴订单成功回调：

```json
[{
    "bizKey": "2806599398122540788",
    "data": {
        "buyerMemberId": "b2b-2207416548807a4d12",
        "currentStatus": "success",
        "orderId": 2806599398122540788,
        "sellerMemberId": "b2b-221280776451649a09",
        "msgSendTime": "2025-07-10 17:55:46"
    },
    "gmtBorn": 1752141346107,
    "msgId": "139830976934",
    "type": "ORDER_BUYER_VIEW_ORDER_SUCCESS",
    "userInfo": "b2b-2207416548807a4d12"
}]
```

**签名：** `38DF23A2911926BD925AA1D89DC2D282777BA572`

## 运行测试

### 运行MessageDispatcher测试
```bash
# Mock测试
mvn test -Dtest=MessageDispatcherMockTest -Ptest -Dspotless.skip=true

# 集成测试
mvn test -Dtest=MessageDispatcherIntegrationTest -Ptest -Dspotless.skip=true
```

### 运行组件测试
```bash
# 消息路由器测试
mvn test -Dtest=MessageRouterTest -Ptest -Dspotless.skip=true

# 签名验证器测试
mvn test -Dtest=SignatureValidatorTest -Ptest -Dspotless.skip=true

# 幂等性管理器测试
mvn test -Dtest=IdempotentManagerTest -Ptest -Dspotless.skip=true
```

### 运行数据模型测试
```bash
mvn test -Dtest=OrderSuccessDataTest -Ptest -Dspotless.skip=true
```

### 运行所有Webhook测试
```bash
mvn test -Dtest="*webhook*" -Ptest -Dspotless.skip=true
```

## 相关组件

测试涉及的主要组件：

1. **MessageDispatcher** - 消息分发器（核心组件）
2. **MessageRouter** - 消息路由器
3. **SignatureValidator** - 签名验证器
4. **IdempotentManager** - 幂等性管理器
5. **OrderSuccessMessageHandler** - 订单成功消息处理器
6. **OrderSuccessData** - 订单成功数据模型

## 测试覆盖场景

### MessageDispatcher核心功能
- ✅ 消息分发逻辑
- ✅ 签名验证流程
- ✅ 幂等性检查
- ✅ 异常处理机制
- ✅ 多消息处理
- ✅ 完整的端到端流程

### 组件功能
- ✅ 消息路由和处理器匹配
- ✅ 处理器优先级管理
- ✅ 签名验证（多种场景）
- ✅ 重复消息检测
- ✅ 并发访问处理

### 数据处理
- ✅ 数据模型序列化/反序列化
- ✅ 消息解析和数据转换
- ✅ 空值和异常数据处理
- ✅ 数据验证和格式检查

## 注意事项

1. **测试目的**：这些类仅用于测试目的，不应在生产代码中使用
2. **消息格式**：测试数据基于真实的阿里巴巴webhook消息格式
3. **数据完整性**：测试覆盖了各种数据场景，确保数据处理的稳定性

## 扩展测试

如需添加新的测试用例：

1. 在OrderSuccessDataTest中添加新的测试方法
2. 使用 `@Tag` 注解标记测试类型（如 "data", "serialization", "validation"）
3. 遵循现有的日志格式和断言模式
4. 确保测试数据的真实性和完整性
