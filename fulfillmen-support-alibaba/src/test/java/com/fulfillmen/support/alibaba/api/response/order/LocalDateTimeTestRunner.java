/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.order;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.LocalDateTime;

/**
 * LocalDateTime 测试运行器
 * 
 * <AUTHOR>
 * @created 2025-01-12
 */
public class LocalDateTimeTestRunner {

    public static void main(String[] args) {
        try {
            testLocalDateTimeDeserialization();
            System.out.println("✅ 所有 LocalDateTime 测试通过！");
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void testLocalDateTimeDeserialization() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();

        // 测试阿里巴巴日期格式
        String jsonWithAlibabaFormat = """
            {
                "result": {
                    "baseInfo": {
                        "id": 123456789,
                        "createTime": "20250625153309000+0800",
                        "payTime": "20250625171936000+0800",
                        "completeTime": "20250705174806000+0800"
                    },
                    "tradeTerms": [{
                        "payTime": "20250625171937000+0800",
                        "payStatus": "6"
                    }],
                "tradeProductItems": [{
                        "gmtCreate": "20250625153309000+0800",
                        "gmtModified": "20250705174805000+0800",
                        "gmtCompleted": "20250705174806000+0800",
                        "gmtPayExpireTime": "2025-06-25 17:34:28"
                    }]
                },
                "success": "true"
            }
            """;

        OrderDetailResponse response = objectMapper.readValue(jsonWithAlibabaFormat, OrderDetailResponse.class);

        // 验证基础信息日期
        LocalDateTime createTime = response.getResult().getBaseInfo().getCreateTime();
        LocalDateTime payTime = response.getResult().getBaseInfo().getPayTime();
        LocalDateTime completeTime = response.getResult().getBaseInfo().getCompleteTime();

        System.out.println("📅 基础信息日期测试:");
        System.out.println("  创建时间: " + createTime);
        System.out.println("  支付时间: " + payTime);
        System.out.println("  完成时间: " + completeTime);

        if (createTime == null || payTime == null || completeTime == null) {
            throw new RuntimeException("基础信息日期解析失败");
        }

        // 验证交易条款日期
        LocalDateTime tradePayTime = response.getResult().getTradeTerms().get(0).getPayTime();
        System.out.println("  交易支付时间: " + tradePayTime);

        if (tradePayTime == null) {
            throw new RuntimeException("交易条款日期解析失败");
        }

        // 验证商品信息日期
        var product = response.getResult().getProductItems().get(0);
        LocalDateTime productCreateTime = product.getGmtCreate();
        LocalDateTime productModifiedTime = product.getGmtModified();
        LocalDateTime productCompletedTime = product.getGmtCompleted();
        LocalDateTime productExpireTime = product.getGmtPayExpireTime();

        System.out.println("📦 商品信息日期测试:");
        System.out.println("  商品创建时间: " + productCreateTime);
        System.out.println("  商品修改时间: " + productModifiedTime);
        System.out.println("  商品完成时间: " + productCompletedTime);
        System.out.println("  支付过期时间: " + productExpireTime);

        if (productCreateTime == null || productModifiedTime == null ||
            productCompletedTime == null || productExpireTime == null) {
            throw new RuntimeException("商品信息日期解析失败");
        }

        // 验证日期比较功能
        System.out.println("⏰ 日期比较测试:");
        if (payTime.isAfter(createTime)) {
            System.out.println("  ✅ 支付时间在创建时间之后");
        } else {
            throw new RuntimeException("日期比较逻辑错误");
        }

        if (completeTime.isAfter(payTime)) {
            System.out.println("  ✅ 完成时间在支付时间之后");
        } else {
            throw new RuntimeException("日期比较逻辑错误");
        }

        // 验证日期操作
        LocalDateTime nextDay = createTime.plusDays(1);
        System.out.println("  ✅ 日期操作: " + createTime + " + 1天 = " + nextDay);

        // 验证时间差计算
        long minutesBetween = java.time.Duration.between(createTime, payTime).toMinutes();
        System.out.println("  ✅ 时间差: 创建到支付相差 " + minutesBetween + " 分钟");

        System.out.println("🎉 LocalDateTime 功能验证完成！");
    }
}
