/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.integration.data;

import java.math.BigDecimal;

/**
 * 订单基础信息预期数据
 * 
 * <AUTHOR>
 * @created 2025-07-12
 */
public record BaseInfoExpected(
    String status,
    BigDecimal totalAmount,
    String buyerID,
    String sellerID,
    String businessType,
    String outOrderId,
    ContactExpected buyerContact,
    ContactExpected sellerContact,
    boolean validateTimestamps
) {

    /**
     * 联系人信息预期数据
     */
    public record ContactExpected(
        String name,
        String companyName
    ) {
    }

    /**
     * 创建基础信息预期数据的构建器
     */
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {

        private String status;
        private BigDecimal totalAmount;
        private String buyerID;
        private String sellerID;
        private String businessType;
        private String outOrderId;
        private ContactExpected buyerContact;
        private ContactExpected sellerContact;
        private boolean validateTimestamps = true;

        public Builder status(String status) {
            this.status = status;
            return this;
        }

        public Builder totalAmount(BigDecimal totalAmount) {
            this.totalAmount = totalAmount;
            return this;
        }

        public Builder totalAmount(String amount) {
            this.totalAmount = new BigDecimal(amount);
            return this;
        }

        public Builder buyerID(String buyerID) {
            this.buyerID = buyerID;
            return this;
        }

        public Builder sellerID(String sellerID) {
            this.sellerID = sellerID;
            return this;
        }

        public Builder businessType(String businessType) {
            this.businessType = businessType;
            return this;
        }

        public Builder outOrderId(String outOrderId) {
            this.outOrderId = outOrderId;
            return this;
        }

        public Builder buyerContact(String name, String companyName) {
            this.buyerContact = new ContactExpected(name, companyName);
            return this;
        }

        public Builder sellerContact(String name, String companyName) {
            this.sellerContact = new ContactExpected(name, companyName);
            return this;
        }

        public Builder validateTimestamps(boolean validateTimestamps) {
            this.validateTimestamps = validateTimestamps;
            return this;
        }

        public BaseInfoExpected build() {
            return new BaseInfoExpected(
                status, totalAmount, buyerID, sellerID, businessType, outOrderId,
                buyerContact, sellerContact, validateTimestamps
            );
        }
    }
}
