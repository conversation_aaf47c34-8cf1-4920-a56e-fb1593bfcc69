{"result": [{"orderRateInfo": {"sellerRateStatus": 5, "buyerRateStatus": 5}, "tradeTerms": [], "nativeLogistics": {"zip": "516000", "area": "惠城区", "address": "广东省 惠州市 惠城区", "city": "惠州市", "contactPerson": "中田 13411", "areaCode": "441302", "privacyProtection": false, "province": "广东省"}, "baseInfo": {"businessType": "cb", "buyerID": "b2b-2207*********a4d12", "createTime": "20250104130205000+0800", "id": 2430287185067540788, "modifyTime": "20250104130206000+0800", "refund": 0, "sellerID": "b2b-*************7072b", "shippingFee": 10, "status": "waitbuyerpay", "totalAmount": 302, "discount": 0, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "***********", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"phone": "86-159-15407954", "imInPlatform": "深圳市怡霖霏科技有限公司", "name": "王冰冰", "mobile": "***********", "companyName": "深圳市怡霖霏科技有限公司"}, "tradeType": "50060", "refundPayment": 0, "idOfStr": "2430287185067540788", "alipayTradeId": "UNCREATED", "receiverInfo": {"toDivisionCode": "441302", "toFullName": "中田 13411", "toArea": "广东省 惠州市 惠城区", "toPost": "516000"}, "buyerLoginId": "惠州中田贸易", "sellerLoginId": "深圳市怡霖霏科技有限公司", "buyerUserId": 2207*********, "sellerUserId": *************, "buyerAlipayId": "****************", "sellerAlipayId": "****************", "sumProductPayment": 292, "stepPayAll": false, "overSeaOrder": false}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false}, "productItems": [{"cargoNumber": "214C", "itemAmount": 292, "name": "跨境正品榨汁杯便携式电动充电迷你榨汁机随行果汁机厂家直营批发", "price": 73, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN015U4VJ31Bs2yhqI1aD_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN015U4VJ31Bs2yhqI1aD_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2430287185067540788", "quantity": 4, "refund": 0, "skuID": *************, "status": "waitbuyerpay", "subItemID": 2430287185067540788, "type": "common", "unit": "个", "guaranteesTerms": [{"assuranceInfo": "卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。", "assuranceType": "qtwlybt", "qualityAssuranceType": "7天无理由退货", "shortCode": "qtwlybt"}, {"shortCode": "qtwlybt_2"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "ssbxsfh", "qualityAssuranceType": "48小时发货", "shortCode": "D"}, {"shortCode": "D_2"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}, {"shortCode": "EOT"}], "productCargoNumber": "214", "skuInfos": [{"name": "颜色", "value": "214C粉色（TYPE-C充电款）"}], "entryDiscount": 0, "specId": "3892792d8d8977865389865d91665894", "quantityFactor": 1, "statusStr": "等待买家付款"}]}, {"orderRateInfo": {"sellerRateStatus": 5, "buyerRateStatus": 5}, "tradeTerms": [], "nativeLogistics": {"zip": "516000", "area": "惠城区", "address": "广东省 惠州市 惠城区", "city": "惠州市", "contactPerson": "中田 13411", "areaCode": "441302", "privacyProtection": false, "province": "广东省"}, "baseInfo": {"businessType": "cb", "buyerID": "b2b-2207*********a4d12", "createTime": "20250104125611000+0800", "id": 2429929202510540788, "modifyTime": "20250104125612000+0800", "refund": 0, "sellerID": "b2b-*************7072b", "shippingFee": 6, "status": "waitbuyerpay", "totalAmount": 84, "discount": 0, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "***********", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"phone": "86-159-15407954", "imInPlatform": "深圳市怡霖霏科技有限公司", "name": "王冰冰", "mobile": "***********", "companyName": "深圳市怡霖霏科技有限公司"}, "tradeType": "50060", "refundPayment": 0, "idOfStr": "2429929202510540788", "alipayTradeId": "UNCREATED", "receiverInfo": {"toDivisionCode": "441302", "toFullName": "中田 13411", "toArea": "广东省 惠州市 惠城区", "toPost": "516000"}, "buyerLoginId": "惠州中田贸易", "sellerLoginId": "深圳市怡霖霏科技有限公司", "buyerUserId": 2207*********, "sellerUserId": *************, "buyerAlipayId": "****************", "sellerAlipayId": "****************", "sumProductPayment": 78, "stepPayAll": false, "overSeaOrder": false}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false}, "productItems": [{"cargoNumber": "214B", "itemAmount": 78, "name": "跨境正品榨汁杯便携式电动充电迷你榨汁机随行果汁机厂家直营批发", "price": 78, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01OA1RIi1Bs2yI0AInw_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01OA1RIi1Bs2yI0AInw_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2429929202510540788", "quantity": 1, "refund": 0, "skuID": *************, "status": "waitbuyerpay", "subItemID": 2429929202510540788, "type": "common", "unit": "个", "guaranteesTerms": [{"assuranceInfo": "卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。", "assuranceType": "qtwlybt", "qualityAssuranceType": "7天无理由退货", "shortCode": "qtwlybt"}, {"shortCode": "qtwlybt_2"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "ssbxsfh", "qualityAssuranceType": "48小时发货", "shortCode": "D"}, {"shortCode": "D_2"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}, {"shortCode": "EOT"}], "productCargoNumber": "214", "skuInfos": [{"name": "颜色", "value": "214B白色（磁吸无线充电款）"}], "entryDiscount": 0, "specId": "c9cd40a018b4fa70d92e53d9379bdb9e", "quantityFactor": 1, "statusStr": "等待买家付款"}]}, {"orderRateInfo": {"sellerRateStatus": 5, "buyerRateStatus": 5}, "tradeTerms": [], "nativeLogistics": {"zip": "516001", "area": "惠城区", "address": "广东省 惠州市 惠城区", "city": "惠州市", "contactPerson": "中田 仓库", "areaCode": "441302", "privacyProtection": false, "province": "广东省"}, "baseInfo": {"businessType": "cb", "buyerID": "b2b-2207*********a4d12", "buyerSubID": 2216824975661, "createTime": "20250104120451000+0800", "id": 2430225733206540788, "modifyTime": "20250104120452000+0800", "refund": 0, "sellerID": "b2b-*************bb2b1", "shippingFee": 5, "status": "waitbuyerpay", "totalAmount": 235.8, "discount": 0, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "***********", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"phone": "", "imInPlatform": "昌衢饰品厂", "name": "周恒宇", "companyName": "义乌市昌衢电子商务商行"}, "tradeType": "50060", "refundPayment": 0, "idOfStr": "2430225733206540788", "alipayTradeId": "UNCREATED", "receiverInfo": {"toDivisionCode": "441302", "toFullName": "中田 仓库", "toArea": "广东省 惠州市 惠城区", "toPost": "516001"}, "buyerLoginId": "惠州中田贸易", "sellerLoginId": "昌衢饰品厂", "buyerUserId": 2207*********, "sellerUserId": *************, "buyerAlipayId": "****************", "sellerAlipayId": "****************", "sumProductPayment": 246.4, "stepPayAll": false, "overSeaOrder": false}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false, "shippingInsurance": "givenByAnXinGou"}, "productItems": [{"itemAmount": 44, "name": "NFC Bracelet Bible Verse 跨境热卖TikTok 每日圣经NFC手环", "price": 8.8, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01GGbHaP1Bs30ooNJUg_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01GGbHaP1Bs30ooNJUg_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2430225733207540788", "quantity": 5, "refund": 0, "skuID": *************, "status": "waitbuyerpay", "subItemID": 2430225733207540788, "type": "common", "unit": "条", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，自愿选择向买家提供“交期保障”服务。卖家向买家承诺，买家通过在线交易方式成功下单付款后，卖家将在双方确定的交期内（不可抗力因素除外），发送买家所购实物商品的服务，否则向买家进行赔付", "assuranceType": "jqbz", "qualityAssuranceType": "交期保障", "value": "7", "shortCode": "J"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "productCargoNumber": "NFC", "skuInfos": [{"name": "颜色", "value": "蓝色-十字架布标"}], "entryDiscount": 0, "specId": "41dd4ec88fc33c8f3ef00927b0d6db59", "quantityFactor": 1, "statusStr": "等待买家付款"}, {"itemAmount": 26.4, "name": "NFC Bracelet Bible Verse 跨境热卖TikTok 每日圣经NFC手环", "price": 8.8, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01OMvm991Bs30qxqFxE_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01OMvm991Bs30qxqFxE_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2430225733208540788", "quantity": 3, "refund": 0, "skuID": *************, "status": "waitbuyerpay", "subItemID": 2430225733208540788, "type": "common", "unit": "条", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，自愿选择向买家提供“交期保障”服务。卖家向买家承诺，买家通过在线交易方式成功下单付款后，卖家将在双方确定的交期内（不可抗力因素除外），发送买家所购实物商品的服务，否则向买家进行赔付", "assuranceType": "jqbz", "qualityAssuranceType": "交期保障", "value": "7", "shortCode": "J"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "productCargoNumber": "NFC", "skuInfos": [{"name": "颜色", "value": "海军蓝和平鸽"}], "entryDiscount": 0, "specId": "63389b6ceb5fcc38386461d181974ab6", "quantityFactor": 1, "statusStr": "等待买家付款"}, {"itemAmount": 8.8, "name": "NFC Bracelet Bible Verse 跨境热卖TikTok 每日圣经NFC手环", "price": 8.8, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01sfbWas1Bs30lbrKxe_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01sfbWas1Bs30lbrKxe_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2430225733209540788", "quantity": 1, "refund": 0, "skuID": *************, "status": "waitbuyerpay", "subItemID": 2430225733209540788, "type": "common", "unit": "条", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，自愿选择向买家提供“交期保障”服务。卖家向买家承诺，买家通过在线交易方式成功下单付款后，卖家将在双方确定的交期内（不可抗力因素除外），发送买家所购实物商品的服务，否则向买家进行赔付", "assuranceType": "jqbz", "qualityAssuranceType": "交期保障", "value": "7", "shortCode": "J"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "productCargoNumber": "NFC", "skuInfos": [{"name": "颜色", "value": "玫粉和平鸽"}], "entryDiscount": 0, "specId": "a7aae3ba40b8023e956efe9243911882", "quantityFactor": 1, "statusStr": "等待买家付款"}, {"itemAmount": 88, "name": "NFC Bracelet Bible Verse 跨境热卖TikTok 每日圣经NFC手环", "price": 8.8, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01XNbd4z1Bs30iPklaj_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01XNbd4z1Bs30iPklaj_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2430225733210540788", "quantity": 10, "refund": 0, "skuID": *************, "status": "waitbuyerpay", "subItemID": 2430225733210540788, "type": "common", "unit": "条", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，自愿选择向买家提供“交期保障”服务。卖家向买家承诺，买家通过在线交易方式成功下单付款后，卖家将在双方确定的交期内（不可抗力因素除外），发送买家所购实物商品的服务，否则向买家进行赔付", "assuranceType": "jqbz", "qualityAssuranceType": "交期保障", "value": "7", "shortCode": "J"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "productCargoNumber": "NFC", "skuInfos": [{"name": "颜色", "value": "黑色和平鸽"}], "entryDiscount": 0, "specId": "b3e0b9ac743fef404ebbf838d487df2b", "quantityFactor": 1, "statusStr": "等待买家付款"}, {"itemAmount": 70.4, "name": "NFC Bracelet Bible Verse 跨境热卖TikTok 每日圣经NFC手环", "price": 8.8, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01uJs2Ft1Bs30o75kYJ_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01uJs2Ft1Bs30o75kYJ_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2430225733211540788", "quantity": 8, "refund": 0, "skuID": *************, "status": "waitbuyerpay", "subItemID": 2430225733211540788, "type": "common", "unit": "条", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，自愿选择向买家提供“交期保障”服务。卖家向买家承诺，买家通过在线交易方式成功下单付款后，卖家将在双方确定的交期内（不可抗力因素除外），发送买家所购实物商品的服务，否则向买家进行赔付", "assuranceType": "jqbz", "qualityAssuranceType": "交期保障", "value": "7", "shortCode": "J"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "productCargoNumber": "NFC", "skuInfos": [{"name": "颜色", "value": "米色-和平鸽"}], "entryDiscount": 0, "specId": "bf2641fd27c389409e8a22ff2817fe40", "quantityFactor": 1, "statusStr": "等待买家付款"}, {"itemAmount": 8.8, "name": "NFC Bracelet Bible Verse 跨境热卖TikTok 每日圣经NFC手环", "price": 8.8, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01rwiMoz1Bs30ps14tr_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01rwiMoz1Bs30ps14tr_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2430225733212540788", "quantity": 1, "refund": 0, "skuID": *************, "status": "waitbuyerpay", "subItemID": 2430225733212540788, "type": "common", "unit": "条", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，自愿选择向买家提供“交期保障”服务。卖家向买家承诺，买家通过在线交易方式成功下单付款后，卖家将在双方确定的交期内（不可抗力因素除外），发送买家所购实物商品的服务，否则向买家进行赔付", "assuranceType": "jqbz", "qualityAssuranceType": "交期保障", "value": "7", "shortCode": "J"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "productCargoNumber": "NFC", "skuInfos": [{"name": "颜色", "value": "草绿和平鸽"}], "entryDiscount": 0, "specId": "e5f0023a2ad10d3e9cf8708b8365f64e", "quantityFactor": 1, "statusStr": "等待买家付款"}]}, {"orderRateInfo": {"sellerRateStatus": 5, "buyerRateStatus": 5}, "tradeTerms": [], "nativeLogistics": {"zip": "516000", "area": "惠城区", "address": "广东省 惠州市 惠城区", "city": "惠州市", "contactPerson": "中田 12092", "areaCode": "441302", "privacyProtection": false, "province": "广东省"}, "baseInfo": {"businessType": "cb", "buyerID": "b2b-2207*********a4d12", "createTime": "20250103153342000+0800", "id": 2428690188868540788, "modifyTime": "20250103153344000+0800", "refund": 0, "sellerID": "b2b-**********e1351", "shippingFee": 685, "status": "waitbuyerpay", "totalAmount": 4570, "discount": 0, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "***********", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"phone": "86-0576-***********", "imInPlatform": "森嗨boy", "name": "叶薇", "mobile": "***********", "companyName": "台州市登翔鞋业有限公司"}, "tradeType": "50060", "refundPayment": 0, "idOfStr": "2428690188868540788", "alipayTradeId": "UNCREATED", "receiverInfo": {"toDivisionCode": "441302", "toFullName": "中田 12092", "toArea": "广东省 惠州市 惠城区", "toPost": "516000"}, "buyerLoginId": "惠州中田贸易", "sellerLoginId": "森嗨boy", "buyerUserId": 2207*********, "sellerUserId": **********, "buyerAlipayId": "****************", "sellerAlipayId": "****************", "sumProductPayment": 3885, "stepPayAll": false, "overSeaOrder": false}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false}, "productItems": [{"cargoNumber": "星辰汇&1855", "itemAmount": 555, "name": "法式小香风高跟包头凉鞋女2024新款夏季细跟尖头套脚单鞋批发代发", "price": 18.5, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN013IoCHK1Bs30cMBkpi_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN013IoCHK1Bs30cMBkpi_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2428690188869540788", "quantity": 30, "refund": 0, "skuID": *************, "status": "waitbuyerpay", "subItemID": 2428690188869540788, "type": "common", "unit": "双", "guaranteesTerms": [{"assuranceInfo": "卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。", "assuranceType": "qtwlybt", "qualityAssuranceType": "7天无理由退货", "shortCode": "qtwlybt"}, {"shortCode": "qtwlybt_2"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“10天发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起10天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "stfh", "qualityAssuranceType": "10天发货", "shortCode": "stfh"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "skuInfos": [{"name": "颜色", "value": "黑色"}, {"name": "尺码", "value": "38"}], "entryDiscount": 0, "specId": "24a8dfcd3e63f946b66b8a1713ac8c51", "quantityFactor": 1, "statusStr": "等待买家付款"}, {"cargoNumber": "星辰汇&1855", "itemAmount": 555, "name": "法式小香风高跟包头凉鞋女2024新款夏季细跟尖头套脚单鞋批发代发", "price": 18.5, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN013IoCHK1Bs30cMBkpi_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN013IoCHK1Bs30cMBkpi_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2428690188870540788", "quantity": 30, "refund": 0, "skuID": *************, "status": "waitbuyerpay", "subItemID": 2428690188870540788, "type": "common", "unit": "双", "guaranteesTerms": [{"assuranceInfo": "卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。", "assuranceType": "qtwlybt", "qualityAssuranceType": "7天无理由退货", "shortCode": "qtwlybt"}, {"shortCode": "qtwlybt_2"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“10天发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起10天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "stfh", "qualityAssuranceType": "10天发货", "shortCode": "stfh"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "skuInfos": [{"name": "颜色", "value": "黑色"}, {"name": "尺码", "value": "39"}], "entryDiscount": 0, "specId": "409e988ccd22beae7ed1373859c47d15", "quantityFactor": 1, "statusStr": "等待买家付款"}, {"cargoNumber": "星辰汇&1855", "itemAmount": 277.5, "name": "法式小香风高跟包头凉鞋女2024新款夏季细跟尖头套脚单鞋批发代发", "price": 18.5, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01Pj1IHL1Bs31KrmAG6_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01Pj1IHL1Bs31KrmAG6_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2428690188871540788", "quantity": 15, "refund": 0, "skuID": *************, "status": "waitbuyerpay", "subItemID": 2428690188871540788, "type": "common", "unit": "双", "guaranteesTerms": [{"assuranceInfo": "卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。", "assuranceType": "qtwlybt", "qualityAssuranceType": "7天无理由退货", "shortCode": "qtwlybt"}, {"shortCode": "qtwlybt_2"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“10天发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起10天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "stfh", "qualityAssuranceType": "10天发货", "shortCode": "stfh"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "skuInfos": [{"name": "颜色", "value": "米色"}, {"name": "尺码", "value": "39"}], "entryDiscount": 0, "specId": "46feb3900c84afd9c196e2c0855d93b5", "quantityFactor": 1, "statusStr": "等待买家付款"}, {"cargoNumber": "星辰汇&1855", "itemAmount": 277.5, "name": "法式小香风高跟包头凉鞋女2024新款夏季细跟尖头套脚单鞋批发代发", "price": 18.5, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01Pj1IHL1Bs31KrmAG6_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01Pj1IHL1Bs31KrmAG6_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2428690188872540788", "quantity": 15, "refund": 0, "skuID": *************, "status": "waitbuyerpay", "subItemID": 2428690188872540788, "type": "common", "unit": "双", "guaranteesTerms": [{"assuranceInfo": "卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。", "assuranceType": "qtwlybt", "qualityAssuranceType": "7天无理由退货", "shortCode": "qtwlybt"}, {"shortCode": "qtwlybt_2"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“10天发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起10天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "stfh", "qualityAssuranceType": "10天发货", "shortCode": "stfh"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "skuInfos": [{"name": "颜色", "value": "米色"}, {"name": "尺码", "value": "37"}], "entryDiscount": 0, "specId": "6ae770fdaebcc86ba302e8579fb817e7", "quantityFactor": 1, "statusStr": "等待买家付款"}, {"cargoNumber": "星辰汇&1855", "itemAmount": 555, "name": "法式小香风高跟包头凉鞋女2024新款夏季细跟尖头套脚单鞋批发代发", "price": 18.5, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01Pj1IHL1Bs31KrmAG6_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01Pj1IHL1Bs31KrmAG6_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2428690188873540788", "quantity": 30, "refund": 0, "skuID": *************, "status": "waitbuyerpay", "subItemID": 2428690188873540788, "type": "common", "unit": "双", "guaranteesTerms": [{"assuranceInfo": "卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。", "assuranceType": "qtwlybt", "qualityAssuranceType": "7天无理由退货", "shortCode": "qtwlybt"}, {"shortCode": "qtwlybt_2"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“10天发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起10天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "stfh", "qualityAssuranceType": "10天发货", "shortCode": "stfh"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "skuInfos": [{"name": "颜色", "value": "米色"}, {"name": "尺码", "value": "38"}], "entryDiscount": 0, "specId": "9223aa067d446aae59df1f911e8696a5", "quantityFactor": 1, "statusStr": "等待买家付款"}, {"cargoNumber": "星辰汇&1855", "itemAmount": 555, "name": "法式小香风高跟包头凉鞋女2024新款夏季细跟尖头套脚单鞋批发代发", "price": 18.5, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN013IoCHK1Bs30cMBkpi_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN013IoCHK1Bs30cMBkpi_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2428690188874540788", "quantity": 30, "refund": 0, "skuID": *************, "status": "waitbuyerpay", "subItemID": 2428690188874540788, "type": "common", "unit": "双", "guaranteesTerms": [{"assuranceInfo": "卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。", "assuranceType": "qtwlybt", "qualityAssuranceType": "7天无理由退货", "shortCode": "qtwlybt"}, {"shortCode": "qtwlybt_2"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“10天发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起10天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "stfh", "qualityAssuranceType": "10天发货", "shortCode": "stfh"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "skuInfos": [{"name": "颜色", "value": "黑色"}, {"name": "尺码", "value": "37"}], "entryDiscount": 0, "specId": "988984c04b103c093f25b4ecd71c7220", "quantityFactor": 1, "statusStr": "等待买家付款"}, {"cargoNumber": "星辰汇&1855", "itemAmount": 555, "name": "法式小香风高跟包头凉鞋女2024新款夏季细跟尖头套脚单鞋批发代发", "price": 18.5, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01Pj1IHL1Bs31KrmAG6_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01Pj1IHL1Bs31KrmAG6_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2428690188875540788", "quantity": 30, "refund": 0, "skuID": *************, "status": "waitbuyerpay", "subItemID": 2428690188875540788, "type": "common", "unit": "双", "guaranteesTerms": [{"assuranceInfo": "卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。", "assuranceType": "qtwlybt", "qualityAssuranceType": "7天无理由退货", "shortCode": "qtwlybt"}, {"shortCode": "qtwlybt_2"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“10天发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起10天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "stfh", "qualityAssuranceType": "10天发货", "shortCode": "stfh"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "skuInfos": [{"name": "颜色", "value": "米色"}, {"name": "尺码", "value": "40"}], "entryDiscount": 0, "specId": "f0aa443e3efd9a0bb26e683c6e83f257", "quantityFactor": 1, "statusStr": "等待买家付款"}, {"cargoNumber": "星辰汇&1855", "itemAmount": 555, "name": "法式小香风高跟包头凉鞋女2024新款夏季细跟尖头套脚单鞋批发代发", "price": 18.5, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN013IoCHK1Bs30cMBkpi_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN013IoCHK1Bs30cMBkpi_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2428690188876540788", "quantity": 30, "refund": 0, "skuID": *************, "status": "waitbuyerpay", "subItemID": 2428690188876540788, "type": "common", "unit": "双", "guaranteesTerms": [{"assuranceInfo": "卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。", "assuranceType": "qtwlybt", "qualityAssuranceType": "7天无理由退货", "shortCode": "qtwlybt"}, {"shortCode": "qtwlybt_2"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“10天发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起10天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "stfh", "qualityAssuranceType": "10天发货", "shortCode": "stfh"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "skuInfos": [{"name": "颜色", "value": "黑色"}, {"name": "尺码", "value": "40"}], "entryDiscount": 0, "specId": "f279f76ad1b737009211a09179cb4d19", "quantityFactor": 1, "statusStr": "等待买家付款"}]}], "totalRecord": 4}