{"result": [{"orderRateInfo": {"sellerRateStatus": 5, "buyerRateStatus": 5}, "tradeTerms": [], "nativeLogistics": {"zip": "", "area": "惠城区", "address": "广东省 惠州市 惠城区 汝湖镇", "town": "汝湖镇", "townCode": "*********", "city": "惠州市", "contactPerson": "中田", "areaCode": "441302", "province": "广东省", "privacyProtection": false}, "baseInfo": {"businessType": "cn", "buyerID": "b2b-*************a4d12", "completeTime": "20250104142037000+0800", "createTime": "20250104142001000+0800", "id": 2429708592988540788, "modifyTime": "20250104142036000+0800", "refund": 0, "sellerID": "b2b-2218165581183d1541", "shippingFee": 2, "status": "cancel", "totalAmount": 13.43, "discount": 0, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "***********", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"phone": "", "imInPlatform": "绢彩纺织品厂", "name": "汪市超", "companyName": "蠡县绢彩纺织品厂（个体工商户）"}, "tradeType": "50060", "refundPayment": 0, "idOfStr": "2429708592988540788", "alipayTradeId": "UNCREATED", "receiverInfo": {"toDivisionCode": "441302", "toTownCode": "*********", "toFullName": "中田", "toArea": "广东省 惠州市 惠城区 汝湖镇", "toPost": ""}, "buyerLoginId": "惠州中田贸易", "sellerLoginId": "绢彩纺织品厂", "buyerUserId": *************, "sellerUserId": 2218165581183, "buyerAlipayId": "****************", "sellerAlipayId": "****************", "closeReason": "testcancel", "sumProductPayment": 11.43, "stepPayAll": false, "overSeaOrder": false}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false, "shippingInsurance": "givenByAnXinGou"}, "productItems": [{"itemAmount": 11.43, "name": "4股牛奶棉依棉宝宝中粗线手工编织diy玩偶花朵帽子毯子材料", "price": 1.27, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01ALE4Hw1Bs2yRL0TuZ_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01ALE4Hw1Bs2yRL0TuZ_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2429708592988540788", "quantity": 9, "refund": 0, "skuID": *************, "status": "cancel", "subItemID": 2429708592988540788, "type": "common", "unit": "卷", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "ssbxsfh", "qualityAssuranceType": "48小时发货", "shortCode": "D"}, {"shortCode": "D_2"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}, {"shortCode": "EOT"}], "skuInfos": [{"name": "规格型号", "value": "01姜黄"}], "entryDiscount": 0, "specId": "0969ff3ef11d53d0dc5a6c9fb1ba1e14", "quantityFactor": 1, "statusStr": "交易取消"}]}, {"orderRateInfo": {"sellerRateStatus": 5, "buyerRateStatus": 5}, "tradeTerms": [], "nativeLogistics": {"zip": "516000", "area": "惠城区", "address": "广东省 惠州市 惠城区", "city": "惠州市", "contactPerson": "中田 10002", "areaCode": "441302", "province": "广东省", "privacyProtection": false}, "baseInfo": {"businessType": "cb", "buyerID": "b2b-*************a4d12", "completeTime": "20250104141236000+0800", "createTime": "20250104135211000+0800", "id": 2429989502226540788, "modifyTime": "20250104141236000+0800", "refund": 0, "sellerID": "b2b-221**********0cc91", "shippingFee": 8, "status": "cancel", "totalAmount": 298, "discount": 0, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "***********", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"phone": "86-", "imInPlatform": "酷玩动漫手办", "name": "李婷", "mobile": "***********", "companyName": "深圳市宝安区酷玩动漫玩具商行(个体工商户)"}, "tradeType": "50060", "refundPayment": 0, "idOfStr": "2429989502226540788", "alipayTradeId": "UNCREATED", "receiverInfo": {"toDivisionCode": "441302", "toFullName": "中田 10002", "toArea": "广东省 惠州市 惠城区", "toPost": "516000"}, "buyerLoginId": "惠州中田贸易", "sellerLoginId": "酷玩动漫手办", "buyerUserId": *************, "sellerUserId": 221**********, "buyerAlipayId": "****************", "sellerAlipayId": "****************", "closeReason": "buyerCancel", "sumProductPayment": 290, "stepPayAll": false, "overSeaOrder": false}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false}, "productItems": [{"cargoNumber": "18-12584", "itemAmount": 290, "name": "光环5：守护者 Play Arts改 Pa改 HALO光环5 士官长 可动盒装手办", "price": 145, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01Wn5aJR1Bs3160x2Xe_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01Wn5aJR1Bs3160x2Xe_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2429989502226540788", "quantity": 2, "refund": 0, "skuID": *************, "status": "cancel", "subItemID": 2429989502226540788, "type": "common", "unit": "个", "guaranteesTerms": [{"assuranceInfo": "卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。", "assuranceType": "qtwlybt", "qualityAssuranceType": "7天无理由退货", "shortCode": "qtwlybt"}, {"shortCode": "qtwlybt_2"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "ssbxsfh", "qualityAssuranceType": "48小时发货", "shortCode": "D"}, {"shortCode": "D_2"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}, {"shortCode": "EOT"}], "productCargoNumber": "改士官长", "skuInfos": [{"name": "颜色", "value": "改士官长"}], "entryDiscount": 0, "specId": "f0890319eabd320c1e20ae971f2504fa", "quantityFactor": 1, "statusStr": "交易取消"}]}, {"orderRateInfo": {"sellerRateStatus": 5, "buyerRateStatus": 5}, "tradeTerms": [], "nativeLogistics": {"zip": "516000", "area": "惠城区", "address": "广东省 惠州市 惠城区", "city": "惠州市", "contactPerson": "中田 13411", "areaCode": "441302", "province": "广东省", "privacyProtection": false}, "baseInfo": {"businessType": "cb", "buyerID": "b2b-*************a4d12", "createTime": "20250104130205000+0800", "id": 2430287185067540788, "modifyTime": "20250104130206000+0800", "refund": 0, "sellerID": "b2b-*************7072b", "shippingFee": 10, "status": "waitbuyerpay", "totalAmount": 302, "discount": 0, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "***********", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"phone": "86-159-15407954", "imInPlatform": "深圳市怡霖霏科技有限公司", "name": "王冰冰", "mobile": "***********", "companyName": "深圳市怡霖霏科技有限公司"}, "tradeType": "50060", "refundPayment": 0, "idOfStr": "2430287185067540788", "alipayTradeId": "UNCREATED", "receiverInfo": {"toDivisionCode": "441302", "toFullName": "中田 13411", "toArea": "广东省 惠州市 惠城区", "toPost": "516000"}, "buyerLoginId": "惠州中田贸易", "sellerLoginId": "深圳市怡霖霏科技有限公司", "buyerUserId": *************, "sellerUserId": *************, "buyerAlipayId": "****************", "sellerAlipayId": "****************", "sumProductPayment": 292, "stepPayAll": false, "overSeaOrder": false}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false}, "productItems": [{"cargoNumber": "214C", "itemAmount": 292, "name": "跨境正品榨汁杯便携式电动充电迷你榨汁机随行果汁机厂家直营批发", "price": 73, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN015U4VJ31Bs2yhqI1aD_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN015U4VJ31Bs2yhqI1aD_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2430287185067540788", "quantity": 4, "refund": 0, "skuID": *************, "status": "waitbuyerpay", "subItemID": 2430287185067540788, "type": "common", "unit": "个", "guaranteesTerms": [{"assuranceInfo": "卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。", "assuranceType": "qtwlybt", "qualityAssuranceType": "7天无理由退货", "shortCode": "qtwlybt"}, {"shortCode": "qtwlybt_2"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "ssbxsfh", "qualityAssuranceType": "48小时发货", "shortCode": "D"}, {"shortCode": "D_2"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}, {"shortCode": "EOT"}], "productCargoNumber": "214", "skuInfos": [{"name": "颜色", "value": "214C粉色（TYPE-C充电款）"}], "entryDiscount": 0, "specId": "3892792d8d8977865389865d91665894", "quantityFactor": 1, "statusStr": "等待买家付款"}]}, {"orderRateInfo": {"sellerRateStatus": 5, "buyerRateStatus": 5}, "tradeTerms": [], "nativeLogistics": {"zip": "516000", "area": "惠城区", "address": "广东省 惠州市 惠城区", "city": "惠州市", "contactPerson": "中田 13411", "areaCode": "441302", "province": "广东省", "privacyProtection": false}, "baseInfo": {"businessType": "cb", "buyerID": "b2b-*************a4d12", "createTime": "20250104125611000+0800", "id": 2429929202510540788, "modifyTime": "20250104125612000+0800", "refund": 0, "sellerID": "b2b-*************7072b", "shippingFee": 6, "status": "waitbuyerpay", "totalAmount": 84, "discount": 0, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "***********", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"phone": "86-159-15407954", "imInPlatform": "深圳市怡霖霏科技有限公司", "name": "王冰冰", "mobile": "***********", "companyName": "深圳市怡霖霏科技有限公司"}, "tradeType": "50060", "refundPayment": 0, "idOfStr": "2429929202510540788", "alipayTradeId": "UNCREATED", "receiverInfo": {"toDivisionCode": "441302", "toFullName": "中田 13411", "toArea": "广东省 惠州市 惠城区", "toPost": "516000"}, "buyerLoginId": "惠州中田贸易", "sellerLoginId": "深圳市怡霖霏科技有限公司", "buyerUserId": *************, "sellerUserId": *************, "buyerAlipayId": "****************", "sellerAlipayId": "****************", "sumProductPayment": 78, "stepPayAll": false, "overSeaOrder": false}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false}, "productItems": [{"cargoNumber": "214B", "itemAmount": 78, "name": "跨境正品榨汁杯便携式电动充电迷你榨汁机随行果汁机厂家直营批发", "price": 78, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01OA1RIi1Bs2yI0AInw_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01OA1RIi1Bs2yI0AInw_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2429929202510540788", "quantity": 1, "refund": 0, "skuID": *************, "status": "waitbuyerpay", "subItemID": 2429929202510540788, "type": "common", "unit": "个", "guaranteesTerms": [{"assuranceInfo": "卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。", "assuranceType": "qtwlybt", "qualityAssuranceType": "7天无理由退货", "shortCode": "qtwlybt"}, {"shortCode": "qtwlybt_2"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "ssbxsfh", "qualityAssuranceType": "48小时发货", "shortCode": "D"}, {"shortCode": "D_2"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}, {"shortCode": "EOT"}], "productCargoNumber": "214", "skuInfos": [{"name": "颜色", "value": "214B白色（磁吸无线充电款）"}], "entryDiscount": 0, "specId": "c9cd40a018b4fa70d92e53d9379bdb9e", "quantityFactor": 1, "statusStr": "等待买家付款"}]}, {"orderRateInfo": {"sellerRateStatus": 5, "buyerRateStatus": 5}, "tradeTerms": [], "nativeLogistics": {"zip": "516001", "area": "惠城区", "address": "广东省 惠州市 惠城区", "city": "惠州市", "contactPerson": "中田 仓库", "areaCode": "441302", "province": "广东省", "privacyProtection": false}, "baseInfo": {"businessType": "cb", "buyerID": "b2b-*************a4d12", "buyerSubID": 2216824975661, "createTime": "20250104120451000+0800", "id": 2430225733206540788, "modifyTime": "20250104120452000+0800", "refund": 0, "sellerID": "b2b-*************bb2b1", "shippingFee": 5, "status": "waitbuyerpay", "totalAmount": 235.8, "discount": 0, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "***********", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"phone": "", "imInPlatform": "昌衢饰品厂", "name": "周恒宇", "companyName": "义乌市昌衢电子商务商行"}, "tradeType": "50060", "refundPayment": 0, "idOfStr": "2430225733206540788", "alipayTradeId": "UNCREATED", "receiverInfo": {"toDivisionCode": "441302", "toFullName": "中田 仓库", "toArea": "广东省 惠州市 惠城区", "toPost": "516001"}, "buyerLoginId": "惠州中田贸易", "sellerLoginId": "昌衢饰品厂", "buyerUserId": *************, "sellerUserId": *************, "buyerAlipayId": "****************", "sellerAlipayId": "****************", "sumProductPayment": 246.4, "stepPayAll": false, "overSeaOrder": false}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false, "shippingInsurance": "givenByAnXinGou"}, "productItems": [{"itemAmount": 44, "name": "NFC Bracelet Bible Verse 跨境热卖TikTok 每日圣经NFC手环", "price": 8.8, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01GGbHaP1Bs30ooNJUg_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01GGbHaP1Bs30ooNJUg_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2430225733207540788", "quantity": 5, "refund": 0, "skuID": *************, "status": "waitbuyerpay", "subItemID": 2430225733207540788, "type": "common", "unit": "条", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，自愿选择向买家提供“交期保障”服务。卖家向买家承诺，买家通过在线交易方式成功下单付款后，卖家将在双方确定的交期内（不可抗力因素除外），发送买家所购实物商品的服务，否则向买家进行赔付", "assuranceType": "jqbz", "qualityAssuranceType": "交期保障", "value": "7", "shortCode": "J"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "productCargoNumber": "NFC", "skuInfos": [{"name": "颜色", "value": "蓝色-十字架布标"}], "entryDiscount": 0, "specId": "41dd4ec88fc33c8f3ef00927b0d6db59", "quantityFactor": 1, "statusStr": "等待买家付款"}, {"itemAmount": 26.4, "name": "NFC Bracelet Bible Verse 跨境热卖TikTok 每日圣经NFC手环", "price": 8.8, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01OMvm991Bs30qxqFxE_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01OMvm991Bs30qxqFxE_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2430225733208540788", "quantity": 3, "refund": 0, "skuID": *************, "status": "waitbuyerpay", "subItemID": 2430225733208540788, "type": "common", "unit": "条", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，自愿选择向买家提供“交期保障”服务。卖家向买家承诺，买家通过在线交易方式成功下单付款后，卖家将在双方确定的交期内（不可抗力因素除外），发送买家所购实物商品的服务，否则向买家进行赔付", "assuranceType": "jqbz", "qualityAssuranceType": "交期保障", "value": "7", "shortCode": "J"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "productCargoNumber": "NFC", "skuInfos": [{"name": "颜色", "value": "海军蓝和平鸽"}], "entryDiscount": 0, "specId": "63389b6ceb5fcc38386461d181974ab6", "quantityFactor": 1, "statusStr": "等待买家付款"}, {"itemAmount": 8.8, "name": "NFC Bracelet Bible Verse 跨境热卖TikTok 每日圣经NFC手环", "price": 8.8, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01sfbWas1Bs30lbrKxe_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01sfbWas1Bs30lbrKxe_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2430225733209540788", "quantity": 1, "refund": 0, "skuID": *************, "status": "waitbuyerpay", "subItemID": 2430225733209540788, "type": "common", "unit": "条", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，自愿选择向买家提供“交期保障”服务。卖家向买家承诺，买家通过在线交易方式成功下单付款后，卖家将在双方确定的交期内（不可抗力因素除外），发送买家所购实物商品的服务，否则向买家进行赔付", "assuranceType": "jqbz", "qualityAssuranceType": "交期保障", "value": "7", "shortCode": "J"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "productCargoNumber": "NFC", "skuInfos": [{"name": "颜色", "value": "玫粉和平鸽"}], "entryDiscount": 0, "specId": "a7aae3ba40b8023e956efe9243911882", "quantityFactor": 1, "statusStr": "等待买家付款"}, {"itemAmount": 88, "name": "NFC Bracelet Bible Verse 跨境热卖TikTok 每日圣经NFC手环", "price": 8.8, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01XNbd4z1Bs30iPklaj_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01XNbd4z1Bs30iPklaj_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2430225733210540788", "quantity": 10, "refund": 0, "skuID": *************, "status": "waitbuyerpay", "subItemID": 2430225733210540788, "type": "common", "unit": "条", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，自愿选择向买家提供“交期保障”服务。卖家向买家承诺，买家通过在线交易方式成功下单付款后，卖家将在双方确定的交期内（不可抗力因素除外），发送买家所购实物商品的服务，否则向买家进行赔付", "assuranceType": "jqbz", "qualityAssuranceType": "交期保障", "value": "7", "shortCode": "J"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "productCargoNumber": "NFC", "skuInfos": [{"name": "颜色", "value": "黑色和平鸽"}], "entryDiscount": 0, "specId": "b3e0b9ac743fef404ebbf838d487df2b", "quantityFactor": 1, "statusStr": "等待买家付款"}, {"itemAmount": 70.4, "name": "NFC Bracelet Bible Verse 跨境热卖TikTok 每日圣经NFC手环", "price": 8.8, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01uJs2Ft1Bs30o75kYJ_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01uJs2Ft1Bs30o75kYJ_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2430225733211540788", "quantity": 8, "refund": 0, "skuID": *************, "status": "waitbuyerpay", "subItemID": 2430225733211540788, "type": "common", "unit": "条", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，自愿选择向买家提供“交期保障”服务。卖家向买家承诺，买家通过在线交易方式成功下单付款后，卖家将在双方确定的交期内（不可抗力因素除外），发送买家所购实物商品的服务，否则向买家进行赔付", "assuranceType": "jqbz", "qualityAssuranceType": "交期保障", "value": "7", "shortCode": "J"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "productCargoNumber": "NFC", "skuInfos": [{"name": "颜色", "value": "米色-和平鸽"}], "entryDiscount": 0, "specId": "bf2641fd27c389409e8a22ff2817fe40", "quantityFactor": 1, "statusStr": "等待买家付款"}, {"itemAmount": 8.8, "name": "NFC Bracelet Bible Verse 跨境热卖TikTok 每日圣经NFC手环", "price": 8.8, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01rwiMoz1Bs30ps14tr_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01rwiMoz1Bs30ps14tr_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2430225733212540788", "quantity": 1, "refund": 0, "skuID": *************, "status": "waitbuyerpay", "subItemID": 2430225733212540788, "type": "common", "unit": "条", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，自愿选择向买家提供“交期保障”服务。卖家向买家承诺，买家通过在线交易方式成功下单付款后，卖家将在双方确定的交期内（不可抗力因素除外），发送买家所购实物商品的服务，否则向买家进行赔付", "assuranceType": "jqbz", "qualityAssuranceType": "交期保障", "value": "7", "shortCode": "J"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "productCargoNumber": "NFC", "skuInfos": [{"name": "颜色", "value": "草绿和平鸽"}], "entryDiscount": 0, "specId": "e5f0023a2ad10d3e9cf8708b8365f64e", "quantityFactor": 1, "statusStr": "等待买家付款"}]}, {"orderRateInfo": {"sellerRateStatus": 5, "buyerRateStatus": 5}, "tradeTerms": [{"phase": 51315636738540788, "payWayDesc": "支付平台", "expressPay": false, "payTime": "20250104121455000+0800", "payStatusDesc": "已付款", "payWay": "13", "cardPay": false, "payStatus": "2", "phasAmount": 4951.8}], "nativeLogistics": {"zip": "516000", "area": "惠城区", "address": "广东省 惠州市 惠城区", "city": "惠州市", "contactPerson": "中田 10808", "areaCode": "441302", "province": "广东省", "privacyProtection": false}, "baseInfo": {"allDeliveredTime": "20250104134334000+0800", "businessType": "cb", "buyerID": "b2b-*************a4d12", "createTime": "20250104111500000+0800", "id": 2429522328214540788, "modifyTime": "20250104134334000+0800", "payTime": "20250104121455000+0800", "refund": 0, "sellerID": "nbshengao", "shippingFee": 0, "status": "waitbuyerreceive", "totalAmount": 4951.8, "discount": -17440, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "***********", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"phone": "86-0574-62467229", "email": "<EMAIL>", "imInPlatform": "圣奥净水", "name": "陈建新", "mobile": "***********", "companyName": "余姚市圣奥净水电器有限公司"}, "tradeType": "50060", "refundPayment": 0, "idOfStr": "2429522328214540788", "alipayTradeId": "11120600025010459027518548807", "receiverInfo": {"toDivisionCode": "441302", "toFullName": "中田 10808", "toArea": "广东省 惠州市 惠城区", "toPost": "516000"}, "buyerLoginId": "惠州中田贸易", "sellerLoginId": "圣奥净水", "buyerUserId": *************, "sellerUserId": **********, "buyerAlipayId": "****************", "sellerAlipayId": "****************", "sumProductPayment": 5396, "stepPayAll": false, "overSeaOrder": false}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false, "shippingInsurance": "givenByMerchant"}, "productItems": [{"itemAmount": 4951.8, "name": "花洒过滤器 沐浴净水器淋浴过滤器", "price": 71, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01WVZ1rw1Bs317DMPXD_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01WVZ1rw1Bs317DMPXD_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2429522328214540788", "quantity": 76, "refund": 0, "skuID": *************, "status": "waitbuyerreceive", "subItemID": 2429522328214540788, "type": "common", "unit": "台", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，自愿选择向买家提供“交期保障”服务。卖家向买家承诺，买家通过在线交易方式成功下单付款后，卖家将在双方确定的交期内（不可抗力因素除外），发送买家所购实物商品的服务，否则向买家进行赔付", "assuranceType": "jqbz", "qualityAssuranceType": "交期保障", "value": "30", "shortCode": "J"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}, {"shortCode": "EOT"}], "skuInfos": [{"name": "容量", "value": "15层淋浴套装"}], "entryDiscount": -17440, "specId": "51e216d1c0e7040f14d95885bbedca21", "quantityFactor": 1, "statusStr": "等待买家收货"}]}, {"orderRateInfo": {"sellerRateStatus": 5, "buyerRateStatus": 5}, "tradeTerms": [], "nativeLogistics": {"zip": "516001", "area": "惠城区", "address": "广东省 惠州市 惠城区", "city": "惠州市", "contactPerson": "中田 仓库", "areaCode": "441302", "province": "广东省", "privacyProtection": false}, "baseInfo": {"businessType": "cb", "buyerID": "b2b-*************a4d12", "buyerSubID": 2216824975661, "completeTime": "20250104120353000+0800", "createTime": "20250104111410000+0800", "id": 2429818682931540788, "modifyTime": "20250104120353000+0800", "refund": 0, "sellerID": "b2b-*************1e235", "shippingFee": 4, "status": "cancel", "totalAmount": 23.4, "discount": 0, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "***********", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"phone": "", "imInPlatform": "逸晟气动", "name": "包蒙蒙", "mobile": "***********", "companyName": "乐清市逸晟气动元件厂"}, "tradeType": "50060", "refundPayment": 0, "idOfStr": "2429818682931540788", "alipayTradeId": "UNCREATED", "receiverInfo": {"toDivisionCode": "441302", "toFullName": "中田 仓库", "toArea": "广东省 惠州市 惠城区", "toPost": "516001"}, "buyerLoginId": "惠州中田贸易", "sellerLoginId": "逸晟气动", "buyerUserId": *************, "sellerUserId": *************, "buyerAlipayId": "****************", "sellerAlipayId": "****************", "closeReason": "noPurchasingNeeds", "sumProductPayment": 35, "stepPayAll": false, "overSeaOrder": false}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false, "shippingInsurance": "givenByAnXinGou"}, "productItems": [{"itemAmount": 35, "name": "气动电磁阀4v210-08换向阀24V气动控制阀220v二位五通电子阀4V310", "price": 35, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01aWacvp1Bs31ghOIc8_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01aWacvp1Bs31ghOIc8_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2429818682931540788", "quantity": 1, "refund": 0, "skuID": *************, "status": "cancel", "subItemID": 2429818682931540788, "type": "common", "unit": "个", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "ssbxsfh", "qualityAssuranceType": "48小时发货", "shortCode": "D"}, {"shortCode": "D_2"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}, {"shortCode": "EOT"}], "skuInfos": [{"name": "规格型号", "value": "4V230-08~DC24V【精品】"}], "entryDiscount": 0, "specId": "b38c3e5e6bc6f921fd2f73b4133668ab", "quantityFactor": 1, "statusStr": "交易取消"}]}, {"orderRateInfo": {"sellerRateStatus": 5, "buyerRateStatus": 5}, "tradeTerms": [], "nativeLogistics": {"zip": "516000", "area": "惠城区", "address": "广东省 惠州市 惠城区", "city": "惠州市", "contactPerson": "中田 10808", "areaCode": "441302", "province": "广东省", "privacyProtection": false}, "baseInfo": {"businessType": "cb", "buyerID": "b2b-*************a4d12", "completeTime": "20250104111109000+0800", "createTime": "20250104103645000+0800", "id": 2430132997495540788, "modifyTime": "20250104111108000+0800", "refund": 0, "sellerID": "nbshengao", "shippingFee": 0, "status": "cancel", "totalAmount": 4936.2, "discount": -17440, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "***********", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"phone": "86-0574-62467229", "email": "<EMAIL>", "imInPlatform": "圣奥净水", "name": "陈建新", "mobile": "***********", "companyName": "余姚市圣奥净水电器有限公司"}, "tradeType": "50060", "refundPayment": 0, "idOfStr": "2430132997495540788", "alipayTradeId": "UNCREATED", "receiverInfo": {"toDivisionCode": "441302", "toFullName": "中田 10808", "toArea": "广东省 惠州市 惠城区", "toPost": "516000"}, "buyerLoginId": "惠州中田贸易", "sellerLoginId": "圣奥净水", "buyerUserId": *************, "sellerUserId": **********, "buyerAlipayId": "****************", "sellerAlipayId": "****************", "closeReason": "buyerCancel", "sumProductPayment": 5396, "stepPayAll": false, "overSeaOrder": false}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false, "shippingInsurance": "givenByMerchant"}, "productItems": [{"itemAmount": 4951.8, "name": "花洒过滤器 沐浴净水器淋浴过滤器", "price": 71, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01WVZ1rw1Bs317DMPXD_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01WVZ1rw1Bs317DMPXD_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2430132997495540788", "quantity": 76, "refund": 0, "skuID": *************, "status": "cancel", "subItemID": 2430132997495540788, "type": "common", "unit": "台", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，自愿选择向买家提供“交期保障”服务。卖家向买家承诺，买家通过在线交易方式成功下单付款后，卖家将在双方确定的交期内（不可抗力因素除外），发送买家所购实物商品的服务，否则向买家进行赔付", "assuranceType": "jqbz", "qualityAssuranceType": "交期保障", "value": "30", "shortCode": "J"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}, {"shortCode": "EOT"}], "skuInfos": [{"name": "容量", "value": "15层淋浴套装"}], "entryDiscount": -17440, "specId": "51e216d1c0e7040f14d95885bbedca21", "quantityFactor": 1, "statusStr": "交易取消"}]}, {"orderRateInfo": {"sellerRateStatus": 5, "buyerRateStatus": 5}, "tradeTerms": [{"phase": 51339397674540788, "payWayDesc": "支付平台", "expressPay": false, "payTime": "20250104114123000+0800", "payStatusDesc": "已付款", "payWay": "13", "cardPay": false, "payStatus": "2", "phasAmount": 366}], "nativeLogistics": {"zip": "516000", "area": "惠城区", "address": "广东省 惠州市 惠城区", "city": "惠州市", "contactPerson": "中田 12092", "areaCode": "441302", "province": "广东省", "privacyProtection": false}, "baseInfo": {"allDeliveredTime": "20250104151858000+0800", "businessType": "cb", "buyerID": "b2b-*************a4d12", "createTime": "20250104102739000+0800", "id": 2429285703364540788, "modifyTime": "20250104151858000+0800", "payTime": "20250104114123000+0800", "refund": 0, "sellerID": "b2b-*************1e235", "shippingFee": 16, "status": "waitbuyerreceive", "totalAmount": 366, "discount": 0, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "***********", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"phone": "", "imInPlatform": "逸晟气动", "name": "包蒙蒙", "mobile": "***********", "companyName": "乐清市逸晟气动元件厂"}, "tradeType": "50060", "refundPayment": 0, "idOfStr": "2429285703364540788", "alipayTradeId": "11110600025010451611375548807", "receiverInfo": {"toDivisionCode": "441302", "toFullName": "中田 12092", "toArea": "广东省 惠州市 惠城区", "toPost": "516000"}, "buyerLoginId": "惠州中田贸易", "sellerLoginId": "逸晟气动", "buyerUserId": *************, "sellerUserId": *************, "buyerAlipayId": "****************", "sellerAlipayId": "****************", "sumProductPayment": 350, "stepPayAll": false, "overSeaOrder": false}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false}, "productItems": [{"itemAmount": 350, "name": "气动电磁阀4v210-08换向阀24V气动控制阀220v二位五通电子阀4V310", "price": 35, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01aWacvp1Bs31ghOIc8_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01aWacvp1Bs31ghOIc8_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2429285703364540788", "quantity": 10, "refund": 0, "skuID": *************, "status": "waitbuyerreceive", "subItemID": 2429285703364540788, "type": "common", "unit": "个", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "ssbxsfh", "qualityAssuranceType": "48小时发货", "shortCode": "D"}, {"shortCode": "D_2"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}, {"shortCode": "EOT"}], "skuInfos": [{"name": "规格型号", "value": "4V230-08~DC24V【精品】"}], "entryDiscount": 0, "specId": "b38c3e5e6bc6f921fd2f73b4133668ab", "quantityFactor": 1, "statusStr": "等待买家收货"}]}, {"orderRateInfo": {"sellerRateStatus": 5, "buyerRateStatus": 5}, "tradeTerms": [{"phase": 51284930251540788, "payWayDesc": "支付平台", "expressPay": false, "payTime": "20250104114123000+0800", "payStatusDesc": "已付款", "payWay": "13", "cardPay": false, "payStatus": "2", "phasAmount": 37.5}], "nativeLogistics": {"zip": "516000", "area": "惠城区", "address": "广东省 惠州市 惠城区", "city": "惠州市", "contactPerson": "中田 12092", "areaCode": "441302", "province": "广东省", "privacyProtection": false}, "baseInfo": {"allDeliveredTime": "20250104142032000+0800", "businessType": "cb", "buyerID": "b2b-*************a4d12", "createTime": "20250104102322000+0800", "id": 2429765438546540788, "modifyTime": "20250104142032000+0800", "payTime": "20250104114123000+0800", "refund": 0, "sellerID": "b2b-*************dec3e", "shippingFee": 3.5, "status": "waitbuyerreceive", "totalAmount": 37.5, "discount": 0, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "***********", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"phone": "86-", "imInPlatform": "哈美电器厂", "name": "黄灼芬", "companyName": "揭阳市榕城区哈美电器厂"}, "tradeType": "50060", "refundPayment": 0, "idOfStr": "2429765438546540788", "alipayTradeId": "11110600025010420370931548807", "receiverInfo": {"toDivisionCode": "441302", "toFullName": "中田 12092", "toArea": "广东省 惠州市 惠城区", "toPost": "516000"}, "buyerLoginId": "惠州中田贸易", "sellerLoginId": "哈美电器厂", "buyerUserId": *************, "sellerUserId": *************, "buyerAlipayId": "****************", "sellerAlipayId": "****************", "sumProductPayment": 34, "stepPayAll": false, "overSeaOrder": false}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false}, "productItems": [{"itemAmount": 34, "name": "动漫纸雕灯烙印勇士二次元周边手办礼品灯节日动漫相框灯", "price": 34, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01bNGUlO1Bs2yj45UOD_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01bNGUlO1Bs2yj45UOD_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2429765438546540788", "quantity": 1, "refund": 0, "skuID": *************, "status": "waitbuyerreceive", "subItemID": 2429765438546540788, "type": "common", "unit": "个", "guaranteesTerms": [{"assuranceInfo": "卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。", "assuranceType": "qtwlybt", "qualityAssuranceType": "7天无理由退货", "shortCode": "qtwlybt"}, {"shortCode": "qtwlybt_2"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "ssbxsfh", "qualityAssuranceType": "48小时发货", "shortCode": "D"}, {"shortCode": "D_2"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}, {"shortCode": "EOT"}], "skuInfos": [{"name": "灯光颜色", "value": "如图展示08"}, {"name": "光源功率", "value": "RGB遥控款"}], "entryDiscount": 0, "specId": "0a270075ed7868aba0cc07842dac4ce4", "quantityFactor": 1, "statusStr": "等待买家收货"}]}, {"orderRateInfo": {"sellerRateStatus": 5, "buyerRateStatus": 5}, "tradeTerms": [{"phase": 51284498354540788, "payWayDesc": "支付平台", "expressPay": false, "payTime": "20250104114123000+0800", "payStatusDesc": "已付款", "payWay": "13", "cardPay": false, "payStatus": "2", "phasAmount": 46.4}], "nativeLogistics": {"zip": "516000", "area": "惠城区", "address": "广东省 惠州市 惠城区", "city": "惠州市", "contactPerson": "中田 12092", "areaCode": "441302", "province": "广东省", "privacyProtection": false}, "baseInfo": {"allDeliveredTime": "20250104172549000+0800", "businessType": "cb", "buyerID": "b2b-*************a4d12", "createTime": "20250104102210000+0800", "id": 2429763674613540788, "modifyTime": "20250104172549000+0800", "payTime": "20250104114123000+0800", "refund": 0, "sellerID": "b2b-*************6238c", "shippingFee": 5, "status": "waitbuyerreceive", "totalAmount": 46.4, "discount": 0, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "***********", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"phone": "", "imInPlatform": "深圳市梦尘科技有限公司", "name": "张伟中", "mobile": "***********", "companyName": "深圳市梦尘科技有限公司"}, "tradeType": "50060", "refundPayment": 0, "idOfStr": "2429763674613540788", "alipayTradeId": "11110600025010451071740548807", "receiverInfo": {"toDivisionCode": "441302", "toFullName": "中田 12092", "toArea": "广东省 惠州市 惠城区", "toPost": "516000"}, "buyerLoginId": "惠州中田贸易", "sellerLoginId": "深圳市梦尘科技有限公司", "buyerUserId": *************, "sellerUserId": *************, "buyerAlipayId": "****************", "sellerAlipayId": "****************", "sumProductPayment": 45, "stepPayAll": false, "overSeaOrder": false}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false}, "productItems": [{"cargoNumber": "单色白光", "itemAmount": 41.4, "name": "纸雕灯动漫周边摆件二次元机箱热门爆款景品圣诞生日礼品氛围灯", "price": 45, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01YpGRlz1Bs31Iu7QpY_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01YpGRlz1Bs31Iu7QpY_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2429763674613540788", "quantity": 1, "refund": 0, "skuID": *************, "status": "waitbuyerreceive", "subItemID": 2429763674613540788, "type": "common", "unit": "个", "guaranteesTerms": [{"assuranceInfo": "卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。", "assuranceType": "qtwlybt", "qualityAssuranceType": "7天无理由退货", "shortCode": "qtwlybt"}, {"shortCode": "qtwlybt_2"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "ssbxsfh", "qualityAssuranceType": "48小时发货", "shortCode": "D"}, {"shortCode": "D_2"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}, {"shortCode": "EOT"}], "productCargoNumber": "纸雕灯", "skuInfos": [{"name": "灯光颜色", "value": "B3-03"}, {"name": "光源功率", "value": "RGB遥控款 纸雕灯"}], "entryDiscount": 0, "specId": "eb60e5cb80873f053a930cee6d70bb05", "quantityFactor": 1, "statusStr": "等待买家收货"}]}, {"orderRateInfo": {"sellerRateStatus": 5, "buyerRateStatus": 5}, "tradeTerms": [{"phase": 51329389622540788, "payWayDesc": "支付平台", "expressPay": false, "payTime": "20250104114124000+0800", "payStatusDesc": "已付款", "payWay": "13", "cardPay": false, "payStatus": "2", "phasAmount": 71}], "nativeLogistics": {"zip": "516000", "area": "惠城区", "address": "广东省 惠州市 惠城区", "city": "惠州市", "contactPerson": "中田 12429", "areaCode": "441302", "province": "广东省", "privacyProtection": false}, "baseInfo": {"allDeliveredTime": "20250104161832000+0800", "businessType": "cb", "buyerID": "b2b-*************a4d12", "createTime": "20250104000143000+0800", "id": 2429539826741540788, "modifyTime": "20250104161832000+0800", "payTime": "20250104114123000+0800", "refund": 0, "sellerID": "b2b-*************8a169", "shippingFee": 5, "status": "waitbuyerreceive", "totalAmount": 71, "discount": 0, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "***********", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"phone": "", "imInPlatform": "昌鑫箱包厂", "name": "刘水平", "companyName": "广州市花都区狮岭镇昌鑫皮具商行（个体工商户）"}, "tradeType": "50060", "refundPayment": 0, "idOfStr": "2429539826741540788", "alipayTradeId": "11110600025010428017474548807", "receiverInfo": {"toDivisionCode": "441302", "toFullName": "中田 12429", "toArea": "广东省 惠州市 惠城区", "toPost": "516000"}, "buyerLoginId": "惠州中田贸易", "sellerLoginId": "昌鑫箱包厂", "buyerUserId": *************, "sellerUserId": *************, "buyerAlipayId": "****************", "sellerAlipayId": "****************", "sumProductPayment": 66, "stepPayAll": false, "overSeaOrder": false}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false, "shippingInsurance": "givenByMerchant"}, "productItems": [{"itemAmount": 66, "name": "小众设计新款金属绳扣编织托特包单肩包手提斜挎复古女包百搭简约", "price": 66, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01wOcUyg1Bs30hIIAE4_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01wOcUyg1Bs30hIIAE4_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2429539826741540788", "quantity": 1, "refund": 0, "skuID": *************, "status": "waitbuyerreceive", "subItemID": 2429539826741540788, "type": "common", "unit": "件", "guaranteesTerms": [{"assuranceInfo": "卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。", "assuranceType": "qtwlybt", "qualityAssuranceType": "7天无理由退货", "shortCode": "qtwlybt"}, {"shortCode": "qtwlybt_2"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "ssbxsfh", "qualityAssuranceType": "48小时发货", "shortCode": "D"}, {"shortCode": "D_2"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}, {"shortCode": "EOT"}], "skuInfos": [{"name": "颜色", "value": "黑色"}], "entryDiscount": 0, "specId": "f561c4f7cdb23de81fc2303ebf1e8f55", "quantityFactor": 1, "statusStr": "等待买家收货"}]}, {"orderRateInfo": {"sellerRateStatus": 5, "buyerRateStatus": 5}, "tradeTerms": [{"phase": 51294612091540788, "payWayDesc": "支付平台", "expressPay": false, "payTime": "20250103184004000+0800", "payStatusDesc": "已付款", "payWay": "13", "cardPay": false, "payStatus": "2", "phasAmount": 71}], "nativeLogistics": {"zip": "516000", "area": "惠城区", "address": "广东省 惠州市 惠城区", "city": "惠州市", "contactPerson": "中田 12429", "areaCode": "441302", "province": "广东省", "privacyProtection": false}, "baseInfo": {"allDeliveredTime": "20250103204640000+0800", "businessType": "cb", "buyerID": "b2b-*************a4d12", "createTime": "20250103174553000+0800", "id": 2429490541339540788, "modifyTime": "20250103204640000+0800", "payTime": "20250103184004000+0800", "refund": 0, "sellerID": "b2b-*************8a169", "shippingFee": 5, "status": "waitbuyerreceive", "totalAmount": 47, "discount": 0, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "***********", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"phone": "", "imInPlatform": "昌鑫箱包厂", "name": "刘水平", "companyName": "广州市花都区狮岭镇昌鑫皮具商行（个体工商户）"}, "tradeType": "50060", "refundPayment": 0, "idOfStr": "2429490541339540788", "alipayTradeId": "11180600025010389021334548807", "receiverInfo": {"toDivisionCode": "441302", "toFullName": "中田 12429", "toArea": "广东省 惠州市 惠城区", "toPost": "516000"}, "buyerLoginId": "惠州中田贸易", "sellerLoginId": "昌鑫箱包厂", "buyerUserId": *************, "sellerUserId": *************, "buyerAlipayId": "****************", "sellerAlipayId": "****************", "sumProductPayment": 66, "stepPayAll": false, "overSeaOrder": false}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false, "shippingInsurance": "givenByMerchant"}, "productItems": [{"itemAmount": 66, "name": "小众设计新款金属绳扣编织托特包单肩包手提斜挎复古女包百搭简约", "price": 66, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01ma12GM1Bs30EFrfLJ_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01ma12GM1Bs30EFrfLJ_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2429490541339540788", "quantity": 1, "refund": 0, "skuID": *************, "status": "waitbuyerreceive", "subItemID": 2429490541339540788, "type": "common", "unit": "件", "guaranteesTerms": [{"assuranceInfo": "卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。", "assuranceType": "qtwlybt", "qualityAssuranceType": "7天无理由退货", "shortCode": "qtwlybt"}, {"shortCode": "qtwlybt_2"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "ssbxsfh", "qualityAssuranceType": "48小时发货", "shortCode": "D"}, {"shortCode": "D_2"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}, {"shortCode": "EOT"}], "skuInfos": [{"name": "颜色", "value": "酒红色"}], "entryDiscount": 0, "specId": "8e33ced938f52664c2730391dcea6714", "quantityFactor": 1, "statusStr": "等待买家收货"}]}, {"orderRateInfo": {"sellerRateStatus": 5, "buyerRateStatus": 5}, "tradeTerms": [{"phase": 51293820548540788, "payWayDesc": "支付平台", "expressPay": false, "payTime": "20250103184730000+0800", "payStatusDesc": "已付款", "payWay": "13", "cardPay": false, "payStatus": "2", "phasAmount": 4550}], "nativeLogistics": {"zip": "516000", "area": "惠城区", "address": "广东省 惠州市 惠城区", "city": "惠州市", "contactPerson": "中田 12727", "areaCode": "441302", "province": "广东省", "privacyProtection": false}, "baseInfo": {"allDeliveredTime": "20250103184831000+0800", "businessType": "cb", "buyerID": "b2b-*************a4d12", "createTime": "20250103172734000+0800", "id": 2428814496774540788, "modifyTime": "20250103184831000+0800", "payTime": "20250103184730000+0800", "refund": 0, "sellerID": "b2b-22116959774142de30", "shippingFee": 0, "status": "waitbuyerreceive", "totalAmount": 4550, "discount": -15000, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "***********", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"phone": "86-1526-8675859", "email": "<EMAIL>", "imInPlatform": "永康项阳", "name": "曾伟军", "mobile": "***********", "companyName": "永康市项阳工贸有限公司"}, "tradeType": "50060", "refundPayment": 0, "idOfStr": "2428814496774540788", "alipayTradeId": "11180600025010390821195548807", "receiverInfo": {"toDivisionCode": "441302", "toFullName": "中田 12727", "toArea": "广东省 惠州市 惠城区", "toPost": "516000"}, "buyerLoginId": "惠州中田贸易", "sellerLoginId": "永康项阳", "buyerUserId": *************, "sellerUserId": 2211695977414, "buyerAlipayId": "****************", "sellerAlipayId": "****************", "sumProductPayment": 4700, "stepPayAll": false, "overSeaOrder": false}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false, "shippingInsurance": "givenByMerchant"}, "productItems": [{"cargoNumber": "xy-5", "itemAmount": 4550, "name": "富氢水杯氢氧分离富氢杯高浓度超饱和水素杯家用SPE膜富氢发生器", "price": 47, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01eZGJR21Bs312bHLMa_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01eZGJR21Bs312bHLMa_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2428814496774540788", "quantity": 100, "refund": 0, "skuID": *************, "status": "waitbuyerreceive", "subItemID": 2428814496774540788, "type": "common", "unit": "个", "guaranteesTerms": [{"assuranceInfo": "卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。", "assuranceType": "qtwlybt", "qualityAssuranceType": "7天无理由退货", "shortCode": "qtwlybt"}, {"shortCode": "qtwlybt_2"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "ssbxsfh", "qualityAssuranceType": "48小时发货", "shortCode": "D"}, {"shortCode": "D_2"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}, {"shortCode": "EOT"}], "productCargoNumber": "xy-5", "skuInfos": [{"name": "容量", "value": "光底座金属色配件"}, {"name": "颜色", "value": "金属色底座"}], "entryDiscount": -15000, "specId": "87e41fb405a15662cf86f70e8640c2e5", "quantityFactor": 1, "statusStr": "等待买家收货"}]}, {"orderRateInfo": {"sellerRateStatus": 5, "buyerRateStatus": 5}, "tradeTerms": [], "nativeLogistics": {"zip": "516000", "area": "惠城区", "address": "广东省 惠州市 惠城区", "city": "惠州市", "contactPerson": "中田 12727", "areaCode": "441302", "province": "广东省", "privacyProtection": false}, "baseInfo": {"businessType": "cb", "buyerID": "b2b-*************a4d12", "completeTime": "20250103172720000+0800", "createTime": "20250103172612000+0800", "id": 2429116358480540788, "modifyTime": "20250103172719000+0800", "refund": 0, "sellerID": "b2b-22116959774142de30", "shippingFee": 0, "status": "cancel", "totalAmount": 6600, "discount": 0, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "***********", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"phone": "86-1526-8675859", "email": "<EMAIL>", "imInPlatform": "永康项阳", "name": "曾伟军", "mobile": "***********", "companyName": "永康市项阳工贸有限公司"}, "tradeType": "50060", "refundPayment": 0, "idOfStr": "2429116358480540788", "alipayTradeId": "UNCREATED", "receiverInfo": {"toDivisionCode": "441302", "toFullName": "中田 12727", "toArea": "广东省 惠州市 惠城区", "toPost": "516000"}, "buyerLoginId": "惠州中田贸易", "sellerLoginId": "永康项阳", "buyerUserId": *************, "sellerUserId": 2211695977414, "buyerAlipayId": "****************", "sellerAlipayId": "****************", "closeReason": "buyerCancel", "sumProductPayment": 6600, "stepPayAll": false, "overSeaOrder": false}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false, "shippingInsurance": "givenByMerchant"}, "productItems": [{"itemAmount": 6600, "name": "富氢水杯氢氧分离富氢杯高浓度超饱和水素杯家用SPE膜富氢发生器", "price": 66, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01TlhzE71Bs31ewDnEm_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01TlhzE71Bs31ewDnEm_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2429116358480540788", "quantity": 100, "refund": 0, "skuID": *************, "status": "cancel", "subItemID": 2429116358480540788, "type": "common", "unit": "个", "guaranteesTerms": [{"assuranceInfo": "卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。", "assuranceType": "qtwlybt", "qualityAssuranceType": "7天无理由退货", "shortCode": "qtwlybt"}, {"shortCode": "qtwlybt_2"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "ssbxsfh", "qualityAssuranceType": "48小时发货", "shortCode": "D"}, {"shortCode": "D_2"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}, {"shortCode": "EOT"}], "productCargoNumber": "xy-5", "skuInfos": [{"name": "容量", "value": "详情私信"}, {"name": "颜色", "value": "金属色底座"}], "entryDiscount": 0, "specId": "5eab156e2fd59a1c4d3f817c92ebc782", "quantityFactor": 1, "statusStr": "交易取消"}]}, {"orderRateInfo": {"sellerRateStatus": 5, "buyerRateStatus": 5}, "tradeTerms": [{"phase": 51260990855540788, "payWayDesc": "支付平台", "expressPay": false, "payTime": "20250103184731000+0800", "payStatusDesc": "已付款", "payWay": "13", "cardPay": false, "payStatus": "2", "phasAmount": 56100}], "nativeLogistics": {"zip": "516000", "area": "惠城区", "address": "广东省 惠州市 惠城区", "city": "惠州市", "contactPerson": "中田 12727", "areaCode": "441302", "province": "广东省", "privacyProtection": false}, "baseInfo": {"allDeliveredTime": "20250104182855000+0800", "businessType": "cb", "buyerID": "b2b-*************a4d12", "createTime": "20250103163350000+0800", "id": 2428761684401540788, "modifyTime": "20250104182855000+0800", "payTime": "20250103184731000+0800", "refund": 0, "sellerID": "b2b-22116959774142de30", "shippingFee": 0, "status": "waitbuyerreceive", "totalAmount": 56100, "discount": -102000, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "***********", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"phone": "86-1526-8675859", "email": "<EMAIL>", "imInPlatform": "永康项阳", "name": "曾伟军", "mobile": "***********", "companyName": "永康市项阳工贸有限公司"}, "tradeType": "50060", "refundPayment": 0, "idOfStr": "2428761684401540788", "alipayTradeId": "11180600025010315099194548807", "receiverInfo": {"toDivisionCode": "441302", "toFullName": "中田 12727", "toArea": "广东省 惠州市 惠城区", "toPost": "516000"}, "buyerLoginId": "惠州中田贸易", "sellerLoginId": "永康项阳", "buyerUserId": *************, "sellerUserId": 2211695977414, "buyerAlipayId": "****************", "sellerAlipayId": "****************", "sumProductPayment": 57120, "stepPayAll": false, "overSeaOrder": false}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false, "shippingInsurance": "givenByMerchant"}, "productItems": [{"cargoNumber": "xy-5", "itemAmount": 56100, "name": "富氢水杯制氢水素水氢氧分离会销礼品杯子电解弱碱玻璃杯养生水杯", "price": 56, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01ihNTx01Bs30PiYuds_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01ihNTx01Bs30PiYuds_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2428761684401540788", "quantity": 1020, "refund": 0, "skuID": *************, "status": "waitbuyerreceive", "subItemID": 2428761684401540788, "type": "common", "unit": "个", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，自愿选择向买家提供“交期保障”服务。卖家向买家承诺，买家通过在线交易方式成功下单付款后，卖家将在双方确定的交期内（不可抗力因素除外），发送买家所购实物商品的服务，否则向买家进行赔付", "assuranceType": "jqbz", "qualityAssuranceType": "交期保障", "value": "15", "shortCode": "J"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}, {"shortCode": "EOT"}], "productCargoNumber": "xy-5", "skuInfos": [{"name": "容量", "value": "420ml"}, {"name": "颜色", "value": "灰色金属色"}], "entryDiscount": -102000, "specId": "84a5fa55a1c845c18b361d48a0dd9270", "quantityFactor": 1, "statusStr": "等待买家收货"}]}, {"orderRateInfo": {"sellerRateStatus": 5, "buyerRateStatus": 5}, "tradeTerms": [{"phase": 51289212486540788, "payWayDesc": "支付平台", "expressPay": false, "payTime": "20250103184731000+0800", "payStatusDesc": "已付款", "payWay": "13", "cardPay": false, "payStatus": "2", "phasAmount": 96800}], "nativeLogistics": {"zip": "516000", "area": "惠城区", "address": "广东省 惠州市 惠城区", "city": "惠州市", "contactPerson": "中田 12727", "areaCode": "441302", "province": "广东省", "privacyProtection": false}, "baseInfo": {"businessType": "cn", "buyerID": "b2b-*************a4d12", "createTime": "20250103162840000+0800", "id": 2429408065370540788, "modifyTime": "20250103184731000+0800", "payTime": "20250103184731000+0800", "refund": 0, "sellerID": "b2b-*************8ab1b", "shippingFee": 0, "status": "waitsellersend", "totalAmount": 96800, "discount": -160000, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "***********", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"phone": "86-", "imInPlatform": "宇恒接地床单厂", "name": "王胜锋", "mobile": "***********", "companyName": "北京宇恒联创机械设备有限公司"}, "tradeType": "50060", "refundPayment": 0, "idOfStr": "2429408065370540788", "alipayTradeId": "11180600025010389273988548807", "receiverInfo": {"toDivisionCode": "441302", "toFullName": "中田 12727", "toArea": "广东省 惠州市 惠城区", "toPost": "516000"}, "buyerLoginId": "惠州中田贸易", "sellerLoginId": "宇恒接地床单厂", "buyerUserId": *************, "sellerUserId": *************, "buyerAlipayId": "****************", "sellerAlipayId": "****************", "sumProductPayment": 98400, "stepPayAll": false, "overSeaOrder": false}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false}, "productItems": [{"itemAmount": 24200, "name": "接地床单 带接地线 银纤维", "price": 123, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01rBg3aC1Bs2yEsdNwX_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01rBg3aC1Bs2yEsdNwX_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2429408065371540788", "quantity": 200, "refund": 0, "skuID": *************, "status": "waitsellersend", "subItemID": 2429408065371540788, "type": "common", "unit": "件", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“45天发货”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起45天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "sswtfh", "qualityAssuranceType": "45天发货", "shortCode": "sswtfh"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "productCargoNumber": "接地床单001", "skuInfos": [{"name": "花型", "value": "澳标"}, {"name": "尺寸规格", "value": "90x230cm"}], "entryDiscount": -40000, "specId": "1a8885024337bb27cb24205d42e31502", "quantityFactor": 1, "statusStr": "等待卖家发货"}, {"itemAmount": 36300, "name": "接地床单 带接地线 银纤维", "price": 123, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01rBg3aC1Bs2yEsdNwX_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01rBg3aC1Bs2yEsdNwX_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2429408065372540788", "quantity": 300, "refund": 0, "skuID": *************, "status": "waitsellersend", "subItemID": 2429408065372540788, "type": "common", "unit": "件", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“45天发货”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起45天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "sswtfh", "qualityAssuranceType": "45天发货", "shortCode": "sswtfh"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "productCargoNumber": "接地床单001", "skuInfos": [{"name": "花型", "value": "英标"}, {"name": "尺寸规格", "value": "90x230cm"}], "entryDiscount": -60000, "specId": "226b415e985ddfacfc72b50c13d6807d", "quantityFactor": 1, "statusStr": "等待卖家发货"}, {"itemAmount": 36300, "name": "接地床单 带接地线 银纤维", "price": 123, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01rBg3aC1Bs2yEsdNwX_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01rBg3aC1Bs2yEsdNwX_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2429408065373540788", "quantity": 300, "refund": 0, "skuID": *************, "status": "waitsellersend", "subItemID": 2429408065373540788, "type": "common", "unit": "件", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“45天发货”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起45天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "sswtfh", "qualityAssuranceType": "45天发货", "shortCode": "sswtfh"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "productCargoNumber": "接地床单001", "skuInfos": [{"name": "花型", "value": "美标"}, {"name": "尺寸规格", "value": "90x230cm"}], "entryDiscount": -60000, "specId": "76071b7b9037c299b1dc9aefdc4d76bd", "quantityFactor": 1, "statusStr": "等待卖家发货"}]}, {"orderRateInfo": {"sellerRateStatus": 5, "buyerRateStatus": 5}, "tradeTerms": [{"phase": 51218007927540788, "payWayDesc": "支付平台", "expressPay": false, "payTime": "20250103184732000+0800", "payStatusDesc": "已付款", "payWay": "13", "cardPay": false, "payStatus": "2", "phasAmount": 40200}], "nativeLogistics": {"zip": "516000", "area": "惠城区", "address": "广东省 惠州市 惠城区", "city": "惠州市", "contactPerson": "中田 12727", "areaCode": "441302", "province": "广东省", "privacyProtection": false}, "baseInfo": {"businessType": "cn", "buyerID": "b2b-*************a4d12", "createTime": "20250103162231000+0800", "id": 2428566315892540788, "modifyTime": "20250103184731000+0800", "payTime": "20250103184731000+0800", "refund": 0, "sellerID": "b2b-*************8ab1b", "shippingFee": 0, "status": "waitsellersend", "totalAmount": 40200, "discount": -120000, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "***********", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"phone": "86-", "imInPlatform": "宇恒接地床单厂", "name": "王胜锋", "mobile": "***********", "companyName": "北京宇恒联创机械设备有限公司"}, "tradeType": "50060", "refundPayment": 0, "idOfStr": "2428566315892540788", "alipayTradeId": "11180600025010393548918548807", "receiverInfo": {"toDivisionCode": "441302", "toFullName": "中田 12727", "toArea": "广东省 惠州市 惠城区", "toPost": "516000"}, "buyerLoginId": "惠州中田贸易", "sellerLoginId": "宇恒接地床单厂", "buyerUserId": *************, "sellerUserId": *************, "buyerAlipayId": "****************", "sellerAlipayId": "****************", "sumProductPayment": 41400, "stepPayAll": false, "overSeaOrder": false}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false}, "productItems": [{"itemAmount": 20100, "name": "接地床单 带接地线 银纤维", "price": 69, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01rBg3aC1Bs2yEsdNwX_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01rBg3aC1Bs2yEsdNwX_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2428566315893540788", "quantity": 300, "refund": 0, "skuID": *************, "status": "waitsellersend", "subItemID": 2428566315893540788, "type": "common", "unit": "件", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“45天发货”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起45天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "sswtfh", "qualityAssuranceType": "45天发货", "shortCode": "sswtfh"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "productCargoNumber": "接地床单001", "skuInfos": [{"name": "花型", "value": "英标"}, {"name": "尺寸规格", "value": "68x132cm"}], "entryDiscount": -60000, "specId": "52975fc88e81713729ce8be69b8275bb", "quantityFactor": 1, "statusStr": "等待卖家发货"}, {"itemAmount": 20100, "name": "接地床单 带接地线 银纤维", "price": 69, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01rBg3aC1Bs2yEsdNwX_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01rBg3aC1Bs2yEsdNwX_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2428566315894540788", "quantity": 300, "refund": 0, "skuID": *************, "status": "waitsellersend", "subItemID": 2428566315894540788, "type": "common", "unit": "件", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“45天发货”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起45天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "sswtfh", "qualityAssuranceType": "45天发货", "shortCode": "sswtfh"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "productCargoNumber": "接地床单001", "skuInfos": [{"name": "花型", "value": "美标"}, {"name": "尺寸规格", "value": "68x132cm"}], "entryDiscount": -60000, "specId": "61fbb225eb5b8e5b6541a3159302db7b", "quantityFactor": 1, "statusStr": "等待卖家发货"}]}, {"orderRateInfo": {"sellerRateStatus": 5, "buyerRateStatus": 5}, "tradeTerms": [{"phase": 51313585898540788, "payWayDesc": "支付平台", "expressPay": false, "payTime": "20250103184141000+0800", "payStatusDesc": "已付款", "payWay": "13", "cardPay": false, "payStatus": "2", "phasAmount": 8900}], "nativeLogistics": {"zip": "516000", "area": "惠城区", "address": "广东省 惠州市 惠城区", "city": "惠州市", "contactPerson": "中田 12155", "areaCode": "441302", "province": "广东省", "privacyProtection": false}, "baseInfo": {"businessType": "cb", "buyerID": "b2b-*************a4d12", "createTime": "20250103161403000+0800", "id": 2429387797661540788, "modifyTime": "20250103184141000+0800", "payTime": "20250103184141000+0800", "refund": 0, "sellerID": "b2b-*************f9608", "shippingFee": 0, "status": "waitsellersend", "totalAmount": 8900, "discount": -130000, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "***********", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"phone": "", "imInPlatform": "酥昂电子商务", "name": "赵方方", "companyName": "义乌市酥昂电子商务有限公司"}, "tradeType": "50060", "refundPayment": 0, "idOfStr": "2429387797661540788", "alipayTradeId": "11180600025010313119679548807", "receiverInfo": {"toDivisionCode": "441302", "toFullName": "中田 12155", "toArea": "广东省 惠州市 惠城区", "toPost": "516000"}, "buyerLoginId": "惠州中田贸易", "sellerLoginId": "酥昂电子商务", "buyerUserId": *************, "sellerUserId": *************, "buyerAlipayId": "****************", "sellerAlipayId": "****************", "sumProductPayment": 10200, "stepPayAll": false, "overSeaOrder": false}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false}, "productItems": [{"itemAmount": 2966.66, "name": "跨境Curl Define Styling Brush弹跳卷曲定义造型刷梳子", "price": 8.5, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01McA9c41Bs30NCVdVF_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01McA9c41Bs30NCVdVF_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2429387797662540788", "quantity": 400, "refund": 0, "skuID": *************, "status": "waitsellersend", "subItemID": 2429387797662540788, "type": "common", "unit": "个", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“5天发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起5天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "wtfh", "qualityAssuranceType": "5天发货", "shortCode": "wtfh"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "skuInfos": [{"name": "颜色", "value": "升级版紫色"}], "entryDiscount": -43334, "specId": "2c8a6d3e78e86cffdf080a0c77e50c45", "quantityFactor": 1, "statusStr": "等待卖家发货"}, {"itemAmount": 741.66, "name": "跨境Curl Define Styling Brush弹跳卷曲定义造型刷梳子", "price": 8.5, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01QalAoS1Bs30lRR7j4_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01QalAoS1Bs30lRR7j4_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2429387797663540788", "quantity": 100, "refund": 0, "skuID": *************, "status": "waitsellersend", "subItemID": 2429387797663540788, "type": "common", "unit": "个", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“5天发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起5天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "wtfh", "qualityAssuranceType": "5天发货", "shortCode": "wtfh"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "skuInfos": [{"name": "颜色", "value": "升级版米色"}], "entryDiscount": -10834, "specId": "5634d76fcac2d0035cf2fb25c3d1eb47", "quantityFactor": 1, "statusStr": "等待卖家发货"}, {"itemAmount": 1854.16, "name": "跨境Curl Define Styling Brush弹跳卷曲定义造型刷梳子", "price": 8.5, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN0129nztK1Bs30YKNoBT_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN0129nztK1Bs30YKNoBT_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2429387797664540788", "quantity": 250, "refund": 0, "skuID": *************, "status": "waitsellersend", "subItemID": 2429387797664540788, "type": "common", "unit": "个", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“5天发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起5天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "wtfh", "qualityAssuranceType": "5天发货", "shortCode": "wtfh"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "skuInfos": [{"name": "颜色", "value": "升级版粉色"}], "entryDiscount": -27084, "specId": "7f3f723459e920350603f94718f2aa9a", "quantityFactor": 1, "statusStr": "等待卖家发货"}, {"itemAmount": 1483.33, "name": "跨境Curl Define Styling Brush弹跳卷曲定义造型刷梳子", "price": 8.5, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01ehuqDC1Bs30Nujzw7_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01ehuqDC1Bs30Nujzw7_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2429387797665540788", "quantity": 200, "refund": 0, "skuID": *************, "status": "waitsellersend", "subItemID": 2429387797665540788, "type": "common", "unit": "个", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“5天发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起5天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "wtfh", "qualityAssuranceType": "5天发货", "shortCode": "wtfh"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "skuInfos": [{"name": "颜色", "value": "升级版黑色"}], "entryDiscount": -21667, "specId": "ded8dff11c65fe061c6437fcecf50307", "quantityFactor": 1, "statusStr": "等待卖家发货"}, {"itemAmount": 1854.19, "name": "跨境Curl Define Styling Brush弹跳卷曲定义造型刷梳子", "price": 8.5, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01NG1M2x1Bs30k0ktPE_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01NG1M2x1Bs30k0ktPE_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2429387797666540788", "quantity": 250, "refund": 0, "skuID": *************, "status": "waitsellersend", "subItemID": 2429387797666540788, "type": "common", "unit": "个", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“5天发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起5天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "wtfh", "qualityAssuranceType": "5天发货", "shortCode": "wtfh"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}], "skuInfos": [{"name": "颜色", "value": "升级版浅绿色"}], "entryDiscount": -27081, "specId": "f22d3102a2976992f12b0616060bab01", "quantityFactor": 1, "statusStr": "等待卖家发货"}]}, {"orderRateInfo": {"sellerRateStatus": 5, "buyerRateStatus": 5}, "tradeTerms": [{"phase": 51258470860540788, "payWayDesc": "支付平台", "expressPay": false, "payTime": "20250103184036000+0800", "payStatusDesc": "已付款", "payWay": "13", "cardPay": false, "payStatus": "2", "phasAmount": 61}], "nativeLogistics": {"zip": "516000", "area": "惠城区", "address": "广东省 惠州市 惠城区", "city": "惠州市", "contactPerson": "中田 11975", "areaCode": "441302", "province": "广东省", "privacyProtection": false}, "baseInfo": {"allDeliveredTime": "20250104215655000+0800", "businessType": "cn", "buyerID": "b2b-*************a4d12", "createTime": "20250103155957000+0800", "id": 2429016782975540788, "modifyTime": "20250104215655000+0800", "payTime": "20250103184035000+0800", "refund": 0, "sellerID": "b2b-221**********d423a", "shippingFee": 6, "status": "waitbuyerreceive", "totalAmount": 61, "discount": -1150, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "***********", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"phone": "86-", "imInPlatform": "润颜堂跨境化妆品", "name": "王满香", "companyName": "深圳市润颜堂跨境供应链有限公司"}, "tradeType": "50060", "refundPayment": 0, "idOfStr": "2429016782975540788", "alipayTradeId": "11180600025010392360728548807", "receiverInfo": {"toDivisionCode": "441302", "toFullName": "中田 11975", "toArea": "广东省 惠州市 惠城区", "toPost": "516000"}, "buyerLoginId": "惠州中田贸易", "sellerLoginId": "润颜堂跨境化妆品", "buyerUserId": *************, "sellerUserId": 221**********, "buyerAlipayId": "****************", "sellerAlipayId": "****************", "sumProductPayment": 70, "stepPayAll": false, "overSeaOrder": false}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false}, "productItems": [{"cargoNumber": "ZZ090904-aobenz祛斑精华液40ml-QW", "itemAmount": 55, "name": "亚马逊跨境热销 维生素精华液VC血清精华面部去斑霜抗衰despeckle", "price": 7, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01TeLkbA1Bs2zbxOghq_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01TeLkbA1Bs2zbxOghq_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2429016782975540788", "quantity": 10, "refund": 0, "skuID": *************, "status": "waitbuyerreceive", "subItemID": 2429016782975540788, "type": "common", "unit": "瓶", "guaranteesTerms": [{"assuranceInfo": "卖家在承诺买家保障服务的基础上，自愿选择向买家提供“交期保障”服务。卖家向买家承诺，买家通过在线交易方式成功下单付款后，卖家将在双方确定的交期内（不可抗力因素除外），发送买家所购实物商品的服务，否则向买家进行赔付", "assuranceType": "jqbz", "qualityAssuranceType": "交期保障", "value": "7", "shortCode": "J"}, {"assuranceInfo": "“在符合一定的退款申请原因、时效和退款金额范围内，可享受0秒退款服务。", "assuranceType": "jstk_p", "qualityAssuranceType": "极速退款", "shortCode": "jstk_p"}, {"shortCode": "EOT"}], "productCargoNumber": "ZZ072001", "skuInfos": [{"name": "产品规格", "value": "A标-40ml"}], "entryDiscount": -1150, "specId": "92c78f7c3e62508ea4a674a197c93d28", "quantityFactor": 1, "statusStr": "等待买家收货"}]}], "totalRecord": 3530}