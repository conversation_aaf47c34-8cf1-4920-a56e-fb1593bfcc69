{"result": {"baseInfo": {"allDeliveredTime": "20250625174800000+0800", "payTime": "20250625171936000+0800", "discount": -86000, "alipayTradeId": "2025062522001846301423602512", "sumProductPayment": 37660, "flowTemplateCode": "assureTrade", "sellerOrder": false, "buyerLoginId": "惠州中田贸易", "modifyTime": "20250707085831000+0800", "id": 2626652931211540788, "buyerContact": {"phone": "86-752-2313067", "imInPlatform": "惠州中田贸易", "name": "汤维政", "mobile": "13725034447", "companyName": "惠州市中田贸易有限公司"}, "completeTime": "*****************+0800", "sellerLoginId": "宇恒接地床单厂", "buyerID": "b2b-2207416548807a4d12", "closeOperateType": "", "totalAmount": 36800, "sellerID": "b2b-22069085846688ab1b", "shippingFee": 0, "refund": 0, "status": "success", "refundPayment": 0, "sellerContact": {"phone": "86-", "imInPlatform": "宇恒接地床单厂", "name": "王胜锋", "mobile": "13280026963", "companyName": "北京宇恒联创机械设备有限公司", "shopName": "北京宇恒联创机械设备有限公司"}, "couponFee": 0, "receiverInfo": {"toFullName": "中田 12727", "toDivisionCode": "441302", "toMobile": "18124000751", "toPost": "516000", "toArea": "广东省 惠州市 惠城区 江北 金泽物流园二期一号楼四楼-12727"}, "tradeType": "50060", "receivingTime": "20250705174804276+0800", "idOfStr": "2626652931211540788", "stepPayAll": false, "newStepOrderList": [{"gmtStart": "*****************+0800", "gmtPay": "*****************+0800", "gmtEnd": "*****************+0800", "stepNo": 1, "lastStep": true, "stepName": "全款交易", "activeStatus": 1, "payStatus": 6, "logisticsStatus": 3, "payFee": 36800, "paidFee": 36800, "adjustFee": -860, "discountFee": 0, "postFee": 0, "paidPostFee": 0}], "createTime": "*****************+0800", "businessType": "cb", "overSeaOrder": false, "tradeTypeDesc": "担保交易", "payChannelList": ["跨境宝2.0"], "tradeTypeCode": "assureTrade", "payTimeout": 432000, "payTimeoutType": 0, "payChannelCodeList": ["kjpayV2"], "outOrderId": "12727"}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false, "shippingInsurance": "givenByAnXinGou"}, "tradeTerms": [{"phase": *****************, "payWayDesc": "支付平台", "expressPay": false, "payTime": "*****************+0800", "payStatusDesc": "已付款", "payWay": "13", "cardPay": false, "payStatus": "6", "phasAmount": 36800}], "productItems": [{"itemAmount": 390.86, "name": "配件链接 沟通再拍", "price": 20, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01pGgsFE1Bs327gkTys_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01pGgsFE1Bs327gkTys_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2626652931212540788", "quantity": 20, "refund": 0, "skuID": *************, "status": "success", "subItemID": 2626652931212540788, "type": "common", "unit": "件", "guaranteesTerms": [{"assuranceInfo": "“满足相应条件时，用户在退货寄出后，享受极速退款到账。", "assuranceType": "lsjst_s", "qualityAssuranceType": "极速退款"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“15天发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起15天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "swtfh", "qualityAssuranceType": "15天发货"}], "productCargoNumber": "接地床单001", "skuInfos": [{"name": "花型", "value": "插座测试仪"}, {"name": "尺寸规格", "value": "澳"}], "entryDiscount": -914, "specId": "a4ed7a44b0a427902ee4478835512cb5", "quantityFactor": 1, "statusStr": "交易成功", "logisticsStatus": 3, "gmtCreate": "*****************+0800", "gmtModified": "*****************+0800", "gmtCompleted": "*****************+0800", "subItemIDString": "2626652931212540788"}, {"itemAmount": 1035.79, "name": "配件链接 沟通再拍", "price": 53, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01pGgsFE1Bs327gkTys_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01pGgsFE1Bs327gkTys_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2626652931213540788", "quantity": 20, "refund": 0, "skuID": *************, "status": "success", "subItemID": 2626652931213540788, "type": "common", "unit": "件", "guaranteesTerms": [{"assuranceInfo": "“满足相应条件时，用户在退货寄出后，享受极速退款到账。", "assuranceType": "lsjst_s", "qualityAssuranceType": "极速退款"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“15天发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起15天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "swtfh", "qualityAssuranceType": "15天发货"}], "productCargoNumber": "接地床单001", "skuInfos": [{"name": "花型", "value": "连续测试仪带插头"}, {"name": "尺寸规格", "value": "澳"}], "entryDiscount": -2421, "specId": "b022bcd25a9f62be23f44a31f109d99d", "quantityFactor": 1, "statusStr": "交易成功", "logisticsStatus": 3, "gmtCreate": "*****************+0800", "gmtModified": "*****************+0800", "gmtCompleted": "*****************+0800", "subItemIDString": "2626652931213540788"}, {"itemAmount": 5130.11, "name": "接地床笠 各种尺寸 接地床笠", "price": 210, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01xzdPX21Bs31ylFTcy_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01xzdPX21Bs31ylFTcy_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2626652931214540788", "quantity": 25, "refund": 0, "skuID": *************, "status": "success", "subItemID": 2626652931214540788, "type": "common", "unit": "件", "guaranteesTerms": [{"assuranceInfo": "卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。", "assuranceType": "qtwlybt", "qualityAssuranceType": "7天无理由退货"}, {"assuranceInfo": "“满足相应条件时，用户在退货寄出后，享受极速退款到账。", "assuranceType": "lsjst_s", "qualityAssuranceType": "极速退款"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“45天发货”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起45天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "sswtfh", "qualityAssuranceType": "45天发货"}], "productCargoNumber": "003", "skuInfos": [{"name": "花型", "value": "白色"}, {"name": "尺寸规格", "value": "152x203x30cm 澳标"}], "entryDiscount": -11989, "specId": "027bb7193d7f42b5553e06aeae32aec4", "quantityFactor": 1, "statusStr": "交易成功", "logisticsStatus": 3, "gmtCreate": "*****************+0800", "gmtModified": "*****************+0800", "gmtCompleted": "*****************+0800", "subItemIDString": "2626652931214540788", "sharePostage": 98.99}, {"itemAmount": 1221.45, "name": "接地床笠 各种尺寸 接地床笠", "price": 250, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01xzdPX21Bs31ylFTcy_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01xzdPX21Bs31ylFTcy_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2626652931215540788", "quantity": 5, "refund": 0, "skuID": *************, "status": "success", "subItemID": 2626652931215540788, "type": "common", "unit": "件", "guaranteesTerms": [{"assuranceInfo": "卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。", "assuranceType": "qtwlybt", "qualityAssuranceType": "7天无理由退货"}, {"assuranceInfo": "“满足相应条件时，用户在退货寄出后，享受极速退款到账。", "assuranceType": "lsjst_s", "qualityAssuranceType": "极速退款"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“45天发货”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起45天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "sswtfh", "qualityAssuranceType": "45天发货"}], "productCargoNumber": "003", "skuInfos": [{"name": "花型", "value": "白色"}, {"name": "尺寸规格", "value": "182x203x30cm 澳标"}], "entryDiscount": -2855, "specId": "35259e127f01e334eeed5aed7e02b9bc", "quantityFactor": 1, "statusStr": "交易成功", "logisticsStatus": 3, "gmtCreate": "*****************+0800", "gmtModified": "*****************+0800", "gmtCompleted": "*****************+0800", "subItemIDString": "2626652931215540788", "sharePostage": 24.75}, {"itemAmount": 4104.08, "name": "接地床笠 各种尺寸 接地床笠", "price": 210, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01xzdPX21Bs31ylFTcy_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01xzdPX21Bs31ylFTcy_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2626652931216540788", "quantity": 20, "refund": 0, "skuID": *************, "status": "success", "subItemID": 2626652931216540788, "type": "common", "unit": "件", "guaranteesTerms": [{"assuranceInfo": "卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。", "assuranceType": "qtwlybt", "qualityAssuranceType": "7天无理由退货"}, {"assuranceInfo": "“满足相应条件时，用户在退货寄出后，享受极速退款到账。", "assuranceType": "lsjst_s", "qualityAssuranceType": "极速退款"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“45天发货”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起45天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "sswtfh", "qualityAssuranceType": "45天发货"}], "productCargoNumber": "003", "skuInfos": [{"name": "花型", "value": "灰色"}, {"name": "尺寸规格", "value": "152x203x30cm 澳标"}], "entryDiscount": -9592, "specId": "7ef8b21913cdfef4b95ef83ee418e65d", "quantityFactor": 1, "statusStr": "交易成功", "logisticsStatus": 3, "gmtCreate": "*****************+0800", "gmtModified": "*****************+0800", "gmtCompleted": "*****************+0800", "subItemIDString": "2626652931216540788", "sharePostage": 79.19}, {"itemAmount": 2442.91, "name": "接地床笠 各种尺寸 接地床笠", "price": 250, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01xzdPX21Bs31ylFTcy_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01xzdPX21Bs31ylFTcy_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2626652931217540788", "quantity": 10, "refund": 0, "skuID": *************, "status": "success", "subItemID": 2626652931217540788, "type": "common", "unit": "件", "guaranteesTerms": [{"assuranceInfo": "卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。", "assuranceType": "qtwlybt", "qualityAssuranceType": "7天无理由退货"}, {"assuranceInfo": "“满足相应条件时，用户在退货寄出后，享受极速退款到账。", "assuranceType": "lsjst_s", "qualityAssuranceType": "极速退款"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“45天发货”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起45天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "sswtfh", "qualityAssuranceType": "45天发货"}], "productCargoNumber": "003", "skuInfos": [{"name": "花型", "value": "灰色"}, {"name": "尺寸规格", "value": "182x203x30cm 澳标"}], "entryDiscount": -5709, "specId": "99189bcdce3c889be614dfc7b6cb6692", "quantityFactor": 1, "statusStr": "交易成功", "logisticsStatus": 3, "gmtCreate": "*****************+0800", "gmtModified": "*****************+0800", "gmtCompleted": "*****************+0800", "subItemIDString": "2626652931217540788", "sharePostage": 49.49}, {"itemAmount": 635.15, "name": "接地床笠 各种尺寸 接地床笠", "price": 65, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01xzdPX21Bs31ylFTcy_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01xzdPX21Bs31ylFTcy_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2626652931218540788", "quantity": 10, "refund": 0, "skuID": *************, "status": "success", "subItemID": 2626652931218540788, "type": "common", "unit": "件", "guaranteesTerms": [{"assuranceInfo": "卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。", "assuranceType": "qtwlybt", "qualityAssuranceType": "7天无理由退货"}, {"assuranceInfo": "“满足相应条件时，用户在退货寄出后，享受极速退款到账。", "assuranceType": "lsjst_s", "qualityAssuranceType": "极速退款"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“45天发货”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起45天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "sswtfh", "qualityAssuranceType": "45天发货"}], "productCargoNumber": "003", "skuInfos": [{"name": "花型", "value": "灰色"}, {"name": "尺寸规格", "value": "51x76cm澳标"}], "entryDiscount": -1485, "specId": "e500b1ec36b4c2cba511345a05b96bd7", "quantityFactor": 1, "statusStr": "交易成功", "logisticsStatus": 3, "gmtCreate": "*****************+0800", "gmtModified": "*****************+0800", "gmtCompleted": "*****************+0800", "subItemIDString": "2626652931218540788", "sharePostage": 9.9}, {"itemAmount": 3283.27, "name": "接地床单", "price": 48, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01xzdPX21Bs31ylFTcy_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01xzdPX21Bs31ylFTcy_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2626652931219540788", "quantity": 70, "refund": 0, "skuID": *************, "status": "success", "subItemID": 2626652931219540788, "type": "common", "unit": "件", "guaranteesTerms": [{"assuranceInfo": "“满足相应条件时，用户在退货寄出后，享受极速退款到账。", "assuranceType": "lsjst_s", "qualityAssuranceType": "极速退款"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“10天发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起10天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "stfh", "qualityAssuranceType": "10天发货"}], "productCargoNumber": "接地床单001", "skuInfos": [{"name": "花型", "value": "澳标"}, {"name": "尺寸规格", "value": "33x60cm"}], "entryDiscount": -7673, "specId": "00bc9d240ba66bd3cbf6a0ba7f7e2b53", "quantityFactor": 1, "statusStr": "交易成功", "logisticsStatus": 3, "gmtCreate": "*****************+0800", "gmtModified": "*****************+0800", "gmtCompleted": "*****************+0800", "gmtPayExpireTime": "2025-06-25 17:34:28", "subItemIDString": "2626652931219540788", "sharePostage": 103.93}, {"itemAmount": 10064.79, "name": "接地床单", "price": 103, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01xzdPX21Bs31ylFTcy_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01xzdPX21Bs31ylFTcy_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2626652931220540788", "quantity": 100, "refund": 0, "skuID": *************, "status": "success", "subItemID": 2626652931220540788, "type": "common", "unit": "件", "guaranteesTerms": [{"assuranceInfo": "“满足相应条件时，用户在退货寄出后，享受极速退款到账。", "assuranceType": "lsjst_s", "qualityAssuranceType": "极速退款"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“10天发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起10天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "stfh", "qualityAssuranceType": "10天发货"}], "productCargoNumber": "接地床单001", "skuInfos": [{"name": "花型", "value": "澳标"}, {"name": "尺寸规格", "value": "60x280cm"}], "entryDiscount": -23521, "specId": "22b434e69db5185dda31b5694e052109", "quantityFactor": 1, "statusStr": "交易成功", "logisticsStatus": 3, "gmtCreate": "*****************+0800", "gmtModified": "*****************+0800", "gmtCompleted": "*****************+0800", "gmtPayExpireTime": "2025-06-25 17:34:28", "subItemIDString": "2626652931220540788", "sharePostage": 178.17}, {"itemAmount": 234.51, "name": "接地床单", "price": 12, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01xzdPX21Bs31ylFTcy_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01xzdPX21Bs31ylFTcy_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2626652931221540788", "quantity": 20, "refund": 0, "skuID": *************, "status": "success", "subItemID": 2626652931221540788, "type": "common", "unit": "件", "guaranteesTerms": [{"assuranceInfo": "“满足相应条件时，用户在退货寄出后，享受极速退款到账。", "assuranceType": "lsjst_s", "qualityAssuranceType": "极速退款"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“10天发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起10天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "stfh", "qualityAssuranceType": "10天发货"}], "productCargoNumber": "接地床单001", "skuInfos": [{"name": "花型", "value": "美标"}, {"name": "尺寸规格", "value": "接地线"}], "entryDiscount": -549, "specId": "5d9261aec449a1db6244d3ce4a8afe80", "quantityFactor": 1, "statusStr": "交易成功", "logisticsStatus": 3, "gmtCreate": "*****************+0800", "gmtModified": "*****************+0800", "gmtCompleted": "*****************+0800", "gmtPayExpireTime": "2025-06-25 17:34:28", "subItemIDString": "2626652931221540788", "sharePostage": 9.9}, {"itemAmount": 6351.56, "name": "接地床单", "price": 65, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01xzdPX21Bs31ylFTcy_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01xzdPX21Bs31ylFTcy_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2626652931222540788", "quantity": 100, "refund": 0, "skuID": *************, "status": "success", "subItemID": 2626652931222540788, "type": "common", "unit": "件", "guaranteesTerms": [{"assuranceInfo": "“满足相应条件时，用户在退货寄出后，享受极速退款到账。", "assuranceType": "lsjst_s", "qualityAssuranceType": "极速退款"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“10天发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起10天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "stfh", "qualityAssuranceType": "10天发货"}], "productCargoNumber": "接地床单001", "skuInfos": [{"name": "花型", "value": "澳标"}, {"name": "尺寸规格", "value": "50x170cm"}], "entryDiscount": -14844, "specId": "cdfeef8f6cfe80c49d62ee9804c9f2a4", "quantityFactor": 1, "statusStr": "交易成功", "logisticsStatus": 3, "gmtCreate": "*****************+0800", "gmtModified": "*****************+0800", "gmtCompleted": "*****************+0800", "gmtPayExpireTime": "2025-06-25 17:34:28", "subItemIDString": "2626652931222540788", "sharePostage": 98.98}, {"itemAmount": 1905.52, "name": "接地床单", "price": 65, "productID": ************, "productImgUrl": ["http://cbu01.alicdn.com/img/ibank/O1CN01xzdPX21Bs31ylFTcy_!!0-0-cib.80x80.jpg", "http://cbu01.alicdn.com/img/ibank/O1CN01xzdPX21Bs31ylFTcy_!!0-0-cib.jpg"], "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2626652931223540788", "quantity": 30, "refund": 0, "skuID": *************, "status": "success", "subItemID": 2626652931223540788, "type": "common", "unit": "件", "guaranteesTerms": [{"assuranceInfo": "“满足相应条件时，用户在退货寄出后，享受极速退款到账。", "assuranceType": "lsjst_s", "qualityAssuranceType": "极速退款"}, {"assuranceInfo": "卖家在承诺买家保障服务的基础上，向买家提供“10天发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起10天内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。", "assuranceType": "stfh", "qualityAssuranceType": "10天发货"}], "productCargoNumber": "接地床单001", "skuInfos": [{"name": "花型", "value": "英标"}, {"name": "尺寸规格", "value": "50x170cm"}], "entryDiscount": -4448, "specId": "ce2c1461e1243306e7681b43a28908df", "quantityFactor": 1, "statusStr": "交易成功", "logisticsStatus": 3, "gmtCreate": "*****************+0800", "gmtModified": "*****************+0800", "gmtCompleted": "*****************+0800", "gmtPayExpireTime": "2025-06-25 17:34:28", "subItemIDString": "2626652931223540788", "sharePostage": 29.7}], "nativeLogistics": {"address": "江北 金泽物流园二期一号楼四楼-12727", "area": "惠城区", "areaCode": "441302", "city": "惠州市", "contactPerson": "中田 12727", "mobile": "18124000751", "province": "广东省", "zip": "516000", "logisticsItems": [{"deliveredTime": "20250625174800000+0800", "logisticsCode": "LP00743775075208", "type": "0", "id": 233026480910732, "status": "alreadysend", "gmtModified": "20250625174759000+0800", "gmtCreate": "20250625174759000+0800", "fromProvince": "山东省", "fromCity": "济南市", "fromArea": "槐荫区", "fromAddress": "美里湖街道 美里北路与黄河大坝交汇处山东文化传媒", "fromPhone": "86-", "fromMobile": "13280026963", "fromPost": "250000", "logisticsCompanyId": 108012, "logisticsCompanyNo": "DISTRIBUTOR_13211725", "logisticsCompanyName": "跨越速运", "logisticsBillNo": "KY4000784755080", "subItemIds": "2626652931212540788,2626652931213540788,2626652931214540788,2626652931215540788,2626652931216540788,2626652931217540788,2626652931218540788,2626652931219540788,2626652931220540788,2626652931221540788,2626652931222540788,2626652931223540788", "toCity": "惠州市", "toArea": "惠城区", "toAddress": "江北 金泽物流园二期一号楼四楼-12727", "toPost": "516000", "noLogisticsName": "", "noLogisticsTel": "", "noLogisticsBillNo": "", "noLogisticsCondition": "-1"}]}, "orderRateInfo": {"buyerRateStatus": 4, "sellerRateStatus": 5}, "extAttributes": [], "fromEncryptOrder": false}, "success": "true"}