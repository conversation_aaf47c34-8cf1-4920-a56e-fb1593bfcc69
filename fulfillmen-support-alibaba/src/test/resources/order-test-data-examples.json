{"orderTestDataExamples": [{"orderId": 2626652931211540788, "description": "接地床单订单 - 包含配件链接、接地床笠、接地床单等多种商品", "baseInfo": {"status": "success", "totalAmount": "36800", "buyerID": "b2b-2207416548807a4d12", "sellerID": "b2b-22069085846688ab1b", "businessType": "cb", "outOrderId": "12727", "buyerContact": {"name": "汤维政", "companyName": "惠州市中田贸易有限公司"}, "sellerContact": {"name": "王胜锋", "companyName": "北京宇恒联创机械设备有限公司"}, "validateTimestamps": true}, "productItems": {"expectedCount": 12, "expectedProductTypes": ["配件链接", "接地床笠", "接地床单"], "firstItem": {"productID": ************, "skuID": 5719623040970, "quantity": 20, "price": "20"}, "validateTotalAmount": true}, "logistics": {"contactPerson": "中田 12727", "mobile": "***********", "province": "广东省", "city": "惠州市", "area": "惠城区", "address": "江北 金泽物流园二期一号楼四楼-12727", "logisticsCompanyName": "跨越速运", "logisticsBillNo": "KY4000784755080", "status": "alreadysend", "fromProvince": "山东省", "fromCity": "济南市", "fromArea": "槐荫区"}, "tradeTerms": {"payStatus": "6", "phasAmount": "36800", "payWayDesc": "支付平台"}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false, "shippingInsurance": "givenByAnXinGou"}, "orderRateInfo": {"buyerRateStatus": 4, "sellerRateStatus": 5}}, {"orderId": *********0*********, "description": "示例订单 - 单一商品类型订单", "baseInfo": {"status": "success", "totalAmount": "15000", "buyerID": "b2b-example-buyer", "sellerID": "b2b-example-seller", "businessType": "cb", "outOrderId": "EX001", "buyerContact": {"name": "张三", "companyName": "示例买家公司"}, "sellerContact": {"name": "李四", "companyName": "示例卖家公司"}, "validateTimestamps": true}, "productItems": {"expectedCount": 5, "expectedProductTypes": ["示例商品"], "firstItem": {"productID": *********, "skuID": *********, "quantity": 10, "price": "150"}, "validateTotalAmount": true}, "logistics": {"contactPerson": "张三", "mobile": "***********", "province": "北京市", "city": "北京市", "area": "朝阳区", "address": "示例地址123号", "logisticsCompanyName": "顺丰速运", "logisticsBillNo": "SF*********0", "status": "alreadysend", "fromProvince": "广东省", "fromCity": "深圳市", "fromArea": "南山区"}, "tradeTerms": {"payStatus": "6", "phasAmount": "15000", "payWayDesc": "支付宝"}, "orderBizInfo": {"odsCyd": false, "creditOrder": false, "dropshipping": false, "shippingInsurance": "none"}, "orderRateInfo": {"buyerRateStatus": 3, "sellerRateStatus": 4}}, {"orderId": *********0*********, "description": "跨境订单 - 包含多种规格的商品", "baseInfo": {"status": "success", "totalAmount": "58900", "buyerID": "b2b-cross-border-buyer", "sellerID": "b2b-cross-border-seller", "businessType": "cb", "outOrderId": "CB2024001", "buyerContact": {"name": "王五", "companyName": "跨境贸易有限公司"}, "sellerContact": {"name": "赵六", "companyName": "出口制造有限公司"}, "validateTimestamps": true}, "productItems": {"expectedCount": 8, "expectedProductTypes": ["电子产品", "配件"], "firstItem": {"productID": *********, "skuID": *********, "quantity": 50, "price": "118"}, "validateTotalAmount": true}, "logistics": {"contactPerson": "王五", "mobile": "***********", "province": "上海市", "city": "上海市", "area": "浦东新区", "address": "自贸区示例路456号", "logisticsCompanyName": "中通快递", "logisticsBillNo": "ZT*********0", "status": "alreadysend", "fromProvince": "江苏省", "fromCity": "苏州市", "fromArea": "工业园区"}, "tradeTerms": {"payStatus": "6", "phasAmount": "58900", "payWayDesc": "银行转账"}, "orderBizInfo": {"odsCyd": true, "creditOrder": false, "dropshipping": true, "shippingInsurance": "givenBySeller"}, "orderRateInfo": {"buyerRateStatus": 5, "sellerRateStatus": 5}}], "description": "订单详情测试数据示例配置文件", "usage": "此文件展示了如何为不同类型的订单配置测试数据，包括：1. 多商品类型订单 2. 单一商品类型订单 3. 跨境订单。每个订单都包含完整的预期数据结构，可以用于参数化测试。", "notes": ["orderId: 订单唯一标识", "description: 测试用例描述，会显示在测试报告中", "baseInfo: 订单基础信息，包含状态、金额、买卖家信息等", "productItems: 商品项目信息，包含数量、类型、第一个商品详情等", "logistics: 物流信息，包含收货地址、物流公司、运单号等", "tradeTerms: 交易条款，包含支付状态、金额、支付方式等", "orderBizInfo: 订单业务信息，包含各种业务标识", "orderRateInfo: 订单评价信息，包含买卖家评价状态"]}