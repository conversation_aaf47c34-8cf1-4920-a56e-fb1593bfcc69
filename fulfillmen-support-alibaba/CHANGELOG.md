# Changelog

## [Unreleased]

### Added

- 新增会员 API 测试用例
    - 新增会员注册接口测试
    - 新增子账号授权相关接口测试（添加、取消、查询）
    - 支持 Mock 测试和集成测试
    - 添加性能指标记录和日志输出

### Changed

- 优化测试用例断言逻辑
    - 移除成功场景下的错误码判断
    - 添加返回值内容的详细验证
    - 规范化测试用例命名和结构

## [0.1.0] - 2025-01-09

### Added

- 新增预览订单接口 `previewOrder`
    - 支持订单创建前的预览功能
    - 返回订单总费用、运费、优惠等金额信息
    - 返回可用的交易方式和支付渠道
    - 返回商品规格和优惠信息
    - 支持特殊商品标记（代销、跨境等）

### Changed

- 规范化所有请求和响应模型类的代码结构
    - 统一使用 JavaDoc 注释格式
    - 添加完整的字段说明
    - 规范化字段命名
    - 添加必要的单位说明（如金额单位：分）

### Fixed

- 修复 `OrderPreviewResponse` 中 `orderPreviewResult` 字段名拼写错误
- 修复 `CreateOrderPreviewResultCargoModel` 中金额相关字段的类型（从 Double 改为 Long）

### Tests

- 新增预览订单接口的单元测试
    - 测试正常预览订单场景
    - 测试错误处理场景（商品不存在、已下架等）
    - 添加性能指标记录
    - 完善日志输出 