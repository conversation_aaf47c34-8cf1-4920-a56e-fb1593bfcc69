# 商品文本翻译

## 产品介绍

商品文本翻译产品专门为电商场景研发，实现 60 多种语向间的精确翻译。借助电商特定数据训练提升翻译质量，并配备智能品牌识别与自定义干预功能，为电商平台和开发者提供高效的多语言市场扩张解决方案。

## API 说明

### 请求地址

```POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao.crossborder/product.text.translate/${APPKEY}
```

### 系统级入参

| 参数名            | 类型     | 是否必填 | 描述     |
|----------------|--------|------|--------|
| _aop_timestamp | String | 是    | 请求时间戳  |
| _aop_signature | String | 是    | 请求签名   |
| access_token   | String | 是    | 用户授权令牌 |

### 应用级入参

| 参数名            | 类型     | 是否必填 | 描述                                                   | 示例值      |
|----------------|--------|------|------------------------------------------------------|----------|
| sourceTextList | String | 是    | 翻译文本，单字符长度不超过1000，建议文本不超过50个                         | ["测试翻译"] |
| sourceLanguage | String | 是    | 源语言代码，使用 ISO 693-1 语言代码标准，例如"zh"代表中文，支持的语言见下文支持语言列表  | zh       |
| targetLanguage | String | 是    | 目标语言代码，使用 ISO 693-1 语言代码标准，例如"en"代表英文，支持的语言见下文支持语言列表 | en       |
| formatType     | String | 否    | 文本类型，目前支持html和text类型                                 | text     |

### 返回结果

| 参数名     | 类型     | 描述             | 示例值                |
|---------|--------|----------------|--------------------|
| success | String | 是否成功           | true               |
| code    | String | 具体的错误码，参考错误码说明 | -                  |
| message | String | 成功/失败信息        | -                  |
| result  | String | 翻译后的文本数组       | ["test translate"] |

### 错误码说明

| 错误码   | 错误描述                | 解决方案                          |
|-------|---------------------|-------------------------------|
| Z0001 | 参数不合法，参数为空          | 必填参数入参检查                      |
| Z0006 | 不支持的源语言             | 不支持的文本目标语言，需要查看支持的语言列表后重新选择语言 |
| Z0007 | 不支持的目标语言            | 不支持的文本目标语言，需要查看支持的语言列表后重新选择语言 |
| Z0009 | 不支持的语言数量，不能超过2000字符 | 不支持的文本长度                      |
| Z0008 | 不支持的语言列表长度，不能超过50项  | 不支持的文本项                       |
| Z0099 | 系统异常，请稍后重试          | -                             |

## 调用示例

### 请求示例

```json
{
    "sourceTextList": [
        "测试翻译"
    ],
    "sourceLanguage": "zh",
    "targetLanguage": "en"
}
```

### 响应示例

```json
{
    "success": "true",
    "code": "",
    "message": "",
    "result": [
        "test translate"
    ]
}
```
