# 图片智能消除

## 接口说明

图片智能消除接口用于去除图片中的文字、特定标识、遮挡手势和牛皮癣，可提高图像使用内的美观度。

## 接口信息

- **接口路径**: `/param2/1/com.alibaba.fenxiao.crossborder/image.elements.remove/${APPKEY}`
- **请求方式**: POST
- **接口版本**: 1.0
- **API名称**: com.alibaba.fenxiao.crossborder.image.elements.remove-1

## 系统级参数

| 参数名            | 类型     | 必填 | 描述     | 示例值 |
|----------------|--------|----|--------|-----|
| _aop_timestamp | String | 否  | 请求时间戳  | -   |
| _aop_signature | String | 是  | 请求签名   | -   |
| access_token   | String | 是  | 用户授权令牌 | -   |

## 业务请求参数

### param 对象

| 参数名                  | 类型      | 必填 | 描述                                                               | 示例值       |
|----------------------|---------|----|------------------------------------------------------------------|-----------|
| imageUrl             | String  | 是  | 图片URL，支持JPG、JPEG、PNG、BMP格式，分辨率在512x512至3000x3000像素之间，文件大小不超过10MB | http://xx |
| noobjRemoveCharacter | Boolean | 否  | 非主体消除文字                                                          | true      |
| noobjRemoveLogo      | Boolean | 否  | 非主体消除Logo                                                        | true      |
| noobjRemoveNpx       | Boolean | 否  | 非主体消除牛皮癣                                                         | true      |
| noobjRemoveQrcode    | Boolean | 否  | 非主体消除二维码                                                         | true      |
| noobjRemoveWatermark | Boolean | 否  | 非主体消除水印                                                          | true      |
| objRemoveCharacter   | Boolean | 否  | 主体消除文字                                                           | true      |
| objRemoveLogo        | Boolean | 否  | 主体消除Logo                                                         | true      |
| objRemoveNpx         | Boolean | 否  | 主体消除牛皮癣                                                          | true      |
| objRemoveQrcode      | Boolean | 否  | 主体消除二维码                                                          | true      |
| objRemoveWatermark   | Boolean | 否  | 主体消除水印                                                           | true      |

## 返回参数

### result 对象

| 参数名     | 类型      | 描述        | 示例值       |
|---------|---------|-----------|-----------|
| success | Boolean | 请求是否成功    | true      |
| code    | String  | 返回code    | 200       |
| message | String  | 返回提示      | 成功        |
| result  | String  | 消除后的图片URL | http://xx |

## 产品简介

图像智能消除产品专为电商图片定制，自动识别并消除电商图片中的文字、特定名称、透明字块和牛皮癣，可按需指定消除图片内对象，实现大批量图片的高效、精确处理，简化图片编辑工作。

## 请求示例

```json
{
    "param": {
        "imageUrl": "http://xx",
        "noobjRemoveCharacter": true,
        "noobjRemoveLogo": true,
        "noobjRemoveNpx": true,
        "noobjRemoveQrcode": true,
        "noobjRemoveWatermark": true,
        "objRemoveCharacter": true,
        "objRemoveLogo": true,
        "objRemoveNpx": true,
        "objRemoveQrcode": true,
        "objRemoveWatermark": true
    }
}
```

## 返回示例

```json
{
    "result": {
        "success": true,
        "code": "200",
        "message": "成功",
        "result": "http://xx"
    }
}
```
