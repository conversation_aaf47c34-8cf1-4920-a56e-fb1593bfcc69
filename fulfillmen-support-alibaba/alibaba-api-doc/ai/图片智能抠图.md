# 图片智能抠图

## 接口说明

智能抠图接口可以自动识别图像中的显著主体，将主体从背景中分离，返回白色或透明背景的主体图像。同时该产品还提供背景置换及定制尺寸选择，优化商品展示效果。

## 接口信息

- **接口路径**: `/param2/1/com.alibaba.fenxiao.crossborder/image.elements.matting/${APPKEY}`
- **请求方式**: POST
- **接口版本**: 1.0
- **API名称**: com.alibaba.fenxiao.crossborder.image.elements.matting-1

## 系统级参数

| 参数名            | 类型     | 必填 | 描述     | 示例值 |
|----------------|--------|----|--------|-----|
| _aop_timestamp | String | 否  | 请求时间戳  | -   |
| _aop_signature | String | 是  | 请求签名   | -   |
| access_token   | String | 是  | 用户授权令牌 | -   |

## 应用级输入参数

| 参数名           | 类型      | 必填 | 描述           | 示例值             |
|---------------|---------|----|--------------|-----------------|
| imageUrl      | String  | 是  | 源图           | http://xx       |
| backgroundBGR | String  | 否  | 背景颜色，默认为透明背景 | "[255,255,255]" |
| height        | Integer | 否  | 高度，指定返回的图像高度 | 800             |
| width         | Integer | 否  | 宽度，指定返回的图像宽度 | 800             |

## 返回结果

| 参数名     | 类型      | 描述        | 示例值       |
|---------|---------|-----------|-----------|
| success | Boolean | 是否成功      | true      |
| code    | String  | 响应代码      | 200       |
| message | String  | 失败信息      | -         |
| result  | String  | 抠图后的图片URL | http://xx |

## 请求示例

```json
{
    "imageUrl": "http://xx",
    "backgroundBGR": "[255,255,255]",
    "height": 800,
    "width": 800
}
```

## 返回示例

注意：此结构没有 result 对象包含。

```json
{
    "success": true,
    "code": "200",
    "message": "",
    "result": "http://xx"
}
```
