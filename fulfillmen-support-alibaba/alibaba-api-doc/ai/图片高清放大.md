# 图片高清放大

## 接口说明

图片高清放大接口用于将图片放大1-4倍，提升图片清晰度。

## 接口信息

- **接口路径**: `/param2/1/com.alibaba.fenxiao.crossborder/image.elements.enlarge/${APPKEY}`
- **请求方式**: POST
- **接口版本**: 1.0
- **API名称**: com.alibaba.fenxiao.crossborder.image.elements.enlarge-1

## 系统级参数

| 参数名            | 类型     | 必填 | 描述     | 示例值 |
|----------------|--------|----|--------|-----|
| _aop_timestamp | String | 否  | 请求时间戳  | -   |
| _aop_signature | String | 是  | 请求签名   | -   |
| access_token   | String | 是  | 用户授权令牌 | -   |

## 业务请求参数

| 参数名           | 类型      | 必填 | 描述                                  | 示例值        |
|---------------|---------|----|-------------------------------------|------------|
| imageUrl      | String  | 是  | 源URL，图片尺寸应大于100*100像素，小于3000*5000像素 | http://xxx |
| upscaleFactor | Integer | 是  | 放大倍数，默认为2，支持2~4                     | 2          |

## 返回参数

| 参数名     | 类型                | 描述      | 示例值     |
|---------|-------------------|---------|---------|
| success | Boolean           | 是否成功    | true    |
| code    | String            | 响应代码    | 200     |
| message | String            | 成功/失败信息 | success |
| result  | EnlargeImageModel | 结果      | -       |

### EnlargeImageModel 对象

| 参数名              | 类型      | 描述        | 示例值       |
|------------------|---------|-----------|-----------|
| enlargedImageUrl | String  | 放大后的图片URL | http://xx |
| imageHeight      | Integer | 图片宽度      | 800       |
| imageWidth       | Integer | 图片高度      | 800       |

## 错误码

| 错误码   | 错误描述                   | 解决方案                |
|-------|------------------------|---------------------|
| Z0001 | 参数不合法，参数必填             | 必填的入参没有填写           |
| Z0004 | 不能支持的放大倍数，放大倍数应当设置在2~4 | 目前支持2-4倍的放大倍数，默认为2倍 |

## 产品简介

高清放大功能可以利用AI算法对图片进行2-4倍的高分辨率放大，有效提升部分细节的清晰度，改善图像纹理细节，全面提高图像清晰度与表现质量。

## 请求示例

```json
{
    "imageUrl": "http://xxx",
    "upscaleFactor": 2
}
```

## 返回示例

```json
{
    "success": true,
    "code": "200",
    "message": "success",
    "result": {
        "enlargedImageUrl": "http://xx",
        "imageHeight": 800,
        "imageWidth": 800
    }
}
```
