# 图像元素识别

## 产品介绍

图像元素识别API是一款高效的异步批处理API，专为电商图像特定元素识别而设计。
它能够深入挖掘图像中的细节，帮助用户快速识别图片主体和背景中的文字、Logo、水印及含字色块等元素，能极大提升图像筛选的效率

## API 说明

### 请求地址

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao.crossborder/image.elements.recognition/${APPKEY}
```

### 系统级入参

| 参数名            | 类型     | 必填 | 描述     |
|----------------|--------|----|--------|
| _aop_timestamp | String | 否  | 请求时间戳  |
| _aop_signature | String | 是  | 请求签名   |
| access_token   | String | 否  | 用户授权令牌 |

### 应用级入参

| 参数名                   | 类型                                                                 | 必填 | 描述       |
|-----------------------|--------------------------------------------------------------------|----|----------|
| recognitionImageParam | com.alibaba.cbu.offerintroduction.open.param.RecognitionImageParam | 是  | 识别图片请求对象 |

### recognitionImageParam对象参数

| 参数名                     | 类型           | 必填 | 描述                                           | 示例值                                                                                             |
|-------------------------|--------------|----|----------------------------------------------|-------------------------------------------------------------------------------------------------|
| imageUrl                | String       | 是  | 图片url                                        | https://cbu01.alicdn.com/img/ibank/O1CN01Caj6h1Ddxhd/L9N_!!*********-0-cib.jpg?__=************* |
| objectDetectElements    | List<String> | 否  | 检测图片是否上方元素（1=水印; 2=Logo; 3=文字; 4=条形码），不传不检测  | ["1","2","3","4"]                                                                               |
| nonObjectDetectElements | List<String> | 否  | 检测图片非主体上方元素（1=水印; 2=Logo; 3=文字; 4=条形码），不传不检测 | ["1","2","3","4"]                                                                               |
| returnCharacter         | Integer      | 否  | 是否返回识别的文字OCR结果（1是，0否），不传默认为0                 | 1                                                                                               |
| returnBorderPixel       | Integer      | 否  | 是否返回主体边缘像素值（1是，0否），不传默认为0                    | 1                                                                                               |
| returnProductProp       | Integer      | 否  | 是否返回图像主体属性占比（1是，0否），不传默认为0                   | 1                                                                                               |
| returnProductNum        | Integer      | 否  | 是否返回主体数量（1是，0否），不传默认为0                       | 1                                                                                               |
| returnCharacterProp     | Integer      | 否  | 是否返回文字占比比例（1是，0否），不传默认为0                     | 1                                                                                               |

### 返回结果

| 参数名    | 类型                                                       | 描述     | 示例值 |
|--------|----------------------------------------------------------|--------|-----|
| result | alibaba.cbu.common.result.image.recognition.CommonResult | 返回结果对象 | -   |

### result对象参数

| 参数名     | 类型                                                             | 描述     | 示例值   |
|---------|----------------------------------------------------------------|--------|-------|
| success | Boolean                                                        | 是否成功   | true  |
| code    | String                                                         | 状态码    | S0000 |
| message | String                                                         | 返回描示   | 成功    |
| result  | com.alibaba.cbu.offerintroduction.open.result.RecognitionModel | 返回识别模型 | -     |

### RecognitionModel对象参数

| 参数名           | 类型           | 描述           | 示例值              |
|---------------|--------------|--------------|------------------|
| recText       | List<String> | 识别的文字        | ["23"]           |
| pdProp        | String       | 主体占比         | 12.38%           |
| noobWatermark | Boolean      | 非主体是否有水印     | true             |
| objLogo       | Boolean      | 主体是否有logo    | true             |
| objQrcode     | Boolean      | 主体是否有二维码     | true             |
| noobQrcode    | Boolean      | 非主体是否有二维码    | true             |
| objCharacter  | Boolean      | 主体是否有文字      | true             |
| noobCharacter | Boolean      | 非主体是否有文字     | true             |
| objWatermark  | Boolean      | 主体是否有水印      | true             |
| noobLogo      | Boolean      | 非主体是否有logo   | true             |
| borderPixel   | String       | 主体图边缘的距离     | 上, 下, 左, 右, 顶部距离 |
| objNgx        | Boolean      | 图像主体是否含有色情暴恐 | true             |

## 调用示例

### 请求示例

```json
{
    "recognitionImageParam": {
        "imageUrl": "https://cbu01.alicdn.com/img/ibank/O1CN01Caj6h1Ddxhd/L9N_!!*********-0-cib.jpg?__=*************",
        "objectDetectElements": [
            "1",
            "2",
            "3",
            "4"
        ],
        "nonObjectDetectElements": [
            "1",
            "2",
            "3",
            "4"
        ],
        "returnCharacter": 1,
        "returnBorderPixel": 1,
        "returnProductProp": 1,
        "returnProductNum": 1,
        "returnCharacterProp": 1
    }
}
```

### 响应示例

```json
{
    "result": {
        "success": true,
        "code": "S0000",
        "message": "成功",
        "result": {
            "recText": [
                "23"
            ],
            "pdProp": "12.38%",
            "noobWatermark": true,
            "objLogo": true,
            "objQrcode": true,
            "noobQrcode": true,
            "objCharacter": true,
            "noobCharacter": true,
            "objWatermark": true,
            "noobLogo": true,
            "borderPixel": "上, 下, 左, 右, 顶部距离",
            "objNgx": true
        }
    }
}
```
