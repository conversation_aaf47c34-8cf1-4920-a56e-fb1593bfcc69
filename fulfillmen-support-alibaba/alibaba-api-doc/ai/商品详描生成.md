# 商品详描生成

## 产品介绍

商品详情描述生成产品专为海外电商商品定制，通过简单入参，可生成内容丰富、卖点精准、充满吸引力的商品描述，解决商品详情文本信息缺失、重点不明确等信息效率低问题，提升商品转化率。

## API 说明

### 请求地址

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao.crossborder/text.generate.description/${APPKEY}
```

### 系统级入参

| 参数名            | 类型     | 是否必填 | 描述     |
|----------------|--------|------|--------|
| _aop_timestamp | String | 是    | 请求时间戳  |
| _aop_signature | String | 是    | 请求签名   |
| access_token   | String | 是    | 用户授权令牌 |

### 应用级入参

| 参数名                 | 类型                                                                | 是否必填 | 描述         |
|---------------------|-------------------------------------------------------------------|------|------------|
| generateDescription | alibaba.cbu.offerintroduction.open.param.GenerateDescriptionParam | 是    | 商品详描生成请求对象 |

### generateDescription对象参数

| 参数名                | 类型               | 是否必填 | 描述                              | 示例值                                  |
|--------------------|------------------|------|---------------------------------|--------------------------------------|
| productName        | java.lang.String | 是    | 商品名称，可以输入商品原商品标题或对商品的简短描述，必填    | VHHD 5145A/B全封闭式四位半数显电压电流电阻功率转速表可订隔离 |
| targetLanguage     | java.lang.String | 是    | 目标语言，请输入语言代码，必填                 | en                                   |
| productCategory    | java.lang.String | 否    | 商品所属的类目，可参考任何一个平台的类目结构，输入商品类目名称 | 电流测量仪表                               |
| productKeyword     | java.lang.String | 否    | 商品相关关键词，一般是商品SEO相关关键词或商品核心卖点词   | xx                                   |
| productDescription | java.lang.String | 否    | 商品详细描述，阐述说明商品的卖点信息。             | xx                                   |
| bizCode            | java.lang.String | 否    | 业务标识code,一般不用写                  | xx                                   |
| itemSpec           | java.lang.String | 否    | 商品cpv信息                         | -                                    |

### 返回结果

| 参数名    | 类型     | 描述     | 示例值 |
|--------|--------|--------|-----|
| result | Object | 返回结果对象 | -   |

### result对象参数

| 参数名     | 类型     | 描述                              | 示例值 |
|---------|--------|---------------------------------|-----|
| success | String | 是否成功                            | -   |
| code    | String | 错误代码：200 代表调用成功，其他的错误代码见参考错误码说明 | -   |
| message | String | 成功/失败信息                         | -   |
| result  | String | 生成的商品详描                         | -   |

### 错误码说明

| 错误码   | 错误描述       | 解决方案     |
|-------|------------|----------|
| Z0001 | 参数不合法，参数为空 | 必填的入参须填写 |
| Z0099 | 系统异常，请稍后重试 | -        |

## 调用示例

### 请求示例

```json
{
    "generateDescription": {
        "productName": "VHHD 5145A/B全封闭式四位半数显电压电流电阻功率转速表可订隔离",
        "targetLanguage": "en",
        "productCategory": "电流测量仪表",
        "productKeyword": "xx",
        "productDescription": "xx",
        "bizCode": "xx",
        "itemSpec": ""
    }
}
```

### 响应示例

```json
{
    "result": {
        "success": "",
        "code": "",
        "message": "",
        "result": ""
    }
}
```
