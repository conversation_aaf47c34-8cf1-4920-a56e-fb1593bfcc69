# 图片裁剪

## 产品介绍

图像裁剪产品可以对输入的图像像素尺寸进行调整，支持自动识别图像主体区域，将裁剪后的各类尺寸，适配各种场景设计需求。

## API 说明

### 请求地址

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao.crossborder/image.elements.cut/${APPKEY}
```

### 系统级入参

| 参数名            | 类型     | 是否必填 | 描述     |
|----------------|--------|------|--------|
| _aop_timestamp | String | 是    | 请求时间戳  |
| _aop_signature | String | 是    | 请求签名   |
| access_token   | String | 是    | 用户授权令牌 |

### 应用级入参

| 参数名      | 类型     | 是否必填 | 描述                                   | 示例值       |
|----------|--------|------|--------------------------------------|-----------|
| imageUrl | String | 是    | 源图，图片尺寸最大不超过100*100像素，小于5000*5000像素。 | http://xx |
| width    | String | 是    | 宽度，期望裁剪成的图片宽度，单位：像素，取值范围：100-5000    | 800       |
| height   | String | 是    | 高度，期望裁剪成的图片高度，单位：像素，取值范围：100-5000    | 800       |

### 返回结果

| 参数名     | 类型     | 描述                              | 示例值 |
|---------|--------|---------------------------------|-----|
| success | String | 是否成功                            | -   |
| code    | String | 错误代码：200 代表调用成功，其他的错误代码见参考错误码说明 | -   |
| message | String | 成功/失败信息                         | -   |
| result  | String | 裁剪后的图片URL                       | -   |

### 错误码说明

| 错误码   | 错误描述            | 解决方案     |
|-------|-----------------|----------|
| Z0001 | 参数不合法，参数为填      | 必填的入参须填写 |
| Z0010 | 不支持的长度/宽度，需要为正数 | -        |
| Z0099 | 系统异常，请稍后重试      | -        |

## 调用示例

### 请求示例

```json
{
    "imageUrl": "http://xx",
    "width": "800",
    "height": "800"
}
```

### 响应示例

```json
{
    "success": "",
    "code": "",
    "message": "",
    "result": ""
}
```
