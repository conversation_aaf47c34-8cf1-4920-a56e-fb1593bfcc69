# API文档编写标准

本文档规定了API接口文档的编写标准，包括文档结构、格式规范、内容要求等。所有API文档都必须遵循此标准。

## 一、基本原则

1. 文档结构完整，层次分明
2. 描述准确清晰，避免歧义
3. 示例真实有效，便于理解
4. 格式统一规范，美观整洁

## 二、文档结构规范

### 1. 必需章节

每个API文档必须包含以下章节，并按照此顺序排列：

```markdown
# 接口名称
## 接口描述
## 请求URL
## 请求参数
### 系统级输入参数
### 应用级输入参数
## 响应参数
## 错误码
## 示例
```

### 2. 章节内容要求

#### 2.1 接口名称

- 包含中文名称和API标识符
- 示例：`# 创建订单预览接口`

#### 2.2 接口描述

- 简要说明接口功能和使用场景
- 说明关键限制和注意事项
- 必要时说明与其他接口的关系

#### 2.3 请求URL

- 使用代码块标注
- 包含HTTP方法、基础URL和路径
- 说明URL中的变量
- 示例：

  ```
  POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.createOrder.preview/${APP_KEY}
  ```

#### 2.4 请求参数

- 分为系统级和应用级参数
- 使用统一的表格格式
- 复杂对象使用独立章节描述

#### 2.5 响应参数

- 使用统一的表格格式
- 包含示例值
- 复杂对象使用独立章节描述

#### 2.6 错误码

- 包含错误码、描述和解决方案
- 解决方案要具体可执行

#### 2.7 示例

- 提供完整的请求和响应示例
- 使用JSON格式
- 示例要真实有效

## 三、表格规范

### 1. 请求参数表格

| 列名   | 说明      | 要求        |
|------|---------|-----------|
| 名称   | 参数的名称   | 使用驼峰命名法   |
| 类型   | 参数的数据类型 | 使用完整包路径   |
| 是否必须 | 是否必填    | 统一使用"是/否" |
| 描述   | 参数的说明   | 简洁明确      |
| 示例值  | 参数的示例   | 真实有效      |

### 2. 响应参数表格

| 列名  | 说明      | 要求      |
|-----|---------|---------|
| 参数名 | 参数的名称   | 使用驼峰命名法 |
| 类型  | 参数的数据类型 | 使用完整包路径 |
| 描述  | 参数的说明   | 简洁明确    |
| 示例值 | 参数的示例   | 真实有效    |

### 3. 错误码表格

| 列名   | 说明    | 要求             |
|------|-------|----------------|
| 错误码  | 错误的编号 | 统一格式(如500_001) |
| 错误描述 | 错误的说明 | 清晰描述原因         |
| 解决方案 | 处理方法  | 具体可执行          |

## 四、内容编写规范

### 1. 命名规范

- 参数名使用驼峰式命名
- 类型名称使用完整包路径
- 错误码使用统一前缀和格式
- 示例：
    - 参数名：`addressParam`、`cargoParamList`
    - 类型名：`alibaba.trade.fast.address`
    - 错误码：`500_001`

### 2. 描述规范

- 使用简洁明确的语言
- 避免使用含糊不清的词语
- 必要时提供补充说明
- 中英文混排使用空格分隔
- 示例：
    - 好的描述：`创建订单时的收货地址信息`
    - 不好的描述：`地址信息`

### 3. 示例规范

- 使用标准JSON格式
- 示例数据要真实有效
- 关键字段都要有示例值
- 使用合适的缩进和换行
- 示例：

```json
{
    "addressParam": {
        "address": "网商路699号",
        "phone": "0517-88990077",
        "mobile": "15251667788"
    }
}
```

## 五、质量检查清单

### 1. 结构完整性检查

- [ ] 包含所有必需章节
- [ ] 章节顺序正确
- [ ] 层级结构清晰
- [ ] 标题格式统一

### 2. 内容准确性检查

- [ ] 参数名称准确
- [ ] 类型定义正确
- [ ] 必填项标记准确
- [ ] 描述清晰准确
- [ ] 示例值有效

### 3. 格式规范检查

- [ ] 表格对齐整齐
- [ ] 代码块格式正确
- [ ] 缩进统一
- [ ] 空行使用合理
- [ ] 标点符号规范

### 4. 示例完整性检查

- [ ] 请求示例完整
- [ ] 响应示例完整
- [ ] JSON格式正确
- [ ] 示例数据合理

### 5. 特殊检查项

- [ ] 复杂对象结构完整
- [ ] 错误码解决方案具体可行
- [ ] 参数之间的关系说明清楚
- [ ] 特殊场景说明充分

## 六、文档模板

```markdown
# ${接口名称}

## 接口描述
[简要说明接口的主要功能和使用场景]

## 请求URL
`${HTTP_METHOD} ${BASE_URL}/${API_PATH}/${APP_KEY}`

## 请求参数

### 系统级输入参数
| 参数名 | 类型 | 是否必填 | 描述 |
|-------|------|---------|------|
| ... | ... | ... | ... |

### 应用级输入参数
| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|---------|------|--------|
| ... | ... | ... | ... | ... |

### ${复杂对象}结构
| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|---------|------|--------|
| ... | ... | ... | ... | ... |

## 响应参数
| 参数名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| ... | ... | ... | ... |

## 错误码
| 错误码 | 错误描述 | 解决方案 |
|--------|----------|----------|
| ... | ... | ... |

## 示例

### 请求示例
```json
{
    // 标准JSON格式的请求示例
}
```

### 响应示例

```json
{
    // 标准JSON格式的响应示例
}
```

```

## 七、注意事项

1. 文档更新时要同步更新所有相关内容
2. 复杂对象的结构要完整描述
3. 示例要覆盖典型场景
4. 错误码要提供明确的解决方案
5. 参数之间的依赖关系要说明清楚

## 八、常见问题

1. Q: 如何处理可选参数的示例值？
   A: 可选参数也应提供合适的示例值，帮助理解参数的使用场景。

2. Q: 复杂对象的结构如何展示？
   A: 使用三级标题创建独立章节，完整展示其结构。

3. Q: 如何处理参数之间的依赖关系？
   A: 在描述中说明依赖关系，必要时添加补充说明。

4. Q: 示例值应该多详细？
   A: 示例值应该真实有效，能够说明参数的实际使用情况。
