# 多语言商品详情API

## 接口说明

获取1688商品的多语言详情信息。

- **接口名称**: com.alibaba.fenxiao.crossborder:product.search.queryProductDetail-1
- **请求方式**: POST
- **请求URL**: `https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao.crossborder/product.search.queryProductDetail/${APPKEY}`

## 系统级输入参数

| 参数名            | 类型     | 是否必填 | 描述     | 示例值 |
|----------------|--------|------|--------|-----|
| _aop_timestamp | String | 否    | 请求时间戳  | -   |
| _aop_signature | String | 是    | 请求签名   | -   |
| access_token   | String | 是    | 用户授权令牌 | -   |

## 应用级输入参数

### offerDetailParam (必填)

| 参数名         | 类型               | 是否必填 | 描述     | 示例值         |
|-------------|------------------|------|--------|-------------|
| offerId     | java.lang.Long   | 是    | 商品ID   | 1           |
| country     | java.lang.String | 是    | 语言     | ja-日语 en-英语 |
| outMemberId | java.lang.String | 否    | 外部用户ID | 1           |

## 返回结果

| 参数名    | 类型                                                  | 描述   | 示例值 |
|--------|-----------------------------------------------------|------|-----|
| result | product.search.queryProductDetail.resut.ResultModel | 返回结果 | -   |

### ResultModel

| 参数名     | 类型                                                         | 描述   | 示例值  |
|---------|------------------------------------------------------------|------|------|
| success | boolean                                                    | 是否成功 | true |
| code    | java.lang.String                                           | 返回码  | 200  |
| message | java.lang.String                                           | 提示   | 成功   |
| result  | product.search.queryProductDetail.model.ProductDetailModel | 结果   | -    |

### ProductDetailModel (商品详情信息)

| 参数名                 | 类型                                                          | 描述                       | 示例值                                                                                                                  |
|---------------------|-------------------------------------------------------------|--------------------------|----------------------------------------------------------------------------------------------------------------------|
| offerId             | java.lang.Long                                              | 商品ID                     | 34513534353434                                                                                                       |
| categoryId          | java.lang.Long                                              | 类目ID                     | 32                                                                                                                   |
| categoryName        | java.lang.String                                            | 类目名称                     | 猫子                                                                                                                   |
| subject             | java.lang.String                                            | 中文标题                     | 猫砂盆超大号防外溅猫厕所加高巨无霸开放式猫砂盆厂家直销批发                                                                                        |
| subjectTrans        | java.lang.String                                            | 译文标题                     | Cat litter box extra large splash-proof cat toilet heightened giant open pet supplies factory direct sales wholesale |
| description         | java.lang.String                                            | 详情描述                     | -                                                                                                                    |
| mainVideo           | java.lang.String                                            | 主视频                      | -                                                                                                                    |
| detailVideo         | java.lang.String                                            | 详情视频                     | -                                                                                                                    |
| productImage        | product.search.queryProductDetail.model.ProductImage        | 图片模型                     | -                                                                                                                    |
| productAttribute    | product.search.queryProductDetail.model.ProductAttribute[]  | 商品CPV属性                  | -                                                                                                                    |
| productSkuInfos     | product.search.queryProductDetail.model.SkuInfo[]           | 商品SKU                    | -                                                                                                                    |
| productSaleInfo     | product.search.queryProductDetail.model.ProductSaleInfo     | 商品销售信息                   | -                                                                                                                    |
| productShippingInfo | product.search.queryProductDetail.model.ProductShippingInfo | 商品包裹试算相关数据               | -                                                                                                                    |
| isJxhy              | boolean                                                     | 是否精选货源                   | true                                                                                                                 |
| sellerOpenId        | java.lang.String                                            | 商家加密ID                   | 23tsdvcdsjngp3o4j3i5                                                                                                 |
| minOrderQuantity    | java.lang.Integer                                           | 最小起批量数量                  | 1                                                                                                                    |
| batchNumber         | Integer                                                     | 一手数量                     | 200                                                                                                                  |
| status              | String                                                      | 商品状态                     | published                                                                                                            |
| tagInfoList         | com.alibaba.cbu.offer.model.ProductTagInfo[]                | 商品服务标签                   | -                                                                                                                    |
| traceInfo           | String                                                      | 打点信息                     | object_id@62020139002337object_type@offer                                                                            |
| sellerMixSetting    | product.search.queryProductDetail.model.SellerMixSetting    | 卖家混配配置                   | -                                                                                                                    |
| productCargoNumber  | String                                                      | 商品货号                     | -                                                                                                                    |
| sellerDataInfo      | product.search.queryProductDetail.model.SellerDataInfo      | 商家属性数据                   | -                                                                                                                    |
| soldOut             | String                                                      | 商品销量                     | 12342                                                                                                                |
| channelPrice        | product.search.queryProductDetail.model.ChannelPrice        | 渠道价格数据                   | -                                                                                                                    |
| promotionModel      | com.alibaba.cbu.offer.model.PromotionModel                  | 营销                       | -                                                                                                                    |
| tradeScore          | String                                                      | 商品评分                     | 5.0                                                                                                                  |
| topCategoryId       | Long                                                        | 一级类目                     | 1                                                                                                                    |
| secondCategoryId    | Long                                                        | 二级类目                     | 2                                                                                                                    |
| thirdCategoryId     | Long                                                        | 三级类目                     | 3                                                                                                                    |
| sellingPoint        | String[]                                                    | 多语言卖点                    | 质量,配送快速                                                                                                              |
| offerIdentities     | String[]                                                    | 商家身份                     | 超级工厂,实力商家,诚信通                                                                                                        |
| createDate          | String                                                      | 创建时间                     | 2025-01-15 00:00:00                                                                                                  |
| isSelect            | String                                                      | 跨境select状态               | true                                                                                                                 |
| certificateList     | com.alibaba.cbu.offer.model.OfferCertificateModel[]         | 商品证书列表                   | -                                                                                                                    |
| promotionUrl        | String                                                      | 营销【站内推广链接】跳转的1688商品详情页链接 | -                                                                                                                    |

### ProductImage (图片模型)

| 参数名        | 类型                 | 描述   | 示例值                                                                      |
|------------|--------------------|------|--------------------------------------------------------------------------|
| images     | java.lang.String[] | 图片列表 | 图片URL数组                                                                  |
| whiteImage | String             | 白底图  | <https://cbu01.alicdn.com/img/ibank/O1CN01pEsVS41Bs2uny9oka_!!0-0-cib.jpg> |

### ProductAttribute (商品CPV属性)

| 参数名                | 类型               | 描述     | 示例值 |
|--------------------|------------------|--------|-----|
| attributeId        | java.lang.String | 属性ID   | 1   |
| attributeName      | java.lang.String | 属性名称   | 1   |
| value              | java.lang.String | 属性值    | 1   |
| attributeNameTrans | java.lang.String | 属性名称翻译 | 1   |
| valueTrans         | java.lang.String | 属性值翻译  | 1   |

### SkuInfo (商品SKU信息)

| 参数名              | 类型                                                     | 描述    | 示例值     |
|------------------|--------------------------------------------------------|-------|---------|
| amountOnSale     | Integer                                                | 库存    | 库存数量    |
| price            | java.lang.String                                       | 价格    | 价格值     |
| jxhyPrice        | java.lang.String                                       | 废弃    | 精选货源价格  |
| skuId            | Long                                                   | sku标识 | sku值    |
| specId           | java.lang.String                                       | 规格ID  | specid值 |
| skuAttributes    | product.search.queryProductDetail.model.SkuAttribute[] | 属性    | 属性列表    |
| pfJxhyPrice      | String                                                 | 废弃    | 1       |
| consignPrice     | String                                                 | 废弃    | 1       |
| cargoNumber      | String                                                 | sku级别 | 1       |
| promotionPrice   | String                                                 | 营销价   | 1       |
| fenxiaoPriceInfo | com.alibaba.cbu.offer.model.FenxiaoPriceInfo           | 分销价格  | 1       |

### ProductSaleInfo (商品销售信息)

| 参数名             | 类型                                                     | 描述     | 示例值                                     |
|-----------------|--------------------------------------------------------|--------|-----------------------------------------|
| amountOnSale    | Integer                                                | 商品库存   | 99999                                   |
| priceRangeList  | product.search.queryProductDetail.model.PriceRangeV2[] | 价格区间   | 数组                                      |
| quoteType       | Integer                                                | 报价类型   | 0-无sku按商品数量报价, 1-按sku规格报价 2-有sku按商品数量报价 |
| consignPrice    | String                                                 | 废弃     | 1                                       |
| jxhyPrice       | String                                                 | 废弃     | 1                                       |
| unitInfo        | com.alibaba.cbu.offer.model.UnitInfo                   | 单位信息   | unit                                    |
| fenxiaoSaleInfo | com.alibaba.cbu.offer.model.FenxiaoSaleInfo            | 分销销售信息 | 1                                       |

### ProductShippingInfo (商品包裹配送相关数据)

| 参数名                   | 类型                                              | 描述                                 | 示例值                                       |
|-----------------------|-------------------------------------------------|------------------------------------|-------------------------------------------|
| sendGoodsAddressText  | java.lang.String                                | 地址                                 | 地址文本                                      |
| weight                | Double                                          | 重量，单位kg                            | 200                                       |
| width                 | Double                                          | 宽，单位cm                             | 1                                         |
| height                | Double                                          | 高，单位cm                             | 3                                         |
| length                | Double                                          | 长，单位cm                             | 2                                         |
| skuShippingInfoList   | com.alibaba.cbu.offer.model.SkuShippingInfo[]   | 废弃(sku物流规格信息)，请用skuShippingDetails | sku信息                                     |
| shippingTimeGuarantee | String                                          | 发货保障                               | shipIn24Hours-24小时发货 shipIn48Hours-48小时发货 |
| skuShippingDetails    | com.alibaba.cbu.offer.model.SkuShippingDetail[] | sku件重尺寸合                           | 1                                         |
| pkgSizeSource         | String                                          | 件重尺数据来源                            | 预测-同款放大、商家自填、官方测量                         |

### TagInfoList (商品服务标签)

| 参数名   | 类型      | 描述                                    | 示例值        |
|-------|---------|---------------------------------------|------------|
| key   | String  | 服务名，isOnePsale-一件代发，select-跨境select货盘 | isOnePsale |
| value | Boolean | 是否开通                                  | true       |

### SellerMixSetting (卖家混批配置)

| 参数名          | 类型      | 描述     | 示例值  |
|--------------|---------|--------|------|
| generalHunpi | Boolean | 是否普通混批 | true |
| mixAmount    | Integer | 混批金额   | 100  |
| mixNumber    | Integer | 混批数量   | 2    |

### SellerDataInfo (商家属性数据)

| 参数名                          | 类型     | 描述          | 示例值    |
|------------------------------|--------|-------------|--------|
| tradeMedalLevel              | String | 卖家交易勋章      | 5      |
| compositeServiceScore        | String | 综合服务分       | 3.5    |
| logisticsExperienceScore     | String | 物流体验分       | 4.5    |
| disputeComplaintScore        | String | 纠纷解决分       | 3.0    |
| offerExperienceScore         | String | 商品体验分       | 4.0    |
| consultingExperienceScore    | String | 咨询体验分       | 5.0    |
| repeatPurchasePercent        | String | 卖家回头率       | 0.4666 |
| afterSalesExperienceScore    | String | 退换体验分       | 3.0    |
| collect30DayWithin48HPercent | String | 最近30天48H揽收率 | 1      |
| qualityRefundWithin30Day     | String | 最近30天品质退款率  | 2      |

### ChannelPrice (渠道价格数据)

| 参数名                 | 类型                                                        | 描述        | 示例值 |
|---------------------|-----------------------------------------------------------|-----------|-----|
| channelSkuPriceList | product.search.queryProductDetail.model.ChannelSkuPrice[] | 渠道sku价格列表 | 如下  |

### ChannelSkuPrice (渠道SKU价格)

| 参数名          | 类型     | 描述     | 示例值       |
|--------------|--------|--------|-----------|
| skuId        | Long   | sku id | 435234325 |
| currentPrice | String | 渠道价格   | 23.41     |

### PromotionModel (营销信息)

| 参数名           | 类型      | 描述    | 示例值         |
|---------------|---------|-------|-------------|
| hasPromotion  | Boolean | 是否有营销 | true        |
| promotionType | String  | 营销类型  | plus-plus会员 |

### OfferCertificateModel (商品证书信息)

| 参数名                  | 类型       | 描述   | 示例值                                             |
|----------------------|----------|------|-------------------------------------------------|
| certificateName      | String   | 证书名字 | 外观专利证书或授权书证书                                    |
| certificateCode      | String   | 证书编号 | ZL 2018 3 0658542.7                             |
| certificatePhotoList | String[] | 证书图片 | ["https://cbu01.alicdn.com/img/ibank/test.jpg"] |

### SkuAttribute (SKU属性)

| 参数名                | 类型               | 描述    | 示例值 |
|--------------------|------------------|-------|-----|
| attributeId        | java.lang.Long   | 属性ID  | 1   |
| attributeName      | java.lang.String | 属性名   | 1   |
| attributeNameTrans | java.lang.String | 属性名翻译 | 1   |
| value              | java.lang.String | 值     | 1   |
| valueTrans         | java.lang.String | 值翻译   | 1   |
| skuImageUrl        | java.lang.String | sku图片 | 1   |

### FenxiaoPriceInfo (分销价格信息)

| 参数名           | 类型     | 描述       | 示例值 |
|---------------|--------|----------|-----|
| onePiecePrice | String | 一件代发包邮价格 | 15  |
| offerPrice    | String | 分销价      | 12  |

### PriceRangeV2 (价格区间)

| 参数名            | 类型      | 描述  | 示例值 |
|----------------|---------|-----|-----|
| startQuantity  | Integer | 起批量 | 10  |
| price          | String  | 批发价 | 10  |
| promotionPrice | String  | 营销价 | 9   |

### UnitInfo (单位信息)

| 参数名       | 类型     | 描述   | 示例值 |
|-----------|--------|------|-----|
| unit      | String | 中文单位 | 盒   |
| transUnit | String | 译文单位 | Box |

### FenxiaoSaleInfo (分销销售信息)

| 参数名                 | 类型      | 描述       | 示例值  |
|---------------------|---------|----------|------|
| onePieceFreePostage | Boolean | 是否一件代发包邮 | true |
| startQuantity       | Integer | 分销起批量    | 2    |
| onePiecePrice       | String  | 一件代发包邮价  | 15   |
| offerPrice          | String  | 分销价      | 12   |

### SkuShippingInfo (SKU物流规格信息)

| 参数名    | 类型     | 描述     | 示例值                              |
|--------|--------|--------|----------------------------------|
| specId | String | 规格id   | 2b36920d5139fd431a2030090d1e2599 |
| skuId  | Long   | skuId  | *************                    |
| width  | Double | 宽,单位cm | 1                                |
| length | Double | 长,单位cm | 2                                |
| height | Double | 高,单位cm | 3                                |
| weight | Long   | 重,单位g  | 4                                |

### SkuShippingDetail (SKU件重尺寸信息)

| 参数名           | 类型     | 描述    | 示例值               |
|---------------|--------|-------|-------------------|
| skuId         | String | skuId | 12123313          |
| width         | Double | 宽, cm | 10                |
| length        | Double | 长, cm | 10                |
| height        | Double | 高, cm | 10                |
| weight        | Double | 重, kg | 1.2               |
| pkgSizeSource | String | 件重尺来源 | 预测-同款放大、商家自填、官方测量 |

## 错误码

| 错误码 | 描述     | 解决方案     |
|-----|--------|----------|
| 200 | 成功     | -        |
| 400 | 请求参数错误 | 检查参数是否正确 |
| 401 | 未授权    | 检查授权信息   |
| 500 | 系统错误   | 联系技术支持   |

## 示例代码

```java
GoodsDetailRequest request = GoodsDetailRequest.builder()
  .offerId(123456789L)
  .country("en")
  .build();

goodsService.

getGoodsDetail(request)
    .

subscribe(response ->{
  if(response.

getResult().

getSuccess()){
var detail = response.getResult().getResult();
            log.

info("商品ID: {}",detail.getOfferId());
  log.

info("商品名称: {}",detail.getSubject());
  log.

info("商品价格: {}",detail.getFenxiaoPriceInfo().

getOfferPrice());
  log.

info("最小起订量: {}",detail.getFenxiaoSaleInfo().

getStartQuantity());
  }
  });
```

## 注意事项

1. 请求时必须包含商品ID和目标语言
2. 支持的语言包括日语(ja)和英语(en)
3. 返回的商品信息会根据指定的语言返回对应的翻译内容
4. 建议使用合适的错误处理机制处理可能的异常情况

## 相关链接

- [1688国家服务站解决方案](https://open.1688.com/solution/1688solution.htm)
- [跨境代采寻源比价采购解决方案（国际化）](https://open.1688.com/solution/crossbordersolution.htm)

## 数据示例

### 请求示例

```json
{
    "offerDetailParam": {
        "offerId": 123456789,
        "country": "en",
        "outMemberId": "USER123"
    }
}
```

### 响应示例

```json
{
    "result": {
        "success": true,
        "code": "200",
        "message": "success",
        "result": {
            "offerId": 34513534353434,
            "categoryId": 32,
            "categoryName": "猫子",
            "subject": "猫砂盆超大号防外溅猫厕所加高巨无霸开放式猫砂盆厂家直销批发",
            "subjectTrans": "Cat litter box extra large splash-proof cat toilet heightened giant open pet supplies factory direct sales wholesale",
            "description": "产品详细描述",
            "mainVideo": "https://video.url",
            "detailVideo": "https://detail-video.url",
            "traceInfo": "object_id@62020139002337object_type@offer",
            "productCargoNumber": "CARGO123456",
            "promotionUrl": "https://detail.1688.com/offer/123456.html",
            "productImage": {
                "images": [
                    "https://cbu01.alicdn.com/img/ibank/O1CN01pEsVS41Bs2uny9oka_!!0-0-cib.jpg",
                    "https://cbu01.alicdn.com/img/ibank/sample2.jpg"
                ],
                "whiteImage": "https://cbu01.alicdn.com/img/ibank/white.jpg"
            },
            "productAttribute": [
                {
                    "attributeId": "1001",
                    "attributeName": "颜色",
                    "value": "蓝色",
                    "attributeNameTrans": "Color",
                    "valueTrans": "Blue"
                }
            ],
            "productSkuInfos": [
                {
                    "amountOnSale": 1000,
                    "price": "99.00",
                    "skuId": *************,
                    "specId": "2b36920d5139fd431a2030090d1e2599",
                    "skuAttributes": [
                        {
                            "attributeId": 1001,
                            "attributeName": "颜色",
                            "attributeNameTrans": "Color",
                            "value": "蓝色",
                            "valueTrans": "Blue",
                            "skuImageUrl": "https://cbu01.alicdn.com/img/ibank/sku1.jpg"
                        }
                    ],
                    "cargoNumber": "SKU001",
                    "promotionPrice": "89.00",
                    "fenxiaoPriceInfo": {
                        "onePiecePrice": "15.00",
                        "offerPrice": "12.00"
                    }
                }
            ],
            "productSaleInfo": {
                "amountOnSale": 99999,
                "priceRangeList": [
                    {
                        "startQuantity": 10,
                        "price": "10.00",
                        "promotionPrice": "9.00"
                    }
                ],
                "quoteType": 1,
                "unitInfo": {
                    "unit": "盒",
                    "transUnit": "Box"
                },
                "fenxiaoSaleInfo": {
                    "onePieceFreePostage": true,
                    "startQuantity": 2,
                    "onePiecePrice": "15.00",
                    "offerPrice": "12.00"
                }
            },
            "productShippingInfo": {
                "sendGoodsAddressText": "浙江省杭州市",
                "weight": 200,
                "width": 1,
                "height": 3,
                "length": 2,
                "shippingTimeGuarantee": "shipIn24Hours",
                "skuShippingDetails": [
                    {
                        "skuId": "12123313",
                        "width": 10,
                        "length": 10,
                        "height": 10,
                        "weight": 1.2,
                        "pkgSizeSource": "商家自填"
                    }
                ]
            },
            "isJxhy": true,
            "sellerOpenId": "23tsdvcdsjngp3o4j3i5",
            "minOrderQuantity": 1,
            "batchNumber": 200,
            "status": "published",
            "tagInfoList": [
                {
                    "key": "isOnePsale",
                    "value": true
                }
            ],
            "sellerMixSetting": {
                "generalHunpi": true,
                "mixAmount": 100,
                "mixNumber": 2
            },
            "sellerDataInfo": {
                "tradeMedalLevel": "5",
                "compositeServiceScore": "3.5",
                "logisticsExperienceScore": "4.5",
                "disputeComplaintScore": "3.0",
                "offerExperienceScore": "4.0",
                "consultingExperienceScore": "5.0",
                "repeatPurchasePercent": "0.4666",
                "afterSalesExperienceScore": "3.0",
                "collect30DayWithin48HPercent": "1",
                "qualityRefundWithin30Day": "2"
            },
            "soldOut": "12342",
            "channelPrice": {
                "channelSkuPriceList": [
                    {
                        "skuId": 435234325,
                        "currentPrice": "23.41"
                    }
                ]
            },
            "promotionModel": {
                "hasPromotion": true,
                "promotionType": "plus-plus会员"
            },
            "tradeScore": "5.0",
            "topCategoryId": 1,
            "secondCategoryId": 2,
            "thirdCategoryId": 3,
            "sellingPoint": [
                "质量",
                "配送快速"
            ],
            "offerIdentities": [
                "超级工厂",
                "实力商家",
                "诚信通"
            ],
            "createDate": "2025-01-15 00:00:00",
            "isSelect": "true",
            "certificateList": [
                {
                    "certificateName": "外观专利证书或授权书证书",
                    "certificateCode": "ZL 2018 3 0658542.7",
                    "certificatePhotoList": [
                        "https://cbu01.alicdn.com/img/ibank/test.jpg"
                    ]
                }
            ]
        }
    }
}
```
