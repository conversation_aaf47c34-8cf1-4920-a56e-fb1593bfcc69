## 商品推荐

基于用户画像的千人千面商品推荐API，提供个性化的商品推荐服务。

### 所属解决方案/能力

- 跨境代采寻源比价搜索解决方案（国际化）
- 1688国家服务站解决方案
- 跨境ERP/独立站SaaS数字化解决方案
- 跨境营销SaaS数字化解决方案
- 跨境超买寻源比价搜索解决方案
- 跨境大客户寻源通解决方案

### 请求URL

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao.crossborder/product.search.offerRecommend/${APPKEY}
```

### 系统级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| _aop_timestamp | String | 否 | 请求时间戳 | - |
| _aop_signature | String | 是 | 请求签名 | - |
| access_token | String | 是 | 用户授权令牌 | - |

### 应用级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| recommendOfferParam | alibaba.cbu.offer.param.RecommendOfferParam | 是 | 推荐参数对象 | - |

#### recommendOfferParam 字段说明

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| beginPage | Integer | 是 | 开始页码，从1开始 | 1 |
| pageSize | Integer | 是 | 每页数量，建议20 | 20 |
| country | String | 是 | 语言代码 | "en" |
| outMemberId | String | 是 | 外部机构用户id | "dferg0001" |

### 返回结果

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| result | com.alibaba.openapi.shared.common.recommend.ResultModel | 返回信息 | - |

#### result 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| success | Boolean | 是否成功 | true |
| code | String | 状态码 | "200" |
| message | String | 提示信息 | "success" |
| result | com.alibaba.cbu.offer.recommend.model.ProductInfoModel[] | 商品列表 | - |

#### result.result 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|----------|------|
| imageUrl | String | 商品图片URL | "<https://global-img-cdn.1688.com/img/ibank/O1CN01nwtZUD1iKV0m6pNWP_!!**********-0-cib.jpg>" |
| subject | String | 中文标题 | "包邮火龙果色阔腿裤女春秋季2022年新款高腰垂感显瘦提花加长直筒" |
| subjectTrans | String | 外文标题 | "Postage Dragon Fruit Color Wide Leg Pants Women's Spring and Autumn 2022 New High Waist Draping Slim Jacquard Lengthened Straight" |
| offerId | Long | 商品ID | ************ |
| monthSold | Integer | 月销量 | 200 |
| repurchaseRate | String | 复购率 | "20%" |
| traceInfo | String | 打点信息 | "12eqweqweqwe1212" |
| isSelect | Boolean | 跨境select货盘 | true |
| promotionURL | String | 具有【AI跨境运营助手】模块的1688商品详情页链接 | "<https://detail.1688.com/offer/************.html>" |
| sellerIdentities | String[] | 商家身份标识 | ["powerful_merchants"] |
| priceInfo | com.alibaba.cbu.offer.model.PriceInfo | 价格信息 | - |

#### result.result.priceInfo 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|----------|------|
| price | String | 批发价 | "200.00" |
| consignPrice | String | 代发价 | "1002.00" |

### 语言代码说明

country 参数支持的语言代码：

- en: 英语
- ja: 日语
- 更多语言代码请参考开发人员参考菜单

### 注意事项

1. pageSize 建议设置为20，可获得最佳效果
2. 返回结果会根据country参数返回对应语言的商品信息
3. 推荐结果基于用户画像和行为数据，相同参数可能返回不同结果
4. 商家身份标识可能包含：
   - powerful_merchants: 实力商家
   - super_factory: 超级工厂
