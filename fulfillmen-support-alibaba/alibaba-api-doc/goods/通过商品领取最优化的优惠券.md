## 通过商品领取最优化的优惠券

通过商品ID列表领取最优惠券组合，一般在下单前调用该接口，完成最优化的领取优惠券策略。

### 所属解决方案/能力

- 分销工具解决方案
- 代发解决方案（分销买家版）
- 代发解决方案（服务商版）
- 跨境代采寻源比价搜索解决方案（国际化）
- 跨境超买寻源比价搜索解决方案
- 跨境ERP/独立站SaaS数字化解决方案

### 请求URL

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.marketing/coupon.optimal.claim/${APPKEY}
```

### 系统级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| _aop_timestamp | String | 否 | 请求时间戳 | - |
| _aop_signature | String | 是 | 请求签名 | - |
| access_token | String | 是 | 用户授权令牌 | - |

### 应用级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| offerIds | Long[] | 是 | 商品ID列表 | [24910983123, 2799731973] |

### 返回结果

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| result | alibaba.openapi.shared.common.coupon.optimal.claim.ResultModel | 返回信息 | - |

#### result 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| success | Boolean | 是否成功 | true |
| code | String | 状态码 | "200" |
| message | String | 提示信息 | "success" |
| result | alibaba.ocean.openplatform.biz.market.result.BestBizCouponGetResult | 优惠券结果 | - |

#### result.result 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| couponIds | String[] | 优惠券ID列表 | ["123456", "789012"] |

### 使用场景

1. 下单前领取优惠券
2. 批量商品的优惠券领取
3. 自动获取最优惠券组合

### 注意事项

1. 接口会自动分析商品可用的优惠券，并返回最优组合
2. 优惠券领取成功后会立即生效
3. 同一个商品的优惠券不能重复领取
4. 建议在确认购买意向后再调用此接口
5. 返回的优惠券ID可直接用于下单接口
