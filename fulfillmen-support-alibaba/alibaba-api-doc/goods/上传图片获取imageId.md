## 上传图片获取imageId

通过上传图片获取1688平台的imageId，用于后续的商品图片相关API调用。

### 所属解决方案/能力

- 1688国家服务站解决方案
- 跨境代采寻源比价搜索解决方案（国际化）
- 跨境ERP/独立站SaaS数字化解决方案
- 跨境营销SaaS数字化解决方案
- 跨境超买寻源比价搜索解决方案
- 跨境大客户寻源通解决方案
- 1688跨境电商采购助手（LP）解决方案

### 请求URL

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao.crossborder/product.image.upload/${APPKEY}
```

### 系统级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| _aop_timestamp | String | 否 | 请求时间戳 | - |
| _aop_signature | String | 是 | 请求签名 | - |
| access_token | String | 是 | 用户授权令牌 | - |

### 应用级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| uploadImageParam | product.image.upload.param.UploadImageParam | 是 | 上传图片参数对象 | - |

#### uploadImageParam 字段说明

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| imageBase64 | String | 是 | 图片base64编码字符串 | "iVBORw0KGgoAAAANSUhEUgAA..." |
| outMemberId | String | 否 | 外部用户id | "12345" |

### 返回结果

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| result | product.image.upload.result.ResultModel | 返回信息 | - |

#### result 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| success | Boolean | 是否成功 | true |
| code | String | 状态码 | "200" |
| message | String | 提示信息 | "success" |
| result | String | 图片ID | "1234567890" |

### 注意事项

1. 图片base64字符串不要包含前缀(如:"data:image/jpeg;base64,")
2. 建议上传图片大小不超过2MB
3. 支持jpg、png、gif等常见图片格式
