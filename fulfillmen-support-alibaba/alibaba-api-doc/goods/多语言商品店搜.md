## 多语言商品店搜

通过店铺ID搜索商家商品列表，支持多语言搜索和返回。

### 所属解决方案/能力

- 跨境代采寻源比价搜索解决方案（国际化）
- 1688国家服务站解决方案
- 跨境ERP/独立站SaaS数字化解决方案
- 跨境营销SaaS数字化解决方案
- 跨境超买寻源比价搜索解决方案

### 请求URL

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao.crossborder/product.search.querySellerOfferList/${APPKEY}
```

### 系统级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| _aop_timestamp | String | 否 | 请求时间戳 | - |
| _aop_signature | String | 是 | 请求签名 | - |
| access_token | String | 是 | 用户授权令牌 | - |

### 应用级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| offerQueryParam | product.search.querySellerOfferList.param.OfferQueryParam | 是 | 查询参数对象 | - |

#### offerQueryParam 字段说明

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| keyword | String | 否 | 搜索关键词 | "饼干" |
| beginPage | Integer | 是 | 分页页码，从1开始 | 1 |
| pageSize | Integer | 是 | 每页数量，最大50，建议20 | 20 |
| filter | String | 否 | 筛选参数，多个用英文逗号分隔 | "shipInToday,ksCiphertext" |
| sort | String | 否 | 排序参数 | {"price":"asc"} |
| outMemberId | String | 否 | 外部用户id | "123" |
| priceStart | String | 否 | 批发价开始 | "1" |
| priceEnd | String | 否 | 批发价结束 | "10" |
| categoryId | Long | 否 | 类目id | 1 |
| country | String | 是 | 语言代码 | "japan" |
| sellerOpenId | String | 是 | 商家店铺id脱敏 | "123456789" |

### 返回结果

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| result | product.search.querySellerOfferList.result.ResultModelV3 | 返回信息 | - |

#### result 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| success | Boolean | 是否成功 | true |
| code | String | 状态码 | "200" |
| message | String | 提示信息 | "success" |
| result | product.search.querySellerOfferList.model.PageInfoV3 | 分页结果对象 | - |

#### result.result 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| totalRecords | Integer | 总记录数 | 100 |
| totalPage | Integer | 总页数 | 5 |
| pageSize | Integer | 每页数量 | 20 |
| currentPage | Integer | 当前页码 | 1 |
| data | product.search.querySellerOfferList.model.ProductInfoModelV2[] | 商品数据列表 | - |

#### result.result.data 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| imageUrl | String | 图片地址 | "<https://example.com/image.jpg>" |
| subject | String | 中文标题 | "2024新款饼干" |
| subjectTrans | String | 外文标题 | "2024 New Cookies" |
| offerId | Long | 商品id | 620201390233 |
| isJxhy | Boolean | 是否精选货源 | true |
| repurchaseRate | String | 复购率 | "10%" |
| monthSold | Integer | 30天销量 | 1213 |
| traceInfo | String | 向1688上报打点数据 | "object_id@620201390233^object_type@offer" |
| isOnePsale | Boolean | 是否一件代发 | true |
| priceInfo | product.search.querySellerOfferList.model.PriceInfoV2 | 价格信息 | - |
| createDate | String | 商品创建时间 | "2024-04-20 08:00:00" |
| modifyDate | String | 商品修改时间 | "2024-04-20 08:00:00" |
| isPatentProduct | Boolean | 是否为专利商品 | true |
| offerIdentities | String[] | 商品标识 | ["select"] |
| isSelect | String | 跨境select货盘 | "true" |
| token | String | 插件返佣token | "5L9Z3683O2iVIvwFxNdqnUlMVbKYHWGWcjs" |
| promotionURL | String | 具有【AI跨境运营助手】模块的1688商品详情页链接 | "<https://detail.1688.com/offer/620201390233.html>" |

#### result.result.data.priceInfo 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|----------|------|---------|
| price | String | 批发价 | "99.00" |
| jxhyPrice | String | 代发精选货源价 | "89.00" |
| pfJxhyPrice | String | 批发精选货源价 | "79.00" |
| consignPrice | String | 一件代发价 | "109.00" |

### 筛选参数说明

filter参数支持的值：

- shipInToday: 24小时发货
- ksCiphertext: 跨境密文
- 更多筛选参数请参考解决方案介绍

### 排序参数说明

sort参数支持的格式：

```json
{
    "price": "asc/desc",    // 按价格升序/降序
    "sales": "desc",        // 按销量降序
    "credit": "desc"        // 按信用降序
}
```

### 语言代码说明

country 参数支持的语言代码：

- japan: 日语
- en: 英语
- 更多语言代码请参考开发人员参考菜单

### 注意事项

1. pageSize 建议设置为20，可获得最佳效果
2. 关键词搜索支持中文和外文
3. 返回结果会根据country参数返回对应语言的商品信息
4. filter 和 sort 参数的具体枚举值请参考解决方案介绍文档
