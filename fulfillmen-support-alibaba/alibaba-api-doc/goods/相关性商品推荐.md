## 相关性商品推荐

基于商品ID推荐相关商品，支持多语言返回。

### 所属解决方案/能力

- 跨境ERP/独立站SaaS数字化解决方案
- 跨境代采寻源比价搜索解决方案（国际化）
- 跨境营销SaaS数字化解决方案
- 跨境超买寻源比价搜索解决方案

### 请求URL

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao.crossborder/product.related.recommend/${APPKEY}
```

### 系统级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| _aop_timestamp | String | 否 | 请求时间戳 | - |
| _aop_signature | String | 是 | 请求签名 | - |
| access_token | String | 是 | 用户授权令牌 | - |

### 应用级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| relatedQueryParams | product.related.recommend.RelatedQueryParams | 是 | 推荐查询参数对象 | - |

#### relatedQueryParams 字段说明

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| offerId | Long | 是 | 商品ID | 689337673960 |
| pageNo | Integer | 是 | 页号，从1开始 | 1 |
| pageSize | Integer | 是 | 每页数量，最大10 | 10 |
| language | String | 是 | 语言代码 | "en" |

### 返回结果

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| result | product.related.recommend.ResultModel | 返回信息 | - |

#### result 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| success | Boolean | 是否成功 | true |
| code | String | 状态码 | "S0000" |
| message | String | 提示信息 | "成功" |
| result | product.related.recommend.ProductInfoModel[] | 商品列表 | - |

#### result.result 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| imageUrl | String | 图片链接 | "<https://cbu01.alicdn.com/img/ibank/O1CN01IEn8mi1i9xzXcd2GU_!!*************-0-cib.jpg>" |
| subject | String | 中文标题 | "跨境自制果蔬全自动面膜机diy英文版带语音智能美容仪" |
| subjectTrans | String | 译文标题 | "Cross-border homemade fruit and vegetable automatic mask machine diy English version" |
| offerId | Long | 商品ID | ************ |
| monthSold | Integer | 最近90天销售件数 | 98374 |
| sellerIdentities | String[] | 商家标识 | ["super_factory"] |
| offerIdentities | String[] | 商品标识 | ["yx"] |
| topCategoryId | Long | 一级类目ID | 1 |
| secondCategoryId | Long | 二级类目ID | 1 |
| thirdCategoryId | Long | 三级类目ID | 1 |
| priceInfo | product.related.recommend.PriceInfo | 价格信息 | - |

#### result.result.priceInfo 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| price | String | 商品批发价格最低价 | "178.00" |

### 语言代码说明

language 参数支持的语言代码：

- en: 英语
- ja: 日语
- 更多语言代码请参考开发人员参考菜单

### 商家标识说明

sellerIdentities 可能的值：

- super_factory: 超级工厂
- powerful_merchants: 实力商家

### 商品标识说明

offerIdentities 可能的值：

- yx: 优选商品
- select: 精选商品

### 注意事项

1. pageSize 最大值为10，超过将被限制
2. 返回结果会根据language参数返回对应语言的商品信息
3. monthSold 表示最近90天的销售数据
4. 推荐结果基于商品相关性算法，相同参数可能返回不同结果
5. 建议合理设置缓存策略，避免频繁调用
