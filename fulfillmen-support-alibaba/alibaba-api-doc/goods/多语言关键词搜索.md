## 多语言关键词搜索

通过关键词搜索商品，支持多语言搜索和返回。

### 所属解决方案/能力

- 1688国家服务站解决方案
- 跨境代采寻源比价搜索解决方案（国际化）
- 跨境ERP/独立站SaaS数字化解决方案
- 跨境营销SaaS数字化解决方案
- 跨境超买寻源比价搜索解决方案
- 跨境大客户寻源通解决方案

### 请求URL

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao.crossborder/product.search.keywordQuery/${APPKEY}
```

### 系统级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| _aop_timestamp | String | 否 | 请求时间戳 | - |
| _aop_signature | String | 是 | 请求签名 | - |
| access_token | String | 是 | 用户授权令牌 | - |

### 应用级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| offerQueryParam | product.search.keywordQuery.param.OfferQueryParam | 是 | 查询参数对象 | - |

#### offerQueryParam 字段说明

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| keyword | String | 是 | 搜索关键词 | "饼干" |
| beginPage | Integer | 是 | 分页页码，从1开始 | 1 |
| pageSize | Integer | 是 | 每页数量，最大50，建议20 | 20 |
| filter | String | 否 | 筛选参数，多个用英文逗号分隔 | "shipInToday,ksCiphertext" |
| sort | String | 否 | 排序参数 | {"price":"asc"} |
| outMemberId | String | 否 | 外部用户id | "123" |
| priceStart | String | 否 | 批发价开始 | "1" |
| priceEnd | String | 否 | 批发价结束 | "10" |
| categoryId | Long | 否 | 类目id | 1 |
| categoryIdList | String | 否 | 类目id列表，英文逗号分隔 | "2,45" |
| country | String | 是 | 语言代码 | "en" |
| regionOpp | String | 否 | 商机 | - |
| productCollectionId | String | 否 | 寻源通工作台货盘id | "174316138" |
| snId | String | 否 | 搜索导航ID | "978:1352" |
| keywordTranslate | Boolean | 否 | 关键词是否已翻译，默认false | false |

### 返回结果

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| result | product.search.keywordQuery.result.ResultModelV3 | 返回信息 | - |

#### result 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| success | Boolean | 是否成功 | true |
| code | String | 状态码 | "200" |
| message | String | 提示信息 | "success" |
| result | product.search.keywordQuery.model.PageInfoV3 | 分页结果对象 | - |

#### result.result 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------| 
| totalRecords | Integer | 总记录数 | 100 |
| totalPage | Integer | 总页数 | 5 |
| pageSize | Integer | 每页数量 | 20 |
| currentPage | Integer | 当前页码 | 1 |
| data | product.search.keywordQuery.model.ProductInfoModelV2[] | 商品数据列表 | - |

#### result.result.data 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| imageUrl | String | 图片地址 | "<https://example.com/image.jpg>" |
| subject | String | 中文标题 | "2024新款饼干" |
| subjectTrans | String | 外文标题 | "2024 New Cookies" |
| offerId | Long | 商品id | 620201390233 |
| isJxhy | Boolean | 是否精选货源 | true |
| priceInfo | product.search.keywordQuery.model.PriceInfoV2 | 价格信息 | - |
| repurchaseRate | String | 复购率 | "10%" |
| monthSold | Integer | 30天销量 | 1213 |
| traceInfo | String | 向1688上报打点数据 | "object_id@620201390233^object_type@offer" |
| isOnePsale | Boolean | 是否一件代发 | true |
| sellerIdentities | String[] | 商家身份 | ["super_factory", "powerful_merchants"] |
| offerIdentities | String[] | 商品标识 | ["yx", "select"] |
| tradeScore | String | 商品交易评分 | "5.0" |
| whiteImage | String | 商品白底图 | "<https://example.com/white.jpg>" |
| promotionModel | product.search.keywordQuery.model.PromotionModelV2 | 营销信息 | - |
| topCategoryId | Long | 一级类目 | 1 |
| secondCategoryId | Long | 二级类目 | 2 |
| thirdCategoryId | Long | 三级类目 | 3 |
| isPatentProduct | Boolean | 是否专利商品 | true |
| createDate | String | 商品上架时间 | "2024-04-20 08:00:00" |
| modifyDate | String | 商品修改时间 | "2024-04-20 08:00:00" |
| isSelect | Boolean | 跨境select货盘 | true |
| minOrderQuantity | Integer | 最小起批量 | 1 |
| sellerDataInfo | product.search.keywordQuery.model.SellerDataInfoV1 | 商品数据 | - |
| productSimpleShippingInfo | product.search.keywordQuery.model.ProductSimpleShippingInfo | 简略发货信息 | - |
| token | String | 插件返佣token | "5L9Z3683O2iVIvwFxNdqnUlMVbKYHWGWcjs" |
| promotionURL | String | 具有【AI跨境运营助手】模块的1688商品详情页链接 | "<https://detail.1688.com/offer/620201390233.html>" |

#### priceInfo 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| price | String | 批发价 | "99.00" |
| jxhyPrice | String | 代发精选货源价 | "89.00" |
| pfJxhyPrice | String | 批发精选货源价 | "79.00" |
| consignPrice | String | 一件代发价 | "109.00" |
| promotionPrice | String | 营销价 | "69.00" |

#### promotionModel 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| hasPromotion | Boolean | 是否有营销 | true |
| promotionType | String | 营销类型 | "plus" |

#### sellerDataInfo 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| tradeMedalLevel | String | 卖家交易勋章等级 | "5" |
| compositeServiceScore | String | 综合服务体验分 | "4.8" |
| logisticsExperienceScore | String | 物流体验分 | "4.7" |
| disputeComplaintScore | String | 纠纷投诉处理分 | "4.9" |
| offerExperienceScore | String | 商品体验分 | "4.8" |
| afterSalesExperienceScore | String | 售后体验分 | "4.6" |
| consultingExperienceScore | String | 咨询体验分 | "4.7" |
| repeatPurchasePercent | String | 重复购买率 | "35%" |

#### productSimpleShippingInfo 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| shippingTimeGuarantee | String | 发货时效(shipIn24Hours-24小时发货 shipIn48Hours-48小时发货) | "shipIn24Hours" |

### 筛选参数说明

filter参数支持的值：

- shipInToday: 24小时发货
- ksCiphertext: 跨境密文
- 更多筛选参数请参考解决方案介绍

### 排序参数说明

sort参数支持的格式：

```json
{
    "price": "asc/desc",    // 按价格升序/降序
    "sales": "desc",        // 按销量降序
    "credit": "desc"        // 按信用降序
}
```

### 语言代码说明

country 参数支持的语言代码：

- en: 英语
- 更多语言代码请参考开发人员参考菜单

### 注意事项

1. pageSize 建议设置为20，可获得最佳效果
2. 关键词搜索支持中文和英文
3. 返回结果会根据language参数返回对应语言的商品信息
4. filter 和 sort 参数的具体枚举值请参考解决方案介绍文档
