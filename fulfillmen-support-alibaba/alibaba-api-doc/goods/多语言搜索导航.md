## 多语言搜索导航

获取多语言关键词搜索导航列表，支持不同语言、地区和币种的搜索导航信息。

### 所属解决方案/能力

- 1688国家服务站解决方案
- 跨境代采寻源比价搜索解决方案（国际化）
- 跨境ERP/独立站SaaS数字化解决方案
- 跨境营销SaaS数字化解决方案
- 跨境超买寻源比价搜索解决方案

### 请求URL

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao.crossborder/product.search.keywordSNQuery/${APPKEY}
```

### 系统级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| _aop_timestamp | String | 否 | 请求时间戳 | - |
| _aop_signature | String | 是 | 请求签名 | - |
| access_token | String | 是 | 用户授权令牌 | - |

### 应用级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| snParams | product.search.keywordSNQuery.KeywordSNQueryParams | 是 | 搜索导航参数对象 | - |

#### snParams 字段说明

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| keyword | String | 是 | 搜索关键词 | "skirt" |
| language | String | 是 | 语言代码 | "en_US" |
| region | String | 是 | 地区代码 | "US" |
| currency | String | 是 | 币种代码 | "USD" |

### 返回结果

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| result | product.search.keywordSNQuery.CommonResult | 返回信息 | - |

#### result 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| success | Boolean | 是否成功 | true |
| retCode | String | 状态码 | "S0000" |
| retMsg | String | 提示信息 | "成功" |
| result | product.search.keywordSNQuery.KeywordSNModel[] | 导航列表 | - |

#### result.result 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| id | String | 搜索主导航ID | "973" |
| name | String | 搜索主导航中文名称 | "风格" |
| translateName | String | 搜索主导航译文名称 | "Style" |
| children | product.search.keywordSNQuery.KeywordSubSNModel[] | 子导航列表 | - |

#### result.result.children 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| id | String | 搜索子导航ID | "973:28105" |
| name | String | 搜索子导航中文名称 | "韩版" |
| translateName | String | 搜索子导航译文名称 | "Korean version" |

### 语言代码说明

language 参数支持的语言代码：

- en_US: 英语（美国）
- ja_JP: 日语（日本）
- 更多语言代码请参考开发人员参考菜单

### 地区代码说明

region 参数支持的地区代码：

- US: 美国
- JP: 日本
- 更多地区代码请参考开发人员参考菜单

### 币种代码说明

currency 参数支持的币种代码：

- USD: 美元
- JPY: 日元
- 更多币种代码请参考开发人员参考菜单

### 注意事项

1. 返回结果会根据language参数返回对应语言的导航信息
2. 导航ID格式：主导航ID为纯数字，子导航ID为"主导航ID:子导航ID"
3. 建议缓存导航信息，避免频繁调用
4. 确保language、region、currency参数的对应关系正确
