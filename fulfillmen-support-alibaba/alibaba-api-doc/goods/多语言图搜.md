## 多语言图搜

通过图片搜索商品，支持多语言搜索。

### 所属解决方案/能力

- 1688国家服务站解决方案
- 跨境代采寻源比价搜索解决方案（国际化）
- 跨境ERP/独立站SaaS数字化解决方案
- 跨境大客户寻源通解决方案
- 跨境营销SaaS数字化解决方案
- 跨境超买寻源比价搜索解决方案

### 请求URL

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao.crossborder/product.search.imageQuery/${APPKEY}
```

### 系统级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| _aop_timestamp | String | 否 | 请求时间戳 | - |
| _aop_signature | String | 是 | 请求签名 | - |
| access_token | String | 是 | 用户授权令牌 | - |

### 应用级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| offerQueryParam | product.search.imageQuery.param.OfferQueryParam | 是 | 查询参数对象 | - |

#### offerQueryParam 字段说明

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| imageId | String | 是 | 图片id，必传 | "12345678" |
| beginPage | Integer | 是 | 分页页码 | 1 |
| pageSize | Integer | 是 | 每页数量，最大不超过50，建议20效果最佳 | 20 |
| region | String | 否 | 主体选择 | "266,799,48,581" |
| filter | String | 否 | 筛选参数，多个通过英文逗号分隔，枚举参见解决方案介绍 | "shipInToday,ksCiphertext" |
| sort | String | 否 | 排序参数，枚举参见解决方案介绍 | {"price":"asc"} |
| outMemberId | String | 否 | 外部用户uid | "user123" |
| priceStart | String | 否 | 批发价开始 | "10" |
| priceEnd | String | 否 | 批发价结束 | "20" |
| categoryId | Long | 否 | 类目id | 1234 |
| imageAddress | String | 否 | 图片地址，仅使用1688图片链接查询场景，其他不保证有数据返回 | "<https://example.com/image.jpg>" |
| country | String | 是 | 语言代码 | "en" |
| keyword | String | 否 | 在结果中搜索 | "书本" |
| auxiliaryText | String | 否 | 多模态图搜文案 | "热卖的" |
| productCollectionId | String | 否 | 寻源通工作台货盘ID | "21432232" |
| keywordTranslate | Boolean | 否 | 搜索词是否已经翻译，true的话直接搜索，不翻译关键词 | false |

### 返回结果

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| result | product.search.imageQuery.result.ResultModelV5 | 返回值对象 | - |

#### result 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| success | String | 是否成功 | "true" |
| code | String | 返回码 | "200" |
| message | String | 返回信息 | "success" |
| result | product.search.imageQuery.model.PageInfoV4 | 分页结果对象 | - |

#### result.result 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| totalRecords | Integer | 总数量 | 100 |
| totalPage | Integer | 总页数 | 5 |
| pageSize | Integer | 每页数量 | 20 |
| currentPage | Integer | 当前页码 | 1 |
| data | product.search.imageQuery.model.ProductInfoModelV3[] | 商品数据列表 | - |
| picRegionInfo | com.alibaba.cbu.offer.model.PicRegionInfo | 主体信息 | {"currentRegion":"265,597,326,764","yoloCropRegion":"265,597,326,764;443,783,154,595"} |

#### result.result.data 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| imageUrl | String | 图片url | "<https://example.com/image.jpg>" |
| subject | String | 标题 | "2024新款连衣裙" |
| subjectTrans | String | 多语言标题 | "2024 New Dress" |
| priceInfo | product.search.imageQuery.model.PriceInfoV3 | 价格信息 | - |
| offerId | Long | 商品id | 620201390233 |
| isJxhy | Boolean | 是否精选货源 | true |
| repurchaseRate | String | 复购率 | "13%" |
| monthSold | Integer | 30天销量 | 1234 |
| traceInfo | String | 向1688上报打点数据 | "object_id@620201390233^object_type@offer" |
| isOnePsale | Boolean | 是否一件代发 | true |
| sellerIdentities | String[] | 商家身份 | ["super_factory", "powerful_merchants", "tp_member"] |
| offerIdentities | String[] | 商品标识 | ["yx"] |
| tradeScore | String | 商品交易评分 | "5.0" |
| promotionModel | product.search.imageQuery.model.PromotionModelV2 | 营销信息 | - |
| topCategoryId | Long | 一级类目 | 1 |
| secondCategoryId | Long | 二级类目 | 2 |
| thirdCategoryId | Long | 三级类目 | 3 |
| isPatentProduct | Boolean | 是否为专利商品 | true |
| createDate | String | 商品创建时间 | "2024-04-20 08:00:00" |
| modifyDate | String | 商品修改时间 | "2024-04-20 08:00:00" |
| isSelect | Boolean | 跨境select货盘 | true |
| sellerDataInfo | product.search.imageQuery.model.SellerDataInfoV1 | 商品数据 | - |
| productSimpleShippingInfo | product.search.imageQuery.model.ProductSimpleShippingInfo | 简略发货信息 | - |
| minOrderQuantity | Integer | 最小起批量 | 1 |
| token | String | 插件同店返佣标识 | "5L9Z3683O2iVIvwFxNdqnUlMVbKYHWGWcjs" |
| promotionURL | String | 具有【AI跨境运营助手】模块的1688商品详情页链接 | "<https://detail.1688.com/offer/620201390233.html>" |
| picRegionInfo | com.alibaba.cbu.offer.model.PicRegionInfo | 主体信息 | - |

#### result.result.data.priceInfo 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| price | String | 批发价 | "99.00" |
| jxhyPrice | String | 代发精选货源价 | "89.00" |
| pfJxhyPrice | String | 批发精选货源价 | "79.00" |
| consignPrice | String | 一件代发价，当isOnePsale=true时有效 | "109.00" |
| promotionPrice | String | 营销价 | "69.00" |

#### result.result.data.promotionModel 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| hasPromotion | Boolean | 是否有营销 | true |
| promotionType | String | 营销类型 | "plus" |

#### result.result.data.sellerDataInfo 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| tradeMedalLevel | String | 卖家交易勋章等级 | "5" |
| compositeServiceScore | String | 综合服务体验分 | "4.8" |
| logisticsExperienceScore | String | 物流体验分 | "4.7" |
| disputeComplaintScore | String | 纠纷投诉处理分 | "4.9" |
| offerExperienceScore | String | 商品体验分 | "4.8" |
| afterSalesExperienceScore | String | 售后体验分 | "4.6" |
| consultingExperienceScore | String | 咨询体验分 | "4.7" |
| repeatPurchasePercent | String | 重复购买率 | "35%" |

#### result.result.data.productSimpleShippingInfo 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| shippingTimeGuarantee | String | 发货时效(shipIn24Hours-24小时发货 shipIn48Hours-48小时发货) | "shipIn24Hours" |

#### result.result.picRegionInfo 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| currentRegion | String | 当前主体坐标(x0,y0,x1,y1) | "265,597,326,764" |
| yoloCropRegion | String | 所有主体坐标(x0,y0,x1,y1;x0,y0,x1,y1) | "265,597,326,764;443,783,154,595" |

### 语言代码说明

country 参数支持的语言代码：

- en: 英语
- 更多语言代码请参考开发人员参考菜单

### 注意事项

1. pageSize 建议设置为20，可获得最佳效果
2. imageAddress 参数仅支持1688图片链接
3. filter 和 sort 参数的具体枚举值请参考解决方案介绍文档
