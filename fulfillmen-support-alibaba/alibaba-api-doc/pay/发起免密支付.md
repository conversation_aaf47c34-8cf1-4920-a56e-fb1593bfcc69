# 发起免密支付

com.alibaba.trade:alibaba.trade.pay.protocolPay.preparePay-1

## 接口说明

发起免密支付，会自动判断是否开通了支付宝免密签约的免密支付，并发起扣款，优先发起诚E赊自动扣款，如果失败，则尝试支付宝自动扣款，该接口自动返回错误不详，在发起扣款失败后，建议重试3次，不要无限制重试。

## 所属解决方案/能力

- 分销工具解决方案
- ERP分销解决方案（分销商用）
- 采购解决方案（买家自用版）
- 代采解决方案（服务商版）
- 1688分销产品采购解决方案（服务商版）
- 1688分销产品采购解决方案（分销采购版）
- 代采解决方案（分销采购版）
- 1688国家级分站解决方案
- 跨境代采寻源比价搜索解决方案（国际化）
- 跨境超买寻源比价搜索解决方案
- 跨境ERP独立站SaaS数字化解决方案
- 跨境大客户寻源通解决方案
- 采购解决方案（服务商版）

## 接口地址

- 请求方式：POST
- URL：`https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.pay.protocolPay.preparePay/${APPKEY}`

## 系统级入参

| 名称             | 类型     | 是否必须 | 描述     | 文档地址       |
|----------------|--------|------|--------|------------|
| _aop_timestamp | String | 否    | 请求时间戳  | [查看](文档地址) |
| _aop_signature | String | 是    | 请求签名   | [查看](文档地址) |
| access_token   | String | 是    | 用户授权令牌 | [查看](文档地址) |

## 应用级入参

| 名称                           | 类型     | 是否必须 | 描述     | 示例值 |
|------------------------------|--------|------|--------|-----|
| tradeWithholdPreparePayParam | Object | 是    | 发起免密支付 | -   |

### tradeWithholdPreparePayParam 数据结构

| 名称          | 类型             | 是否必须 | 描述             | 示例值        |
|-------------|----------------|------|----------------|------------|
| orderId     | java.lang.Long | 是    | 订单ID           | 1938489823 |
| payChannel  | String         | 否    | 跨境宝支付传入kjpayV2 | kjpayV2    |
| payAmount   | Long           | 否    | 付款总金额,单位分      | 123        |
| opRequestId | String         | 否    | requestId      | 134134134  |

## 返回结果

| 名称     | 类型     | 描述     | 示例值 |
|--------|--------|--------|-----|
| result | Object | 返回结果对象 | {}  |

### result 数据结构

| 名称      | 类型      | 描述     | 示例值  |
|---------|---------|--------|------|
| result  | Object  | 免密支付结果 | {}   |
| code    | String  | 错误码    | null |
| message | String  | 错误消息   | null |
| success | Boolean | 是否成功   | true |

### result.result 数据结构

| 名称         | 类型      | 描述                                              | 示例值    |
|------------|---------|-------------------------------------------------|--------|
| payChannel | String  | 支付成功通道                                          | Alipay |
| paySuccess | Boolean | 支付是否成功,在超时的情况下，可能返回false但实际扣款成功的情况，需要查询订单实际支付状态 | true   |

## 请求示例

```json
{
    "_aop_timestamp": "1641830400000",
    "_aop_signature": "MOCK_SIGNATURE",
    "access_token": "MOCK_ACCESS_TOKEN",
    "tradeWithholdPreparePayParam": {
        "orderId": 1938489823,
        "payChannel": "kjpayV2",
        "payAmount": 123,
        "opRequestId": "134134134"
    }
}
```

## 返回示例

```json
{
    "result": {
        "result": {
            "payChannel": "Alipay",
            "paySuccess": true
        },
        "code": null,
        "message": null,
        "success": true
    }
}
```

## 错误返回示例

```json
{
    "result": {
        "result": {
            "payChannel": "Alipay",
            "paySuccess": false
        },
        "code": "PAYMENT_FAILED",
        "message": "支付失败，请重试",
        "success": false
    }
}
```
