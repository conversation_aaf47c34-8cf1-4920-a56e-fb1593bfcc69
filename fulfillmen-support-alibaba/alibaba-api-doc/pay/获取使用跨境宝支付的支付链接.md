# 获取使用跨境宝支付的支付链接

## 接口说明

获取使用跨境宝支付的支付链接

## 接口地址

- 请求方式：POST
- URL：`https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.crossBorderPay.url.get/${APPKEY}`

## 系统级入参

| 名称             | 类型     | 是否必须 | 描述     | 文档地址       |
|----------------|--------|------|--------|------------|
| _aop_timestamp | String | 否    | 请求时间戳  | [查看](文档地址) |
| _aop_signature | String | 是    | 请求签名   | [查看](文档地址) |
| access_token   | String | 是    | 用户授权令牌 | [查看](文档地址) |

## 应用级入参

| 名称          | 类型     | 是否必须 | 描述                                   | 示例值               |
|-------------|--------|------|--------------------------------------|-------------------|
| orderIdList | Long[] | 是    | 订单ID列表，最多批量30个订单，订单过多会导致超时，建议一次10个订单 | [111111,22222333] |

## 返回结果

| 名称               | 类型     | 描述                   | 示例值                                                         |
|------------------|--------|----------------------|-------------------------------------------------------------|
| success          | String | 是否成功                 | true                                                        |
| errorCode        | String | 错误码                  | 400_1                                                       |
| errorMsg         | String | 错误描述                 |                                                             |
| payUrl           | String | 收银台支付链接              | https://trade.1688.com/order/cashier.htm?orderId=1540514326 |
| cantPayOrderList | Long[] | 由于额度及风控原因不能批量支付的订单列表 | [123123,23123123]                                           |

## 错误码

| 错误码   | 错误描述               | 解决方案                 |
|-------|--------------------|----------------------|
| 400_4 | 无可使用支付渠道[跨境宝]付款的订单 | 订单列表里面没有可以使用跨境宝支付的订单 |

## 返回示例

```json
{
    "payUrl": "https://trade.1688.com/order/cashier.htm?orderId=151923545498520",
    "cantPayOrderList": [
        12123123,
        1231222222
    ],
    "success": true
}
```
