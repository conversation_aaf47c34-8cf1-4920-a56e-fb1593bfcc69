# 查询订单可以使用的支付方式

com.alibaba.trade:alibaba.trade.payWay.query-1

## 接口说明

查询某笔未付订单可以使用的支付方式通道支付方式通道

## 所属解决方案/能力

- 分销工具解决方案
- 采购解决方案（买家自用版）
- 代采解决方案（服务商用）
- 1688分销产品采购解决方案（服务商版）
- 1688分销产品采购解决方案（分销采购版）
- 代采解决方案（分销采购版）
- 1688国家级分站解决方案
- 跨境代采寻源比价搜索解决方案（国际化）
- 跨境超买寻源比价搜索解决方案
- 跨境ERP独立站SaaS数字化解决方案
- 跨境大客户寻源通解决方案
- 跨境导购通加工定制解决方案

## 接口地址

- 请求方式：POST
- URL：`https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.payWay.query/${APPKEY}`

## 系统级入参

| 名称             | 类型     | 是否必须 | 描述     | 文档地址       |
|----------------|--------|------|--------|------------|
| _aop_timestamp | String | 否    | 请求时间戳  | [查看](文档地址) |
| _aop_signature | String | 是    | 请求签名   | [查看](文档地址) |
| access_token   | String | 是    | 用户授权令牌 | [查看](文档地址) |

## 应用级入参

| 名称      | 类型     | 是否必须 | 描述  | 示例值    |
|---------|--------|------|-----|--------|
| orderId | String | 是    | 订单号 | 123123 |

## 返回结果

| 名称         | 类型     | 描述   | 示例值   |
|------------|--------|------|-------|
| success    | String | 是否成功 | true  |
| errorCode  | String | 错误码  | 500_1 |
| errorMsg   | String | 错误信息 |       |
| resultList | Object | 返回结果 |       |

### resultList 数据结构

| 名称       | 类型            | 描述       | 示例值                   |
|----------|---------------|----------|-----------------------|
| channels | PayTypeInfo[] | 可用支付通道列表 | []                    |
| orderId  | String        | 订单号      | 239695213738498520    |
| payFee   | Long          | 支付金额，单位分 | 32120                 |
| timeout  | String        | 最晚支付时间   | 2018-11-04 14: 00: 45 |

### PayTypeInfo 数据结构

| 名称   | 类型               | 描述                                                      | 示例值 |
|------|------------------|---------------------------------------------------------|-----|
| code | java.lang.Long   | 支付通道编码，1:支付宝,3:诚e赊,4:公对公转账,7:账期支付,15:银行转账,16:诚易宝,20:跨境宝 | 1   |
| name | java.lang.String | 支付通道名称，1:支付宝,3:诚e赊,4:公对公转账,7:账期支付,15:银行转账,16:诚易宝,20:跨境宝 | 支付宝 |

## 错误码

| 错误码   | 错误描述            | 解决方案                      |
|-------|-----------------|---------------------------|
| 500_2 | 没有权限或该订单无可用支付方式 | 检查授权用户，授权是否为买家，且必须为未主动取消。 |

## 请求示例

```json
{
    "_aop_timestamp": "1641830400000",
    "_aop_signature": "MOCK_SIGNATURE",
    "access_token": "MOCK_ACCESS_TOKEN",
    "orderId": "239695213738498520"
}
```

## 返回示例

```json
{
    "resultList": {
        "channels": [
            {
                "code": 1,
                "name": "支付宝"
            },
            {
                "code": 3,
                "name": "诚e赊"
            },
            {
                "code": 16,
                "name": "诚易宝"
            }
        ],
        "orderId": "239695213738498520",
        "payFee": 32120,
        "timeout": "2018-11-04 14: 00: 45"
    },
    "success": true,
    "errorCode": null,
    "errorMsg": null
}
```

## 错误返回示例

```json
{
    "success": false,
    "errorCode": "500_2",
    "errorMsg": "没有权限或该订单无可用支付方式",
    "resultList": null
}
```
