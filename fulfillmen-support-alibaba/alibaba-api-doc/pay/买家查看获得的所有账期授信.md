# 买家信用账期的所有账期信息

com.alibaba.trade:alibaba.accountPeriod.list.buyerView-1

## 接口说明

买家信用账期看所有存在的账期信息，可翻页查看，每次返回不超过10条

## 所属解决方案/能力

- 分销工具解决方案
- ERP分销解决方案（分销商用）
- 代采解决方案（服务商用）
- 1688分销产品采购解决方案（服务商用）
- 1688分销产品采购解决方案（分销采购版）
- 代采解决方案（分销采购版）
- 1688国家级分站解决方案
- 跨境代采寻源比价搜索解决方案（国际化）
- 跨境超买寻源比价搜索解决方案
- 跨境ERP独立站SaaS数字化解决方案
- 跨境大客户寻源通解决方案

## 接口地址

- 请求方式：POST
- URL：`https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.accountPeriod.list.buyerView/${APPKEY}`

## 系统级入参

| 名称             | 类型     | 是否必须 | 描述     | 文档地址       |
|----------------|--------|------|--------|------------|
| _aop_timestamp | String | 否    | 请求时间戳  | [查看](文档地址) |
| _aop_signature | String | 是    | 请求签名   | [查看](文档地址) |
| access_token   | String | 是    | 用户授权令牌 | [查看](文档地址) |

## 应用级入参

| 名称            | 类型     | 是否必须 | 描述           | 示例值             |
|---------------|--------|------|--------------|-----------------|
| pageIndex     | Long   | 否    | 页码           | 1               |
| sellerLoginId | String | 否    | 卖家ID，不填则查询全部 | alitestforisv01 |

## 返回结果

| 名称                | 类型                                                                    | 描述     | 示例值   |
|-------------------|-----------------------------------------------------------------------|--------|-------|
| success           | Boolean                                                               | 是否成功   | true  |
| errorCode         | String                                                                | 错误码    | 500_1 |
| errorMsg          | String                                                                | 错误信息   |       |
| resultList        | accountPeriod.list.buyerView.result                                   | 返回数据结果 | []    |
| totalCount        | String                                                                | 总数据条数  | 100   |
| accountPeriodList | alibaba.ocean.openplatform.biz.trade.common.model.AccountPeriodInfo[] | 授信列表   | []    |

### AccountPeriodInfo 数据结构

| 名称                | 类型                | 描述           | 示例值                    |
|-------------------|-------------------|--------------|------------------------|
| sellerLoginId     | java.lang.String  | 卖家loginId    | alitestforisv02        |
| sellerCompanyName | String            | 卖家公司名        | 公司名                    |
| gmtQuota          | java.util.Date    | 授信日期         | *****************+0800 |
| quota             | java.lang.Long    | 授信额度值,单位为分   | 10000                  |
| surplusQuota      | java.lang.Long    | 可用授信额度值,单位为分 | 10000                  |
| statusStr         | java.lang.String  | 状态描述         | 有效                     |
| tapDateStr        | java.lang.String  | 账期日期描述       | 下个月一号, 20号             |
| tapOverdue        | java.lang.Integer | 逾期次数         | 0                      |

## 请求示例

```json
{
    "_aop_timestamp": "*************",
    "_aop_signature": "MOCK_SIGNATURE",
    "access_token": "MOCK_ACCESS_TOKEN",
    "pageIndex": 1,
    "sellerLoginId": "alitestforisv01"
}
```

## 返回示例

```json
{
    "success": true,
    "errorCode": null,
    "errorMsg": null,
    "resultList": {
        "accountPeriodList": [
            {
                "gmtQuota": "*****************+0800",
                "quota": ********,
                "sellerCompanyName": "AOP分销测试号01",
                "sellerLoginId": "alitestforisv1",
                "statusStr": "有效",
                "surplusQuota": ********,
                "tapDateStr": "1120天",
                "tapOverdue": 0
            },
            {
                "gmtQuota": "*****************+0800",
                "quota": ********,
                "sellerCompanyName": "AOP分销测试号02",
                "sellerLoginId": "alitestforisv2",
                "statusStr": "有效",
                "surplusQuota": ********,
                "tapDateStr": "30天",
                "tapOverdue": 0
            }
        ],
        "totalCount": "2"
    }
}
```

## 错误返回示例

```json
{
    "success": false,
    "errorCode": "500_1",
    "errorMsg": "系统错误",
    "resultList": null
}
```
