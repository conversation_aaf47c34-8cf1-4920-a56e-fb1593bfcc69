# 查询是否开通免密支付

com.alibaba.trade:alibaba.trade.pay.protocolPay.isopen-1

## 接口说明

查询是否开通代扣协议

## 接口地址

- 请求方式：POST
- URL：`https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.pay.protocolPay.isopen/${APPKEY}`

## 系统级入参

| 名称             | 类型     | 是否必须 | 描述     | 文档地址       |
|----------------|--------|------|--------|------------|
| _aop_timestamp | String | 否    | 请求时间戳  | [查看](文档地址) |
| _aop_signature | String | 是    | 请求签名   | [查看](文档地址) |
| access_token   | String | 是    | 用户授权令牌 | [查看](文档地址) |

## 返回结果

| 名称     | 类型     | 描述     | 示例值 |
|--------|--------|--------|-----|
| result | Object | 返回结果对象 |     |

### result 数据结构

| 名称      | 类型      | 描述     | 示例值  |
|---------|---------|--------|------|
| result  | Object  | 内部结果对象 |      |
| code    | String  | 错误码    | null |
| message | String  | 错误消息   | null |
| success | Boolean | 是否成功   | true |

### result.result 数据结构

| 名称                | 类型                 | 描述     | 示例值 |
|-------------------|--------------------|--------|-----|
| paymentAgreements | PaymentAgreement[] | 签约状态列表 | []  |

### PaymentAgreement 数据结构

| 名称            | 类型     | 描述        | 示例值                                      |
|---------------|--------|-----------|------------------------------------------|
| payChannel    | String | 支付通道      | "ALIPAY"                                 |
| bindingStatus | String | 签约绑定状态    | "true"                                   |
| signedStatus  | String | 签约状态      | "false"                                  |
| signUrl       | String | 签约URL（可选） | "https://mapi.alipay.com/gateway.do?..." |

## 请求示例

```json
{
    "_aop_timestamp": "1641830400000",
    "_aop_signature": "MOCK_SIGNATURE",
    "access_token": "MOCK_ACCESS_TOKEN"
}
```

## 返回示例

```json
{
    "result": {
        "result": {
            "paymentAgreements": [
                {
                    "bindingStatus": "false",
                    "payChannel": "SHEGOU",
                    "signedStatus": "false"
                },
                {
                    "bindingStatus": "true",
                    "payChannel": "ALIPAY",
                    "signUrl": "https://mapi.alipay.com/gateway.do?sales_product_code=TAOBAO_TRAVEL_HOTEL&notify_url=http%3A%2F%2Fpre-center.finnet.alibaba.com%2Fcallback%2Fv1%2Ffm-PG.MID.CBU.NewRetail-alipay.tbapi.escrow-sign%2Fip_33_7_69_219-0%2F589540333&product_code=PERSONAL_TRAVEL_HOTEL&scene=INDUSTRY%7CCBU_AOTUPAY&partner=2088006300088887&service=alipay.dut.customer.agreement.page.sign&external_sign_no=119901210704561230970333&sign_type=DSA&sign=jm4udyi_d_a7_d46_yxsb_m_w3_xdb3_w_j9_wx_sg_u_w_g3d_d_rv_epkws_h_hid_e_h6i_cg%253D%253D",
                    "signedStatus": "false"
                }
            ]
        },
        "success": true,
        "code": null,
        "message": null
    }
}
```

## 错误返回示例

```json
{
    "result": {
        "result": {
            "paymentAgreements": []
        },
        "success": false,
        "code": "USER_NOT_AUTHORIZED",
        "message": "用户未授权"
    }
}
```
