# 批量获取订单的支付链接

## 接口说明

通过ERP付款时，可以通过本API获取批量支付的收银台链接。单个订单返回1688收银台地址，多个订单返回支付宝收银台地址。ERP可以引导用户跳转到收银台地址完成支付操作。支付前会验证用户在1688的登录状态。

## 接口地址

- 请求方式：POST
- URL：`https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.alipay.url.get/${APPKEY}`

## 系统级入参

| 名称             | 类型     | 是否必须 | 描述     | 文档地址       |
|----------------|--------|------|--------|------------|
| _aop_timestamp | String | 否    | 请求时间戳  | [查看](文档地址) |
| _aop_signature | String | 是    | 请求签名   | [查看](文档地址) |
| access_token   | String | 是    | 用户授权令牌 | [查看](文档地址) |

## 应用级入参

| 名称          | 类型     | 是否必须 | 描述                                   | 示例值                  |
|-------------|--------|------|--------------------------------------|----------------------|
| orderIdList | Long[] | 是    | 订单ID列表，最多批量30个订单，订单过多会导致超时，建议一次10个订单 | [743213493914985520] |

## 返回结果

| 名称                  | 类型      | 描述                                    | 示例值                      |
|---------------------|---------|---------------------------------------|--------------------------|
| errorMsg            | String  | 错误信息                                  |                          |
| payUrl              | String  | 支付链接                                  | https://a.b.com          |
| success             | Boolean | 是否成功，可能部分成功，需要结合payFailureOrderList查看 | true                     |
| errorCode           | String  | 错误码                                   |                          |
| payFailureOrderList | Long[]  | 部分失败订单id                              | [1299871823,19798172783] |

## 错误码

| 错误码                                   | 错误描述                                    | 解决方案                                                            |
|---------------------------------------|-----------------------------------------|-----------------------------------------------------------------|
| Batch pay : not surport MANUAL-TRADE! | Batch pay : not surport MANUAL-TRADE!   | 未补充实收收款信息的进货订单不能合并付款，是否选择的订单可以通过订单详情里面的baseInfo.sellerOrder字段判断 |
| 操作库存失败:PRODUCT_TRADE_STAT_ERROR       | inventoryErrorIds: [16397675**722128**] | 存在付款源库存订单且扣库存失败。                                                |

## 返回示例

### 多个订单的支付链接

```json
{
    "payUrl": "https://trade.1688.com/order/cashier.htm?orderId=164051432607498520;151923545459498520",
    "success": true
}
```

### 单个订单支付链接

```json
{
    "payUrl": "https://trade.1688.com/order/cashier.htm?orderId=151923545459498520",
    "success": true
}
```
