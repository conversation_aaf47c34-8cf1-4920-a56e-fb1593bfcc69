# 获取交易订单的物流跟踪信息(买家视角)

## 接口说明

该接口需要获取订单买家的授权，获取买家的订单的物流跟踪信息。
在采购流程或分销场景中，作为买家也有获取物流详情的需求。
该接口可能会根据物流单号查看物流跟踪信息。
由于物流单录入的时间因素，可能跟踪信息的API返回会有延迟。

## 请求URL

- POST `https://gw.open.1688.com/openapi/param2/1/com.alibaba.logistics/alibaba.trade.getLogisticsTraceInfo.buyerView/${APPKEY}`

## 系统级输入参数

| 参数名            | 类型     | 是否必须 | 描述     | 示例值 |
|----------------|--------|------|--------|-----|
| _aop_timestamp | String | 否    | 请求时间戳  | -   |
| _aop_signature | String | 是    | 请求签名   | -   |
| access_token   | String | 是    | 用户授权令牌 | -   |

## 应用级输入参数

| 参数名         | 类型     | 是否必须 | 描述              | 示例值           |
|-------------|--------|------|-----------------|---------------|
| logisticsId | String | 否    | 该订单下的物流编号       | AL8234243     |
| orderId     | Long   | 是    | 订单号             | 13342343      |
| webSite     | String | 是    | 是1688业务还是icbu业务 | 1688或者alibaba |

## 返回结果

| 参数名            | 类型                   | 描述    | 示例值 |
|----------------|----------------------|-------|-----|
| logisticsTrace | List<LogisticsTrace> | 跟踪单详情 | -   |
| errorCode      | String               | 错误码   | -   |
| errorMessage   | String               | 错误描述  | -   |
| success        | Boolean              | 是否成功  | -   |

### logisticsTrace 对象

| 参数名             | 类型                              | 描述     | 示例值               |
|-----------------|---------------------------------|--------|-------------------|
| logisticsId     | String                          | 物流编号   | BX110096003841234 |
| orderId         | Long                            | 订单编号   | -                 |
| logisticsBillNo | String                          | 物流单编号  | 480330616596      |
| logisticsSteps  | List<OpenPlatformLogisticsStep> | 物流跟踪步骤 | -                 |

### logisticsSteps 对象

| 参数名        | 类型     | 描述          | 示例值                            |
|------------|--------|-------------|--------------------------------|
| acceptTime | String | 物流跟踪单该步骤的时间 | -                              |
| remark     | String | 备注          | "在浙江浦江县公司进行下级地点扫描，即将发往：广东深圳公司" |

## 错误码

| 错误码                      | 错误描述              | 解决方案             |
|--------------------------|-------------------|------------------|
| 404                      | 无法找到相对应的物流单跟踪信息。  | 无法找到相对应的物流单跟踪信息。 |
| order.nopermission.buyer | 你没有权限获取该订单详情(买家端) | 不是授权状态用户的订单      |
| order.createtime.history | 不支持查询一年前的物流轨迹信息   | 不支持查询一年前的物流轨迹信息  |

## 示例

```json
{
    "logisticsTrace": [
        {
            "logisticsId": "LP00106397027178",
            "logisticsBillNo": "3832890717253",
            "orderId": 188983797838441800,
            "logisticsSteps": [
                {
                    "acceptTime": "2018-07-24 21:55:33",
                    "remark": "在广东广州天河区天平架一公司进行揽件扫描"
                },
                {
                    "acceptTime": "2018-07-24 22:10:50",
                    "remark": "在广东广州天河区天平架一公司进行下级地点扫描，即将发往：浙江宁波分拨中心"
                },
                {
                    "acceptTime": "2018-07-25 01:45:05",
                    "remark": "在分拨中心广东广州分拨中心进行称重扫描"
                },
                {
                    "acceptTime": "2018-07-25 01:47:42",
                    "remark": "在广东广州分拨中心进行装车扫描，即将发往：浙江宁波分拨中心"
                },
                {
                    "acceptTime": "2018-07-26 03:01:41",
                    "remark": "在分拨中心浙江宁波分拨中心进行卸车扫描"
                },
                {
                    "acceptTime": "2018-07-26 03:21:34",
                    "remark": "从浙江宁波分拨中心发出，本次转运目的地：浙江宁波鄞州区邱隘公司"
                },
                {
                    "acceptTime": "2018-07-26 07:06:21",
                    "remark": "到达目的地网点浙江宁波鄞州区邱隘公司，快件将很快进行派送"
                },
                {
                    "acceptTime": "2018-07-26 08:54:15",
                    "remark": "在浙江宁波鄞州区邱隘公司进行派件扫描；派送业务员：徐洲；联系电话：xxxxx"
                },
                {
                    "acceptTime": "2018-07-26 13:33:11",
                    "remark": "快件已被 已签收 签收"
                }
            ]
        }
    ]
}
```