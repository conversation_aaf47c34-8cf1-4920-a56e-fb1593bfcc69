# 根据运单号或无主件码查询外部订单ID

接口地址：com.alibaba.fenxiao.crossborder.logistics.order.getOutOrderId-1

## 接口说明

根据运单号或无主件码查询外部订单ID

## 请求 URL

- POST https://gw.open.1688.com/openapi/param/2/1/com.alibaba.fenxiao.crossborder/logistics.order.getOutOrderId/${APPKEY}

## 系统级输入参数

| 名称             | 类型     | 是否必须 | 描述     | 文档     |
|----------------|--------|------|--------|--------|
| _app_timestamp | String | 否    | 请求时间戳  | [文档地址] |
| _app_signature | String | 是    | 请求签名   | [文档地址] |
| access_token   | String | 是    | 用户授权令牌 | [文档地址] |

## 应用级输入参数

| 名称             | 类型               | 是否必须 | 描述   | 示例值 |
|----------------|------------------|------|------|-----|
| shipmentId     | java.lang.String | 否    | 运单号  | -   |
| noMainPartCode | java.lang.String | 否    | 无主件码 | -   |

## 返回结果

### result

| 名称     | 类型          | 描述 | 示例值 |
|--------|-------------|----|-----|
| result | ResultModel | -  | -   |

### ResultModel

| 名称      | 类型                                                | 描述 | 示例值 |
|---------|---------------------------------------------------|----|-----|
| success | java.lang.Boolean                                 | -  | -   |
| msg     | java.lang.String                                  | -  | -   |
| model   | alibaba.cbu.sc.label.model.ScOutOrderIdQueryModel | -  | -   |

### model

| 名称         | 类型               | 描述 | 示例值 |
|------------|------------------|----|-----|
| outOrderId | java.lang.String | -  | -   |
| orderId    | java.lang.String | -  | -   |

## 仓库打印标签解决方案介绍

功能介绍：当给有运单号的1688包裹发货时，客户需要贴上正确的通联和店铺标签到国外。

我们提供了两种能力：

### 1. 基础版：打印运单号

可以查询你的订单号，你通过订单号打印联邦和速卖通标签。本api可以帮你通过运单号或者无主件码查出你平台订单号和1688订单号。

需要你做的事情：
在下单的时候传两个参数：outOrderId = 你平台订单号 和 dropshipping = y

### 2. 升级版：在基础版基础上，提前与你平台打印通联和店铺标签建立对接，提供标准接口供你调用打印机。

打印标签的网页地址：https://air.1688.com/app/channel-fe/chain-work/printwayybill.html

需要你平台提供的事情：在基础版的基础上，额外提供通过你平台订单号查询所有联盟和店铺标签的接口，返回以pdf，1688侧接入开放，开放版方案需要联系系开发同学。