# 运费险信息查询

## 接口名称

com.alibaba.trade.shipping.insurance.get-1

## 请求 URL

POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/shipping.insurance.get/${APPKEY}

## 系统参数

| 名称             | 类型     | 是否必须 | 描述     | 文档        |
|----------------|--------|------|--------|-----------|
| _aop_timestamp | String | 否    | 请求时间戳  | [文档地址](#) |
| _aop_signature | String | 是    | 请求签名   | [文档地址](#) |
| access_token   | String | 是    | 用户授权令牌 | [文档地址](#) |

## 应用级输入参数

| 名称      | 类型     | 是否必须 | 描述    | 示例值                                        |
|---------|--------|------|-------|--------------------------------------------|
| orderId | Long   | 是    | 订单号   | 订单号                                        |
| type    | String | 是    | 运费险类型 | givenByPlatform 平台赠送, givenByMerchant 商家赠送 |

## 返回结果

## result

| 名称     | 类型          | 描述   | 示例值  |
|--------|-------------|------|------|
| result | ResultModel | 返回结果 | 返回结果 |

### ResultModel

| 名称      | 类型                       | 描述   | 示例值  |
|---------|--------------------------|------|------|
| success | Boolean                  | 是否成功 | 是成功  |
| code    | String                   | 响应码  | 响应码  |
| message | String                   | 响应信息 | 响应信息 |
| result  | TradeFreightPolicyResult | 返回结果 | 返回结果 |

### TradeFreightPolicyResult

| 名称             | 类型                 | 描述    | 示例值   |
|----------------|--------------------|-------|-------|
| insuranceId    | Long               | 保单id  | 保单id  |
| orderId        | Long               | 订单id  | 订单id  |
| tradeClaimList | TradeClaimResult[] | 理赔单信息 | 理赔单信息 |

### TradeClaimResult

| 名称              | 类型     | 描述       | 示例值      |
|-----------------|--------|----------|----------|
| applicationTime | Date   | 申请时间     | 申请时间     |
| claimAmount     | Long   | 理赔金额     | 理赔金额     |
| claimId         | String | 理赔单id    | 理赔单id    |
| payTime         | Date   | 打款时间     | 打款时间     |
| status          | String | 理赔状态     | 理赔状态     |
| tradeNO         | String | 支付宝交易流水号 | 支付宝交易流水号 |