# 获取物流模板详情

## 接口说明

根据物流模板id获取买家承担的物流模板，运费模板ID为0表示运费说明说明，为1表示买家承担运费。

## 请求URL

- POST `https://gw.open.1688.com/openapi/param2/1/com.alibaba.logistics/alibaba.logistics.myFreightTemplate.list.get/${APPKEY}`

## 系统级输入参数

| 参数名            | 类型     | 是否必须 | 描述     | 示例值 |
|----------------|--------|------|--------|-----|
| _aop_timestamp | String | 否    | 请求时间戳  | -   |
| _aop_signature | String | 是    | 请求签名   | -   |
| access_token   | String | 是    | 用户授权令牌 | -   |

## 应用级输入参数

| 参数名              | 类型                | 是否必须 | 描述             | 示例值   |
|------------------|-------------------|------|----------------|-------|
| templateId       | java.lang.Long    | 否    | 模板id，用于单条查询的场景 | xxx   |
| querySubTemplate | java.lang.Boolean | 否    | 是否查询子模板        | false |
| queryRate        | java.lang.Boolean | 否    | 是否查询子模板费率      | false |

## 返回结果

| 参数名       | 类型                    | 描述   | 示例值  |
|-----------|-----------------------|------|------|
| result    | List<FreightTemplate> | 返回结果 | []   |
| errorCode | java.lang.String      | 错误码  | 错误码  |
| errorMsg  | java.lang.String      | 错误描述 | 错误描述 |

### FreightTemplate 对象

| 参数名                  | 类型                                                          | 描述                       | 示例值 |
|----------------------|-------------------------------------------------------------|--------------------------|-----|
| addressCodeText      | java.lang.String                                            | 地址信息                     | xxx |
| fromAreaCode         | java.lang.String                                            | 发货地址地区码                  | xxx |
| id                   | java.lang.Long                                              | 地址ID                     | xxx |
| memberId             | java.lang.String                                            | 会员ID                     | xxx |
| name                 | java.lang.String                                            | 名称                       | xxx |
| remark               | java.lang.String                                            | 备注                       | xxx |
| status               | java.lang.Integer                                           | 状态                       | xxx |
| expressSubTemplate   | alibaba.openplatform.logistics.DeliverySubTemplateDetailDTO | 快递子模版                    | xxx |
| logisticsSubTemplate | alibaba.openplatform.logistics.DeliverySubTemplateDetailDTO | 货运子模版                    | xxx |
| codSubTemplate       | alibaba.openplatform.logistics.DeliverySubTemplateDetailDTO | 货到付款子模版                  | xxx |
| type                 | String                                                      | 类型 3-官方物流模板，2或无值时-用户物流模板 | 3   |

### DeliverySubTemplateDetailDTO 对象

| 参数名            | 类型                                                    | 描述  | 示例值 |
|----------------|-------------------------------------------------------|-----|-----|
| subTemplateDTO | alibaba.openplatform.logistics.DeliverySubTemplateDTO | 子模板 | -   |
| rateList       | List<DeliveryRateDetailDTO>                           | 费率  | -   |

### DeliverySubTemplateDTO 对象

| 参数名               | 类型                | 描述                    | 示例值 |
|-------------------|-------------------|-----------------------|-----|
| chargeType        | java.lang.Integer | 计件类型。0:重量 1:件数 2:体积   | -   |
| isSysTemplate     | java.lang.Boolean | 是否系统模板                | -   |
| serviceChargeType | java.lang.Integer | 运费承担类型 卖家承担：0；买家承担：1  | -   |
| serviceType       | java.lang.Integer | 服务类型。0:快递 1:货运 2:货到付款 | -   |
| type              | java.lang.Integer | 子模板类型 0基准 1增值。默认0     | -   |

### DeliveryRateDetailDTO 对象

| 参数名            | 类型                                                | 描述                         | 示例值 |
|----------------|---------------------------------------------------|----------------------------|-----|
| isSysRate      | boolean                                           | 是否系统模板                     | -   |
| toAreaCodeText | java.lang.String                                  | 地址编码文本，用顿号隔开。例如：上海、福建省、广东省 | -   |
| rateDTO        | alibaba.openplatform.logistics.DeliveryRateDTO    | 普通子模板费率                    | -   |
| sysRateDTO     | alibaba.openplatform.logistics.DeliverySysRateDTO | 系统子模板费率                    | -   |

### DeliveryRateDTO 对象

| 参数名           | 类型             | 描述                | 示例值 |
|---------------|----------------|-------------------|-----|
| firstUnit     | java.lang.Long | 首重（单位：克）或首件（单位：件） | -   |
| firstUnitFee  | java.lang.Long | 首重或首件的价格          | -   |
| leastExpenses | java.lang.Long | 最低一票              | -   |

### DeliverySysRateDTO 对象

| 参数名           | 类型             | 描述                  | 示例值 |
|---------------|----------------|---------------------|-----|
| firstUnit     | java.lang.Long | 首重（单位：克）或首件（单位：件）   | -   |
| firstUnitFee  | java.lang.Long | 首重或首件的价格            | -   |
| leastExpenses | java.lang.Long | 最低一票                | -   |
| nextUnit      | java.lang.Long | 续重（单位：克）或续件（单位：件）单位 | -   |

## 返回示例

```json
{
    "result": [
        {
            "addressCodeText": "河北省/广东东莞区",
            "fromAreaCode": "420802",
            "id": 11864709,
            "memberId": "b2b-1624961198",
            "name": "test168",
            "remark": "",
            "status": 1,
            "type": "3"
        }
    ]
}
