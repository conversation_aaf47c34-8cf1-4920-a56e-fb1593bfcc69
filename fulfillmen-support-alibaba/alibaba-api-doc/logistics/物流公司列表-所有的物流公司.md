# 物流公司列表-所有的物流公司

## 接口说明

获取所有的物流公司名称。

## 请求URL

- POST `https://gw.open.1688.com/openapi/param2/1/com.alibaba.logistics/alibaba.logistics.OpQueryLogisticCompanyList/${APPKEY}`

## 系统级输入参数

| 参数名            | 类型     | 是否必须 | 描述     | 示例值 |
|----------------|--------|------|--------|-----|
| _aop_timestamp | String | 否    | 请求时间戳  | -   |
| _aop_signature | String | 是    | 请求签名   | -   |
| access_token   | String | 是    | 用户授权令牌 | -   |

## 应用级输入参数

无

## 返回结果

| 参数名             | 类型                            | 描述      | 示例值     |
|-----------------|-------------------------------|---------|---------|
| result          | List<OpLogisticsCompanyModel> | 物流公司列表  | {}      |
| success         | Boolean                       | 是否成功    | true    |
| errorCode       | String                        | 错误码     | 500     |
| errorMessage    | String                        | 错误码描述   | 错误码描述   |
| extErrorMessage | String                        | 扩展错误码描述 | 扩展错误码描述 |

### OpLogisticsCompanyModel 对象

| 参数名          | 类型      | 描述       | 示例值 |
|--------------|---------|----------|-----|
| id           | Long    | -        | -   |
| companyName  | String  | 物流公司名称   | xxx |
| companyNo    | String  | 物流公司编号   | xxx |
| companyPhone | String  | 物流公司服务电话 | xxx |
| supportPrint | Boolean | 是否支持打印   | xxx |
| spelling     | String  | 全拼       | xxx |
