# 获取交易订单的物流信息(买家视角)

## 接口说明

该接口需要获取订单买家的授权，获取买家订单的物流详情。
在采购或者分销场景中，作为买家也有获取物流详情的需求。
该接口可以查看订单ID号查看物流详情，包括发件人、收件人、所发货物的明细等。
由于物流单录入的时间因素，可能跟踪信息的API返回会有延迟。

## 请求URL

- POST `https://gw.open.1688.com/openapi/param2/1/com.alibaba.logistics/alibaba.trade.getLogisticsInfos.buyerView/${APPKEY}`

## 系统级输入参数

| 参数名            | 类型     | 是否必须 | 描述     | 示例值 |
|----------------|--------|------|--------|-----|
| _aop_timestamp | String | 否    | 请求时间戳  | -   |
| _aop_signature | String | 是    | 请求签名   | -   |
| access_token   | String | 是    | 用户授权令牌 | -   |

## 应用级输入参数

| 参数名     | 类型     | 是否必须 | 描述                                                               | 示例值                                   |
|---------|--------|------|------------------------------------------------------------------|---------------------------------------|
| orderId | Long   | 是    | 订单号                                                              | 1221434                               |
| fields  | String | 否    | 需要返回的字段，目前有:company.name,sender,receiver,sendgood，返回的字段按要用英文逗号分开 | company,name,sender,receiver,sendgood |
| webSite | String | 是    | 是1688业务还是icbu业务                                                  | 1688或者alibaba                         |

## 返回结果

| 参数名          | 类型                               | 描述   | 示例值  |
|--------------|----------------------------------|------|------|
| result       | List<OpenPlatformLogisticsOrder> | 返回结果 | {}   |
| errorCode    | String                           | 错误码  | -    |
| errorMessage | String                           | 错误描述 | -    |
| success      | Boolean                          | 是否成功 | true |

### OpenPlatformLogisticsOrder 对象

| 参数名                    | 类型                                       | 描述                                                                                      | 示例值                                                                         |
|------------------------|------------------------------------------|-----------------------------------------------------------------------------------------|-----------------------------------------------------------------------------|
| logisticsId            | String                                   | 物流信息ID                                                                                  | 物流信息ID                                                                      |
| logisticsBillNo        | String                                   | 物流单号，运单号                                                                                | 物流单号，运单号                                                                    |
| orderEntryIds          | String                                   | 订单号列表，无子订单的等于主订单编号，否则对应子订单列表                                                            | 129232515787615400,129232515788615400,129232515789615400,129232515790615400 |
| status                 | String                                   | 物流状态。WAITACCEPT:未受理;CANCEL:已撤销;ACCEPT:已受理;TRANSPORT:运输中;NOGET:揽件失败;SIGN:已签收;UNSIGN:签收异常 | WAITACCEPT                                                                  |
| logisticsCompanyId     | String                                   | 物流公司ID                                                                                  | 物流公司ID                                                                      |
| logisticsCompanyName   | String                                   | 物流公司编码                                                                                  | 物流公司编码                                                                      |
| logisticsCompanyNo     | String                                   | 物流公司编号                                                                                  | 物流公司编号                                                                      |
| remarks                | String                                   | 备注                                                                                      | 备注                                                                          |
| serviceFeature         | String                                   | serviceFeature                                                                          | serviceFeature                                                              |
| gmtSystemSend          | String                                   | gmtSystemSend                                                                           | gmtSystemSend                                                               |
| sendGoods              | List<OpenPlatformLogisticsSendGood>      | 商品信息                                                                                    | 商品信息                                                                        |
| receiver               | OpenPlatformLogisticsReceiver            | 收件人信息                                                                                   | 收件人信息                                                                       |
| sender                 | OpenPlatformLogisticsSender              | 发件人信息                                                                                   | 发件人信息                                                                       |
| logisticsOrderGoods    | List<OpenPlatformLogisticsOrderSendGood> | 物流订单商品关系模型                                                                              | {}                                                                          |
| logisticsOrderSendGood | List<OpenPlatformLogisticsOrderSendGood> | 物流和订单关联模型                                                                               | {}                                                                          |

### OpenPlatformLogisticsSendGood 对象

| 参数名      | 类型     | 描述   | 示例值 |
|----------|--------|------|-----|
| goodName | String | 商品名  | -   |
| quantity | String | 商品数量 | -   |
| unit     | String | 商品单位 | -   |

### OpenPlatformLogisticsReceiver 对象

| 参数名                  | 类型     | 描述    | 示例值 |
|----------------------|--------|-------|-----|
| receiverName         | String | 收件人名字 | -   |
| receiverPhone        | String | 收件人电话 | -   |
| receiverMobile       | String | 收件人电话 | -   |
| encrypt              | String | -     | -   |
| receiverProvinceCode | String | 省编码   | -   |
| receiverCityCode     | String | 市编码   | -   |
| receiverCountyCode   | String | 国家编码  | -   |
| receiverAddress      | String | 地址    | -   |
| receiverProvince     | String | 省份    | -   |
| receiverCity         | String | 城市    | -   |
| receiverCounty       | String | 国家    | -   |

### OpenPlatformLogisticsSender 对象

| 参数名                | 类型     | 描述    | 示例值 |
|--------------------|--------|-------|-----|
| senderName         | String | 发件人姓名 | -   |
| senderPhone        | String | 发件人电话 | -   |
| senderMobile       | String | 发件人电话 | -   |
| encrypt            | String | -     | -   |
| senderProvinceCode | String | 省编码   | -   |
| senderCityCode     | String | 城市编码  | -   |
| senderCountyCode   | String | 国家编码  | -   |
| senderAddress      | String | 发货人地址 | -   |
| senderProvince     | String | 省份    | -   |
| senderCity         | String | 城市    | -   |
| senderCounty       | String | 国家    | -   |

### OpenPlatformLogisticsOrderSendGood 对象

| 参数名              | 类型     | 描述     | 示例值                |
|------------------|--------|--------|--------------------|
| logisticsId      | String | 物流编号   | LP00616288919385   |
| tradeOrderId     | Long   | 交易主订单号 | 365749927217923233 |
| tradeOrderItemId | Long   | 交易子订单号 | 365749927217923233 |
| description      | String | sku描述  | 颜色: 黑色; 尺码: M;     |
| quantity         | Double | 数量     | 1                  |
| unit             | String | 单位     | 件                  |
| productName      | String | 商品名称   | 品名                 |

## 错误码

| 错误码   | 错误描述                 | 解决方案                                                                                                                                                                        |
|-------|----------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 500_2 | 订单尚未发货，暂无物流详情，请稍后再试。 | 订单尚未发货，建议您听订单发货消息后，收到消息后再查订单物流状态。参考：https://open.1688.com/doc/topicDetail.htm?spm=a260s.11630592.0.0.555655ed4QUvy5&topicGroup=ORDER&id=ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS |

## 示例

```json
{
    "result": [
        {
            "logisticsId": "BX111841674232006",
            "orderEntryIds": "149989279191615400",
            "status": "SIGN",
            "logisticsCompanyId": "8",
            "remarks": "asdasdasdasdsad啊啊啊",
            "sendGoods": [
                {
                    "unit": "个",
                    "quantity": 2,
                    "goodName": "【茂茂回归】12.9公开-sku区间价-淘货源"
                }
            ],
            "receiver": {
                "receiverCountyCode": "310105",
                "receiverCity": "上海市",
                "receiverProvinceCode": "310000",
                "receiverCityCode": "310100",
                "encrypt": "CN",
                "receiverCounty": "长宁区",
                "receiverMobile": "13800138000",
                "receiverProvince": "上海",
                "receiverName": "XXX",
                "receiverAddress": "XXX街道 XX路999",
                "receiverPhone": ""
            },
            "sender": {
                "senderCityCode": "330100",
                "senderAddress": "网商路699号",
                "senderName": "张三",
                "senderProvince": "浙江省",
                "encrypt": "CN",
                "senderCity": "杭州市",
                "senderCountyCode": "330108",
                "senderProvinceCode": "330000",
                "senderMobile": "12345678900",
                "senderPhone": "",
                "senderCounty": "滨江区"
            }
        }
    ]
}
```