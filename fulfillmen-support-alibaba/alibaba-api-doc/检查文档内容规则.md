# 文档内容检查规则

## 一、文档基本结构检查

1. 必需章节
    - 接口描述
    - 请求URL
    - 请求参数(系统级和应用级)
    - 响应参数
    - 错误码
    - 示例

2. 章节顺序
    - 标题层级使用 #, ##, ###
    - 保持章节顺序一致性
    - 相关内容应该放在一起

## 二、参数核对规则

1. 参数表格格式

```
名称    类型    是否必须    描述    示例值
```

2. 参数检查要点
    - 参数数量是否完整
    - 字段类型是否正确
    - 必填性标注是否准确
    - 描述是否清晰无歧义
    - 示例值是否合理

3. 复合参数结构检查
    - 检查子参数完整性
    - 确保类型定义准确
    - 验证示例值格式
    - 确保必填字段都有值

## 三、示例检查规则

1. 请求示例
    - JSON格式规范
    - 包含所有必填字段
    - 示例值真实有效
    - 注释充分

2. 响应示例
    - 格式规范
    - 覆盖成功和失败场景
    - 数据结构完整
    - 字段值合理

## 四、错误码规范

1. 错误码格式

```
错误码    错误描述    解决方案
```

2. 检查要点
    - 错误码系统完整
    - 描述清晰具体
    - 解决方案可操作
    - 覆盖主要错误场景

## 五、参数核对模板

```
请核对以下参数定义是否匹配:

参数名: [参数名]
类型定义:
名称    类型    是否必须    描述    示例值
[字段名1]    [类型1]    [是/否]    [描述1]    [示例值1]
[字段名2]    [类型2]    [是/否]    [描述2]    [示例值2]
...

对比内容:
[要对比的完整参数定义]

请检查:
1. 参数数量是否一致
2. 字段类型是否匹配
3. 必填性是否正确
4. 描述是否准确
5. 示例值是否合理
```

## 六、注意事项

1. 一致性要求
    - 参数命名保持一致
    - 类型定义统一
    - 描述风格统一
    - 示例格式统一

2. 完整性要求
    - 参数定义完整
    - 示例覆盖完整
    - 错误码覆盖完整
    - 文档结构完整

3. 准确性要求
    - 参数类型准确
    - 必填性标注准确
    - 描述文字准确
    - 示例值准确

4. 可用性要求
    - 描述易于理解
    - 示例易于参考
    - 错误处理清晰
    - 文档结构清晰
