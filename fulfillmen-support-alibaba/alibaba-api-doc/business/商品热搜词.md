# 商品热搜词

## 接口说明

商品热搜词接口，用于获取指定条件下的热搜关键词。

## 接口信息

- **接口路径**: `/param2/1/com.alibaba.fenxiao.crossborder/product.search.topKeyword/${APPKEY}`
- **请求方式**: POST
- **接口版本**: 1.0
- **API名称**: com.alibaba.fenxiao.crossborder.product.search.topKeyword-1

## 系统级参数

| 参数名            | 类型     | 必填 | 描述     | 示例值 |
|----------------|--------|----|--------|-----|
| _aop_timestamp | String | 否  | 请求时间戳  | -   |
| _aop_signature | String | 是  | 请求签名   | -   |
| access_token   | String | 否  | 用户授权令牌 | -   |

## 业务请求参数

### topSeKeywordParam 对象

| 参数名            | 类型     | 必填 | 描述             | 示例值  |
|----------------|--------|----|----------------|------|
| country        | String | 是  | 语言，参考开发参考枚举    | en   |
| sourceId       | String | 是  | 查询id，如类目id     | 1    |
| hotKeywordType | String | 是  | 热搜类型，目前只提供类目维度 | cate |

## 请求示例

```json
{
    "topSeKeywordParam": {
        "country": "en",
        "sourceId": "1",
        "hotKeywordType": "cate"
    }
}
```

## 返回参数

### result 对象

| 参数名     | 类型                      | 描述 | 示例值 |
|---------|-------------------------|----|-----|
| success | String                  | -  | -   |
| code    | String                  | -  | -   |
| message | String                  | -  | -   |
| result  | List<TopSeKeywordModel> | -  | -   |

### TopSeKeywordModel 对象

| 参数名                  | 类型     | 描述           | 示例值           |
|----------------------|--------|--------------|---------------|
| seKeyword            | String | 中文热词，请用词进行词搜 | 大型宠物犬         |
| seKeywordTranslation | String | 译文热词，展示使用    | Large Pet Dog |

## 返回示例

```json
{
    "result": {
        "success": "true",
        "code": "200",
        "message": "操作成功",
        "result": [
            {
                "seKeyword": "大型宠物犬",
                "seKeywordTranslation": "Large Pet Dog"
            }
        ]
    }
}
```

## 注意事项

1. 确保查询条件的正确性。
2. 热搜类型目前仅支持类目维度。

## 相关链接

- [跨境代采寻源比价搜索解决方案（国际化）]()
- [1688国家服务站解决方案]()
- [跨境超买寻源比价搜索解决方案]()
- [跨境营销SaaS数字化解决方案]()
