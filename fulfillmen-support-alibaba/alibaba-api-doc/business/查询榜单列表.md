# 查询榜单列表

## 接口说明

查询榜单列表接口，用于获取指定榜单的商品信息。

## 接口信息

- **接口路径**: `/param2/1/com.alibaba.fenxiao.crossborder/product.topList.query/${APPKEY}`
- **请求方式**: POST
- **接口版本**: 1.0
- **API名称**: com.alibaba.fenxiao.crossborder.product.topList.query-1

## 系统级参数

| 参数名            | 类型     | 必填 | 描述     | 示例值 |
|----------------|--------|----|--------|-----|
| _aop_timestamp | String | 否  | 请求时间戳  | -   |
| _aop_signature | String | 是  | 请求签名   | -   |
| access_token   | String | 是  | 用户授权令牌 | -   |

## 业务请求参数

### rankQueryParams 对象

| 参数名      | 类型      | 必填 | 描述          | 示例值     |
|----------|---------|----|-------------|---------|
| rankId   | String  | 是  | 榜单ID        | 1111    |
| rankType | String  | 是  | 榜单类型        | complex |
| limit    | Integer | 是  | 榜单商品个数，最多20 | 10      |
| language | String  | 是  | 榜单商品语言      | en      |

## 请求示例

```json
{
    "rankQueryParams": {
        "rankId": "1111",
        "rankType": "complex",
        "limit": 10,
        "language": "en"
    }
}
```

## 返回参数

### result 对象

| 参数名     | 类型        | 描述   | 示例值   |
|---------|-----------|------|-------|
| success | Boolean   | 是否成功 | true  |
| code    | String    | 错误码  | S0000 |
| message | String    | 错误描述 | 成功    |
| result  | RankModel | 结果   | 结果    |

### RankModel 对象

| 参数名               | 类型                     | 描述   | 示例值                |
|-------------------|------------------------|------|--------------------|
| rankId            | String                 | 榜单ID | 111                |
| rankName          | String                 | 榜单名称 | Comprehensive List |
| rankType          | String                 | 榜单类型 | complex            |
| rankProductModels | List<RankProductModel> | 榜单结果 | 如下                 |

### RankProductModel 对象

| 参数名            | 类型           | 描述         | 示例值                                                                                         |
|----------------|--------------|------------|---------------------------------------------------------------------------------------------|
| itemId         | Long         | 商品ID       | ************                                                                                |
| title          | String       | 商品中文标题     | 2023厚底女式拖鞋                                                                                  |
| translateTitle | String       | 商品译文标题     | 2023 thick-soled ladies sandals                                                             |
| imgUrl         | String       | 商品图片       | https://cbu01.alicdn.com/img/ibank/O1CN01p4SIPo1D6Wx4c0xs8_!!*************-0-cib.search.jpg |
| sort           | Integer      | 商品排行       | 1                                                                                           |
| serviceList    | List<String> | 商品包含的服务    | ["sendGoods48H"]                                                                            |
| buyerNum       | Integer      | 最近30天买家数   | 1334                                                                                        |
| soldOut        | Integer      | 最近30天商品售件数 | 433454                                                                                      |
| goodsScore     | String       | 商品交易评分     | 5                                                                                           |

## 返回示例

```json
{
    "result": {
        "success": true,
        "code": "S0000",
        "message": "成功",
        "result": {
            "rankId": "111",
            "rankName": "Comprehensive List",
            "rankType": "complex",
            "rankProductModels": [
                {
                    "itemId": ************,
                    "title": "2023厚底女式拖鞋",
                    "translateTitle": "2023 thick-soled ladies sandals",
                    "imgUrl": "https://cbu01.alicdn.com/img/ibank/O1CN01p4SIPo1D6Wx4c0xs8_!!*************-0-cib.search.jpg",
                    "sort": 1,
                    "serviceList": [
                        "sendGoods48H"
                    ],
                    "buyerNum": 1334,
                    "soldOut": 433454,
                    "goodsScore": "5"
                }
            ]
        }
    }
}
```

## 注意事项

1. 确保榜单ID和类型的正确性。
2. 榜单商品个数最多为20。

## 相关链接

- [跨境代采寻源比价搜索解决方案（国际化）]()
- [跨境ERP独立站SaaS数字化解决方案]()
- [1688国家服务站解决方案]()
- [跨境超买寻源比价搜索解决方案]()
