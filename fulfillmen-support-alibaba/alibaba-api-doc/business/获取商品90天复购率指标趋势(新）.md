# 获取商品90天复购率指标趋势（新）

## 接口说明

获取商品90天复购率指标趋势接口，用于获取商品在指定时间段内的复购率趋势。

## 接口信息

- **接口路径**: `/param2/1/com.alibaba.fenxiao.crossborder/product.analyze.getRepurchaseRateTrendNew/${APPKEY}`
- **请求方式**: POST
- **接口版本**: 1.0
- **API名称**: com.alibaba.fenxiao.crossborder.product.analyze.getRepurchaseRateTrendNew-1

## 系统级参数

| 参数名            | 类型     | 必填 | 描述     | 示例值 |
|----------------|--------|----|--------|-----|
| _aop_timestamp | String | 否  | 请求时间戳  | -   |
| _aop_signature | String | 是  | 请求签名   | -   |
| access_token   | String | 是  | 用户授权令牌 | -   |

## 业务请求参数

### trendQueryParam 对象

| 参数名       | 类型     | 必填 | 描述     | 示例值      |
|-----------|--------|----|--------|----------|
| offerId   | Long   | 是  | 商品id   | 123      |
| startDate | String | 是  | 查询起始时间 | 20240701 |
| endDate   | String | 是  | 查询截止时间 | 20240710 |

## 请求示例

```json
{
    "trendQueryParam": {
        "offerId": 123,
        "startDate": "20240701",
        "endDate": "20240710"
    }
}
```

## 返回参数

### result 对象

| 参数名     | 类型                            | 描述   | 示例值   |
|---------|-------------------------------|------|-------|
| success | Boolean                       | 是否成功 | true  |
| retCode | String                        | 错误码  | S0000 |
| retMsg  | String                        | 返回信息 | 成功    |
| result  | List<OfferSellTrendDataModel> | 结果   | 结果    |

### OfferSellTrendDataModel 对象

| 参数名   | 类型     | 描述          | 示例值      |
|-------|--------|-------------|----------|
| date  | String | 查询指标数据对应的日期 | 20240701 |
| value | String | 指标数据        | 10%      |

## 返回示例

```json
{
    "result": {
        "success": true,
        "retCode": "S0000",
        "retMsg": "成功",
        "result": [
            {
                "date": "20240701",
                "value": "10%"
            }
        ]
    }
}
```

## 注意事项

1. 确保商品ID和日期范围的正确性。
2. 查询时间段最多支持1个月。

## 相关链接

- [跨境代采寻源比价搜索解决方案（国际化）]()
- [跨境ERP独立站SaaS数字化解决方案]()
- [跨境大客户寻源通解决方案]()
- [跨境营销SaaS数字化解决方案]()
