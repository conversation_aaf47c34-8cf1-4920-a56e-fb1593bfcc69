# 下游销售订单同步

## 接口说明

下游销售订单同步接口，用于同步订单信息。

## 接口信息

- **接口路径**: `/param2/1/com.alibaba.fenxiao.crossborder/trade.cross.orderSync/${APPKEY}`
- **请求方式**: POST
- **接口版本**: 1.0
- **API名称**: com.alibaba.fenxiao.crossborder.trade.cross.orderSync-1

## 系统级参数

| 参数名            | 类型     | 必填 | 描述     | 示例值 |
|----------------|--------|----|--------|-----|
| _aop_timestamp | String | 否  | 请求时间戳  | -   |
| _aop_signature | String | 是  | 请求签名   | -   |
| access_token   | String | 是  | 用户授权令牌 | -   |

## 业务请求参数

### orderParam 对象

| 参数名               | 类型                  | 必填 | 描述                 | 示例值                 |
|-------------------|---------------------|----|--------------------|---------------------|
| orderId           | String              | 是  | 订单id               | 1234                |
| productId         | String              | 否  | 商品id，子单或主子和一订单必填   | 12                  |
| productName       | String              | 否  | 商品名称，子单或主子和一订单必填   | 手套                  |
| skulId            | String              | 否  | skulId，子单或主子和一订单必填 | asds1213            |
| skuName           | String              | 否  | sku名称，子单或主子和一订单必填  | 绿色                  |
| buyAmount         | Long                | 是  | 购买数量               | 2                   |
| createTime        | String              | 是  | 创建时间               | 2023-10-01 10:10:10 |
| outMemberId       | String              | 是  | 下游用户id             | 1212113             |
| payTime           | String              | 是  | 支付时间               | 2023-10-10 10:10:10 |
| endTime           | String              | 否  | 完成时间               | 2023-10-01 10:10:10 |
| paidFee           | Long                | 是  | 实付金额 分             | 200                 |
| refundStatus      | String              | 否  | 退款状态 0-未退款 1-退款    | 0                   |
| status            | String              | 否  | 订单状态               | success             |
| subOrderParamList | List<SubOrderParam> | 否  | 子单信息               | -                   |

### subOrderParamList 对象

| 参数名          | 类型     | 必填 | 描述                   | 示例值 |
|--------------|--------|----|----------------------|-----|
| orderId      | String | 是  | 子单id                 | 2   |
| productId    | String | 是  | 1688商品id需明文文         | 1   |
| productName  | String | 是  | 1688商品名称需明文文         | 林子  |
| skulId       | String | 是  | 1688skul需明文文         | 1   |
| skuName      | String | 是  | 1688sku名称需明文文        | 绿色  |
| buyAmount    | Long   | 是  | 子单购买数量               | 1   |
| paidFee      | Long   | 是  | 实付金额 单位分             | 100 |
| refundStatus | String | 否  | 退款状态 0-未退款或退款关闭 1-退款 | 0   |

### 请求示例

```json
{
    "orderParam": {
        "orderId": "1234",
        "productId": "12",
        "productName": "手套",
        "skulId": "asds1213",
        "skuName": "绿色",
        "buyAmount": 2,
        "createTime": "2023-10-01 10:10:10",
        "outMemberId": "1212113",
        "payTime": "2023-10-10 10:10:10",
        "endTime": "2023-10-01 10:10:10",
        "paidFee": 200,
        "refundStatus": "0",
        "status": "success",
        "subOrderParamList": [
            {
                "orderId": "2",
                "productId": "1",
                "productName": "林子",
                "skulId": "1",
                "skuName": "绿色",
                "buyAmount": 1,
                "paidFee": 100,
                "refundStatus": "0"
            }
        ]
    }
}
```

## 返回参数

### result 对象

| 参数名     | 类型      | 描述   | 示例值   |
|---------|---------|------|-------|
| success | Boolean | 是否成功 | true  |
| code    | String  | 错误码  | "200" |
| message | String  | 返回信息 | "成功"  |

## 返回示例

```json
{
    "result": {
        "success": true,
        "code": "200",
        "message": "成功"
    }
}
```

## 错误码说明

| 错误码 | 描述 | 解决方案 |
|-----|----|------|
| 200 | 成功 | -    |

## 注意事项

1. 确保所有订单信息的正确性和完整性
2. 时间格式需要符合 yyyy-MM-dd HH:mm:ss 格式
3. 退款状态和订单状态需要准确反映订单的实际情况

## 相关链接

- [跨境代采源头比价搜索解决方案（国际化）]()
- [1688国家服务站解决方案]()
- [跨境超买寻源比价搜索解决方案]()
