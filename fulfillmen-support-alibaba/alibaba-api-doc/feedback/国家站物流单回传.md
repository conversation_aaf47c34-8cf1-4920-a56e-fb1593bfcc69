# 国家站物流单回传

## 接口说明

国家站物流单回传接口，用于同步物流订单信息。

## 接口信息

- **接口路径**: `/param2/1/com.alibaba.fenxiao.crossborder/trade.cross.logisticsOrderSync/${APPKEY}`
- **请求方式**: POST
- **接口版本**: 1.0
- **API名称**: com.alibaba.fenxiao.crossborder.trade.cross.logisticsOrderSync-1

## 系统级参数

| 参数名            | 类型     | 必填 | 描述     | 示例值 |
|----------------|--------|----|--------|-----|
| _aop_timestamp | String | 否  | 请求时间戳  | -   |
| _aop_signature | String | 是  | 请求签名   | -   |
| access_token   | String | 是  | 用户授权令牌 | -   |

## 业务请求参数

### orderLogisticsParam 对象

| 参数名             | 类型             | 必填 | 描述                                             | 示例值                 |
|-----------------|----------------|----|------------------------------------------------|---------------------|
| orderId         | String         | 是  | 国家站主单id                                        | 1213                |
| logisticsId     | String         | 是  | 下游物流单id                                        | 1-21213             |
| logisticsStatus | String         | 是  | 物流状态(send-已发货 sign-签收 partSign-部分收货 refund-退货) | sign                |
| createTime      | java.util.Date | 是  | 创建时间                                           | 2023-10-10 10:12:12 |
| signTime        | java.util.Date | 是  | 签收时间                                           | 2023-10-10 10:12:12 |
| province        | String         | 是  | 省                                              | 江西省                 |
| city            | String         | 是  | 市                                              | 南昌市                 |
| area            | String         | 是  | 区                                              | 白云区                 |
| address         | String         | 是  | 详细地址                                           | 江西省南昌市白云区xx号        |

## 请求示例

```json
{
    "orderLogisticsParam": {
        "orderId": "1213",
        "logisticsId": "1-21213",
        "logisticsStatus": "sign",
        "createTime": "2023-10-10 10:12:12",
        "signTime": "2023-10-10 10:12:12",
        "province": "江西省",
        "city": "南昌市",
        "area": "白云区",
        "address": "江西省南昌市白云区xx号"
    }
}
```

## 返回参数

### result 对象

| 参数名     | 类型      | 描述   | 示例值   |
|---------|---------|------|-------|
| success | Boolean | 是否成功 | true  |
| code    | String  | 错误码  | "200" |
| message | String  | 返回信息 | "成功"  |

## 返回示例

```json
{
    "result": {
        "success": true,
        "code": "200",
        "message": "成功"
    }
}
```

## 错误码说明

| 错误码 | 描述 | 解决方案 |
|-----|----|------|
| 200 | 成功 | -    |

## 注意事项

1. 物流状态(logisticsStatus)的可选值包括:
    - send: 已发货
    - sign: 签收
    - partSign: 部分收货
    - refund: 退货
2. 时间格式需要符合 yyyy-MM-dd HH:mm:ss 格式
3. 地址信息需要完整包含省市区和详细地址

