# 保存账号所属业务线

## 接口说明

保存账号所属业务线接口，用于保存用户账号的业务线信息。

## 接口信息

- **接口路径**: `/param2/1/com.alibaba.fenxiao.crossborder/account.business.save/${APPKEY}`
- **请求方式**: POST
- **接口版本**: 1.0
- **API名称**: com.alibaba.fenxiao.crossborder.account.business.save-1

## 系统级参数

| 参数名            | 类型     | 必填 | 描述     | 示例值 |
|----------------|--------|----|--------|-----|
| _aop_timestamp | String | 否  | 请求时间戳  | -   |
| _aop_signature | String | 是  | 请求签名   | -   |
| access_token   | String | 是  | 用户授权令牌 | -   |

## 业务请求参数

### accountPerformance 对象

| 参数名      | 类型     | 必填 | 描述    | 示例值      |
|----------|--------|----|-------|----------|
| account  | String | 是  | 账号名称  | b测试账号009 |
| business | String | 是  | 所属业务线 | 东南亚sea   |

## 请求示例

```json
{
    "accountPerformance": {
        "account": "b测试账号009",
        "business": "东南亚sea"
    }
}
```

## 返回参数

### result 对象

| 参数名        | 类型      | 描述                  | 示例值  |
|------------|---------|---------------------|------|
| saveResult | Boolean | 保存结果，true成功，false失败 | true |

## 返回示例

```json
{
    "result": {
        "saveResult": true
    }
}
```

## 注意事项

1. 确保账号名称和业务线的正确性
2. 业务线的可选值包括：东南亚sea、南亚sa、日韩jk、港澳台hmt、中东me、北美na、拉美la、西欧we、泛俄ru、非洲af

## 相关链接

- [跨境代采源头比价搜索解决方案（国际化）]()
- [1688国家服务站解决方案]()
- [跨境超买寻源比价搜索解决方案]()
