# 回传机构真实用户订单和1688订单的映射关系

## 接口说明

将机构真实用户的订单和机构账号产生的1688订单的映射关系写入1688。

## 接口信息

- **接口路径**: `/param2/1/com.alibaba.fenxiao.crossborder/order.relation.write/${appKey}`
- **请求方式**: POST
- **接口版本**: 1.0
- **API名称**: com.alibaba.fenxiao.crossborder.order.relation.write-1

## 系统级参数

| 参数名            | 类型     | 必填 | 描述     | 示例值 |
|----------------|--------|----|--------|-----|
| _aop_timestamp | String | 否  | 请求时间戳  | -   |
| _aop_signature | String | 是  | 请求签名   | -   |
| access_token   | String | 是  | 用户授权令牌 | -   |

## 业务请求参数

### orderRelationParam 对象

| 参数名                   | 类型     | 必填 | 描述          | 示例值              |
|-----------------------|--------|----|-------------|------------------|
| orderId               | String | 是  | 外部机构子订单ID   | 4590347523948375 |
| parentOrderId         | String | 是  | 外部机构主订单ID   | 4590347523948370 |
| purchaseOrderId       | Long   | 是  | 1688采购子订单ID | 3209572465452734 |
| purchaseParentOrderId | Long   | 是  | 1688采购主订单ID | 3209572465452730 |

## 请求示例

```json
{
    "orderRelationParam": {
        "orderId": "4590347523948375",
        "parentOrderId": "4590347523948370",
        "purchaseOrderId": 3209572465452734,
        "purchaseParentOrderId": 3209572465452730
    }
}
```

## 返回参数

| 参数名     | 类型      | 描述   | 示例值  |
|---------|---------|------|------|
| success | boolean | 是否成功 | true |
| code    | String  | 错误码  | "0"  |
| message | String  | 错误描述 | ""   |

## 返回示例

```json
{
    "success": true,
    "code": "0",
    "message": ""
}
```

## 错误码说明

| 错误码 | 描述 | 解决方案 |
|-----|----|------|
| 0   | 成功 | -    |

## 注意事项

1. 确保所有订单ID的正确性和对应关系
2. 建议在回传前验证订单状态
3. 注意处理可能的异常情况

## 相关链接

- [跨境代采源头比价搜索解决方案（国际化）]()
- [1688国家服务站解决方案]()
- [跨境超买寻源比价搜索解决方案]()