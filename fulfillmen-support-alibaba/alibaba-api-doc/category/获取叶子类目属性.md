## 获取叶子类目属性

根据叶子类目ID获取类目属性。

### 请求URL

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.category.attribute.get/${APPKEY}
```

### 系统级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| _aop_timestamp | String | 否 | 请求时间戳 | - |
| _aop_signature | String | 是 | 请求签名 | - |
| access_token | String | 是 | 用户授权令牌 | - |

### 应用级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| categoryID | Long | 是 | 类目ID | - |
| webSite | String | 是 | 站点信息，指定调用的API是属于国际站(alibaba) 还是1688网站 (1688) | alibaba/1688 |
| scene | String | 否 | 场景值，可选值为空 和 processing，默认为空 | - |

### 返回结果

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| attributes | alibaba.category.AttributeInfo[] | 类目属性信息 | [] |
| levelAttrRelList | alibaba.category.PostLevelAttrRel[] | (废弃)类目属性级联关系，只有1688业务返回该字段 | [] |
| attributeLevelMapStr | java.util.Map | 级联信息字符串，可还原成map | {"1811:3289490":"20602,291738 0,7001","10000069:46874>7108:21958":"8243"} |
| errorMsg | String | 错误描述 | - |
| errorCode | String | 错误码 | 500_1 |
| success | Boolean | 是否成功 | true |

#### attributes 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| attrID | Long | 属性id | 123 |
| name | String | 名称 | "长度" |
| required | Boolean | 是否必填属性 | true |
| units | String[] | 该属性的单位 | ["mm", "cm"] |
| isSKUAttribute | Boolean | 该属性能否当成SKU属性 | true |
| attrValues | alibaba.category.AttributeValueInfo[] | 属性可选的属性值 | [] |
| inputType | String | 输入类型。下拉框:1, 多选框:2, 单选框:3, 文本输入框:0, 数字输入框:-1, 下拉框列表:4, 日期：5 | "1" |
| isSupportCustomizeValue | Boolean | 用成SKU属性时，是否支持自定义属性值名称，1688不返回该信息 | true |
| isSupportCustomizeImage | Boolean | 用成SKU属性时，是否支持自定义图片展示，1688不返回该信息 | true |
| enName | String | 英文名称，1688无此属性 | "length" |
| parentAttrID | String | 父属性ID，如果此值为空或零，则表示此属性为一级属性 | "287" |
| parentAttrValueID | String | 父属性值ID，如果此值为空或零，则表示此属性为一级属性 | "3737061" |
| aspect | String | 产品属性:0, 交易属性:3, spu匹配属性:5 | "0" |
| fieldType | String | 类型，int：数字；string:字符串；enum：枚举 | "enum" |
| isSpecPicAttr | Boolean | 是否图片属性 | false |
| firstLevel | Boolean | 是否为一级属性 | true |
| attrType | String | 专业化类目，属性类型(0：产品属性，1：规格属性，2：规格扩展属性) | "0" |
| sort | Integer | 类目排序 | 1 |
| recommendAttr | Boolean | 是否推荐属性 | false |

#### attributes.attrValues 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| attrValueID | Long | 属性值id | 456 |
| name | String | 名称 | "红色" |
| enName | String | 英文名称 | "red" |
| childAttrs | Long[] | 该属性值的子属性id | [789, 790] |
| isSKU | Boolean | 是否SKU属性值 | true |

#### levelAttrRelList 字段说明

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| fid | int | 属性id | 123 |
| subFids | int[] | 子关联属性 | [456, 789] |
| attrType | int | 属性层级关系类型(0和空：现货属性层级关系，1：加工属性层级关系) | 0 |

### 错误码

| 错误码 | 错误描述 | 解决方案 |
|--------|----------|----------|
| 500_2 | 数据准备中，请稍后重试。 | 数据正在后台加载，稍后重试，间隔时间建议1~3s |
