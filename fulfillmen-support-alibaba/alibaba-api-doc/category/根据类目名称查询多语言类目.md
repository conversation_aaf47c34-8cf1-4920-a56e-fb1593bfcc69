# 根据类目名称查询多语言类目API文档

## 接口信息
- **接口名称**: category.translation.getByKeyword-1
- **请求方式**: POST
- **请求URL**: `https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao.crossborder/category.translation.getByKeyword/${APPKEY}`

## 系统级输入参数

| 名称            | 类型     | 是否必须 | 描述         | 文档地址          |
|----------------|---------|--------|--------------|------------------|
| _aop_timestamp | String  | 否      | 请求时间戳    | [时间戳文档]()    |
| _aop_signature | String  | 是      | 请求签名      | [签名文档]()      |
| access_token   | String  | 是      | 用户授权令牌  | [授权文档]()      |

## 应用级输入参数

| 名称         | 类型               | 是否必须 | 描述                          | 示例值               |
|-------------|--------------------|--------|------------------------------|---------------------|
| outMemberId | java.lang.String   | 否      | 用户在机构的唯一ID（64位字符） | 2423523f13tr12412f  |
| language    | java.lang.String   | 是      | 语种代码（见枚举表）           | ja                  |
| cateName    | java.lang.String   | 是      | 类目名称（支持模糊搜索）        | 裙子                |

## 返回结果

| 名称      | 类型                                     | 描述       | 示例值       |
|----------|----------------------------------------|------------|-------------|
| result   | category.translation.getByKeyword.ResultModel | 返回结果    | 如下         |

## ResultModel结构


| 名称      | 类型                               | 描述       | 示例值       |
|----------|------------------------------------|------------|-------------|
| success  | boolean                            | 是否成功    | true        |
| code     | java.lang.String                   | 错误码      | S0000       |
| message  | java.lang.String                   | 错误描述    | 成功         |
| result   | category.translation.getByKeyword.Category[] | 实际结果    | 如下         |

## Category结构

| 名称            | 类型               | 描述          | 示例值        |
|----------------|--------------------|---------------|--------------|
| categoryId     | java.lang.Long     | 类目ID        | 1031910      |
| chineseName    | java.lang.String   | 类目中文名称    | 连衣裙        |
| translatedName | java.lang.String   | 类目翻译名称    | ワンピース     |
| language       | java.lang.String   | 语种          | ja           |
| leaf           | java.lang.Boolean  | 是否叶子类目    | true         |
| level          | java.lang.String   | 类目层级       | 2            |
| parentCateId   | java.lang.Long     | 上层类目ID     | 10166        |

## 错误码说明

| 错误码   | 描述               | 解决方案                 |
|----------|--------------------|--------------------------|
| S0000    | 成功               | -                        |
| E2101    | 类目名称不能为空     | 检查cateName参数          |
| E2102    | 无效的语种类型       | 检查language参数值        |

## 请求示例

```json
{
  "outMemberId": "2423523f13tr12412f",
  "language": "ja",
  "cateName": "裙子"
}
```

## 返回示例

```json
{
  "result": {
    "success": true,
    "code": "S0000",
    "message": "成功",
    "result": [
      {
        "categoryId": 1031910,
        "chineseName": "连衣裙",
        "translatedName": "ワンピース",
        "language": "ja",
        "leaf": true,
        "level": "2",
        "parentCateId": 10166
      }
    ]
  }
}
```

## 注意事项

1. 模糊搜索默认返回前20条匹配结果
2. parentCateId字段用于层级关系过滤
3. leaf=true时表示当前类目下无子类目
4. 语言代码需使用阿里巴巴官方提供的枚举值