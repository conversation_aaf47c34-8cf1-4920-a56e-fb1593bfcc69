# 根据类目ID查询多语言类目API文档

## 接口概述

该接口用于根据类目ID查询对应的多语言翻译信息,包括类目名称的各语言版本、层级关系等信息。支持查询父子类目结构,并具有缓存机制。

## 接口信息

- **接口名称**: category.translation.getById
- **请求方式**: POST
- **请求URL**: `https://gw.open.1688.com/openapi/param/2/1/com.alibaba.fenxiao.crossborder/category.translation.getById/${APPKEY}`

## 请求参数

### 系统级参数

| 参数名            | 类型     | 必填 | 说明     |
|----------------|--------|----|--------|
| _aop_timestamp | String | 是  | 请求时间戳  |
| _aop_signature | String | 是  | 请求签名   |
| access_token   | String | 是  | 用户授权令牌 |

### 业务参数

| 参数名          | 类型               | 必填 | 说明                | 示例值          |
|--------------|------------------|----|-------------------|--------------|
| outMemberId  | String           | 否  | 用户在机构的唯一ID,不超过64位 | 23423532?wef |
| language     | java.lang.String | 是  | 语种代码              | ja           |
| categoryId   | java.lang.Long   | 是  | 类目ID              | 0            |
| parentCateId | Long             | 否  | 父类目ID             | 0            |

## 响应结构

### 返回结果

```json
{
    "result": { // ResultModel
        "success": boolean, // 是否成功
        "code": string, // 错误码
        "message": string, // 错误描述
        "result": { // Category
            "categoryId": long, // 类目ID
            "chineseName": string, // 类目中文名称
            "translatedName": string, // 类目翻译名称
            "language": string, // 语种
            "leaf": string, // 是否叶子类目
            "level": string, // 类目层级
            "parentCateId": string, // 上级类目ID
            "fromCache": boolean, // 数据是否来自缓存
            "children": [ // ChildCategory[]
                {
                    // 子类目信息(字段同上)
                }
            ]
        }
    }
}
```

### 响应参数说明

| 参数路径                                  | 类型              | 说明      | 示例值     |
|---------------------------------------|-----------------|---------|---------|
| result                                | ResultModel     | 结果模型    | -       |
| result.success                        | boolean         | 是否成功    | true    |
| result.code                           | string          | 错误码     | S0000   |
| result.message                        | string          | 错误描述    | 成功      |
| result.result.categoryId              | long            | 类目ID    | 1031910 |
| result.result.chineseName             | string          | 类目中文名称  | 连衣裙     |
| result.result.translatedName          | string          | 类目翻译名称  | ワンピース   |
| result.result.language                | string          | 语种      | ja      |
| result.result.leaf                    | string          | 是否叶子类目  | true    |
| result.result.level                   | string          | 类目层级    | 2       |
| result.result.parentCateId            | string          | 上级类目ID  | 10166   |
| result.result.fromCache               | boolean         | 是否来自缓存  | true    |
| result.result.children                | ChildCategory[] | 子类目信息   | -       |
| result.result.children.categoryId     | long            | 子类目ID   | 1031910 |
| result.result.children.chineseName    | string          | 子类目中文名称 | 连衣裙     |
| result.result.children.translatedName | string          | 子类目翻译名称 | ワンピース   |
| result.result.children.language       | string          | 语种      | ja      |
| result.result.children.leaf           | string          | 是否叶子类目  | true    |
| result.result.children.level          | string          | 类目层级    | 2       |
| result.result.children.parentCateId   | string          | 上级类目ID  | 10166   |
| result.result.children.fromCache      | boolean         | 是否来自缓存  | true    |

## 注意事项

1. 父类目和子类目的某些字段类型可能不同(如leaf字段)
2. 返回结果支持多层级嵌套的类目结构
3. 接口具有缓存机制,fromCache字段标识数据来源
4. language参数需要参考语言代码枚举值

## 错误码说明

| 错误码   | 描述 | 解决方案 |
|-------|----|------|
| S0000 | 成功 | -    |

## 示例

### 请求示例

```json
{
  "outMemberId": "2212505444921",
  "language": "ja",
  "categoryId": 1031910,
  "parentCateId": 0
}
```

### 返回示例

```json
{
  "result": {
    "success": true,
    "code": "S0000",
    "message": "成功",
    "result": {
      "categoryId": 1031910,
      "chineseName": "连衣裙",
      "translatedName": "ワンピース",
      "language": "ja",
      "leaf": "true",
      "level": "2",
      "parentCateId": "10166",
      "fromCache": true
    }
  }
}
```
