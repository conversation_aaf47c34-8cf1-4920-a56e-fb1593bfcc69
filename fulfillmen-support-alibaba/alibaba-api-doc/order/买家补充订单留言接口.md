# 买家补充订单留言

买家补充订单留言接口，用于在订单创建后追加留言信息。

## 使用限制

1. 留言总长度不能超过500字符
2. 只有订单的买家可以补充留言
3. 需要订单处于有效状态
4. 每次追加的留言会累加到原有留言中

## 所属解决方案/能力

- 跨境超买寻源比价搜索解决方案
- 跨境ERP/独立站SaaS数字化解决方案
- 跨境大客户寻源通解决方案
- 采购解决方案（买家自用版）
- 采购解决方案（服务商版）
- 跨境代采寻源比价搜索解决方案（国际化）

## 请求URL

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.addFeedback/${APPKEY}
```

## 请求参数

### 系统级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| _aop_timestamp | String | 否 | 请求时间戳 | 1623391999000 |
| _aop_signature | String | 是 | 请求签名 | XXXXXX |
| access_token | String | 是 | 用户授权令牌 | XXXXXX |

### 应用级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| tradeFeedbackParam | Object | 是 | 请求参数对象 | - |

#### tradeFeedbackParam 字段说明

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| feedback | String | 是 | 留言内容(不超过500字符) | "请尽快发货，谢谢" |
| orderId | String | 是 | 订单ID | "12344444555545" |

## 返回结果

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| success | Boolean | 是否成功 | true |
| errorCode | String | 错误码 | "400_1" |
| errorInfo | String | 错误描述 | "parameter invalid" |

## 错误码

| 错误码 | 错误描述 | 解决方案 |
|--------|----------|----------|
| 400_1 | parameter invalid | 检查参数是否有传及参数类型是否符合要求 |
| 400_2 | need authorization | 需要授权登录 |
| 500_1 | invoke remote service error | 调用远程服务异常，请联系技术支持 |
| 500_2 | remote service error | 服务异常，请稍后再试 |
| 500_2 | invalid parameter error | 请求参数无效，请检查参数 |
| 500_2 | user order not exist error | 订单不属于当前授权用户，无权操作 |
| 500_2 | order not exist error | 订单号不存在，请检查订单号是否正确 |

## 请求示例

### 请求参数示例

```json
{
    "tradeFeedbackParam": {
        "feedback": "请尽快发货，谢谢",
        "orderId": "12344444555545"
    }
}
```

### 返回示例

#### 成功响应

```json
{
    "success": true,
    "errorCode": null,
    "errorInfo": null
}
```

#### 失败响应

```json
{
    "success": false,
    "errorCode": "400_1",
    "errorInfo": "parameter invalid"
}
```

## 注意事项

1. 留言内容不要超过500字符限制
2. 确保订单号正确且属于当前授权用户
3. 建议在留言前检查订单状态
4. 如遇错误需根据错误码排查原因
5. 留言内容建议不要包含特殊字符
