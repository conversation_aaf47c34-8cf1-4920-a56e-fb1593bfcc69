# 买家确认收货

买家确认收到商品的接口。支持整单确认收货和部分子订单确认收货。

## 使用限制

1. 仅支持已发货订单的确认收货
2. 只有订单的买家可以确认收货
3. 确认收货后订单状态不可恢复
4. 支持批量子订单确认收货

## 所属解决方案/能力

- 代发解决方案（分销买家版）
- 跨境大客户寻源通解决方案
- 代发解决方案（服务商版）
- 跨境代采寻源比价搜索解决方案（国际化）
- 跨境ERP/独立站SaaS数字化解决方案
- 跨境寻源通加工定制解决方案
- 采购解决方案（买家自用版）
- 1688分销严选采购解决方案（分销买家版）

## 请求URL

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/trade.receivegoods.confirm/${APPKEY}
```

## 请求参数

### 系统级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| _aop_timestamp | String | 否 | 请求时间戳 | 1623391999000 |
| _aop_signature | String | 是 | 请求签名 | XXXXXX |
| access_token | String | 是 | 用户授权令牌 | XXXXXX |

### 应用级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| orderId | Long | 是 | 订单ID | 56623232655125698 |
| orderEntryIds | Long[] | 是 | 子订单ID列表 | [562356635566365512] |

## 返回结果

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| success | Boolean | 是否处理成功 | true |
| errorCode | String | 错误码 | ORDER_STATUS_ERROR |
| errorInfo | String | 错误信息 | 订单状态错误 |

## 错误码

| 错误码 | 错误描述 | 解决方案 |
|--------|----------|----------|
| ORDER_STATUS_ERROR | 订单状态错误 | 只能确认已发货的订单，需要先确认订单状态 |
| ORDER_NOT_EXIST | 订单不存在 | 确认订单号是否正确 |
| NO_PERMISSION | 没有权限确认收货 | 只有订单的买家才能确认收货 |
| ENTRY_NOT_EXIST | 子订单不存在 | 确认子订单ID是否正确 |

## 请求示例

### 整单确认收货

```json
{
    "orderId": 56623232655125698,
    "orderEntryIds": [562356635566365512]
}
```

### 部分子订单确认收货

```json
{
    "orderId": 56623232655125698,
    "orderEntryIds": [
        562356635566365512,
        562356635566365513
    ]
}
```

### 返回示例

```json
{
    "success": true,
    "errorCode": null,
    "errorInfo": null
}
```

## 注意事项

1. 确认收货前请先确认订单状态是否已发货
2. 确认收货后将无法退货/退款
3. 如果是部分确认收货，需要传入对应的子订单ID
4. 如遇错误需根据错误码排查原因
5. 确认收货后系统将自动进入交易完成状态
