# 订单列表查看(买家视角)

## 接口说明

查询买家订单列表。如果用户的memberId与当前订单买家memberId不一致，调用只返回订单基本信息，不会返回订单详细信息和联系信息。如果需要获取完整信息，请确保是订单买家进行调用。

## 接口信息

- **接口名称**: com.alibaba.trade.getBuyerOrderList-1
- **请求方式**: POST
- **请求URL**: <https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.getBuyerOrderList-1>

## 请求参数

### 系统级参数

| 参数名            | 类型     | 必填 | 描述     | 示例值 |
|----------------|--------|----|--------|-----|
| _aop_timestamp | String | 是  | 请求时间戳  |     |
| _aop_signature | String | 是  | 请求签名   |     |
| access_token   | String | 是  | 用户授权令牌 |     |

### 应用级参数

| 参数名                      | 类型                | 必填 | 描述                                                                                                                                                                                                                                                                                                                                                                | 示例值                    |
|--------------------------|-------------------|----|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------|
| bizTypes                 | List<String>      | 否  | 业务类型，支持： "cn"(普通订单类型), "ws"(大额批发订单类型), "yp"(普通拿样订单类型), "yf"(一分钱拿样订单类型), "fs"(倒批(限时折扣)订单类型), "cz"(加工定制订单类型), "ag"(协议采购订单类型), "hp"(伙拼订单类型), "gc"(国采订单类型), "supply"(供销订单类型), "nyg"(nyg订单类型), "factory"(淘工厂订单类型), "quick"(快订下单), "xiangpin"(享拼订单), "nest"(采购商城-鸟巢), "f2f"(当面付), "cyfw"(存样服务), "sp"(代销订单标记), "wg"(微供订单), "factorysamp"(淘工厂打样订单), "factorybig"(淘工厂大货订单) | ["cn","ws"]            |
| createEndTime            | java.util.Date    | 否  | 下单结束时间                                                                                                                                                                                                                                                                                                                                                            | 20180802211111300+0800 |
| createStartTime          | java.util.Date    | 否  | 下单开始时间                                                                                                                                                                                                                                                                                                                                                            | 20180102211111300+0800 |
| isHis                    | boolean           | 否  | 是否查询历史订单,默认查询近三个月订单,如果查询历史订单设置为true                                                                                                                                                                                                                                                                                                                               | false                  |
| modifyEndTime            | java.util.Date    | 否  | 查询修改时间结束                                                                                                                                                                                                                                                                                                                                                          | 20180802211111300+0800 |
| modifyStartTime          | java.util.Date    | 否  | 查询修改时间开始                                                                                                                                                                                                                                                                                                                                                          | 20180102211111300+0800 |
| orderStatus              | java.lang.String  | 否  | 订单状态，值有 success, cancel(交易取消，违约金等交割完毕), waitbuyerpay(等待卖家付款)， waitsellersend(等待卖家发货), waitbuyerreceive(等待买家收货 )                                                                                                                                                                                                                                                   | success                |
| page                     | int               | 否  | 查询第几页,从1开始                                                                                                                                                                                                                                                                                                                                                        | 1                      |
| pageSize                 | int               | 否  | 每页显示的数量                                                                                                                                                                                                                                                                                                                                                           | 20                     |
| refundStatus             | java.lang.String  | 否  | 退款状态，支持： "waitselleragree"(等待卖家同意), "refundsuccess"(退款成功), "refundclose"(退款关闭), "waitbuyermodify"(待买家修改), "waitbuyersend"(等待买家退货), "waitsellerreceive"(等待卖家确认收货)                                                                                                                                                                                                  | refundsuccess          |
| sellerMemberId           | java.lang.String  | 否  | 卖家memberId                                                                                                                                                                                                                                                                                                                                                        | b2b-162496198          |
| sellerLoginId            | String            | 否  | 卖家loginId                                                                                                                                                                                                                                                                                                                                                         | alitestforisv02        |
| sellerRateStatus         | java.lang.Integer | 否  | 卖家评价状态(4:已评价,6:未评价)                                                                                                                                                                                                                                                                                                                                               | 6                      |
| tradeType                | java.lang.String  | 否  | 交易类型: 担保交易(1), 预存款交易(2), ETC境外收单交易(3), 即时到帐交易(4), 保障金安全交易(5), 统一交易流程(6), 分阶段交易(7), 货到付款交易(8), 信用凭证支付交易(9), 账期支付交易(10), 1688交易4.0，新分阶段交易(50060), 当面付的交易流程(50070), 服务类的交易流程(50080)                                                                                                                                                                                  | 50060                  |
| productName              | java.lang.String  | 否  | 商品名称                                                                                                                                                                                                                                                                                                                                                              | 测试商品                   |
| needBuyerAddressAndPhone | java.lang.Boolean | 否  | 是否需要买家收货地址和联系电话                                                                                                                                                                                                                                                                                                                                                   | false                  |
| needMemoInfo             | java.lang.Boolean | 否  | 是否需要备注信息                                                                                                                                                                                                                                                                                                                                                          | false                  |
| outOrderId               | String            | 否  | 外部订单号,可用于订单查询                                                                                                                                                                                                                                                                                                                                                     | 9018787289837          |

#### bizTypes 可选值说明

- cn: 普通订单类型
- ws: 大额批发订单类型
- yp: 普通拿样订单类型
- yf: 一分钱拿样订单类型
- fs: 倒批(限时折扣)订单类型
- cz: 加工定制订单类型
- ag: 协议采购订单类型
- hp: 伙拼订单类型
- gc: 国采订单类型
- supply: 供销订单类型
- nyg: nyg订单类型
- factory: 淘工厂订单类型
- quick: 快订下单
- xiangpin: 享拼订单
- nest: 采购商城-鸟巢
- f2f: 当面付
- cyfw: 存样服务
- sp: 代销订单标记
- wg: 微供订单
- factorysamp: 淘工厂打样订单
- factorybig: 淘工厂大货订单

#### orderStatus 可选值说明

- success: 交易成功
- cancel: 交易取消(违约金等交割完毕)
- waitbuyerpay: 等待买家付款
- waitsellersend: 等待卖家发货
- waitbuyerreceive: 等待买家收货
- waitlogisticstakein: 等待物流公司接件
- waitbuyersign: 等待买家签收
- signinsuccess: 买家已签收
- confirm_goods: 已收货
- terminated: 交易终止

#### refundStatus 可选值说明

- waitselleragree: 等待卖家同意
- refundsuccess: 退款成功
- refundclose: 退款关闭
- waitbuyermodify: 待买家修改
- waitbuyersend: 等待买家退货
- waitsellerreceive: 等待卖家确认收货

## 返回结果

| 参数名          | 类型     | 描述     | 示例值 |
|--------------|--------|--------|-----|
| errorCode    | String | 错误码    |     |
| errorMessage | String | 错误信息   |     |
| totalRecord  | Long   | 总记录数   | 528 |
| result       | Array  | 查询返回列表 | []  |

### result 字段说明

注意：返回一共 12 个对象字段

| 参数名                  | 类型                                                     | 描述                             | 示例值 |
|----------------------|--------------------------------------------------------|--------------------------------|-----|
| quoteList            | List<alibaba.orderDetail.caigouQuoteInfo>              | 采购单详情列表,为大企业采购订单独有域            |     |
| extAttributes        | List<alibaba.openplatform.trade.KeyValuePair>          | 订单的扩展属性信息                      |     |
| orderRateInfo        | alibaba.trade.OrderRateInfo                            | 订单的评价状态信息                      |     |
| orderInvoiceInfo     | alibaba.invoice.OrderInvoiceModel                      | 订单发票相关信息                       |     |
| tradeTerms           | List<alibaba.openplatform.trade.model.TradeTermsInfo>  | 订单交易相关的条款信息                    |     |
| nativeLogistics      | alibaba.openplatform.trade.model.NativeLogisticsInfo   | 国内物流信息,包含收货地址、联系人等             |     |
| guaranteesTerms      | alibaba.openplatform.trade.model.GuaranteeTermsInfo    | 订单的保障服务信息                      |     |
| baseInfo             | alibaba.openplatform.trade.model.OrderBaseInfo         | 订单基础信息,包含订单ID、买卖家账号信息、订单状态、金额等 |     |
| orderBizInfo         | alibaba.order.bizInfo                                  | 订单业务信息,包含采源宝标识、账期交易、诚e赊等信息     |     |
| tradeProductItems    | List<alibaba.openplatform.trade.model.ProductItemInfo> | 商品信息列表,包含商品详情、价格、数量等           |     |
| overseasExtraAddress | alibaba.trade.OverseasExtraAddress                     | 跨境订单的地址扩展信息                    |     |
| customs              | alibaba.trade.Customs                                  | 跨境报关信息                         |     |

#### result.quoteList: 采购单详情列表

| 参数名              | 类型     | 描述      | 示例值  |
|------------------|--------|---------|------|
| productQuoteName | String | 供应单项的名称 | 物料01 |
| price            | Long   | 价格(分)   | 100  |
| count            | Double | 购买数量    | 10   |

#### result.extAttributes: 订单扩展属性信息

| 参数名         | 类型     | 描述   | 示例值 |
|-------------|--------|------|-----|
| key         | String | 属性名  | 属性值 |
| value       | String | 属性值  | 属性值 |
| description | String | 属性描述 | 属性值 |

#### result.orderRateInfo: 订单评价信息

| 参数名              | 类型      | 描述                          | 示例值 |
|------------------|---------|-----------------------------|-----|
| buyerRateStatus  | Integer | 买家评价状态(4:已评论,5:未评论,6;不需要评论) | 5   |
| sellerRateStatus | Integer | 卖家评价状态(4:已评论,5:未评论,6;不需要评论) | 5   |

#### result.orderInvoiceInfo: 订单发票信息

| 参数名                | 类型                | 描述                         | 示例值 |
|--------------------|-------------------|----------------------------|-----|
| invoiceCompanyName | java.lang.String  | 发票公司名称(即发票抬头-title)        | -   |
| invoiceType        | java.lang.Integer | 发票类型. 0：普通发票，1:增值税发票，9未知类型 | -   |
| localInvoiceId     | java.lang.Long    | 本地发票号                      | -   |
| orderId            | java.lang.Long    | 订单Id                       | -   |
| receiveCode        | java.lang.String  | (收件人)址区域编码                 | -   |
| receiveCodeText    | java.lang.String  | (收件人) 省市区编码对应的文案(增值税发票信息)  | -   |
| receiveMobile      | java.lang.String  | （收件者）发票收货人手机               | -   |
| receiveName        | java.lang.String  | （收件者）发票收货人                 | -   |
| receivePhone       | java.lang.String  | （收件者）发票收货人电话               | -   |
| receivePost        | java.lang.String  | （收件者）发票收货地址邮编              | -   |
| receiveStreet      | java.lang.String  | (收件人) 街道地址(增值税发票信息)        | -   |
| registerAccountId  | java.lang.String  | (公司)银行账号                   | -   |
| registerBank       | java.lang.String  | (公司)开户银行                   | -   |
| registerCode       | java.lang.String  | (注册)省市区编码                  | -   |
| registerCodeText   | java.lang.String  | (注册)省市区文本                  | -   |
| registerPhone      | java.lang.String  | （公司）注册电话                   | -   |
| registerStreet     | java.lang.String  | (注册)街道地址                   | -   |
| taxpayerIdentify   | java.lang.String  | 纳税人识别号                     | -   |

#### result.tradeTerms: 订单交易条款信息

| 参数名            | 类型                   | 描述                                                                                                                                                                                                          | 示例值                    |
|----------------|----------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------|
| payStatus      | java.lang.String     | 支付状态。国际站：WAIT_PAY(未支付),PAYER_PAID(已完成支付),PART_SUCCESS(部分支付成功),PAY_SUCCESS(支付成功),CLOSED(风控关闭),CANCELLED(支付撤销),SUCCESS(成功),FAIL(失败)。 1688:未付款，1;已付款，2;全额退款，4;卖家有收到钱，回款完成，6 ;未创建外部支付单，7; 付款前取消 ，8 ; 正在支付中 ，9 6 |                        |
| payTime        | java.util.Date       | 完成阶段支付时间                                                                                                                                                                                                    | *****************+0800 |
| payWay         | java.lang.String     | 支付方式。国际站：ECL(融资支付),CC(信用卡),TT(线下TT),ACH(echecking支付)。 1688:支付宝1;网商银行金票2;赊购(诚e赊)3;网商银行大额转账4;电子承兑汇票 6;账期支付 7;虚拟支付8;不进行任何支付动作 9;零售通赊购支付 10 1                                                                   |                        |
| phasAmount     | java.math.BigDecimal | 阶段金额                                                                                                                                                                                                        | 1158                   |
| phase          | java.lang.Long       | 阶段                                                                                                                                                                                                          | 2988886630908522       |
| phaseCondition | java.lang.String     | 阶段条件，1688无此内容                                                                                                                                                                                               |                        |
| phaseDate      | java.lang.String     | 阶段时间，1688无此内容                                                                                                                                                                                               |                        |
| payWayDesc     | String               | 支付方式                                                                                                                                                                                                        | 支付宝                    |

#### result.nativeLogistics: 国内物流信息

| 参数名           | 类型               | 描述      | 示例值         |
|---------------|------------------|---------|-------------|
| address       | java.lang.String | 详细地址    | 浙江省杭州市滨江区   |
| area          | java.lang.String | 县，区     | 滨江区         |
| areaCode      | java.lang.String | 省市区编码   | 330108      |
| city          | java.lang.String | 城市      | 杭州市         |
| contactPerson | java.lang.String | 联系人姓名   | 张三          |
| fax           | java.lang.String | 传真      |             |
| mobile        | java.lang.String | 手机      | 15000001111 |
| province      | java.lang.String | 省份      | 浙江省         |
| telephone     | java.lang.String | 电话      | 7314065     |
| zip           | java.lang.String | 邮编      | 888888      |
| townCode      | java.lang.String | 镇，街道地址码 | 4403011     |
| town          | java.lang.String | 镇，街道    | 测试街道        |

#### result.guaranteesTerms: 保障条款

| 参数名                  | 类型               | 描述     | 示例值               |
|----------------------|------------------|--------|-------------------|
| assuranceInfo        | java.lang.String | 保障条款   | 自愿选择向买家提供"交期保障"服务 |
| assuranceType        | java.lang.String | 保障方式   | jqbz              |
| qualityAssuranceType | java.lang.String | 质量保证类型 | 交期保障              |
| value                | java.lang.String | 保障条款值  | 6                 |

#### result.baseInfo: 订单基础信息

| 参数名               | 类型                                 | 描述                                                                                                                                                                                                                                                                                                                          | 示例值                    |
|-------------------|------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------|
| allDeliveredTime  | java.util.Date                     | 完全发货时间                                                                                                                                                                                                                                                                                                                      |                        |
| businessType      | java.lang.String                   | 业务类型。国际站：ta(信保),wholesale(在线批发)。 中文站：普通订单类型 = "cn"; 大额批发订单类型 = "ws"; 普通拿样订单类型 = "yp"; 一分钱拿样订单类型 = "yf"; 倒批(限时折扣)订单类型 = "fs"; 加工定制订单类型 = "cz"; 协议采购订单类型 = "ag"; 伙拼订单类型 = "hp"; 供销订单类型 = "supply"; 淘工厂订单 = "factory"; 快订下单 = "quick"; 享拼订单 = "xiangpin"; 当面付 = "f2f"; 存样服务 = "cyfw"; 代销订单 = "sp"; 微供订单 = "wg";零售通 = "lst"; cb 
| buyerID           | java.lang.String                   | 买家主账号id                                                                                                                                                                                                                                                                                                                     | b2b-397390228          |
| buyerMemo         | java.lang.String                   | 买家备忘信息                                                                                                                                                                                                                                                                                                                      |                        |
| buyerSubID        | java.lang.Long                     | 买家子账号id，1688无此内容                                                                                                                                                                                                                                                                                                            |                        |
| completeTime      | java.util.Date                     | 完成时间                                                                                                                                                                                                                                                                                                                        |                        |
| createTime        | java.util.Date                     | 创建时间                                                                                                                                                                                                                                                                                                                        | 20180807152315000+0800 |
| currency          | java.lang.String                   | 币种，币种，整个交易单使用同一个币种。值范围：USD,RMB,HKD,GBP,CAD,AUD,JPY,KRW,EUR                                                                                                                                                                                                                                                                  |                        |
| id                | java.lang.Long                     | 交易id                                                                                                                                                                                                                                                                                                                        | 1997017368139085       |
| modifyTime        | java.util.Date                     | 修改时间                                                                                                                                                                                                                                                                                                                        | 20180807152533000+0800 |
| payTime           | java.util.Date                     | 付款时间，如果有多次付款，这里返回的是首次付款时间                                                                                                                                                                                                                                                                                                   | *****************+0800 |
| receivingTime     | java.util.Date                     | 收货时间，这里返回的是完全收货时间                                                                                                                                                                                                                                                                                                           |                        |
| refund            | java.math.BigDecimal               | 退款金额，单位为元                                                                                                                                                                                                                                                                                                                   | 0                      |
| remark            | java.lang.String                   | 备注，1688指下单时的备注                                                                                                                                                                                                                                                                                                              | 测试备注                   |
| sellerID          | java.lang.String                   | 卖家主账号id                                                                                                                                                                                                                                                                                                                     | b2b-23187037           |
| sellerSubID       | java.lang.Long                     | 卖家子账号id，1688无此内容                                                                                                                                                                                                                                                                                                            |                        |
| shippingFee       | java.math.BigDecimal               | 运费，单位为元                                                                                                                                                                                                                                                                                                                     | 10                     |
| status            | java.lang.String                   | 交易状态，waitbuyerpay:等待买家付款;waitsellersend:等待卖家发货;waitlogisticstakein:等待物流公司揽件;waitbuyerreceive:等待买家收货;waitbuyersign:等待买家签收;signinsuccess:买家已签收;confirm_goods:已收货;success:交易成功;cancel:交易取消;terminated:交易终止;未枚举:其他状态                                                                                                            | waitbuyerpay           |
| totalAmount       | java.math.BigDecimal               | 应付款总金额，totalAmount = ∑itemAmount + shippingFee，单位为元                                                                                                                                                                                                                                                                         | 528                    |
| buyerRemarkIcon   | String                             | 买家备忘标志                                                                                                                                                                                                                                                                                                                      |                        |
| discount          | Long                               | 折扣信息，单位分                                                                                                                                                                                                                                                                                                                    | -226                   |
| buyerContact      | tradeContact                       | 买家联系人                                                                                                                                                                                                                                                                                                                       | {}                     |
| sellerContact     | tradeContact                       | 卖家联系人                                                                                                                                                                                                                                                                                                                       | {}                     |
| tradeType         | String                             | 1:担保交易 2:预存款交易 3:ETC境外收单交易 4:即时到帐交易 5:保障金安全交易 6:统一交易流程 7:分阶段付款 8.货到付款交易 9.信用凭证支付交易 10.账期支付交易 50060                                                                                                                                                                                                                          |                        |
| refundStatus      | String                             | 订单的售中退款状态                                                                                                                                                                                                                                                                                                                   |                        |
| refundStatusForAs | String                             | 订单的售后退款状态                                                                                                                                                                                                                                                                                                                   |                        |
| refundPayment     | Long                               | 退款金额                                                                                                                                                                                                                                                                                                                        | 0                      |
| idOfStr           | String                             | 交易id(字符串格式)                                                                                                                                                                                                                                                                                                                 | 1997017368139085       |
| alipayTradeId     | java.lang.String                   | 外部支付交易Id                                                                                                                                                                                                                                                                                                                    | UNCREAT                |
| receiverInfo      | orderReceiverInfo                  | 收件人信息                                                                                                                                                                                                                                                                                                                       | {}                     |
| buyerLoginId      | java.lang.String                   | 买家loginId，旺旺Id                                                                                                                                                                                                                                                                                                              | alitestforisv01        |
| sellerLoginId     | java.lang.String                   | 卖家oginId，旺旺Id                                                                                                                                                                                                                                                                                                               | alitestforisv02        |
| buyerUserId       | java.lang.Long                     | 买家数字id                                                                                                                                                                                                                                                                                                                      | 39739022               |
| sellerUserId      | java.lang.Long                     | 卖家数字id                                                                                                                                                                                                                                                                                                                      | 23187037               |
| buyerAlipayId     | java.lang.String                   | 买家支付宝id                                                                                                                                                                                                                                                                                                                     | 20881314957453         |
| sellerAlipayId    | java.lang.String                   | 卖家支付宝id                                                                                                                                                                                                                                                                                                                     | 20887127689595         |
| confirmedTime     | java.util.Date                     | 确认时间                                                                                                                                                                                                                                                                                                                        |                        |
| closeReason       | java.lang.String                   | 关闭原因                                                                                                                                                                                                                                                                                                                        |                        |
| sumProductPayment | java.math.BigDecimal               | 产品总金额(该订单产品明细表中的产品金额的和)，单位元                                                                                                                                                                                                                                                                                                 | 258                    |
| stepOrderList     | List<alibaba.trade.StepOrderModel> | [交易3.0]分阶段交易，分阶段订单list                                                                                                                                                                                                                                                                                                      |                        |
| stepAgreementPath | java.lang.String                   | 分阶段法务协议地址                                                                                                                                                                                                                                                                                                                   |                        |
| stepPayAll        | java.lang.Boolean                  | 是否一次性付款                                                                                                                                                                                                                                                                                                                     | false                  |
| buyerFeedback     | java.lang.String                   | 买家留言                                                                                                                                                                                                                                                                                                                        | 测试买家留言                 |
| relatedCode       | java.lang.String                   | 订单关联码(如属于哪个活动等)                                                                                                                                                                                                                                                                                                             |                        |
| overSeaOrder      | java.lang.Boolean                  | 是否海外代发订单，是：true                                                                                                                                                                                                                                                                                                             | true                   |
| subBuyerLoginId   | String                             | 买家子账号                                                                                                                                                                                                                                                                                                                       |                        |
| sellerOrder       | java.lang.Boolean                  | 是否自主订单（邀约订单）                                                                                                                                                                                                                                                                                                                |                        |
| preOrderId        | java.lang.Long                     | 预订单ID                                                                                                                                                                                                                                                                                                                       |                        |
| refundId          | java.lang.String                   | 退款单ID                                                                                                                                                                                                                                                                                                                       |                        |
| flowTemplateCode  | String                             | 4.0交易流程模板code                                                                                                                                                                                                                                                                                                               |                        |

##### result.baseInfo.tradeContact 联系人

| 参数名          | 类型               | 描述          | 示例值             |
|--------------|------------------|-------------|-----------------|
| phone        | String           | 联系电话        | 0912-2525263    |
| fax          | String           | 传真          |                 |
| email        | String           | 邮箱          |                 |
| imInPlatform | String           | 联系人在平台的IM账号 | alitestforisv01 |
| name         | String           | 联系人名称       | 张三              |
| mobile       | String           | 联系人手机号      | 15123543625     |
| companyName  | java.lang.String | 公司名称        | 阿里巴巴            |

##### result.baseInfo.receiverInfo 收件人信息

| 参数名            | 类型               | 描述               | 示例值              |
|----------------|------------------|------------------|------------------|
| toFullName     | java.lang.String | 收件人              | 张三               |
| toDivisionCode | java.lang.String | 收货人地址区域编码        | 440311           |
| toMobile       | java.lang.String | 收件人移动电话          | 15236457596      |
| toPhone        | java.lang.String | 收件人电话            | 0912-44548585    |
| toPost         | java.lang.String | 邮编               | 000000           |
| toTownCode     | java.lang.String | 收货人街道或镇区域编码，可能为空 | *********        |
| toArea         | java.lang.String | 收货地址             | 广东省 深圳市 龙华区 大浪街道 |

##### result.baseInfo.stepOrderList 收件人信息

| 参数名                  | 类型                   | 描述                                                                                                                                                                                                                                            | 示例值 |
|----------------------|----------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----|
| stepOrderId          | java.lang.Long       | 阶段id                                                                                                                                                                                                                                          |     |
| stepOrderStatus      | java.lang.String     | waitactivate 未开始（待激活） waitsellerpush 等待卖家推进 success 本阶段完成 settlebill 分账 cancel 本阶段终止 inactiveandcancel 本阶段未开始便终止 waitbuyerpay 等待买家付款 waitsellersend 等待卖家发货 waitbuyerreceive 等待买家确认收货 waitselleract 等待卖家XX操作 waitbuyerconfirmaction 等待买家确认XX操作 |     |
| stepPayStatus        | java.lang.Integer    | 1 未冻结/未付款 2 已冻结/已付款 4 已退款 6 已转交易 8 交易未付款被关闭                                                                                                                                                                                                   |     |
| stepNo               | java.lang.Integer    | 阶段序列：1、2、3...                                                                                                                                                                                                                                 |     |
| lastStep             | java.lang.Boolean    | 是否最后一个阶段                                                                                                                                                                                                                                      |     |
| hasDisbursed         | java.lang.Boolean    | 是否已打款给卖家                                                                                                                                                                                                                                      |     |
| payFee               | java.math.BigDecimal | 创建时需要付款的金额，不含运费                                                                                                                                                                                                                               |     |
| actualPayFee         | java.math.BigDecimal | 应付款（含运费）= 单价×数量-单品优惠-店铺优惠+运费+修改的金额（除运费外，均指分摊后的金额）                                                                                                                                                                                             |     |
| discountFee          | java.math.BigDecimal | 本阶段分摊的店铺优惠                                                                                                                                                                                                                                    |     |
| itemDiscountFee      | java.math.BigDecimal | 本阶段分摊的单品优惠                                                                                                                                                                                                                                    |     |
| price                | java.math.BigDecimal | 本阶段分摊的单价                                                                                                                                                                                                                                      |     |
| amount               | java.lang.Long       | 购买数量                                                                                                                                                                                                                                          |     |
| postFee              | java.math.BigDecimal | 运费                                                                                                                                                                                                                                            |     |
| adjustFee            | java.math.BigDecimal | 修改价格修改的金额                                                                                                                                                                                                                                     |     |
| gmtCreate            | java.util.Date       | 创建时间                                                                                                                                                                                                                                          |     |
| gmtModified          | java.util.Date       | 修改时间                                                                                                                                                                                                                                          |     |
| enterTime            | java.util.Date       | 开始时间                                                                                                                                                                                                                                          |     |
| payTime              | java.util.Date       | 付款时间                                                                                                                                                                                                                                          |     |
| sellerActionTime     | java.util.Date       | 卖家操作时间                                                                                                                                                                                                                                        |     |
| endTime              | java.util.Date       | 本阶段结束时间                                                                                                                                                                                                                                       |     |
| messagePath          | java.lang.String     | 卖家操作留言路径                                                                                                                                                                                                                                      |     |
| picturePath          | java.lang.String     | 卖家上传图片凭据路径                                                                                                                                                                                                                                    |     |
| message              | java.lang.String     | 卖家操作留言                                                                                                                                                                                                                                        |     |
| templateId           | java.lang.Long       | 使用的模板id                                                                                                                                                                                                                                       |     |
| stepName             | java.lang.String     | 当前步骤的名称                                                                                                                                                                                                                                       |     |
| sellerActionName     | java.lang.String     | 卖家操作名称                                                                                                                                                                                                                                        |     |
| buyerPayTimeout      | java.lang.Long       | 买家不付款的超时时间(秒)                                                                                                                                                                                                                                 |     |
| buyerConfirmTimeout  | java.lang.Long       | 买家不确认的超时时间                                                                                                                                                                                                                                    |     |
| needLogistics        | java.lang.Boolean    | 是否需要物流                                                                                                                                                                                                                                        |     |
| needSellerAction     | java.lang.Boolean    | 是否需要卖家操作和买家确认                                                                                                                                                                                                                                 |     |
| transferAfterConfirm | java.lang.Boolean    | 阶段结束是否打款                                                                                                                                                                                                                                      |     |
| needSellerCallNext   | java.lang.Boolean    | 是否需要卖家推进                                                                                                                                                                                                                                      |     |
| instantPay           | java.lang.Boolean    | 是否允许即时到帐                                                                                                                                                                                                                                      |     |

#### result.orderBizInfo: 订单业务信息

| 参数名               | 类型                             | 描述                                                                         | 示例值                 |
|-------------------|--------------------------------|----------------------------------------------------------------------------|---------------------|
| odsCyd            | java.lang.Boolean              | 是否采源宝订单                                                                    | true                |
| accountPeriodTime | java.lang.String               | 账期交易订单的到账时间                                                                | yyyy-MM-dd HH:mm:ss |
| creditOrder       | java.lang.Boolean              | 为true，表示下单时选择了诚e赊交易方式。注意不等同于"诚e赊支付"，支付时有可能是支付宝付款，具体支付方式查询tradeTerms.payWay | false               |
| creditOrderDetail | alibaba.creditOrder.forDetail  | 诚e赊支付详情，只有使用诚e赊付款时返回                                                       |                     |
| preOrderInfo      | alibaba.order.preOrder.forRead | 预订单信息                                                                      | {}                  |
| dropshipping      | Boolean                        | 是否dropshipping订单，该类型订单不允许合并发货                                              | true                |
| shippingInsurance | String                         | givenByPlatform:平台赠送运费险 givenByMerchant:商家赠送运费险，为空表示订单无运费险                 | givenByMerchant     |

##### result.orderBizInfo.creditOrderDetail: 诚e赊支付详情

| 参数名                | 类型               | 描述     | 示例值                 |
|--------------------|------------------|--------|---------------------|
| payAmount          | java.lang.Long   | 订单金额   | 10                  |
| createTime         | java.lang.String | 支付时间   | 2018-01-01 00:00:00 |
| status             | java.lang.String | 状态     | END                 |
| gracePeriodEndTime | java.lang.String | 最晚还款时间 | 2018-01-01 00:00:00 |
| statusStr          | java.lang.String | 状态描述   | 已完结                 |
| restRepayAmount    | java.lang.Long   | 应还金额   | 11                  |

##### result.orderBizInfo.preOrderInfo: 预订单信息

| 参数名               | 类型      | 描述           | 示例值   |
|-------------------|---------|--------------|-------|
| appkey            | String  | 创建预订单的appkey | 12345 |
| marketName        | String  | 创建预订单时传入的市场名 | dxc   |
| createPreOrderApp | Boolean | 是否当前ERP创建    | false |

#### result.tradeProductItems: 商品信息

| 参数名                | 类型                                                        | 描述                                                                                                                                                               | 示例值                                                                                              |
|--------------------|-----------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------|
| cargoNumber        | java.lang.String                                          | 指定单品货号，国际站无需关注。该字段不一定有值，仅仅在下单时才会把货号记录(如果卖家设置了单品货号的话)。别的订单类型的货号只能通过商品接口去获取。请注意：通过商品接口获取时的货号和下单时的货号可能不一致，因为下单完成后卖家可能修改商品信息，改变了货号。                                  | E0003                                                                                            |
| description        | java.lang.String                                          | 描述,1688无此信息                                                                                                                                                      |                                                                                                  |
| itemAmount         | java.math.BigDecimal                                      | 实付金额，单位为元                                                                                                                                                        | 279                                                                                              |
| name               | java.lang.String                                          | 商品名称                                                                                                                                                             | 测试商品                                                                                             |
| price              | java.math.BigDecimal                                      | 原始单价，以元为单位                                                                                                                                                       | 3                                                                                                |
| productID          | java.lang.Long                                            | 产品ID（非在线产品为空）                                                                                                                                                    | ************                                                                                     |
| productImgUrl      | List<String>                                              | 商品图片url                                                                                                                                                          | <https://cbu01.alicdn.com/img/order/trading/025/894/036055839091/8536232543_347415001.80x80.jpg> |
| productSnapshotUrl | java.lang.String                                          | 产品快照url，交易订单产生时会自动记录下当时的商品快照，供后续纠纷时参考                                                                                                                            | <https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=190938550630498520>              |
| quantity           | java.math.BigDecimal                                      | 以unit为单位的数量，例如多少个、多少件、多少箱、多少吨                                                                                                                                    | 20                                                                                               |
| refund             | java.math.BigDecimal                                      | 退款金额，单位为元                                                                                                                                                        | 0                                                                                                |
| skuID              | java.lang.Long                                            | skuID                                                                                                                                                            | ************                                                                                     |
| sort               | java.lang.Integer                                         | 排序字段，商品列表按此字段进行排序，从0开始，1688不提供                                                                                                                                   |                                                                                                  |
| status             | java.lang.String                                          | 子订单状态                                                                                                                                                            | waitbuyerpay                                                                                     |
| subItemID          | java.lang.Long                                            | 商品明细条目ID                                                                                                                                                         | ***************22                                                                                |
| type               | java.lang.String                                          | 类型，国际站使用，供卖家标注商品所属类型                                                                                                                                             | common                                                                                           |
| unit               | java.lang.String                                          | 售卖单位 例如：个、件、箱、吨                                                                                                                                                  | 件                                                                                                |
| weight             | java.lang.String                                          | 重量 按重量单位计算的重量，例如：100                                                                                                                                             |                                                                                                  |
| weightUnit         | java.lang.String                                          | 重量单位 例如：g，kg，t                                                                                                                                                   |                                                                                                  |
| guaranteesTerms    | List<alibaba.openplatform.trade.model.GuaranteeTermsInfo> | 保障条款，此字段仅针对1688                                                                                                                                                  |                                                                                                  |
| productCargoNumber | java.lang.String                                          | 指定商品货号，该字段不一定有值，在下单时才会把货号记录。别的订单类型的货号只能通过商品接口去获取。请注意：通过商品接口获取时的货号和下单时的货号可能不一致，因为下单完成后卖家可能修改商品信息，改变了货号。该字段和cargoNUmber的区别是：该字段是定义在商品级别上的货号，cargoNUmber是定义在单品级别的货号 | C017                                                                                             |
| skuInfos           | List<alibaba.trade.SkuItemDesc>                           | SKU属性描述                                                                                                                                                          | []                                                                                               |
| entryDiscount      | Long                                                      | 订单明细涨价或降价的金额                                                                                                                                                     | 0                                                                                                |
| specId             | java.lang.String                                          | 订单销售属性ID                                                                                                                                                         | 2b3878b01d251c057668066c085d75                                                                   |
| quantityFactor     | java.math.BigDecimal                                      | 以unit为单位的quantity精度系数，值为10的幂次，例如:quantityFactor=1000,unit=吨，那么quantity的最小精度为0.001吨                                                                               | 1                                                                                                |
| statusStr          | java.lang.String                                          | 子订单状态描述                                                                                                                                                          | 等待买家付款                                                                                           |
| refundStatus       | java.lang.String                                          | WAIT_SELLER_AGREE 等待卖家同意 REFUND_SUCCESS 退款成功 REFUND_CLOSED 退款关闭 WAIT_BUYER_MODIFY 待买家修改 WAIT_BUYER_SEND 等待买家退货 WAIT_SELLER_RECEIVE 等待卖家确认收货                      |                                                                                                  |
| closeReason        | java.lang.String                                          | 关闭原因                                                                                                                                                             |                                                                                                  |
| logisticsStatus    | java.lang.Integer                                         | 1 未发货 2 已发货 3 已收货 4 已经退货 5 部分发货 8 还未创建物流订单 1                                                                                                                     |                                                                                                  |
| refundId           | String                                                    | 售中退款单号                                                                                                                                                           | TQ123123                                                                                         |
| refundIdForAs      | String                                                    | 售后退款单号                                                                                                                                                           | TQ123123                                                                                         |
| relatedCode        | String                                                    | 子订单关联码                                                                                                                                                           | 1365465                                                                                          |

##### result.tradeProductItems.guaranteesTerms: 保障条款

| 参数名                  | 类型     | 描述     | 示例值               |
|----------------------|--------|--------|-------------------|
| assuranceInfo        | String | 保障条款   | 自愿选择向买家提供"交期保障"服务 |
| assuranceType        | String | 保障方式   | jqbz              |
| qualityAssuranceType | String | 质量保证类型 | 交期保障              |
| value                | String | 保障条款值  | 6                 |

##### result.tradeProductItems.skuInfos: SKU属性描述

| 参数名   | 类型     | 描述  | 示例值 |
|-------|--------|-----|-----|
| name  | String | 属性名 | 颜色  |
| value | String | 属性值 | 黑色  |

#### result.nativeLogistics: 物流信息

| 参数名           | 类型     | 描述      | 示例值         |
|---------------|--------|---------|-------------|
| address       | String | 详细地址    | 浙江省杭州市滨江区   |
| area          | String | 县、区     | 滨江区         |
| areaCode      | String | 省市区编码   | 330108      |
| city          | String | 城市      | 杭州市         |
| contactPerson | String | 联系人姓名   | 张三          |
| fax           | String | 传真      |             |
| mobile        | String | 手机      | 15000001111 |
| province      | String | 省份      | 浙江省         |
| telephone     | String | 电话      | 7314065     |
| zip           | String | 邮编      | 888888      |
| townCode      | String | 镇、街道地址码 | 4403011     |
| town          | String | 镇、街道    | 测试街道        |

#### result.overseasExtraAddress: 跨境地址扩展信息

| 参数名                 | 类型     | 描述     | 示例值             |
|---------------------|--------|--------|-----------------|
| channelName         | String | 路线名称   | 欧洲小包            |
| channelId           | String | 路线id   | 1               |
| shippingCompanyId   | String | 货代公司id | 222             |
| shippingCompanyName | String | 货代公司名称 | 货代公司1           |
| countryCode         | String | 国家code | UK              |
| country             | String | 国家     | 英国              |
| email               | String | 买家邮箱   | <<EMAIL>> |

#### result.customs: 跨境报关信息

| 参数名         | 类型                                    | 描述     | 示例值                    |
|-------------|---------------------------------------|--------|------------------------|
| id          | Long                                  | id     | 1                      |
| gmtCreate   | Date                                  | 创建时间   | 20170806114526000+0800 |
| gmtModified | Date                                  | 修改时间   | 20170806114526000+0800 |
| buyerId     | Long                                  | 买家id   | 123456                 |
| orderId     | String                                | 主订单id  | 123123123123123        |
| type        | Integer                               | 业务数据类型 | 1                      |
| attributes  | List<alibaba.trade.CustomsAttributes> | 报关信息列表 |                        |

##### result.customs.attributes: 报关信息列表

| 参数名      | 类型     | 描述     | 示例值    |
|----------|--------|--------|--------|
| sku      | String | sku标识  | 1234   |
| cName    | String | 中文名称   | 测试     |
| enName   | String | 英文名称   | test   |
| amount   | Double | 申报价值   | 3000.0 |
| quantity | Double | 数量     | 1.0    |
| weight   | Double | 重量(kg) | 0.5    |
| currency | String | 报关币种   | CNY    |

#### result.orderRateInfo: 订单评价信息

| 参数名              | 类型      | 描述     | 示例值 |
|------------------|---------|--------|-----|
| buyerRateStatus  | Integer | 买家评价状态 | 5   |
| sellerRateStatus | Integer | 卖家评价状态 | 5   |

#### result.orderInvoiceInfo: 发票信息

| 参数名                | 类型      | 描述      | 示例值 |
|--------------------|---------|---------|-----|
| invoiceCompanyName | String  | 发票公司名称  | -   |
| invoiceType        | Integer | 发票类型    | -   |
| localInvoiceId     | Long    | 本地发票号   | -   |
| orderId            | Long    | 订单Id    | -   |
| receiveCode        | String  | 收件人区域编码 | -   |
| receiveCodeText    | String  | 收件人区域文本 | -   |
| receiveMobile      | String  | 收件人手机   | -   |
| receiveName        | String  | 收件人姓名   | -   |
| receivePhone       | String  | 收件人电话   | -   |
| receivePost        | String  | 收件人邮编   | -   |
| receiveStreet      | String  | 收件人街道   | -   |
| registerAccountId  | String  | 公司银行账号  | -   |
| registerBank       | String  | 公司开户银行  | -   |
| registerCode       | String  | 注册区域编码  | -   |
| registerCodeText   | String  | 注册区域文本  | -   |
| registerPhone      | String  | 公司电话    | -   |
| registerStreet     | String  | 注册街道    | -   |
| taxpayerIdentify   | String  | 纳税人识别号  | -   |

#### result.tradeTerms: 交易条款

| 参数名            | 类型         | 描述     | 示例值                    |
|----------------|------------|--------|------------------------|
| payStatus      | String     | 支付状态   | 6                      |
| payTime        | Date       | 支付时间   | *****************+0800 |
| payWay         | String     | 支付方式   | 1                      |
| phaseAmount    | BigDecimal | 阶段金额   | 1158                   |
| phase          | Long       | 阶段     | ****************       |
| phaseCondition | String     | 阶段条件   |                        |
| phaseDate      | String     | 阶段时间   |                        |
| payWayDesc     | String     | 支付方式描述 | 支付宝                    |

## 特殊字段说明

1. **payStatus 支付状态值说明**
    - 1: 未付款
    - 2: 已付款
    - 4: 全额退款
    - 6: 卖家有收到钱，回款完成
    - 7: 未创建外部支付单
    - 8: 付款前取消
    - 9: 正在支付中

2. **payWay 支付方式值说明**
    - 1: 支付宝
    - 2: 网商银行金票
    - 3: 赊账(途虎)
    - 4: 网商银行大额转账
    - 6: 电子承兑汇票
    - 7: 账期支付
    - 8: 虚拟支付
    - 9: 不进行任何支付动作
    - 10: 零售通路购支付

3. **logisticsStatus 物流状态值说明**
    - 1: 未发货
    - 2: 已发货
    - 3: 已收货
    - 4: 已经退
    - 5: 部分发货
    - 8: 还未创建物流订单

4. **评价状态值说明**
    - 4: 已评论
    - 5: 未评论
    - 6: 不需要评论

5. **发票类型值说明**
    - 0: 普通发票
    - 1: 增值税发票
    - 9: 未知类型

## 请求示例

```json
{
    // 系统级参数
    "_aop_timestamp": "1628328195000",
    "_aop_signature": "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
    "access_token": "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
    
    // 应用级参数
    "bizTypes": ["cn", "ws"],
    "createStartTime": "20230101000000000+0800",
    "createEndTime": "20230331235959999+0800",
    "orderStatus": "waitbuyerpay",
    "page": 1,
    "pageSize": 20,
    "needBuyerAddressAndPhone": true,
    "needMemoInfo": true
}
```

## 响应示例

```json
{
    "errorCode": null,
    "errorMessage": null,
    "totalRecord": 528,
    "result": [
        {
            "baseInfo": {
                "id": 1997017368139085,
                "idOfStr": "1997017368139085",
                "businessType": "cb",
                "buyerID": "b2b-39739022B",
                "buyerSubID": null,
                "sellerID": "b2b-23187037",
                "createTime": "20180807152315000+0800",
                "modifyTime": "20180807152533000+0800",
                "payTime": "*****************+0800",
                "status": "waitbuyerpay",
                "totalAmount": 528.00,
                "refund": 0.00,
                "shippingFee": 10.00,
                "buyerMemo": "测试备注",
                "currency": "USD",
                "discount": -226,
                "tradeType": "50060",
                "buyerLoginId": "alitestforisv01",
                "sellerLoginId": "alitestforisv02"
            },
            "quoteList": [
                {
                    "productQuoteName": "物料01",
                    "price": 100,
                    "count": 10.0
                }
            ],
            "orderBizInfo": {
                "odsCyd": true,
                "accountPeriodTime": "2023-04-01 12:00:00",
                "creditOrder": false,
                "dropshipping": true,
                "shippingInsurance": "givenByMerchant"
            },
            "tradeProductItems": [
                {
                    "cargoNumber": "E0003",
                    "itemAmount": 279.00,
                    "name": "测试商品",
                    "price": 3.00,
                    "productID": ************,
                    "productImgUrl": ["https://cbu01.alicdn.com/img/..."],
                    "productSnapshotUrl": "https://trade.1688.com/...",
                    "quantity": 20.00,
                    "skuID": ************,
                    "status": "waitbuyerpay",
                    "subItemID": ***************,
                    "unit": "件",
                    "entryDiscount": 0,
                    "specId": "2b3878b01d251c0576680",
                    "quantityFactor": 1,
                    "statusStr": "等待买家付款"
                }
            ],
            "nativeLogistics": {
                "address": "浙江省杭州市滨江区",
                "area": "滨江区",
                "areaCode": "330108",
                "city": "杭州市",
                "contactPerson": "张三",
                "mobile": "15000001111",
                "province": "浙江省",
                "telephone": "7314065",
                "zip": "888888",
                "townCode": "4403011",
                "town": "测试街道"
            },
            "overseasExtraAddress": {
                "channelName": "欧洲小包",
                "channelId": "1",
                "shippingCompanyId": "222",
                "shippingCompanyName": "货代公司1",
                "countryCode": "UK",
                "country": "英国",
                "email": "<EMAIL>"
            },
            "customs": {
                "id": 1,
                "gmtCreate": "20170806114526000+0800",
                "gmtModified": "20170806114526000+0800",
                "buyerId": 123456,
                "orderId": "123123123123123",
                "type": 1,
                "attributes": [
                    {
                        "sku": "1234",
                        "cName": "测试",
                        "enName": "test",
                        "amount": 3000.0,
                        "quantity": 1.0,
                        "weight": 0.5,
                        "currency": "CNY"
                    }
                ]
            },
            "orderRateInfo": {
                "buyerRateStatus": 5,
                "sellerRateStatus": 5
            },
            "orderInvoiceInfo": {
                "invoiceCompanyName": "阿里巴巴",
                "invoiceType": 1,
                "localInvoiceId": 123456,
                "orderId": *********,
                "receiveCode": "330108",
                "receiveCodeText": "浙江省杭州市滨江区",
                "receiveMobile": "15000001111",
                "receiveName": "张三",
                "taxpayerIdentify": "91330XXX1234567890"
            },
            "tradeTerms": [
                {
                    "payStatus": "6",
                    "payTime": "*****************+0800",
                    "payWay": "1",
                    "phaseAmount": 1158.00,
                    "phase": ****************,
                    "payWayDesc": "支付宝"
                }
            ]
        }
    ]
}
```
