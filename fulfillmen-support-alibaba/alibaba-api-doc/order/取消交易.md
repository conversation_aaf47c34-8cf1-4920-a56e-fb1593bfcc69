# 取消交易

买家或卖家取消交易的接口。注意只有特定状态的交易才能取消，1688仅支持取消未付款的订单。

## 使用限制

1. 仅支持取消未付款订单
2. 只有订单的买卖双方可以取消
3. 取消后订单状态不可恢复
4. 需要提供取消原因

## 所属解决方案/能力

- 档口经营解决方案（买家版）
- 订单管理解决方案（商家版）
- 采购解决方案（服务商版）
- 采购解决方案（买家自用版）
- 订单管理解决方案（加密服务商新版）
- 分销工具解决方案
- ERP分销解决方案（分销商侧）
- 代发解决方案（服务商版）
- 1688分销严选采购解决方案（服务商版）
- 1688分销严选采购解决方案（分销买家版）
- 代发解决方案（分销买家版）
- 新版电商ERP解决方案
- 供应商分销管理解决方案
- 1688国家服务站解决方案
- 跨境代采寻源比价搜索解决方案（国际化）
- 跨境超买寻源比价搜索解决方案
- 跨境ERP/独立站SaaS数字化解决方案
- 跨境大客户寻源通解决方案
- 跨境寻源通加工定制解决方案
- 即时零售场景采购解决方案

## 请求URL

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.cancel/${APPKEY}
```

## 请求参数

### 系统级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| _aop_timestamp | String | 否 | 请求时间戳 | 1623391999000 |
| _aop_signature | String | 是 | 请求签名 | XXXXXX |
| access_token | String | 是 | 用户授权令牌 | XXXXXX |

### 应用级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| webSite | String | 是 | 站点信息:<br/>- 1688: 1688网站<br/>- alibaba: 国际站 | 1688 |
| tradeID | Long | 是 | 交易ID(订单号) | 202711458975969812 |
| cancelReason | String | 是 | 取消原因:<br/>- buyerCancel: 买家取消订单<br/>- sellerGoodsLack: 卖家库存不足<br/>- other: 其它 | other |
| remark | String | 否 | 备注说明 | 暂时不需要此商品 |

## 返回结果

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| success | Boolean | 是否处理成功 | true |
| errorCode | String | 错误码 | ORDER_STATUS_ERROR |
| errorMessage | String | 错误信息 | 订单状态错误 |

## 错误码

| 错误码 | 错误描述 | 解决方案 |
|--------|----------|----------|
| ORDER_STATUS_ERROR | 订单状态错误 | 只能取消待支付的订单，需要先确认订单状态 |
| 400_3 | 没有权限取消该订单 | 只有订单的买卖双方才能取消订单，确认授权用户是否该订单的买卖双方 |
| ORDER_NOT_EXIST | 订单不存在 | 确认订单号是否正确 |

## 请求示例

### 请求参数示例

```json
{
    "webSite": "1688",
    "tradeID": "202711458975969812",
    "cancelReason": "other",
    "remark": "暂时不需要此商品"
}
```

### 返回示例

```json
{
    "success": true
}
```

## 注意事项

1. 取消订单前请先确认订单状态是否可取消
2. 必须提供合理的取消原因
3. 取消后订单状态不可恢复
4. 如遇错误需根据错误码排查原因
