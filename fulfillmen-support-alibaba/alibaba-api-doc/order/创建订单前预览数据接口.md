# 创建订单预览接口

> com.alibaba.trade:alibaba.createOrder.preview-1

## 接口描述

订单创建只允许购买同一个供应商的商品。本接口返回创建订单相关的优惠等信息。 1、校验商品数据是否允许订购。 2、校验代销关系 3、校验库存、起批量、是否满足混批条件

## 请求URL

`POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.createOrder.preview/${APPKEY}`

## 请求参数

### 系统级输入参数

| 参数名            | 类型     | 是否必填 | 描述     |
|----------------|--------|------|--------|
| _aop_timestamp | String | 是    | 请求时间戳  |
| _aop_signature | String | 是    | 请求签名   |
| access_token   | String | 是    | 用户授权令牌 |

### 应用级输入参数

| 名称                  | 类型                                                | 是否必须 | 描述                                             | 示例值                                                                                                                                                                                                                                         |
|---------------------|---------------------------------------------------|------|------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| addressParam        | alibaba.trade.fast.address                        | 是    | 收货地址信息                                         | {"address":"网商路699号","phone": "0517-********","mobile": "***********","fullName": "张三","postCode": "000000","areaText": "滨江区","townText": "","cityText": "杭州市","provinceText": "浙江省"}                                                       |
| cargoParamList      | alibaba.trade.fast.cargo[]                        | 是    | 商品信息                                           | [{"specId": "b266e0726506185beaf205cbae88530d","quantity": 5,"offerId": 554456348334},{"specId": "2ba3d63866a71fbae83909d9b4814f01","quantity": 6,"offerId": 554456348334}]                                                                 |
| invoiceParam        | alibaba.trade.fast.invoice                        | 否    | 发票信息                                           | {"invoiceType":0,"cityText": "杭州市","provinceText": "浙江省","address": "网商路699号","phone": "0517-********","mobile": "***********","fullName": "张五","postCode": "000000","areaText": "滨江区","companyName": "测试公司","taxpayerIdentifier": "12345"} |
| flow                | String                                            | 否    | 流程标识                                           | general                                                                                                                                                                                                                                     |
| instanceId          | String                                            | 否    | 批发团instanceId,从alibaba.pifatuan.product.list获取 | 4063139_1662080400000                                                                                                                                                                                                                       |
| encryptOutOrderInfo | alibaba.trade.fastCreateOrder.EncryptOutOrderInfo | 否    | 下游加密订单信息，用于下游打单使用                              | {}                                                                                                                                                                                                                                          |
| proxySettleRecordId | String                                            | 否    | 分账普通下单采购单id，交易flow为"proxy"                     | 4051300002                                                                                                                                                                                                                                  |
| inventoryMode       | String                                            | 否    | 库存模式，jit（jit模式）或 cang（仓发模式）,目前只提供给AE使用         | jit                                                                                                                                                                                                                                         |
| outOrderId          | String                                            | 否    | 外部订单号                                          | 988129883123                                                                                                                                                                                                                                |
| pickupService       | String                                            | 否    | 上门揽收,目前AE供货可用，其他场景暂不开通                         | y或n,默认为n                                                                                                                                                                                                                                    |

### addressParam 结构

| 名称           | 类型     | 是否必须 | 描述     | 示例值           |
|--------------|--------|------|--------|---------------|
| addressId    | Long   | 是    | 收货地址id | 1234          |
| fullName     | String | 是    | 收货人姓名  | 张三            |
| mobile       | String | 是    | 手机     | ***********   |
| phone        | String | 是    | 电话     | 0517-******** |
| postCode     | String | 是    | 邮编     | 000000        |
| cityText     | String | 是    | 市文本    | 杭州市           |
| provinceText | String | 是    | 省份文本   | 浙江省           |
| areaText     | String | 是    | 区文本    | 滨江区           |
| townText     | String | 是    | 镇文本    | 长河镇           |
| address      | String | 是    | 街道地址   | 网商路699号       |
| districtCode | String | 是    | 地址编码   | 310107        |

### cargoParamList 结构

| 名称          | 类型     | 是否必须 | 描述                                                      | 示例值                                          |
|-------------|--------|------|---------------------------------------------------------|----------------------------------------------|
| offerId     | Long   | 否    | 商品对应的offer id                                           | 5544563483344                                |
| openOfferId | String | 否    | 加密offerId，当搜索返回只有openOfferId时，需采用openOfferId替代offerId下单 | Wcv2w970KoL1BCpJGQp3vwKVcBThOPlpHnUtkK3TOn4= |
| specId      | String | 是    | 商品规格id                                                  | b266e072650618beaf205cbae88530d              |
| quantity    | Double | 是    | 商品数量(计算金额用)                                             | 5                                            |
| outMemberId | String | 否    | 外部下游会员ID                                                | somebody                                     |

### invoiceParam 结构

| 名称                 | 类型      | 是否必须 | 描述                   | 示例值           |
|--------------------|---------|------|----------------------|---------------|
| invoiceType        | Integer | 是    | 发票类型 0: 普通发票，1:增值税发票 | 0             |
| provinceText       | String  | 是    | 省份文本                 | 浙江省           |
| cityText           | String  | 是    | 城市文本                 | 杭州市           |
| areaText           | String  | 是    | 地区文本                 | 滨江区           |
| townText           | String  | 是    | 镇文本                  | 长河镇           |
| postCode           | String  | 是    | 邮编                   | 333333        |
| address            | String  | 是    | 街道                   | 网商路699号       |
| fullName           | String  | 是    | 收票人姓名                | 张三            |
| phone              | String  | 是    | 电话                   | 0517-******** |
| mobile             | String  | 是    | 手机                   | ***********   |
| companyName        | String  | 是    | 购买方公司名称（发票抬头）        | 测试公司          |
| taxpayerIdentifier | String  | 是    | 纳税识别号                | 12345         |
| bankAndAccount     | String  | 是    | 开户行及账号               | 网商银行          |
| localInvoiceId     | String  | 是    | 增值税本地发票号             | *********     |

### encryptOutOrderInfo 结构

| 名称                       | 类型                                                        | 是否必须 | 描述                             | 示例值                             |
|--------------------------|-----------------------------------------------------------|------|--------------------------------|---------------------------------|
| encryptOrder             | Boolean                                                   | 是    | 是否加密订单                         | true                            |
| outPlatformOrderNo       | String                                                    | 是    | 下游平台订单号                        | *************                   |
| outPlatformSupplyOrderNo | String                                                    | 否    | 下游平台供应链采购订单                    | CT740371221887082529            |
| outPlatformCode          | String                                                    | 是    | 下游平台代码                         | taote                           |
| outPlatformAppkey        | String                                                    | 是    | 下游平台获取订单的appkey                | 32154                           |
| outShopId                | String                                                    | 否    | 下游平台店铺id                       | 1879283                         |
| outShopName              | String                                                    | 否    | 下游平台店铺名称                       | 三生科技                            |
| outOriginAddress         | com.alibaba.ocean.openplatform.biz.trade.param.OutAddress | 否    | 外部原始地址信息                       | {}                              |
| oaid                     | String                                                    | 否    | 淘宝oaid                         | 265646-52342354-2354Akf-w3654SF |
| outPatformExtraInfo      | String                                                    | 否    | 下游平台其他扩展信息                     | {}                              |
| encryptReceiverName      | String                                                    | 否    | 下游加密收货人姓名                      | ***                             |
| encryptReceiverMobile    | String                                                    | 否    | 下游加密收货人电话                      | ***                             |
| encryptReceiverAddress   | String                                                    | 否    | 下游加密收货人地址                      | ***                             |
| outPlatformSubCode       | String                                                    | 否    | 下游渠道子业务编码，比如抖音101子渠道，用于供应链订单识别 | 101                             |

### encryptOutOrderInfo.outOriginAddress 结构

| 名称       | 类型                                                   | 是否必须 | 描述   | 示例值                           |
|----------|------------------------------------------------------|------|------|-------------------------------|
| province | com.alibaba.ocean.openplatform.biz.trade.param.Place | 是    | 省    | {"name":"四川省","code":"51000"} |
| city     | com.alibaba.ocean.openplatform.biz.trade.param.Place | 是    | 市    | {}                            |
| area     | com.alibaba.ocean.openplatform.biz.trade.param.Place | 是    | 区    | {}                            |
| town     | com.alibaba.ocean.openplatform.biz.trade.param.Place | 否    | 镇/街道 | {}                            |
| address  | String                                               | 否    | 详细地址 | 网商路699号                       |
| postCode | String                                               | 否    | 邮编   | 511304                        |

### encryptOutOrderInfo.outOriginAddress.Place 结构

| 名称   | 类型     | 是否必须 | 描述     | 示例值    |
|------|--------|------|--------|--------|
| code | String | 是    | 地址code | 511300 |
| name | String | 是    | 地址name | 南充     |

## 响应参数

| 参数名                                | 类型                                         | 描述                  | 示例值                    |
|------------------------------------|--------------------------------------------|---------------------|------------------------|
| orderPreviewResuslt                | alibaba.createOrder.preview.result.model[] | 订单预览结果，过自动拆单会返回多个记录 | []                     |
| success                            | Boolean                                    | 是否成功                | true                   |
| errorCode                          | String                                     | 错误码                 | 500_1                  |
| errorMsg                           | String                                     | 错误信息                | 错误                     |
| postFeeByDescOfferList             | Long[]                                     | 运费说明的商品列表           | [12324324234,12312422] |
| consignOfferList                   | Long[]                                     | 代销商品列表              | [12324324234,12312422] |
| unsupportedCrossBorderPayOfferList | Long[]                                     | 不支持跨境宝支付的商品列表       | [12324324234,12312422] |

### orderPreviewResuslt 结构

| 名称                       | 类型                                              | 描述                                                                                 | 示例值 |
|--------------------------|-------------------------------------------------|------------------------------------------------------------------------------------|-----|
| discountFee              | java.lang.Long                                  | 计算完货品金额后再次进行的减免金额. 单位: 分                                                           |     |
| tradeModeNameList        | String[]                                        | 当前交易在使用下单接口时可以支持的交易方式列表，其中的元素可以直接用于下单接口的tradeType入参。列表为空，当前交易不可通过接口下单，需要在1688页面下单。 |     |
| status                   | boolean                                         | 状态                                                                                 |     |
| taoSampleSinglePromotion | boolean                                         | 是否有淘货源单品优惠 false:有单品优惠 true：没有单品优惠                                                 |     |
| sumPayment               | long                                            | 订单总费用, 单位为分                                                                        |     |
| message                  | java.lang.String                                | 返回信息                                                                               |     |
| sumCarriage              | long                                            | 总运费信息, 单位为分                                                                        |     |
| resultCode               | java.lang.String                                | 返回码                                                                                |     |
| sumPaymentNoCarriage     | long                                            | 不包含运费的货品总费用, 单位为分                                                                  |     |
| additionalFee            | java.lang.Long                                  | 附加费,单位，分                                                                           |     |
| flowFlag                 | java.lang.String                                | 订单下单流程                                                                             |     |
| cargoList                | alibaba.createOrder.preview.resultCargo.model[] | 规格信息                                                                               |     |
| shopPromotionList        | alibaba.trade.promotion.model[]                 | 可用店铺级别优惠列表                                                                         |     |
| tradeModelList           | tradeModelExtensionList[]                       | 当前交易可以支持的交易方式列表，结果可以参照1688下单预览页面的交易方式。                                             |     |
| payChannelInfos          | payChaneelList[]                                | 当前交易支持的支付渠道信息                                                                      | []  |

### orderPreviewResuslt.cargoList 结构

| 名称                 | 类型                              | 描述            | 示例值                |
|--------------------|---------------------------------|---------------|--------------------|
| amount             | java.lang.Double                | 产品总金额         | 10                 |
| message            | java.lang.String                | 返回信息          | null               |
| finalUnitPrice     | java.lang.Double                | 最终单价          | 100                |
| specId             | java.lang.String                | 规格ID，offer内唯一 | 98SDKTKSLFasbFiAlf |
| skuId              | java.lang.Long                  | 规格ID，全局唯一     | ************       |
| resultCode         | java.lang.String                | 返回码           | 200                |
| offerId            | java.lang.Long                  | 商品ID          | ************       |
| openOfferId        | String                          | 加密商品ID        | 9JlsKsfcKLAKDFS    |
| cargoPromotionList | alibaba.trade.promotion.model[] | 商品优惠列表        | []                 |

### orderPreviewResuslt.cargoList.cargoPromotionList 结构

| 名称          | 类型               | 描述        | 示例值                   |
|-------------|------------------|-----------|-----------------------|
| promotionId | java.lang.String | 优惠券ID     | itemCoupon-5600812521 |
| selected    | boolean          | 是否默认选中    | true                  |
| text        | java.lang.String | 优惠券名称     | 满100减10               |
| desc        | java.lang.String | 优惠券描述     | 商品优惠券                 |
| freePostage | boolean          | 是否免邮      | false                 |
| discountFee | java.lang.Long   | 减去金额，单位为分 | 1000                  |

### orderPreviewResuslt.shopPromotionList 结构

| 名称          | 类型               | 描述        | 示例值                   |
|-------------|------------------|-----------|-----------------------|
| promotionId | java.lang.String | 优惠券ID     | shopCoupon-5600812521 |
| selected    | boolean          | 是否默认选中    | true                  |
| text        | java.lang.String | 优惠券名称     | 店铺满1000减100           |
| desc        | java.lang.String | 优惠券描述     | 店铺优惠券                 |
| freePostage | boolean          | 是否免邮      | true                  |
| discountFee | java.lang.Long   | 减去金额，单位为分 | 10000                 |

### orderPreviewResuslt.tradeModelList 结构

| 名称          | 类型      | 描述                                                                         | 示例值                                                                                                         |
|-------------|---------|----------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------|
| name        | String  | 交易方式名称，1688下单预览页面展示的名称                                                     | 担保交易                                                                                                        |
| description | String  | 交易描述                                                                       | 买家下单5天内全款支付。自卖家发货起，买家需在10天内确认收货，确认收货后打款给卖家。特别提醒：如卖家已开通极速到账服务，将优先适用极速到账交易，买家支付款项将直接打入卖家账户，详见《极速到账交易及争议处理规则》。 |
| tradeType   | String  | 做为入参传入下单接口的tradeType字段                                                     | nzassure                                                                                                    |
| opSupport   | Boolean | 开放平台下单是否支持此种交易模式。如果为true,该交易方式可做为下单接口tradeType参数的入参；如果为false,则不可做为下单接口的入参。 | true                                                                                                        |

### orderPreviewResuslt.payChannelInfos 结构

| 名称          | 类型     | 描述          | 示例值    |
|-------------|--------|-------------|--------|
| name        | String | 支付渠道        | alipay |
| amountLimit | Long   | 可用额度金额，单位为分 | 100000 |

## 错误码

| 错误码     | 错误描述                             | 解决方案                    |
|---------|----------------------------------|-------------------------|
| 500_001 | 商品(offerId)不支持在线交易，无法下单          | 商品不支持在线交易，自动不能购买        |
| 500_002 | 商品(offerId)不属于一家卖家或没有指定specId    | 存在多个卖家的商品或者没有指定specId   |
| 500_003 | 商品(offerId)不属于一家卖家或没有存在specId的报价 | 存在多个卖家的商品或者不存在specId的报价 |
| 500_004 | 商品(offerId,specId)库存不足           | 商品的某个规格库存不足             |
| 500_005 | 商品(offerId)的购买数量不满足起批量规则         | 商品的购买数量小于起批量            |
| 500_006 | 商品(offerId)的购买数量或者价格不满足混批规则      | 商品的购买数量或者价格不满足混批规则      |
| 500_007 | 与组合的价格优惠不存在，不能使用saleproxy进行下单    | flow不能使用saleproxy       |
| 500_009 | 商品(offerId)的购买数量不满足组批量规则         | 检查组批量的购买数量              |
| 500_008 | 商品报价(offerId,specId)的单价为0，不可以下单  | 检查下商品报价的价格              |

## 示例

### 请求示例

```json
{
    "addressParam": {
        "address": "网商路699号",
        "areaText": "滨江区",
        "cityText": "杭州市",
        "provinceText": "浙江省",
        "townText": "长河镇",
        "postCode": "333333",
        "fullName": "张三",
        "mobile": "***********",
        "phone": "0517-********",
        "companyName": "测试公司",
        "taxpayerIdentifier": "12345"
    },
    "cargoParamList": [
        {
            "offerId": 5644563483344,
            "specId": "b266e072650618be4f205cbae88530d",
            "quantity": 5,
            "outMemberId": "somebody"
        }
    ]
}
```

### 响应示例

```json
{
  "orderPreviewResult": [
    {
      "tradeModeNameList": [
        "nzassure"
      ],
      "status": true,
      "taoSampleSinglePromotion": false,
      "sumPayment": 4400,
      "sumCarriage": 800,
      "sumPaymentNoCarriage": 3600,
      "flowFlag": "general",
      "cargoList": [
        {
          "amount": 36,
          "finalUnitPrice": 18,
          "specId": "eb81c61de14f4adb405ffcc2c8a4a3fb",
          "skuId": 3332085412530,
          "offerId": ************,
          "cargoPromotionList": []
        }
      ],
      "shopPromotionList": [],
      "tradeModelList": [
        {
          "tradeType": "nzassure",
          "name": "担保交易",
          "description": "买家下单5天内全款支付。自卖家发货起，买家需在10天内确认收货，确认收货后打款给卖家。特别提醒：如卖家已开通极速到账服务，将优先适用极速到账交易，买家支付款项将直接打入卖家账户，详见《极速到账交易及争议处理规则》。",
          "opSupport": true
        }
      ],
      "payChannelInfos": []
    }
  ],
  "success": true
}
