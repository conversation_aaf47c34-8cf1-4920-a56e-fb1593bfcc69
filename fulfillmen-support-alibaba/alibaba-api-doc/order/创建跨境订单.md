# 创建跨境订单

跨境专用订单创建接口。支持大市场及分销两个场景,根据当前授权用户区分主子账号下单。

## 使用限制

1. 单次请求最多支持50个SKU
2. 必须为同一供应商的商品
3. 多个供应商或多于50个SKU的情况,请自行拆单后提交
4. 部分情况会创建多个订单并返回多个订单号

## 所属解决方案/能力

- 1688国家服务站解决方案
- 跨境代采寻源比价搜索解决方案（国际化）
- 跨境超买寻源比价搜索解决方案
- 跨境ERP/独立站SaaS数字化解决方案
- 跨境大客户寻源通解决方案

## 请求URL

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.createCrossOrder/${APPKEY}
```

## 请求参数

### 系统级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| _aop_timestamp | String | 否 | 请求时间戳 | 1623391999000 |
| _aop_signature | String | 是 | 请求签名 | XXXXXX |
| access_token | String | 是 | 用户授权令牌 | XXXXXX |

### 应用级入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
|------|------|----------|------|---------|
| flow | String | 是 | 订单流程类型:<br/>- general: 创建大市场订单<br/>- fenxiao: 创建分销订单<br/>- saleproxy: 代销下单(校验分销关系)<br/>- paired: 火拼下单<br/>- boutiquefenxiao: 精选货源分销价下单(采购量1个包邮)<br/>- boutiquepifa: 精选货源批发价下单(采购量>2) | general |
| message | String | 否 | 买家留言 | 请尽快发货 |
| isvBizType | String | 否 | 开放平台业务码:<br/>- cross: 跨境业务(默认)<br/>- cross_daigou: 跨境代购业务 | cross |
| addressParam | Object | 是 | 收货地址信息,详见下方说明 | - |
| cargoParamList | Array | 是 | 商品信息列表,详见下方说明 | - |
| invoiceParam | Object | 否 | 发票信息,详见下方说明 | - |
| tradeType | String | 否 | 交易方式类型,详见下方说明 | assureTrade |
| shopPromotionId | String | 否 | 店铺优惠ID,通过预览接口获取,为空使用默认优惠 | itemCoupon-xxx |
| anonymousBuyer | Boolean | 否 | 是否匿名下单 | false |
| fenxiaoChannel | String | 否 | 回流订单下游平台,详见下方说明 | douyin |
| inventoryMode | String | 否 | 库存模式(AE供货可用):<br/>- JIT: jit模式<br/>- NORMAL: 仓发模式 | JIT |
| outOrderId | String | 否 | 外部订单号 | 988129883123 |
| pickupService | String | 否 | 上门揽收(AE供货可用),y/n,默认n | n |
| warehouseCode | String | 否 | 上门揽仓库code | any |
| preSelectPayChannel | String | 否 | 预选支付渠道,用于财务订单分流 | alipay |
| smallProcurement | String | 否 | 是否小额采购(AE供货可用),y/n,默认n | y |
| useRedEnvelope | String | 否 | 使用红包,y/n,默认y | n |
| dropshipping | String | 否 | 是否转运订单,y/n,默认n | y |
| addedService | String | 否 | 增值服务,toB/toC | toB |

#### addressParam 收货地址信息

> 重要说明:
>
> 1. 使用保存的收货地址传参示例：{"addressId":593861699}，其中addressId是调用"买家获取保存的收货地址信息列表"接口获取；
> 2. 使用地址编码传参示例：{"address":"桃浦镇 金达路888号贸易8楼","phone": "0517-********","mobile": "***********","fullName": "张三","postCode": "000000","districtCode": "310107"}，其中districtCode需要调用"根据地址解析地区码"接口获取；
> 3. 直接使用文本地址示例：{"address":"网商路699号","phone": "0517-********","mobile": "***********","fullName": "张三","postCode": "000000","areaText": "滨江区","townText": "","cityText": "杭州市","provinceText": "浙江省"}，省市区要传文本；
> 4. 优先级说明，如果同时传入以上参数，系统按从1至3的优先级获取地址，满足1的条件下不会使用上示2中的参数，满足2的条件下不会使用上示3中的参数；

| 字段名 | 类型 | 是否必须 | 描述 | 示例值 |
|-------|------|----------|------|---------|
| addressId | Long | 是 | 收货地址ID(使用已保存地址时必填) | 1234 |
| fullName | String | 是 | 收货人姓名 | 张三 |
| mobile | String | 是 | 手机号码 | *********** |
| phone | String | 是 | 电话号码(注意:只能填号码,不能加其他中文字符) | 0517-******** |
| postCode | String | 是 | 邮编 | 000000 |
| provinceText | String | 是 | 省份文本 | 浙江省 |
| cityText | String | 是 | 城市文本 | 杭州市 |
| areaText | String | 是 | 区/县文本 | 滨江区 |
| townText | String | 是 | 镇/街道文本 | 长河镇 |
| address | String | 是 | 详细地址 | 网商路699号 |
| districtCode | String | 是 | 地址编码(需调用"根据地址解析地区码"接口获取) | 310107 |

#### cargoParamList 商品信息

```json
[
    {
        "specId": "b266e0726506185beaf205cbae88530d",
        "quantity": 5,
        "offerId": ************,
        "openOfferId": "Wcv2w970KoL1BCpJGQp3vwKvcBThOPlpHnUtkK3T0n4=",
        "outMemberId": "********-23"
    }
]
```

| 字段名 | 类型 | 是否必须 | 描述 | 示例值 |
|-------|------|----------|------|---------|
| offerId | Long | 是 | 商品对应的offer ID | ************ |
| specId | String | 是 | 商品规格ID | b266e0726506185beaf205cbae88530d |
| quantity | Double | 是 | 商品数量(用于计算订单金额) | 5 |
| openOfferId | String | 否 | 加密offerId(当搜索返回只有openOfferId时使用) | Wcv2w970KoL1BCp... |
| outMemberId | String | 否 | 外部下游会员ID(用于跟踪订单来源) | ********-23 |

#### invoiceParam 发票信息

```json
{
    "invoiceType": 0,
    "cityText": "杭州市",
    "provinceText": "浙江省",
    "address": "网商路699号",
    "phone": "0517-********",
    "mobile": "***********",
    "fullName": "张五",
    "postCode": "000000",
    "areaText": "滨江区",
    "townText": "长河镇",
    "companyName": "测试公司",
    "taxpayerIdentifier": "123455",
    "bankAndAccount": "工商银行杭州分行 *********",
    "localInvoiceId": "*********"
}
```

| 字段名 | 类型 | 是否必须 | 描述 | 示例值 |
|-------|------|----------|------|---------|
| invoiceType | Integer | 是 | 发票类型:0普通发票,1增值税发票 | 0 |
| provinceText | String | 是 | 省份文本 | 浙江省 |
| cityText | String | 是 | 城市文本 | 杭州市 |
| areaText | String | 是 | 区/县文本 | 滨江区 |
| townText | String | 是 | 镇/街道文本 | 长河镇 |
| postCode | String | 是 | 邮编 | 333333 |
| address | String | 是 | 详细地址 | 网商路699号 |
| fullName | String | 是 | 收票人姓名 | 张三 |
| phone | String | 是 | 电话号码 | 0517-******** |
| mobile | String | 是 | 手机号码 | *********** |
| companyName | String | 是 | 购货公司名(发票抬头) | 测试公司 |
| taxpayerIdentifier | String | 是 | 纳税人识别号 | 12345 |
| bankAndAccount | String | 是 | 开户行及账号 | 工商银行XXXXX |
| localInvoiceId | String | 是 | 增值税本地发票号 | ********* |

#### tradeType 交易方式说明

| 交易方式 | 说明 |
|---------|------|
| assureTrade | 交易4.0通用担保交易 |
| alipay | 大市场通用支付宝担保交易(将下线) |
| period | 普通账期交易 |
| assure | 大买家企业采购询报价担保交易 |
| creditBuy | 诚E赊 |
| bank | 银行转账 |
| 631staged | 631分阶段付款 |
| 37staged | 37分阶段付款 |

说明:

1. 不同商品支持的交易方式不同,没有全局通用的交易方式
2. 当前可用的交易方式必须通过预览接口的tradeModeNameList获取
3. 不传则系统默认选择:开通诚E赊默认用creditBuy,未开通默认用支付宝担保

#### fenxiaoChannel 下游平台说明

| 平台代码 | 平台名称 |
|---------|---------|
| thyny | 淘宝 |
| tm | 天猫 |
| taote | 淘特 |
| c2m | 阿里巴巴C2M |
| jingdong | 京东 |
| pinduoduo | 拼多多 |
| weixin | 微信 |
| kuajing | 跨境 |
| kuaishou | 快手 |
| youzan | 有赞 |
| douyin | 抖音 |
| siku | 寺库 |
| meituan | 美团团好货 |
| xiaohongshu | 小红书 |
| dangdang | 当当 |
| suning | 苏宁 |
| davdian | 大V店 |
| xingyun | 行云 |
| miya | 蜜芽 |
| boluo | 菠萝派商城 |
| kuaituantuan | 快团团 |
| other | 其他 |

## 返回结果

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| success | Boolean | 是否成功 | true |
| code | String | 错误码 | 200 |
| message | String | 错误信息 | success |
| result | Object | 订单结果 | - |

### result 订单结果

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| totalSuccessAmount | Long | 订单总金额(分) | 156800 |
| orderId | String | 订单ID | ***************** |
| success | Boolean | 是否成功 | true |
| code | String | 错误码 | - |
| message | String | 错误信息 | - |
| postFee | Long | 运费(分) | 1000 |
| accountPeriod | Object | 账期信息 | - |
| failedOfferList | Array | 失败商品列表 | - |
| orderList | Array | 多订单列表 | - |

#### result.accountPeriod 账期信息

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| tapType | Integer | 账期类型:<br/>1:一月结算<br/>3:两月结算<br/>6:三月结算<br/>5:按收货时间结算 | 1 |
| tapDate | Integer | 结算日期/周期 | 12 |
| tapOverdue | Integer | 逾期次数 | 0 |

#### result.failedOfferList 失败商品

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| offerId | String | 商品ID | ************ |
| specId | String | 规格ID | b266e0726506185beaf205cbae88530d |
| errorCode | String | 错误码 | stock_not_enough |
| errorMessage | String | 错误描述 | 库存不足 |

#### result.orderList 多订单列表

| 名称 | 类型 | 描述 | 示例值 |
|------|------|------|---------|
| postFee | Long | 运费(分) | 1000 |
| orderAmmount | Long | 实付金额(分) | 156800 |
| message | String | 描述信息 | success |
| resultCode | String | 返回码 | 200 |
| success | Boolean | 是否成功 | true |
| orderId | String | 订单号 | ********* |
| payChannel | String | 支付渠道 | alipay |

## 错误码

| 错误码 | 错误描述 | 解决方案 |
|--------|----------|----------|
| not_support_trade_type | 不支持的交易类型 | 通过预览接口获取支持的交易类型 |
| invalid_address | 地址信息无效 | 检查地址信息完整性 |
| stock_not_enough | 库存不足 | 减少购买数量或更换商品 |
| seller_not_found | 卖家不存在 | 检查商品ID是否有效 |
| param_error | 参数错误 | 检查必填参数是否完整 |

## 返回示例

### 支付宝支付

```json
{
    "success": true,
    "code": "200",
    "message": "success", 
    "result": {
        "totalSuccessAmount": 156800,
        "orderId": "*****************",
        "success": true
    }
}
```

### 账期支付

```json
{
    "success": true,
    "code": "200",
    "message": "success",
    "result": {
        "totalSuccessAmount": 156800,
        "orderId": "*****************",
        "success": true,
        "accountPeriod": {
            "tapType": 5,
            "tapDate": 360,
            "tapOverdue": 1
        }
    }
}
```

### 创建多个订单

```json
{
    "success": true,
    "code": "200",
    "message": "success",
    "result": {
        "success": true,
        "orderList": [
            {
                "postFee": 1000,
                "orderAmmount": 156800,
                "message": "success",
                "resultCode": "200", 
                "success": true,
                "orderId": "*********",
                "payChannel": "alipay"
            },
            {
                "postFee": 1000,
                "orderAmmount": 256800,
                "message": "success",
                "resultCode": "200",
                "success": true, 
                "orderId": "*********",
                "payChannel": "alipay"
            }
        ]
    }
}
```
