# 批量取消子账号授权

对子账号的授权批量取消。

## 接口链接

POST `https://gw.open.1688.com/openapi/param2/1/system.oauth2/subaccount.auth.cancel/${APPKEY}`

## 系统级入参

| 名称             | 类型     | 是否必填 | 描述     |
|----------------|--------|------|--------|
| _aop_timestamp | String | 否    | 请求时间戳  |
| _aop_signature | String | 是    | 请求签名   |
| access_token   | String | 否    | 用户授权令牌 |

## 业务入参

| 名称                  | 类型                 | 是否必填 | 描述      | 示例值     |
|---------------------|--------------------|------|---------|---------|
| subUserIdentityList | java.lang.String[] | 是    | 子账号id列表 | a:b,a:c |

## 返回结果

| 名称     | 类型                                            | 描述   | 示例值 |
|--------|-----------------------------------------------|------|-----|
| result | alibaba.ocean.auth.dto.AuthResultModelBoolean | 返回结果 | xx  |

### result 对象

| 名称           | 类型                | 描述     | 示例值           |
|--------------|-------------------|--------|---------------|
| errorCode    | java.lang.String  | 错误码    | param_error   |
| errorMessage | java.lang.String  | 错误信息描述 | invalid param |
| returnValue  | java.lang.Boolean | 返回结果   | true          |
| success      | boolean           | 是否成功   | true          |

## 请求示例

```
https://gw.open.1688.com/openapi/param2/1/system.oauth2/subaccount.auth.cancel/YOUR_APPKEY?access_token=xx&subUserIdentityList=a,b
``` 