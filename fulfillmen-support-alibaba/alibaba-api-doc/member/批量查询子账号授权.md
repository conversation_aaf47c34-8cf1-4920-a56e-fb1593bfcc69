# 批量查询子账号授权

批量查询主账号下子账号的授权状况。

## 接口链接

POST `https://gw.open.1688.com/openapi/param2/1/system.oauth2/subaccount.auth.list/${APPKEY}`

## 系统级入参

| 名称             | 类型     | 是否必填 | 描述     |
|----------------|--------|------|--------|
| _aop_timestamp | String | 否    | 请求时间戳  |
| _aop_signature | String | 是    | 请求签名   |
| access_token   | String | 是    | 用户授权令牌 |

## 业务入参

| 名称                  | 类型                 | 是否必填 | 描述      | 示例值         |
|---------------------|--------------------|------|---------|-------------|
| subUserIdentityList | java.lang.String[] | 是    | 子账号id列表 | "a:b","a:c" |

## 返回结果

| 名称     | 类型                                             | 描述   | 示例值 |
|--------|------------------------------------------------|------|-----|
| result | alibaba.ocean.auth.dto.AuthResultModelRelation | 查询结果 | xx  |

### result 对象

| 名称           | 类型                                       | 描述     | 示例值           |
|--------------|------------------------------------------|--------|---------------|
| errorCode    | java.lang.String                         | 错误码    | param_error   |
| errorMessage | java.lang.String                         | 错误信息描述 | lack of param |
| returnValue  | alibaba.ocean.auth.dto.AuthRelationDTO[] | 返回结果   | 结构体           |
| success      | boolean                                  | 是否成功   | true          |

### AuthRelationDTO 对象

| 名称             | 类型                | 描述           | 示例值 |
|----------------|-------------------|--------------|-----|
| accessToken    | java.lang.String  | 授权凭证         | xx  |
| adminOwnerId   | java.lang.String  | 主账号loginId   | xx  |
| adminUserId    | java.lang.Long    | 主账号userId    | xx  |
| clientId       | java.lang.String  | appKey       | xx  |
| clientName     | java.lang.String  | appName      | xx  |
| gmtExpired     | java.util.Date    | 授权过期时间       | xx  |
| memberId       | java.lang.String  | 授权用户memberId | xx  |
| ownerId        | java.lang.String  | 授权用户loginId  | xx  |
| resourceScopes | java.lang.String  | 资源域          | xx  |
| site           | java.lang.String  | 授权站点         | xx  |
| status         | java.lang.String  | 授权状态         | xx  |
| subAuth        | java.lang.Boolean | 是否子账号授权      | xx  |
| subOwnerId     | java.lang.String  | 子账号loginId   | xx  |
| subUserId      | java.lang.Long    | 子账号userId    | xx  |
| userId         | java.lang.Long    | 授权用户userId   | xx  |

## 请求示例

```
https://gw.open.1688.com/openapi/param2/1/system.oauth2/subaccount.auth.list/YOUR_APPKEY?access_token=xx&subUserIdentityList=a,b
``` 