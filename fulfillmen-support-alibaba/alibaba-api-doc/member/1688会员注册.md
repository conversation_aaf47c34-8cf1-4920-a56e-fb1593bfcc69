# 1688会员注册

## 接口说明

注册1688会员账号。

## 接口链接

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao.crossborder/account.user.register/${appKey}
```

## 请求参数

### 系统参数

| 参数名            | 类型     | 是否必填 | 描述     | 示例值 |
|----------------|--------|------|--------|-----|
| _aop_timestamp | String | 是    | 请求时间戳  | -   |
| _aop_signature | String | 是    | 请求签名   | -   |
| access_token   | String | 是    | 用户授权令牌 | -   |

### 业务参数

| 参数名            | 类型                                         | 是否必填 | 描述     | 示例值                                                                                                                                                                                                            |
|----------------|--------------------------------------------|------|--------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| countryAccount | account.user.register.param.CountryAccount | 是    | 账号注册入参 | {"country": "japan", "site": "sniff", "outLoginId": "***********", "outMemberId": "c5b9a8a658554771782063f3a444e4d", "mobile": "***********", "mobileArea": "JP", "ip": "***********", "email": "<EMAIL>"} |

### countryAccount 对象结构

| 参数名         | 类型               | 是否必填 | 描述                          | 示例值         |
|-------------|------------------|------|-----------------------------|-------------|
| country     | java.lang.String | 是    | 国家，关联的参数是country枚举，见品类、场景限定 | japan       |
| site        | java.lang.String | 是    | 站点。请求支持按并只不，不超过16字节。        | sniff       |
| outLoginId  | java.lang.String | 是    | 用户在其他的登陆名，不超过60位字符。         | 123test     |
| outMemberId | java.lang.String | 是    | 用户在其他的用户一标识，不超过60位。         | 1232fsf     |
| email       | java.lang.String | 是    | 邮箱，合校验格式，不超过60位。            | <EMAIL> |
| mobile      | java.lang.String | 是    | 手机号，合校验格式，不超过30位。           | **********  |
| mobileArea  | java.lang.String | 是    | 手机号所属地区，支持的参数是mobileArea枚举。 | JP          |
| ip          | java.lang.String | 是    | IP地址，合校验格式。                 | *********** |

## 响应参数

### 返回值说明

| 参数名    | 类型                                       | 描述   | 示例值 |
|--------|------------------------------------------|------|-----|
| result | account.user.register.result.ResultModel | 返回结果 | -   |

### result 对象结构

| 参数名     | 类型                | 描述                     | 示例值   |
|---------|-------------------|------------------------|-------|
| success | boolean           | 是否调用成功，true成功，false失败。 | true  |
| code    | java.lang.String  | 错误码，如50000代表成功。        | 50000 |
| message | java.lang.String  | 错误信息，如成功。              | 成功    |
| result  | java.lang.Boolean | 返回结果。                  | true  |

## 请求示例

```json
{
    "country": "japan",
    "outLoginId": "123test",
    "outUserId": "*********",
    "site": "test",
    "mobileArea": "JP",
    "ip": "***********",
    "email": "<EMAIL>",
    "mobile": "1234"
}
```

## 返回示例

```json
{
    "success": true,
    "code": "50000",
    "message": "success",
    "result": true
}
```

## 错误码说明

| 错误码   | 错误描述 | 解决方案 |
|-------|------|------|
| 50000 | 成功   | -    |

## 注意事项

1. 邮箱和手机号需要符合格式校验要求
2. site参数不能超过16字节
3. outLoginId和outMemberId不能超过60位
4. mobile不能超过30位