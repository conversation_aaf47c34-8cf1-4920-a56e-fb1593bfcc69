# 取消退货申请

取消退款退货申请。

## 接口链接

POST `https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.cancelRefund/${APPKEY}`

## 系统级入参

| 名称             | 类型     | 是否必填 | 描述     |
|----------------|--------|------|--------|
| _aop_timestamp | String | 否    | 请求时间戳  |
| _aop_signature | String | 是    | 请求签名   |
| access_token   | String | 否    | 用户授权令牌 |

## 业务入参

| 名称       | 类型     | 是否必填 | 描述    | 示例值                  |
|----------|--------|------|-------|----------------------|
| refundId | String | 是    | 退款单id | TQ267395256051660259 |

## 返回结果

| 名称     | 类型                                                              | 描述 | 示例值 |
|--------|-----------------------------------------------------------------|----|-----|
| result | alibaba.ocean.openplat.form.biz.trade.result.RefundCancelResult | -  | -   |

### result 对象

| 名称        | 类型               | 描述 | 示例值 |
|-----------|------------------|----|-----|
| success   | boolean          | -  | -   |
| errorInfo | java.lang.String | -  | -   |
| errorCode | java.lang.String | -  | -   |

## 错误码

暂无

## 使用示例

### 请求示例

```json
{
    "refundId": "TQ267395256051660259"
}
```

### 返回示例

```json
{
    "result": {
        "success": true,
        "errorInfo": null,
        "errorCode": null
    }
}
``` 