# 创建退款退货申请

## 接口说明

创建退款退货申请，支持单个或多个子订单的退款申请。

## 接口链接

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.createRefund/{{appKey}}
```

## 请求参数

### 系统参数

| 参数名            | 类型     | 是否必填 | 描述     | 示例值   |
|----------------|--------|------|--------|-------|
| _aop_timestamp | String | 是    | 请求时间戳  | 示例见下文 |
| _aop_signature | String | 是    | 签名信息   | 示例见下文 |
| access_token   | String | 是    | 用户授权令牌 | 示例见下文 |

### 业务参数

| 参数名                 | 类型       | 是否必填 | 描述                     | 示例值   |
|---------------------|----------|------|------------------------|-------|
| orderId             | Long     | 是    | 订单ID                   | -     |
| orderEntryCountList | Array    | 是    | 子订单列表                  | 见下方说明 |
| disputeRequest      | String   | 是    | 退款类型(1:退款 2:退款退货 3:换货) | 1     |
| applyPayment        | Long     | 是    | 申请退款金额（单位：分）           | 1     |
| applyCarriage       | Long     | 否    | 申请退运费金额（单位：分）          | 0     |
| applyReason         | String   | 是    | 申请原因                   | -     |
| applyReasonId       | Long     | 是    | 申请原因ID                 | 20021 |
| description         | String   | 否    | 申请说明                   | -     |
| goodsStatus         | String   | 是    | 货物状态(1:未收到货 2:已收到货)    | 1     |
| vouchers            | String[] | 否    | 凭证图片地址列表               | -     |

### orderEntryCountList 字段说明

| 参数名   | 类型      | 是否必填 | 描述     | 示例值 |
|-------|---------|------|--------|-----|
| id    | Long    | 是    | 子订单ID  | -   |
| count | Integer | 是    | 申请退款数量 | 1   |

## 响应参数

### 返回值说明

| 参数名     | 类型      | 描述     | 示例值  |
|---------|---------|--------|------|
| result  | Object  | 返回结果对象 | -    |
| success | Boolean | 是否成功   | true |

### result 对象结构

```json
{
    "result": {
        "code": "String",
        "message": "String",
        "result": {
            "refundId": "java.lang.String"
        }
    },
    "success": true
}
```

### result 字段说明

| 参数名     | 类型     | 描述     | 示例值     |
|---------|--------|--------|---------|
| code    | String | 错误码    | 200     |
| message | String | 错误信息   | success |
| result  | Object | 退款结果对象 | 见下方说明   |

### result.result 字段说明

| 参数名      | 类型     | 描述    | 示例值                  |
|----------|--------|-------|----------------------|
| refundId | String | 退款单ID | TQ104316274636951198 |

## 错误码说明

| 错误码  | 错误信息       | 解决方案         |
|------|------------|--------------|
| 5001 | 订单不存在      | 检查订单ID是否正确   |
| 5002 | 订单已经申请退款   | 检查订单退款状态     |
| 5003 | 退款金额超过可退金额 | 检查退款金额是否正确   |
| 5004 | 退款申请参数错误   | 检查必填参数是否完整   |
| 5005 | 卖家已拒绝退款    | 联系卖家协商       |
| 5006 | 退款单已关闭     | 重新发起退款申请     |
| 5010 | 退款申请失败     | 请稍后重试        |
| 5011 | 退款金额错误     | 检查退款金额       |
| 5012 | 退货地址错误     | 检查退货地址信息     |
| 5013 | 子订单数量错误    | 检查申请退款数量是否正确 |
| 5100 | 系统处理错误     | 请稍后重试        |
| 4444 | 系统维护中      | 请稍后重试        |

## 示例代码

### 请求示例

```java
MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
params.

add("orderId","2429989502226540788");
params.

add("disputeRequest","1");
params.

add("applyPayment","1");
params.

add("applyReason","买家取消订单");
params.

add("applyReasonId","20021");
params.

add("goodsStatus","1");

List<Map<String, Object>> orderEntryCountList = new ArrayList<>();
Map<String, Object> entry1 = new HashMap<>();
entry1.

put("id",151267031009969811);
entry1.

put("count",1);
orderEntryCountList.

add(entry1);

params.

add("orderEntryCountList",JSON.toJSONString(orderEntryCountList));

// 发起请求
Mono<RefundCreateResponse> response = refundAPI.createRefund(appKey, params);
```

### 返回示例

```json
{
    "result": {
        "code": "200",
        "message": "success",
        "result": {
            "refundId": "TQ104316274636951198"
        }
    },
    "success": true
}
```

## 注意事项

1. 退款金额不能超过订单实付金额
2. 退款原因需要选择系统提供的标准原因
3. 申请退款数量不能超过订单购买数量
4. 建议在调用接口时增加异常处理机制
5. 所有金额相关字段单位均为分
6. 图片凭证需要先上传到图片服务器获取URL 