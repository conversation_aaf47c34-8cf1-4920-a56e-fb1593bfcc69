# 上传退款退货凭证

## 接口说明

上传退款退货凭证，用于退款退货申请。文件类型为byte数组，使用org.apache.commons.io.IOUtils#toByteArray(java.io.InputStream)。

## 接口链接

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.uploadRefundVoucher/{{appKey}}
```

## 请求参数

### 系统参数

| 参数名            | 类型     | 是否必填 | 描述     | 示例值   |
|----------------|--------|------|--------|-------|
| _aop_timestamp | String | 是    | 请求时间戳  | 示例见下文 |
| _aop_signature | String | 是    | 签名信息   | 示例见下文 |
| access_token   | String | 是    | 用户授权令牌 | 示例见下文 |

### 业务参数

| 参数名       | 类型     | 是否必填 | 描述                | 示例值 |
|-----------|--------|------|-------------------|-----|
| imageData | byte[] | 是    | 凭证图片数据，小于1M，jpg格式 | -   |

## 响应参数

### 返回值说明

| 参数名    | 类型     | 描述     | 示例值 |
|--------|--------|--------|-----|
| result | Object | 返回结果对象 | -   |

### result 对象结构

```json
{
    "result": {
        "result": {
            "imageDomain": "java.lang.String",
            "imageRelativeUrl": "java.lang.String"
        },
        "code": "String",
        "message": "String",
        "success": "Boolean"
    }
}
```

### result 字段说明

| 参数名     | 类型      | 描述     | 示例值     |
|---------|---------|--------|---------|
| result  | Object  | 上传结果对象 | 见下方说明   |
| code    | String  | 错误码    | 200     |
| message | String  | 错误信息   | success |
| success | Boolean | 是否成功   | true    |

### result.result 字段说明

| 参数名              | 类型     | 描述     | 示例值 |
|------------------|--------|--------|-----|
| imageDomain      | String | 图片域名   | -   |
| imageRelativeUrl | String | 图片相对路径 | -   |

## 错误码说明

| 错误码  | 错误描述 | 解决方案                                     |
|------|------|------------------------------------------|
| 4000 | -    | 调用者错误：未正确传入imageData参数；服务方错误：网络延迟、内部服务异常 |

## 示例代码

### 请求示例

```java
// 读取图片文件为byte数组
byte[] imageData = IOUtils.toByteArray(new FileInputStream("voucher.jpg"));

MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
params.

add("imageData",Base64.getEncoder().

encodeToString(imageData));

// 发起请求
Mono<RefundVoucherUploadResponse> response = refundAPI.uploadRefundVoucher(appKey, params);
```

### 返回示例

```json
{
    "result": {
        "code": "200",
        "message": "success",
        "result": {
            "imageDomain": "https://cbu01.alicdn.com",
            "imageRelativeUrl": "/img/ibank/2021/123/456/789.jpg"
        },
        "success": true
    }
}
```

## 注意事项

1. 图片大小不能超过1M
2. 仅支持JPG格式图片
3. 建议在调用接口时增加异常处理机制
4. 上传成功后，完整的图片URL需要拼接imageDomain和imageRelativeUrl 