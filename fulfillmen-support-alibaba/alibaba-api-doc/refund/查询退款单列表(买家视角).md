# 查询退款单列表(买家视角)

## 接口说明

查询退款单列表信息，仅返回买家视角的退款单信息。

## 接口链接

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.refund.buyer.queryOrderRefundList/{{appKey}}
```

## 请求参数

### 系统参数

| 参数名            | 类型     | 是否必填 | 描述     | 示例值   |
|----------------|--------|------|--------|-------|
| _aop_timestamp | String | 是    | 请求时间戳  | 示例见下文 |
| _aop_signature | String | 是    | 签名信息   | 示例见下文 |
| access_token   | String | 是    | 用户授权令牌 | 示例见下文 |

### 业务参数

| 参数名             | 类型             | 是否必填 | 描述                                                                                   | 示例值                    |
|-----------------|----------------|------|--------------------------------------------------------------------------------------|------------------------|
| orderId         | Long           | 否    | 订单ID                                                                                 | 1738678885588588520    |
| applyStartTime  | java.util.Date | 否    | 退款申请开始时间（毫秒）                                                                         | 20210928114526000+0800 |
| applyEndTime    | java.util.Date | 否    | 退款申请结束时间（毫秒）                                                                         | 20220929114526000+0800 |
| refundStatusSet | String[]       | 否    | 退款状态列表，多个以英文逗号分隔，可选值：waitsellerconfirm（等待卖家确认）、refundsuccess（退款成功）、refundclose（退款关闭） | waitsellerconfirm      |
| sellerMemberId  | String         | 否    | 卖家memberId                                                                           | b2b-2123456789         |
| pageSize        | Integer        | 否    | 每页条数                                                                                 | 20                     |
| logisticsNo     | String         | 否    | 物流单号（支持模糊查询）                                                                         | 3123****4567           |
| modifyStartTime | java.util.Date | 否    | 退款修改开始时间（毫秒）                                                                         | 20210928114526000+0800 |
| modifyEndTime   | java.util.Date | 否    | 退款修改结束时间（毫秒）                                                                         | 20220929114526000+0800 |
| bizType         | Integer        | 否    | 业务类型                                                                                 | 1                      |

## 响应参数

### 返回值说明

| 参数名    | 类型     | 描述     | 示例值 |
|--------|--------|--------|-----|
| result | Object | 返回结果对象 | -   |

### result 对象结构

```json
{
    "result": {
        "opOrderRefundModels": [
            {
                "alipayPaymentId": "2018061421001008760569923894",
                "applyCarriage": 0,
                "applyPayment": 1,
                "applyReason": "不想买了，已与卖家协商一致",
                "applyReasonId": 20028,
                "applySubReasonId": -1,
                "buyerMemberId": "b2b-1624961198",
                "buyerUserId": 1624961198,
                "canRefundPayment": 1,
                "disputeRequest": 3,
                "disputeType": 1,
                "extInfo": {
                    "EXmrf": "1",
                    "ttid": "2",
                    "reason": "20028",
                    "apply_text_id": "null",
                    "newRefund": "rp2",
                    "bizCode": "cbu.general.refund",
                    "lastOrder": "0",
                    "seller_agreed_refund_fee": "1",
                    "old_reason_id": "20028",
                    "b2b_seller_mId": "b2b-1623492085",
                    "seller_batch": "true",
                    "itemBuyAmount": "0",
                    "b2b_buyer_mId": "b2b-1624961198",
                    "ability": "1",
                    "seller_audit": "0",
                    "ee_trace_id": "0ab2dbd215300798520175927d07d3",
                    "ol_tf": "0",
                    "opRole": "timeout",
                    "apply_init_refund_fee": "1",
                    "isVirtual": "0",
                    "logisticsCompanyId": "-1",
                    "itemPrice": "0",
                    "interceptStatus": "0",
                    "refundFrom": "2",
                    "restartForXiaoer": "1",
                    "appName": "tosp-aftersales",
                    "abnormal_dispute_status": "0",
                    "payMode": "alipay",
                    "workflowName": "cbu_return_and_refund",
                    "sgr": "1",
                    "enfunddetail": "1",
                    "bgmtc": "2018-06-14 14: 20: 21"
                },
                "frozenFund": -1,
                "gmtApply": "20180622141034000+0800",
                "gmtCompleted": "20180702141113000+0800",
                "gmtCreate": "20180622141034000+0800",
                "gmtModified": "20180702141113000+0800",
                "goodsStatus": 2,
                "id": 10426553019961198,
                "orderId": 177681528398969811,
                "productName": "短袖撞色领保罗衫定做纯棉广告衫定制印字logo刺绣男现货批发 白色/橙领 S等3种",
                "refundCarriage": 0,
                "refundId": "TQ10426553019961198",
                "refundPayment": 1,
                "rejectReasonId": 0,
                "rejectTimes": 0,
                "sellerMemberId": "b2b-1623492085",
                "sellerUserId": 1623492085,
                "status": "refundclose",
                "tradeTypeStr": "50060",
                "refundOperationList": [],
                "logisticsCompany": {
                    "companyName": "-",
                    "companyNo": "-",
                    "companyPhone": "-",
                    "gmtCreate": "-",
                    "gmtModified": "-",
                    "id": "-",
                    "outerId": "-",
                    "pinyin": "-",
                    "spellName": "-",
                    "supportPrint": "-",
                    "url": "-"
                }
            }
        ],
        "totalCount": 1,
        "currentPageNum": 0
    },
    "success": true
}
```

### result 字段说明

| 参数名                 | 类型      | 描述    | 示例值   |
|---------------------|---------|-------|-------|
| opOrderRefundModels | Array   | 退款单列表 | 见下方说明 |
| totalCount          | Integer | 总记录数  | 1     |
| currentPageNum      | Integer | 当前页码  | 0     |
| success             | Boolean | 是否成功  | true  |

### result.opOrderRefundModels 字段说明

| 参数名                       | 类型      | 描述                     | 示例值                                       |
|---------------------------|---------|------------------------|-------------------------------------------|
| alipayPaymentId           | String  | 支付宝交易号                 | 2018061421001008760569923894              |
| applyCarriage             | Long    | 申请退运费金额（单位：分）          | 0                                         |
| applyPayment              | Long    | 申请退款金额（单位：分）           | 1                                         |
| applyReason               | String  | 申请原因                   | 不想买了，已与卖家协商一致                             |
| applyReasonId             | Long    | 申请原因ID                 | 20028                                     |
| applySubReasonId          | Long    | 申请子原因ID                | -1                                        |
| buyerMemberId             | String  | 买家会员ID                 | b2b-1624961198                            |
| buyerUserId               | Long    | 买家用户ID                 | 1624961198                                |
| canRefundPayment          | Long    | 可退金额（单位：分）             | 1                                         |
| disputeRequest            | Integer | 退款类型(1:退款 2:退款退货 3:换货) | 3                                         |
| disputeType               | Integer | 纠纷类型                   | 1                                         |
| extInfo                   | Object  | 扩展信息                   | 见示例                                       |
| frozenFund                | Long    | 冻结资金（单位：分）             | -1                                        |
| gmtApply                  | String  | 申请时间                   | 20180622141034000+0800                    |
| gmtCompleted              | String  | 完成时间                   | 20180702141113000+0800                    |
| gmtCreate                 | String  | 创建时间                   | 20180622141034000+0800                    |
| gmtModified               | String  | 修改时间                   | 20180702141113000+0800                    |
| goodsStatus               | Integer | 货物状态(1:未收到货 2:已收到货)    | 2                                         |
| id                        | Long    | 退款单ID                  | 10426553019961198                         |
| orderId                   | Long    | 订单ID                   | 177681528398969811                        |
| productName               | String  | 商品名称                   | 短袖撞色领保罗衫定做纯棉广告衫定制印字logo刺绣男现货批发 白色/橙领 S等3种 |
| refundCarriage            | Long    | 实际退运费金额（单位：分）          | 0                                         |
| refundId                  | String  | 退款单号                   | TQ10426553019961198                       |
| refundPayment             | Long    | 实际退款金额（单位：分）           | 1                                         |
| rejectReasonId            | Long    | 拒绝原因ID                 | 0                                         |
| rejectTimes               | Integer | 拒绝次数                   | 0                                         |
| sellerMemberId            | String  | 卖家会员ID                 | b2b-1623492085                            |
| sellerUserId              | Long    | 卖家用户ID                 | 1623492085                                |
| status                    | String  | 退款状态                   | refundclose                               |
| tradeTypeStr              | String  | 交易类型                   | 50060                                     |
| refundOperationList       | Array   | 退款操作记录列表               | []                                        |
| buyerLoginId              | String  | 买家登录ID                 | -                                         |
| buyerAlipayId             | String  | 买家支付宝ID                | -                                         |
| sellerLoginId             | String  | 卖家登录ID                 | -                                         |
| sellerAlipayId            | String  | 卖家支付宝ID                | -                                         |
| orderEntryIds             | String  | 子订单ID列表                | -                                         |
| refundFee                 | Long    | 退款金额                   | -                                         |
| refundStatus              | String  | 退款状态                   | -                                         |
| refundPhase               | String  | 退款阶段                   | -                                         |
| timeOutFreeze             | Boolean | 是否超时冻结                 | -                                         |
| needSellerAddressAndPhone | Boolean | 是否需要卖家地址和电话            | -                                         |
| needLogistics             | Boolean | 是否需要物流                 | -                                         |
| needModifyRefundFee       | Boolean | 是否需要修改退款金额             | -                                         |
| needUploadVoucher         | Boolean | 是否需要上传凭证               | -                                         |
| needLogisticsBack         | Boolean | 是否需要退回物流               | -                                         |
| needArbitration           | Boolean | 是否需要仲裁                 | -                                         |
| needBuyerModifyRefundFee  | Boolean | 是否需要买家修改退款金额           | -                                         |
| needBuyerUploadVoucher    | Boolean | 是否需要买家上传凭证             | -                                         |
| needBuyerLogisticsBack    | Boolean | 是否需要买家退回物流             | -                                         |
| needBuyerArbitration      | Boolean | 是否需要买家仲裁               | -                                         |
| disputeEndTime            | String  | 纠纷结束时间                 | -                                         |
| disputeStartTime          | String  | 纠纷开始时间                 | -                                         |
| disputeStatusView         | String  | 纠纷状态视图                 | -                                         |
| disputeTypeView           | String  | 纠纷类型视图                 | -                                         |
| disputeStatusDesc         | String  | 纠纷状态描述                 | -                                         |
| disputeTypeDesc           | String  | 纠纷类型描述                 | -                                         |

### extInfo 补充字段说明

| 参数名                      | 类型     | 描述        | 示例值                              |
|--------------------------|--------|-----------|----------------------------------|
| apply_text_id            | String | 申请文本ID    | "null"                           |
| newRefund                | String | 新退款标记     | "rp2"                            |
| lastOrder                | String | 最后订单标记    | "0"                              |
| seller_agreed_refund_fee | String | 卖家同意的退款金额 | "1"                              |
| old_reason_id            | String | 原因ID      | "20028"                          |
| b2b_seller_mId           | String | B2B卖家会员ID | "b2b-1623492085"                 |
| seller_batch             | String | 卖家批次标记    | "true"                           |
| b2b_buyer_mId            | String | B2B买家会员ID | "b2b-1624961198"                 |
| ability                  | String | 能力标记      | "1"                              |
| seller_audit             | String | 卖家审核标记    | "0"                              |
| ee_trace_id              | String | 追踪ID      | "0ab2dbd215300798520175927d07d3" |
| ol_tf                    | String | 在线标记      | "0"                              |
| opRole                   | String | 操作角色      | "timeout"                        |
| apply_init_refund_fee    | String | 初始申请退款金额  | "1"                              |
| interceptStatus          | String | 拦截状态      | "0"                              |
| refundFrom               | String | 退款来源      | "2"                              |
| restartForXiaoer         | String | 小二重启标记    | "1"                              |
| appName                  | String | 应用名称      | "tosp-aftersales"                |
| abnormal_dispute_status  | String | 异常纠纷状态    | "0"                              |
| sgr                      | String | SGR标记     | "1"                              |
| enfunddetail             | String | 退款详情标记    | "1"                              |

### result.opOrderRefundModels.logisticsCompany 字段说明

| 参数名          | 类型      | 描述     | 示例值 |
|--------------|---------|--------|-----|
| companyName  | String  | 物流公司名称 | -   |
| companyNo    | String  | 物流公司编号 | -   |
| companyPhone | String  | 物流公司电话 | -   |
| gmtCreate    | String  | 创建时间   | -   |
| gmtModified  | String  | 修改时间   | -   |
| id           | Long    | 物流公司ID | -   |
| outerId      | String  | 外部ID   | -   |
| pinyin       | String  | 拼音     | -   |
| spellName    | String  | 拼写名称   | -   |
| supportPrint | Boolean | 是否支持打印 | -   |
| url          | String  | 物流公司网址 | -   |

## 错误码说明

| 错误码               | 错误描述         | 解决方案    |
|-------------------|--------------|---------|
| SYSTEM_TIME_ERROR | 时间戳与当前时间差距过大 | 调整请求时间戳 |

## 示例代码

### 请求示例

```java
MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
params.

add("orderId","1738678885588588520");
params.

add("applyStartTime","20210928114526000+0800");
params.

add("applyEndTime","20220929114526000+0800");
params.

add("refundStatusSet","waitsellerconfirm");
params.

add("sellerMemberId","b2b-2123456789");
params.

add("pageSize","20");
params.

add("modifyStartTime","20210928114526000+0800");
params.

add("modifyEndTime","20220929114526000+0800");
params.

add("bizType","1");

// 发起请求
Mono<RefundBuyerQueryListResponse> response = refundAPI.queryBuyerRefundList(appKey, params);
```

### 返回示例

```json
{
    "result": {
        "opOrderRefundModels": [
            {
                "buyerLoginId": "test123",
                "currentPageNum": 1,
                "totalPageNum": 10,
                "success": true
            }
        ]
    }
}
```

## 注意事项

1. 时间参数需要使用毫秒级时间戳
2. 退款状态可以同时查询多个
3. 查询时间范围不要太大，建议控制在一个月内
4. 建议在调用接口时增加异常处理机制 