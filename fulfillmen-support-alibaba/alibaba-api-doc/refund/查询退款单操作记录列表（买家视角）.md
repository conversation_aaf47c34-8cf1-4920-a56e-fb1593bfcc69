# 退款单操作记录列表（买家视角）

## 接口说明

查询退款单的操作记录列表，获取退款单的操作历史信息。

## 接口链接

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.refund.OpQueryOrderRefundOperationList/{{appKey}}
```

## 请求参数

### 系统参数

| 参数名            | 类型     | 是否必填 | 描述     | 示例值   |
|----------------|--------|------|--------|-------|
| _aop_timestamp | String | 是    | 请求时间戳  | 示例见下文 |
| _aop_signature | String | 是    | 签名信息   | 示例见下文 |
| access_token   | String | 是    | 用户授权令牌 | 示例见下文 |

### 业务参数

| 参数名      | 类型     | 是否必填 | 描述    | 示例值                  |
|----------|--------|------|-------|----------------------|
| refundId | String | 是    | 退款单ID | TQ104316274636951198 |
| pageNo   | String | 是    | 页码    | 1                    |
| pageSize | String | 是    | 页大小   | 100                  |

## 响应参数

### 返回值说明

| 参数名          | 类型      | 描述     | 示例值  |
|--------------|---------|--------|------|
| result       | Object  | 返回结果对象 | -    |
| success      | Boolean | 是否成功   | true |
| errorCode    | String  | 错误码    | -    |
| errorMessage | String  | 错误信息   | -    |

### result 对象结构

```json
{
  "success": true,
  "result": {
    "opOrderRefundOperationModels": [
      {
        "afterOperateStatus": "java.lang.String",
        "beforeOperateStatus": "java.lang.String",
        "closeRefundStepId": "long",
        "crmModifyRefund": "Boolean",
        "description": "java.lang.String",
        "email": "java.lang.String",
        "freightBill": "java.lang.String",
        "gmtCreate": "java.util.Date",
        "gmtModified": "java.util.Date",
        "id": "java.lang.Long",
        "messageStatus": "int",
        "mobile": "java.lang.String",
        "msgType": "int",
        "operateRemark": "java.lang.String",
        "operateTypeInt": "int",
        "operatorId": "java.lang.String",
        "operatorLoginId": "java.lang.String",
        "operatorRoleId": "java.lang.Integer",
        "operatorUserId": "java.lang.Long",
        "phone": "java.lang.String",
        "refundAddress": "java.lang.String",
        "refundId": "java.lang.String",
        "rejectReason": "java.lang.String",
        "vouchers": "java.util.List",
        "logisticsCompany": {
          "companyName": "java.lang.String",
          "companyNo": "java.lang.String",
          "companyPhone": "java.lang.String",
          "gmtCreate": "java.util.Date",
          "gmtModified": "java.util.Date",
          "id": "java.lang.Long",
          "spelling": "java.lang.String",
          "supportPrint": "Boolean"
        }
      }
    ]
  }
}
```

### opOrderRefundOperationModels 字段说明

| 参数名                 | 类型      | 描述                                              | 示例值                   |
|---------------------|---------|-------------------------------------------------|-----------------------|
| afterOperateStatus  | String  | 操作后的退款状态                                        | waitselleragree       |
| beforeOperateStatus | String  | 操作前的退款状态                                        | waitselleragree       |
| closeRefundStepId   | Long    | 分阶段订单单向操作关闭退款的阶段ID                              | 0                     |
| crmModifyRefund     | Boolean | 是否小二修改过退款单                                      | false                 |
| description         | String  | 描述说明                                            | 卖家同意退款申请，已退款成功        |
| email               | String  | 联系人EMAIL                                        | -                     |
| freightBill         | String  | 运单号                                             | SF1234567890          |
| gmtCreate           | Date    | 创建时间                                            | 2018061118090100+0800 |
| gmtModified         | Date    | 修改时间                                            | 2018061118090100+0800 |
| id                  | Long    | 主键，退款操作记录流水号                                    | 6055696342            |
| messageStatus       | int     | 凭证状态(1:正常 2:后台小二屏蔽)                             | 3                     |
| mobile              | String  | 联系人手机                                           | -                     |
| msgType             | int     | 留言类型(3:小二留言给卖家和买家 4:给买家的留言 5:给卖家的留言 7:cbu的普通留言) | 7                     |
| operateRemark       | String  | 操作备注                                            | 退款成功                  |
| operateTypeInt      | int     | 操作类型                                            | 19                    |
| operatorId          | String  | 操作者memberID                                     | -                     |
| operatorLoginId     | String  | 操作者loginID                                      | alibaservice02        |
| operatorRoleId      | Integer | 操作者角色ID(买家/卖家/系统)                               | 2                     |
| operatorUserId      | Long    | 操作者userID                                       | -                     |
| phone               | String  | 联系人电话                                           | -                     |
| refundAddress       | String  | 退货地址                                            | -                     |
| refundId            | String  | 退款记录ID                                          | TQ104386770449285     |
| rejectReason        | String  | 卖家拒绝退款原因                                        | -                     |
| vouchers            | List    | 凭证图片地址列表                                        | -                     |

### logisticsCompany 字段说明

| 参数名          | 类型      | 描述       | 示例值                   |
|--------------|---------|----------|-----------------------|
| companyName  | String  | 快递公司名称   | 顺丰速运                  |
| companyNo    | String  | 物流公司编号   | SF                    |
| companyPhone | String  | 物流公司服务电话 | 95338                 |
| gmtCreate    | Date    | 创建时间     | 2018061118090100+0800 |
| gmtModified  | Date    | 修改时间     | 2018061118090100+0800 |
| id           | Long    | ID       | -                     |
| spelling     | String  | 全拼       | shunfeng              |
| supportPrint | Boolean | 是否支持打印   | true                  |

## 错误码说明

| 错误码 | 错误信息  | 解决方案  |
|-----|-------|-------|
| 500 | 服务器错误 | 请稍后重试 |

## 示例代码

### 请求示例

```java
MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
params.add("refundId", "TQ104316274636951198");
params.add("pageNo", "1");
params.add("pageSize", "100");

// 发起请求
Mono<RefundOperationListResponse> response = refundAPI.queryRefundOperationList(appKey, params);
```

### 返回示例

```json
{
  "success": true,
  "result": {
    "opOrderRefundOperationModels": [
      {
        "afterOperateStatus": "waitselleragree",
        "beforeOperateStatus": "waitselleragree",
        "closeRefundStepId": 0,
        "description": "卖家同意退款申请，已退款成功",
        "gmtCreate": "2025-01-10 10:00:00",
        "id": 6055696342,
        "messageStatus": 3,
        "msgType": 7,
        "operateRemark": "退款成功",
        "operateTypeInt": 19,
        "operatorLoginId": "alibaservice02",
        "operatorRoleId": 2,
        "refundId": "TQ104386770449285",
        "logisticsCompany": {
          "companyName": "顺丰速运",
          "companyNo": "SF",
          "companyPhone": "95338",
          "spelling": "shunfeng",
          "supportPrint": true
        }
      }
    ]
  }
}
```

## 注意事项

1. 该接口仅支持买家视角查询退款单操作记录
2. 需要正确配置授权信息和签名
3. 建议在调用接口时增加异常处理机制
4. 返回的时间格式为"yyyy-MM-dd HH:mm:ss"
5. 操作记录按时间倒序排列，最新的操作记录在最前面
6. 凭证状态和留言类型需要特别关注，不同状态代表不同的含义
