# 买家提交退款货信息

## 接口说明

买家提交退货信息，用于退款退货申请。买家快递公司信息使用alibaba.logistics.OpQueryLogisticCompanyList.offline查询物流公司列表。

## 接口链接

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.refund.returnGoods/{{appKey}}
```

## 请求参数

### 系统参数

| 参数名            | 类型     | 是否必填 | 描述     | 示例值   |
|----------------|--------|------|--------|-------|
| _aop_timestamp | String | 是    | 请求时间戳  | 示例见下文 |
| _aop_signature | String | 是    | 签名信息   | 示例见下文 |
| access_token   | String | 是    | 用户授权令牌 | 示例见下文 |

### 业务参数

| 参数名                | 类型       | 是否必填 | 描述                                                                                                                 | 示例值                                                                 |
|--------------------|----------|------|--------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------|
| refundId           | String   | 是    | 退款单号，10开头                                                                                                          | ********************                                                |
| logisticsCompanyNo | String   | 是    | 物流公司编码，使用alibaba.logistics.OpQueryLogisticCompanyList.offline接口查询                                                  | ZTO                                                                 |
| freightBill        | String   | 是    | 物流公司运单号，请填真实单号，否则会有相应的处罚措施                                                                                         | 311004455500343338                                                  |
| description        | String   | 否    | 发货说明，内容在2-200个字之间                                                                                                  | 发货说明                                                                |
| vouchers           | String[] | 否    | 凭证图片URL，必须使用API alibaba.trade.uploadRefundVoucher返回的图片链接，最多上传10张图片，支持的格式：gif、jpeg、png、bmp格式，单上传条件：以上格式都支持（千牛上传无限制） | ["https://cbu01.alicdn.com/img/ibank/2019/901/930/***********.jpg"] |

## 响应参数

### 返回值说明

| 参数名    | 类型     | 描述     | 示例值 |
|--------|--------|--------|-----|
| result | Object | 返回结果对象 | -   |

### result 对象结构

```json
{
    "result": {
        "errorCode": "java.lang.String",
        "errorInfo": "java.lang.String",
        "success": "boolean"
    }
}
```

### result 字段说明

| 参数名       | 类型      | 描述   | 示例值          |
|-----------|---------|------|--------------|
| errorCode | String  | 错误码  | 4002         |
| errorInfo | String  | 错误描述 | 物流公司编号和运单号必填 |
| success   | Boolean | 是否成功 | true         |

## 错误码说明

| 错误码  | 错误描述              | 解决方案                        |
|------|-------------------|-----------------------------|
| 4001 | 需要用户授权            | 检查token是否过期，重新刷新授权          |
| 4002 | 物流公司编号和运单号必填      | 物流公司编号和运单号必填                |
| 4003 | 上传凭证数过多           | 凭证不能超过10张                   |
| 4003 | 凭证URL格式不合法        | 凭证链接不符合要求                   |
| 4004 | 物流公司编号和运单号校验不通过   | 物流公司编号和运单号校验不通过，检查并填写正确的运单号 |
| 4004 | 物流公司编号和运单号校验异常    | 物流公司编号和运单号校验异常，请稍后重试        |
| 4005 | 退款单信息不存在          | 退款单信息不存在                    |
| 4006 | 无当前退款单操作权限        | 检查用户账号是否正确，退款单是否正确          |
| 4007 | 当前退款单状态不支持操作      | 当前退款单状态不支持操作，退款单状态不对        |
| 4008 | 退款单号信息有误，请核对后重新提交 | 退款单号信息有误，请核对后重新提交           |
| 5001 | 退款单信息查询异常         | 退款单信息查询异常                   |
| 5001 | 退款单信息查询异常         | 退款单信息查询异常                   |
| 5002 | 退款退货操作失败          | 退款退货操作失败                    |
| 5003 | 退款退货操作异常          | 退款退货操作异常                    |

## 示例代码

### 请求示例

```java
MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
params.

add("refundId","********************");
params.

add("logisticsCompanyNo","ZTO");
params.

add("freightBill","311004455500343338");
params.

add("description","发货说明");
params.

add("vouchers","[\"https://cbu01.alicdn.com/img/ibank/2019/901/930/***********.jpg\"]");

// 发起请求
Mono<RefundReturnGoodsResponse> response = refundAPI.returnGoods(appKey, params);
```

### 返回示例

```json
{
    "result": {
        "errorCode": "4002",
        "errorInfo": "物流公司编号和运单号必填",
        "success": false
    }
}
```

## 注意事项

1. 本API在要退货退款场景下使用
2. 物流公司编码必须使用指定接口查询获取
3. 运单号必须填写真实信息，否则会受到处罚
4. 凭证图片必须使用指定的上传接口获取URL
5. 建议在调用接口时增加异常处理机制 