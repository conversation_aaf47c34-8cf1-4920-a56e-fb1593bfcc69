# 查询退款单详情-根据退款单ID（买家视角）

## 接口说明

根据退款单ID查询退款单详情，获取退款单的详细信息。

## 接口链接

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.refund.OpQueryOrderRefund-1/{{appKey}}
```

## 请求参数

### 系统参数

| 参数名            | 类型     | 是否必填 | 描述     | 示例值   |
|----------------|--------|------|--------|-------|
| _aop_timestamp | String | 是    | 请求时间戳  | 示例见下文 |
| _aop_signature | String | 是    | 签名信息   | 示例见下文 |
| access_token   | String | 是    | 用户授权令牌 | 示例见下文 |

### 业务参数

| 参数名                      | 类型      | 是否必填 | 描述          | 示例值                  |
|--------------------------|---------|------|-------------|----------------------|
| refundId                 | String  | 是    | 退款单ID       | TQ104316274636951198 |
| needTimeOutInfo          | Boolean | 否    | 是否需要超时信息    | true                 |
| needOrderRefundOperation | Boolean | 否    | 是否需要退款单操作记录 | true                 |

## 响应参数

### 返回值说明

| 参数名          | 类型      | 描述     | 示例值  |
|--------------|---------|--------|------|
| result       | Object  | 返回结果对象 | -    |
| success      | Boolean | 是否成功   | true |
| errorCode    | String  | 错误码    | -    |
| errorMessage | String  | 错误信息   | -    |

### result 对象结构

```json
{
    "success": true,
    "result": {
        "opOrderRefundModelDetail": {
            "alipayPaymentId": "java.lang.String",
            "applyCarriage": "java.lang.Long",
            "applyExpect": "java.lang.Long",
            "applyPayment": "java.lang.Long",
            "applyReason": "java.lang.String",
            "applyReasonId": "int",
            "applySubReason": "java.lang.String",
            "applyRefCode": "java.lang.String",
            "applySubReasonId": "int",
            "applySellerRefCode": "java.lang.String",
            "buyerLoginId": "java.lang.String",
            "buyerMemberId": "java.lang.String",
            "buyerUserId": "java.lang.Long",
            "disburseChannel": "java.lang.String",
            "disputeRequest": "int",
            "disputeType": "int",
            "extends": "java.util.Map",
            "freezeId": "java.lang.String",
            "gmtApply": "java.util.Date",
            "gmtCompleted": "java.util.Date",
            "gmtCreate": "java.util.Date",
            "gmtFreezed": "java.util.Date",
            "gmtModified": "java.util.Date",
            "gmtTimeOut": "java.util.Date",
            "goodsStatus": "int",
            "id": "java.lang.Long",
            "instantRefundType": "java.lang.String",
            "orderEntryCountMap": "java.util.Map",
            "orderEntryIds": "java.util.List",
            "orderId": "java.lang.Long",
            "prepaidBalance": "java.lang.Long",
            "productName": "java.lang.String",
            "refundCarriage": "java.lang.Long",
            "refundId": "java.lang.String",
            "refundPayment": "java.lang.Long",
            "rejectReason": "java.lang.String",
            "rejectReasonId": "int",
            "rejectTimes": "int",
            "sellerLoginId": "java.lang.String",
            "sellerMemberId": "java.lang.String",
            "sellerMobile": "java.lang.String",
            "sellerRealName": "java.lang.String",
            "sellerReceiveAddress": "java.lang.String",
            "sellerTel": "java.lang.String",
            "sellerUserId": "java.lang.Long",
            "status": "java.lang.String",
            "taskStatus": "java.lang.String",
            "timeOutOperationType": "java.lang.String",
            "tradeTypeStr": "java.lang.String",
            "success": "boolean",
            "isOnlyModifyPrice": "Boolean",
            "isTimeOutPhase": "Boolean",
            "isRefuseReceived": "Boolean",
            "isOnlyRefund": "Boolean",
            "isAfterSaleRefund": "Boolean",
            "isSellerDisAgree": "Boolean",
            "isGoodReceived": "Boolean",
            "isAfterSaleAgreeTimeout": "Boolean",
            "isSupportNewDispaly": "Boolean",
            "isRefundBail": "Boolean",
            "isAfterSaleTimeOut": "Boolean",
            "isBuyerSendGoods": "Boolean",
            "isAfterSaleDisburse": "Boolean",
            "refundOperationList": [
                {
                    "afterOperateStatus": "java.lang.String",
                    "beforeOperateStatus": "java.lang.String",
                    "closeRefundStepId": "long",
                    "crmModifyRefund": "Boolean",
                    "discription": "java.lang.String",
                    "email": "java.lang.String",
                    "freightBill": "java.lang.String",
                    "gmtCreate": "java.util.Date",
                    "gmtModified": "java.util.Date",
                    "id": "java.lang.Long",
                    "messageStatus": "int",
                    "mobile": "java.lang.String",
                    "msgType": "int",
                    "operateRemark": "java.lang.String",
                    "operateTypeInt": "int",
                    "operatorId": "java.lang.String",
                    "operatorLoginId": "java.lang.String",
                    "operatorRoleId": "java.lang.Integer",
                    "operatorUserId": "java.lang.Long",
                    "phone": "java.lang.String",
                    "refundAddress": "java.lang.String",
                    "refundId": "java.lang.String",
                    "rejectReason": "java.lang.String",
                    "vouchers": "java.util.List",
                    "logisticsCompany": {
                        "companyName": "java.lang.String",
                        "companyNo": "java.lang.String",
                        "companyPhone": "java.lang.String",
                        "gmtCreate": "java.util.Date",
                        "gmtModified": "java.util.Date",
                        "id": "java.lang.Long",
                        "spelling": "java.lang.String",
                        "supportPrint": "Boolean"
                    }
                }
            ]
        }
    }
}
```

### opOrderRefundModelDetail 字段说明

| 参数名                     | 类型      | 描述             | 示例值                              |
|-------------------------|---------|----------------|----------------------------------|
| alipayPaymentId         | String  | 支付宝交易号         | 2018042321001008760555342267     |
| applyCarriage           | Long    | 运费申请金额（单位：分）   | 0                                |
| applyExpect             | Long    | 买家期望退款金额（单位：分） | 1                                |
| applyPayment            | Long    | 买家申请退款金额（单位：分） | 1                                |
| applyReason             | String  | 申请退款原因         | 颜色/图案/款式不符                       |
| applyReasonId           | int     | 申请原因ID         | 20021                            |
| applySubReason          | String  | 二级退款原因         | -                                |
| applyRefCode            | String  | 退款编码           | -                                |
| applySubReasonId        | int     | 二级退款原因ID       | 0                                |
| buyerLogisticsName      | String  | 买家物流公司名称       | 其他                               |
| buyerLoginId            | String  | 买家登录ID         | alitestforisv02                  |
| buyerMemberId           | String  | 买家会员ID         | b2b-1624961198                   |
| buyerSendGoods          | Boolean | 买家是否已发货        | true                             |
| buyerUserId             | Long    | 买家数字ID         | 1624961198                       |
| canRefundPayment        | Long    | 可退款金额（单位：分）    | 1                                |
| crmModifyRefund         | Boolean | 是否小二修改过退款单     | false                            |
| disburseChannel         | String  | 退款到账方式         | -                                |
| disputeRequest          | int     | 退款请求类型         | 3                                |
| disputeType             | int     | 退款类型           | 1                                |
| extInfo                 | Map     | 扩展信息           | 见示例                              |
| freightBill             | String  | 运单号            | 12345134135435234                |
| freezeId                | String  | 冻结ID           | 123451545125452334               |
| frozenFund              | Long    | 冻结资金           | -1                               |
| gmtApply                | Date    | 申请退款时间         | 20180423171116000+0800           |
| gmtCompleted            | Date    | 完成时间           | 20180423174713000+0800           |
| gmtCreate               | Date    | 创建时间           | 20180423171116000+0800           |
| gmtModified             | Date    | 修改时间           | 20180423174714000+0800           |
| gmtTimeOut              | Date    | 退款超时时间         | 20180423174714000+0800           |
| goodsReceived           | Boolean | 是否已收到货         | true                             |
| goodsStatus             | int     | 货物状态           | 3                                |
| id                      | Long    | 退款单号           | 8651493722961198                 |
| newRefundReturn         | Boolean | 是否新退货退款        | true                             |
| onlyRefund              | Boolean | 是否仅退款          | false                            |
| orderEntryCountMap      | Map     | 子订单数量映射        | {"151267031009969811": 2}        |
| orderEntryIds           | List    | 子订单ID列表        | [151267031009969811]             |
| orderId                 | Long    | 订单ID           | 151267031008969811               |
| prepaidBalance          | Long    | 预收款退款金额（单位：分）  | 0                                |
| productName             | String  | 商品名称           | 欧洲站2018新款宽松显瘦烫金印花字母老鹰短袖T恤打底裤套装女潮 |
| refundCarriage          | Long    | 退运费金额（单位：分）    | 0                                |
| refundGoods             | Boolean | 是否退货           | true                             |
| refundId                | String  | 退款单ID          | TQ8651493722961198               |
| refundPayment           | Long    | 实际退款金额（单位：分）   | 1                                |
| rejectReason            | String  | 卖家拒绝原因         | -                                |
| rejectReasonId          | int     | 拒绝原因ID         | 0                                |
| rejectTimes             | int     | 退款被拒绝次数        | 0                                |
| sellerDelayDisburse     | Boolean | 卖家是否延迟打款       | false                            |
| sellerLoginId           | String  | 卖家登录ID         | alitestforisv01                  |
| sellerMemberId          | String  | 卖家会员ID         | b2b-1623492085                   |
| sellerMobile            | String  | 卖家手机号          | 19926555555                      |
| sellerRealName          | String  | 卖家真实姓名         | 琳琳                               |
| sellerReceiveAddress    | String  | 卖家收货地址         | 山东 聊城 解决了交流交流链接连接                |
| sellerTel               | String  | 卖家电话           | -                                |
| sellerUserId            | Long    | 卖家数字ID         | 1623492085                       |
| status                  | String  | 退款状态           | refundsuccess                    |
| supportNewSteppay       | Boolean | 是否支持新阶段性付款     | true                             |
| taskStatus              | String  | 任务状态           | -                                |
| timeOutFreeze           | Boolean | 是否超时冻结         | false                            |
| timeOutOperationType    | String  | 超时操作类型         | -                                |
| tradeTypeStr            | String  | 交易类型           | 50060                            |
| success                 | boolean | 是否成功           | true                             |
| isOnlyModifyPrice       | Boolean | 是否仅修改价格        | false                            |
| isTimeOutPhase          | Boolean | 是否超时阶段         | false                            |
| isRefuseReceived        | Boolean | 是否拒绝收货         | false                            |
| isOnlyRefund            | Boolean | 是否仅退款          | true                             |
| isAfterSaleRefund       | Boolean | 是否售后退款         | false                            |
| isSellerDisAgree        | Boolean | 卖家是否不同意        | false                            |
| isGoodReceived          | Boolean | 是否已收货          | false                            |
| isAfterSaleAgreeTimeout | Boolean | 售后同意是否超时       | false                            |
| isSupportNewDispaly     | Boolean | 是否支持新展示        | true                             |
| isRefundBail            | Boolean | 是否退保证金         | false                            |
| isAfterSaleTimeOut      | Boolean | 售后是否超时         | false                            |
| isBuyerSendGoods        | Boolean | 买家是否已发货        | false                            |
| isAfterSaleDisburse     | Boolean | 是否售后打款         | false                            |
| refundOperationList     | Array   | 退款操作记录列表       | 见下方说明                            |

### refundOperationList 字段说明

| 参数名                 | 类型      | 描述                                              | 示例值                                                          |
|---------------------|---------|-------------------------------------------------|--------------------------------------------------------------|
| afterOperateStatus  | String  | 操作后的退款状态                                        | waitbuyersend                                                |
| beforeOperateStatus | String  | 操作前的退款状态                                        | waitbuyersend                                                |
| closeRefundStepId   | Long    | 分阶段订单单向操作关闭退款的阶段ID                              | 0                                                            |
| crmModifyRefund     | Boolean | 是否小二修改过退款单                                      | false                                                        |
| discription         | String  | 描述、说明                                           | 买家声明退货，等待卖家确认。\|物流公司：其他\|物流单号：12345134135435234\|说明：asdada\| |
| email               | String  | 联系人EMAIL                                        | -                                                            |
| freightBill         | String  | 运单号                                             | 12345134135435234                                            |
| gmtCreate           | Date    | 创建时间                                            | 20180423174651000+0800                                       |
| gmtModified         | Date    | 修改时间                                            | 20180423174651000+0800                                       |
| id                  | Long    | 主键，退款操作记录流水号                                    | 50027571423                                                  |
| messageStatus       | int     | 凭证状态(1:正常 2:后台小二屏蔽)                             | 3                                                            |
| mobile              | String  | 联系人手机                                           | -                                                            |
| msgType             | int     | 留言类型(3:小二留言给卖家和买家 4:给买家的留言 5:给卖家的留言 7:cbu的普通留言) | 7                                                            |
| operateRemark       | String  | 操作备注                                            | 买家退货                                                         |
| operateTypeInt      | int     | 操作类型                                            | 10                                                           |
| operatorId          | String  | 操作者-memberID                                    | -                                                            |
| operatorLoginId     | String  | 操作者-loginID                                     | alitestforisv02                                              |
| operatorRoleId      | Integer | 操作者角色名称(1:买家 2:卖家 3:系统)                         | 1                                                            |
| operatorUserId      | Long    | 操作者-userID                                      | 1624961198                                                   |
| phone               | String  | 联系人电话                                           | -                                                            |
| refundAddress       | String  | 退货地址                                            | 网商路66号                                                       |
| refundId            | String  | 退款记录ID                                          | TQ8651493722961198                                           |
| rejectReason        | String  | 卖家拒绝退款原因                                        | 无方案                                                          |
| vouchers            | List    | 凭证图片地址列表                                        | null                                                         |
| logisticsCompany    | Object  | 物流公司详情                                          | 见下方logisticsCompany字段说明                                      |

### logisticsCompany 字段说明

| 参数名          | 类型      | 描述       | 示例值                   |
|--------------|---------|----------|-----------------------|
| companyName  | String  | 快递公司名称   | 百世快递                  |
| companyNo    | String  | 物流公司编号   | HTKY                  |
| companyPhone | String  | 物流公司服务电话 | 95320                 |
| gmtCreate    | Date    | 创建时间     | 2016092614261100+0800 |
| gmtModified  | Date    | 修改时间     | 2016092614261100+0800 |
| id           | Long    | ID       | 352                   |
| spelling     | String  | 全拼       | baishi                |
| supportPrint | Boolean | 是否支持打印   | true                  |

## 错误码说明

| 错误码 | 错误信息  | 解决方案  |
|-----|-------|-------|
| 500 | 服务器错误 | 请稍后重试 |

## 示例代码

### 请求示例

```java
MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
params.

add("refundId","TQ104316274636951198");
params.

add("needTimeOutInfo","true");
params.

add("needOrderRefundOperation","true");

// 发起请求
Mono<RefundDetailResponse> response = refundAPI.queryRefundDetail(appKey, params);
```

### 返回示例

```json
{
    "success": true,
    "result": {
        "opOrderRefundModelDetail": {
            "refundId": "TQ104316274636951198",
            "orderId": 1532872310009898111,
            "buyerLoginId": "b2b-16234961234",
            "sellerLoginId": "b2b-16234965678",
            "disputeType": 1,
            "status": "refundSuccess",
            "applyPayment": 10000,
            "refundPayment": 10000,
            "applyReason": "买家取消订单",
            "applyReasonId": 20021,
            "goodsStatus": 1,
            "gmtCreate": "2025-01-10 10:00:00",
            "gmtModified": "2025-01-10 10:30:00",
            "success": true,
            "isOnlyRefund": true,
            "isSupportNewDispaly": true,
            "refundOperationList": [
                {
                    "afterOperateStatus": "waitsellerreceive",
                    "beforeOperateStatus": "waitsellerreceive",
                    "closeRefundStepId": 0,
                    "crmModifyRefund": false,
                    "discription": "退款",
                    "freightBill": "7060****4101371",
                    "gmtCreate": "2018052114133900+0800",
                    "gmtModified": "2018052114133900+0800",
                    "id": 2726676282834,
                    "messageStatus": 1,
                    "msgType": 7,
                    "operateRemark": "卖家拒绝协议，等待买家修改",
                    "operateTypeInt": 15,
                    "operatorLoginId": "alitestforisv02",
                    "operatorRoleId": 4,
                    "operatorUserId": "1623****085",
                    "refundAddress": "网商路66号",
                    "refundId": "TQ83489**0492085",
                    "rejectReason": "无方案",
                    "vouchers": null,
                    "logisticsCompany": {
                        "companyName": "百世快递",
                        "companyNo": "HTKY",
                        "companyPhone": "95320",
                        "gmtCreate": "2016092614261100+0800",
                        "gmtModified": "2016092614261100+0800",
                        "id": 352,
                        "spelling": "baishi",
                        "supportPrint": true
                    }
                }
            ]
        }
    }
}
```

## 注意事项

1. 该接口仅支持买家视角查询退款单详情
2. 需要正确配置授权信息和签名
3. 建议在调用接口时增加异常处理机制
4. 返回的时间格式为"yyyy-MM-dd HH:mm:ss"
5. 所有金额相关字段单位均为分
6. 退款状态需要特别关注，不同状态代表不同的退款阶段
7. 布尔类型的字段表示各种退款场景的标识，可用于业务逻辑判断 