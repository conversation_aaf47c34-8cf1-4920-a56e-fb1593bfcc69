# 查询退款单详情-根据订单ID（买家视角）

## 接口说明

该API为买家使用，卖家查询请使用alibaba.trade.refund.OpQueryBatchRefundByOrderIdAndStatus.sellerView，根据订单号实时查询退款单列表，目前只能查询到售中的退款单

查询退款单详情，获取退款单的详细信息，包括退款状态、金额、原因等。

## 接口链接

```
POST https://open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.refund.OpQueryBatchRefundByOrderIdAndStatus/{{appKey}}
```

## 请求参数

### 系统参数

| 参数名            | 类型     | 是否必填 | 描述    | 示例值           |
|----------------|--------|------|-------|---------------|
| _aop_timestamp | String | 是    | 请求时间戳 | 1578901234567 |
| _aop_signature | String | 是    | 签名信息  | 示例见下文         |
| access_token   | String | 是    | 授权令牌  | 示例见下文         |

### 业务参数

| 参数名       | 类型     | 是否必填 | 描述                             | 示例值                 |
|-----------|--------|------|--------------------------------|---------------------|
| orderId   | String | 是    | 订单ID                           | 2429989502226540788 |
| queryType | String | 是    | 查询类型  1：活动；3:退款成功（只支持退款中和退款成功） | 3                   |

## 响应参数

### 返回值说明

| 参数名             | 类型     | 描述     | 示例值   |
|-----------------|--------|--------|-------|
| result          | Object | 返回结果对象 | -     |
| errorCode       | String | 错误码    | -     |
| errorMessage    | String | 错误信息   | -     |
| extErrorMessage | String | 附加信息   | 订单号有误 |

### result 对象结构

```json
{
    "result": {
        "opOrderRefundModels": [
            {
                "alipayPaymentId": "java.lang.String",
                "applyCarriage": "java.lang.Long",
                "applyExpect": "java.lang.Long",
                "applyPayment": "java.lang.Long",
                "applyReason": "java.lang.String",
                "applyReasonId": "int",
                "applySubReason": "java.lang.String",
                "applyRefCode": "java.lang.String",
                "applySubReasonId": "int",
                "applySellerRefCode": "java.lang.String",
                "buyerLoginId": "java.lang.String",
                "buyerMemberId": "java.lang.String",
                "buyerUserId": "java.lang.Long",
                "disburseChannel": "java.lang.String",
                "disputeRequest": "int",
                "disputeType": "int",
                "extends": "java.util.Map",
                "freezeId": "java.lang.String",
                "gmtApply": "java.util.Date",
                "gmtCompleted": "java.util.Date",
                "gmtCreate": "java.util.Date",
                "gmtFreezed": "java.util.Date",
                "gmtModified": "java.util.Date",
                "gmtTimeOut": "java.util.Date",
                "goodsStatus": "int",
                "id": "java.lang.Long",
                "instantRefundType": "java.lang.String",
                "orderEntryCountMap": "java.util.Map",
                "orderEntryIds": "java.util.List",
                "orderId": "java.lang.Long",
                "prepaidBalance": "java.lang.Long",
                "productName": "java.lang.String",
                "refundCarriage": "java.lang.Long",
                "refundId": "java.lang.String",
                "refundPayment": "java.lang.Long",
                "rejectReason": "java.lang.String",
                "rejectReasonId": "int",
                "rejectTimes": "int",
                "sellerLoginId": "java.lang.String",
                "sellerMemberId": "java.lang.String",
                "sellerMobile": "java.lang.String",
                "sellerRealName": "java.lang.String",
                "sellerReceiveAddress": "java.lang.String",
                "sellerTel": "java.lang.String",
                "sellerUserId": "java.lang.Long",
                "status": "java.lang.String",
                "taskStatus": "java.lang.String",
                "timeOutOperationType": "java.lang.String",
                "tradeTypeStr": "java.lang.String",
                "success": "boolean",
                "refundOperationList": [
                    {
                        "afterOperateStatus": "java.lang.String",
                        "beforeOperateStatus": "java.lang.String",
                        "closeRefundStepId": "long",
                        "crmModifyRefund": "Boolean",
                        "discription": "java.lang.String",
                        "email": "java.lang.String",
                        "freightBill": "java.lang.String",
                        "gmtCreate": "java.util.Date",
                        "gmtModified": "java.util.Date",
                        "id": "java.lang.Long",
                        "messageStatus": "int",
                        "mobile": "java.lang.String",
                        "msgType": "int",
                        "operateRemark": "java.lang.String",
                        "operateTypeInt": "int",
                        "operatorId": "java.lang.String",
                        "operatorLoginId": "java.lang.String",
                        "operatorRoleId": "java.lang.Integer",
                        "operatorUserId": "java.lang.Long",
                        "phone": "java.lang.String",
                        "refundAddress": "java.lang.String",
                        "refundId": "java.lang.String",
                        "rejectReason": "java.lang.String",
                        "vouchers": "java.util.List",
                        "logisticsCompany": {
                            "companyName": "java.lang.String",
                            "companyNo": "java.lang.String",
                            "companyPhone": "java.lang.String",
                            "gmtCreate": "java.util.Date",
                            "gmtModified": "java.util.Date",
                            "id": "java.lang.Long",
                            "spelling": "java.lang.String",
                            "supportPrint": "Boolean"
                        }
                    }
                ],
                "buyerLoginId": "String",
                "sellerLoginId": "String",
                "isOnlyModifyPrice": "Boolean",
                "isTimeOutPhase": "Boolean",
                "isRefuseReceived": "Boolean",
                "isOnlyRefund": "Boolean",
                "isAfterSaleRefund": "Boolean",
                "isSellerDisAgree": "Boolean",
                "isGoodReceived": "Boolean",
                "isAfterSaleAgreeTimeout": "Boolean",
                "isSupportNewDispaly": "Boolean",
                "isRefundBail": "Boolean",
                "isAfterSaleTimeOut": "Boolean",
                "isBuyerSendGoods": "Boolean",
                "isAfterSaleDisburse": "Boolean"
            }
        ]
    }
}
```

#### result.opOrderRefundModels 字段说明

| 参数名                  | 类型      | 描述                             | 示例值                          |
|----------------------|---------|--------------------------------|------------------------------|
| alipayPaymentId      | String  | 支付宝交易号                         | 2018042321****08760555342267 |
| applyCarriage        | Long    | 运费的申请退款金额，单位：分                 | 0                            |
| applyExpect          | Long    | 买家原始输入的退款金额(可以为空)              | -                            |
| applyPayment         | Long    | 买家申请退款金额，单位：分                  | 1                            |
| applyReason          | String  | 申请原因                           | 颜色/图案/款式不符                   |
| applyReasonId        | int     | 申请原因ID                         | 20021                        |
| applySubReason       | String  | 二级退款原因                         | -                            |
| applySubReasonId     | int     | 二级退款原因Id                       | -1                           |
| buyerAlipayId        | String  | 买家支付宝ID                        | -                            |
| buyerLogisticsName   | String  | 买家退货物流公司名                      | 其他                           |
| buyerMemberId        | String  | 买家会员ID                         | b2b-1624961198               |
| buyerUserId          | Long    | 买家阿里帐号ID(包括淘宝帐号Id)             | 1624961198                   |
| canRefundPayment     | Long    | 最大能够退款金额，单位：分                  | 1                            |
| disburseChannel      | String  | 极速到账打款渠道                       | -                            |
| disputeRequest       | int     | 售后退款要求                         | 3                            |
| disputeType          | int     | 纠纷类型：售中退款 售后退款，默认为售中退款         | 1                            |
| extInfo              | Map     | 扩展信息                           | {}                           |
| freightBill          | String  | 运单号                            | 12345134135435234            |
| frozenFund           | Long    | 实际冻结账户金额，单位：分                  | -1                           |
| gmtApply             | Date    | 申请退款时间                         | 20180423171116000+0800       |
| gmtCompleted         | Date    | 完成时间                           | 20180423174713000+0800       |
| gmtCreate            | Date    | 创建时间                           | 20180423171116000+0800       |
| gmtFreezed           | Date    | 该退款单超时冻结开始时间                   | -                            |
| gmtModified          | Date    | 修改时间                           | 20180423174714000+0800       |
| gmtTimeOut           | Date    | 该退款单超时完成的时间期限                  | -                            |
| goodsStatus          | int     | 货物状态：1=买家未收到货 2=买家已收到货 3=买家已退货 | 3                            |
| id                   | Long    | 退款单编号                          | 8651493722961198             |
| instantRefundType    | String  | 极速到账退款类型                       | -                            |
| orderEntryCountMap   | Map     | 子订单退货数量                        | -                            |
| orderEntryIdList     | List    | 退款单包含的订单明细，时间逆序排列              | -                            |
| orderId              | Long    | 退款单对应的订单编号                     | 151267031008969811           |
| prepaidBalance       | Long    | 极速退款垫资金额，该值不为空时只代表该退款单可以走垫资流程  | -                            |
| productName          | String  | 产品名称(退款单关联订单明细的货品名称)           | 欧洲站2018新款...                 |
| refundCarriage       | Long    | 运费的实际退款金额，单位：分                 | 0                            |
| refundId             | String  | 退款单逻辑主键                        | TQ8651493722961198           |
| refundPayment        | Long    | 实际退款金额，单位：分                    | 1                            |
| rejectReason         | String  | 卖家拒绝原因                         | -                            |
| rejectReasonId       | int     | 卖家拒绝原因Id                       | 0                            |
| rejectTimes          | int     | 退款单被拒绝的次数                      | 0                            |
| sellerAlipayId       | String  | 卖家支付宝ID                        | -                            |
| sellerMemberId       | String  | 卖家会员ID                         | b2b-1623492085               |
| sellerMobile         | String  | 收货人手机                          | -                            |
| sellerRealName       | String  | 收货人姓名                          | -                            |
| sellerReceiveAddress | String  | 买家退货时卖家收货地址                    | -                            |
| sellerTel            | String  | 收货人电话                          | -                            |
| sellerUserId         | Long    | 卖家阿里帐号ID(包括淘宝帐号Id)             | 1623492085                   |
| status               | String  | 退款状态                           | refundsuccess                |
| taskStatus           | String  | 工单子状态，没有流到CRM创建工单时为空           | -                            |
| timeOutOperateType   | String  | 超时后执行的动作                       | -                            |
| tradeTypeStr         | String  | 交易类型，用来替换枚举类型的tradeType        | 50060                        |
| success              | Boolean | 是否成功                           | true                         |
| refundOperationList  | Array   | 操作记录列表                         | 暂不返回                         |
| buyerLoginId         | String  | 买家会员ID                         | alitestforisv02              |
| sellerLoginId        | String  | 卖家会员ID                         | alitestforisv01              |

### 布尔标记字段说明

| 参数名                     | 类型      | 描述             | 示例值   |
|-------------------------|---------|----------------|-------|
| isCrmModifyRefund       | Boolean | 是否小二修改过退款单     | false |
| isTimeOutFreeze         | Boolean | 是否超时系统冻结       | false |
| isInsufficientAccount   | Boolean | 交易4.0退款余额不足    | false |
| isGoodsReceived         | Boolean | 买家是否已收到货       | true  |
| isOnlyRefund            | Boolean | 是否仅退款          | false |
| isRefundGoods           | Boolean | 是否要求退货         | true  |
| isSellerDelayDisburse   | Boolean | 是否卖家延迟打款(安全退款) | false |
| isAftersaleAutoDisburse | Boolean | 售后自动打款         | false |
| isSupportNewSteppay     | Boolean | 是否支持交易4.0      | true  |
| isNewRefundReturn       | Boolean | 是否新流程创建的退款退货   | true  |
| isBuyerSendGoods        | Boolean | 买家是否已经发货       | true  |
| isAftersaleAgreeTimeout | Boolean | 售后超时标记         | false |
| isInsufficientBail      | Boolean | 极速到账退款保证金不足    | false |

#### result.opOrderRefundModels.refundOperationList 字段说明

| 参数名                 | 类型      | 描述                                                                   | 示例值 |
|---------------------|---------|----------------------------------------------------------------------|-----|
| afterOperateStatus  | String  | 操作后的退款状态                                                             | -   |
| beforeOperateStatus | String  | 操作前的退款状态                                                             | -   |
| closeRefundStepId   | Long    | 分阶段订单正向操作关闭退款时的阶段ID                                                  | -   |
| crmModifyRefund     | Boolean | 是否小二修改过退款单                                                           | -   |
| discription         | String  | 描述、说明                                                                | -   |
| email               | String  | 联系人EMAIL                                                             | -   |
| freightBill         | String  | 运单号                                                                  | -   |
| gmtCreate           | Date    | 创建时间                                                                 | -   |
| gmtModified         | Date    | 修改时间                                                                 | -   |
| id                  | Long    | 主键，退款操作记录流水号                                                         | -   |
| messageStatus       | int     | 凭证状态，1:正常 2:后台小二屏蔽                                                   | -   |
| mobile              | String  | 联系人手机                                                                | -   |
| msgType             | int     | 留言类型：<br>3=小二留言给买家和卖家<br>4=给买家的留言<br>5=给卖家的留言<br>7=cbu的普通留言(等同于淘宝的1) | -   |
| operateRemark       | String  | 操作备注                                                                 | -   |
| operateTypeInt      | int     | 操作类型(取代operateType)                                                  | -   |
| operatorId          | String  | 操作者-memberID                                                         | -   |
| operatorLoginId     | String  | 操作者-loginID                                                          | -   |
| operatorRoleId      | Integer | 操作者角色名称(买家/卖家/系统)                                                    | -   |
| operatorUserId      | Long    | 操作者-userID                                                           | -   |
| phone               | String  | 联系人电话                                                                | -   |
| refundAddress       | String  | 退货地址                                                                 | -   |
| refundId            | String  | 退款记录ID                                                               | -   |
| rejectReason        | String  | 卖家拒绝退款原因                                                             | -   |
| vouchers            | List    | 凭证图片地址列表                                                             | -   |
| logisticsCompany    | Object  | 物流公司详情                                                               | -   |

#### result.opOrderRefundModels.refundOperationList.logisticsCompany 字段说明

| 参数名          | 类型      | 描述       | 示例值 |
|--------------|---------|----------|-----|
| companyName  | String  | 快递公司名    | -   |
| companyNo    | String  | 物流公司编号   | -   |
| companyPhone | String  | 物流公司服务电话 | -   |
| gmtCreate    | Date    | 创建时间     | -   |
| gmtModified  | Date    | 修改时间     | -   |
| id           | Long    | ID       | -   |
| spelling     | String  | 全拼       | -   |
| supportPrint | Boolean | 是否支持打印   | -   |

#### 退款状态说明

| 状态值                    | 描述     |
|------------------------|--------|
| refundSuccess          | 退款成功   |
| waitSellerAgree        | 等待卖家同意 |
| waitBuyerSendGoods     | 等待买家发货 |
| waitSellerReceiveGoods | 等待卖家收货 |
| refundClose            | 退款关闭   |

## 错误码说明

| 错误码   | 错误信息      | 解决方案       |
|-------|-----------|------------|
| 400_5 | 订单不存在或已关闭 | 检查订单ID是否正确 |
| 500   | 系统错误      | 请稍后重试      |

## 示例代码

### 请求示例

```java
MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
params.

add("orderId","2429989502226540788");
params.

add("queryType","");

// 发起请求
Mono<RefundOrderDetailResponse> response = refundAPI.queryRefundOrderDetail(appKey, params);
```

### 返回示例

```json
{
    "success": true,
    "result": {
        "opOrderRefundModels": [
            {
                "refundId": "RF1234567890",
                "orderId": 2429989502226540788,
                "buyerLoginId": "buyer123",
                "sellerLoginId": "seller456",
                "disputeType": 1,
                "status": "waitSellerAgree",
                "applyPayment": 10000,
                "refundPayment": 10000,
                "applyReason": "买家取消订单",
                "applyReasonId": 20021,
                "goodsStatus": 1,
                "gmtCreate": "2025-01-10 10:00:00",
                "gmtModified": "2025-01-10 10:30:00",
                "success": true
            }
        ]
    }
}
```

## 注意事项

1. 该接口仅支持买家视角查询退款单详情
2. 需要正确配置授权信息和签名
3. 建议在调用接口时增加异常处理机制
4. 返回的时间格式为"yyyy-MM-dd HH:mm:ss"
5. 所有金额相关字段单位均为分
6. 退款状态需要特别关注，不同状态代表不同的退款阶段

### 时间格式说明

所有时间字段(gmt开头)均采用格式：yyyyMMddHHmmssSSS+0800
示例：20180423171116000+0800

### 金额说明

所有金额相关字段均以分为单位，包括：

- applyCarriage (申请退运费)
- applyPayment (申请退款金额)
- refundCarriage (实际退运费)
- refundPayment (实际退款金额)
- frozenFund (冻结金额)
- canRefundPayment (可退金额)
