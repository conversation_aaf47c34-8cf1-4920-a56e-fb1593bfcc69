# 查询退款退货原因（用于创建退款退货）

## 接口说明

查询可用的退款退货原因列表，用于创建退款退货申请时选择原因。

## 接口链接

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.getRefundReasonList/{{appKey}}
```

## 请求参数

### 系统参数

| 参数名            | 类型     | 是否必填 | 描述     | 示例值   |
|----------------|--------|------|--------|-------|
| _aop_timestamp | String | 是    | 请求时间戳  | 示例见下文 |
| _aop_signature | String | 是    | 签名信息   | 示例见下文 |
| access_token   | String | 是    | 用户授权令牌 | 示例见下文 |

### 业务参数

| 参数名           | 类型     | 是否必填 | 描述    | 示例值                                                                                                                                                       |
|---------------|--------|------|-------|-----------------------------------------------------------------------------------------------------------------------------------------------------------|
| orderId       | Long   | 是    | 主订单ID | -                                                                                                                                                         |
| orderEntryIds | Long[] | 是    | 子订单ID | -                                                                                                                                                         |
| goodsStatus   | String | 是    | 货物状态  | refundWaitSellerSend（集中等待卖家发货）、refundWaitBuyerReceive（集中等待买家收货）、refundBuyerReceived（集中已收货）、afterSaleBuyerReceived（售后已收货）、afterSaleBuyerNotReceived（售后未收货） |

## 响应参数

### 返回值说明

| 参数名    | 类型     | 描述     | 示例值 |
|--------|--------|--------|-----|
| result | Object | 返回结果对象 | -   |

### result 对象结构

```json
{
  "result": {
    "result": {
        "reasons": [
          {
            "id": "java.lang.Long",
            "name": "java.lang.String",
            "needVoucher": "java.lang.Boolean",
            "noRefundCarriage": "java.lang.Boolean",
            "tip": "java.lang.String"
          }
        ]
    },
    "code": "String",
    "message": "String",
    "success": "Boolean"
  }
}
```

### result 字段说明

| 参数名     | 类型      | 描述       | 示例值     |
|---------|---------|----------|---------|
| result  | Object  | 退款原因结果对象 | 见下方说明   |
| code    | String  | 错误码      | 200     |
| message | String  | 错误信息     | success |
| success | Boolean | 是否成功     | true    |

### result.result 字段说明

| 参数名     | 类型    | 描述   | 示例值   |
|---------|-------|------|-------|
| reasons | Array | 原因列表 | 见下方说明 |

### reasons 数组对象字段说明

| 参数名              | 类型      | 描述           | 示例值          |
|------------------|---------|--------------|--------------|
| id               | Long    | 原因ID         | -            |
| name             | String  | 原因           | -            |
| needVoucher      | Boolean | 是否需要上传凭证     | true表示必须上传凭证 |
| noRefundCarriage | Boolean | true表示不支持退运费 | true         |
| tip              | String  | 提示           | -            |

## 错误码说明

| 错误码  | 错误信息  | 解决方案      |
|------|-------|-----------|
| 5xxx | 调用者错误 | 请检查参数是否合法 |
| 4xxx | 服务方错误 | 请稍后重试     |

## 示例代码

### 请求示例

```java
MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
params.add("orderId", "2586683458997473215");
params.add("orderEntryIds", "[2586683458997473215]");
params.add("goodsStatus", "afterSaleBuyerNotReceived");

// 发起请求
Mono<RefundReasonListResponse> response = refundAPI.getRefundReasonList(appKey, params);
```

### 返回示例

```json
{
  "result": {
    "code": "200",
    "message": "success",
    "result": {
      "reasons": [
        {
          "id": 20021,
          "name": "颜色/图案/款式不符",
          "needVoucher": true,
          "noRefundCarriage": true,
          "tip": "请提供实物照片"
        }
      ]
    }
  },
  "success": true
}
```

## 注意事项

1. 根据订单状态和货物状态，可查询的退款原因会有所不同
2. 部分退款原因需要上传凭证
3. 建议在调用接口时增加异常处理机制 