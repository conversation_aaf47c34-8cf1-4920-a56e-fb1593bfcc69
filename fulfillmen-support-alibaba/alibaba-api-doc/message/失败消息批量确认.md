# 失败消息批量确认

## API 概述

手动调用确认API，确认消息已经被消费成功。仅当使用查询式获取失败消息的时候，才需要使用此接口进行确认。

- **接口路径**: `cn.alibaba.open:push.message.confirm-1`
- **请求方式**: POST
- **请求URL**: `https://gw.open.1688.com/openapi/param2/1/cn.alibaba.open/push.message.confirm/{APPKEY}`

## 系统级入参

| 参数名            | 类型     | 必填 | 描述     | 文档     |
|----------------|--------|----|--------|--------|
| _aop_timestamp | String | 否  | 请求时间戳  | [文档地址] |
| _aop_signature | String | 是  | 请求签名   | [文档地址] |
| access_token   | String | 否  | 用户授权令牌 | [文档地址] |

## 应用级入参

| 参数名       | 类型             | 必填 | 描述         | 示例值       |
|-----------|----------------|----|------------|-----------|
| msgIdList | java.util.List | 否  | 待确认的消息id列表 | [123,456] |

## 返回结果

| 参数名       | 类型      | 描述     | 示例值  |
|-----------|---------|--------|------|
| isSuccess | boolean | 操作是否成功 | true |

## JSON返回示例

```json
{
    "isSuccess": true
}
```