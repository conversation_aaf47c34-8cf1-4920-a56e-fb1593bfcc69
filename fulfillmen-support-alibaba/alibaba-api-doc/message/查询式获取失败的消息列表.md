# 查询式获取失败的消息列表

## API 概述

查询式获取失败的消息列表，获取的消息不会自动确认，需要调用方手动调用确认API来确认消息状态。需注意，确认后，会标记分页段的所有消息。

- **接口路径**: `cn.alibaba.open:push.query.messageList-1`
- **请求方式**: POST
- **请求URL**: `https://gw.open.1688.com/openapi/param2/1/cn.alibaba.open/push.query.messageList/{APPKEY}`

## 系统级入参

| 参数名            | 类型     | 必填 | 描述     | 文档     |
|----------------|--------|----|--------|--------|
| _aop_timestamp | String | 否  | 请求时间戳  | [文档地址] |
| _aop_signature | String | 是  | 请求签名   | [文档地址] |
| access_token   | String | 否  | 用户授权令牌 | [文档地址] |

## 应用级入参

| 参数名             | 类型             | 必填 | 描述                       | 示例值                    |
|-----------------|----------------|----|--------------------------|------------------------|
| createStartTime | java.util.Date | 否  | 消息创建时间查找范围开始             | 20130417000000000+0800 |
| createEndTime   | java.util.Date | 否  | 消息创建时间查找范围结束             | 20130417000000000+0800 |
| page            | int            | 否  | 当前数据页，默认从1开始             | 1                      |
| pageSize        | int            | 否  | 每次分页获取的数据量，范围20-200，默认20 | 20                     |
| type            | String         | 否  | 消息类型                     | ORDER_BUYER_MAKER      |
| userInfo        | String         | 否  | 用户Id                     | b2b-413749517f12513    |

## 返回结果

| 参数名             | 类型              | 描述   | 示例值     |
|-----------------|-----------------|------|---------|
| pushMessagePage | PushMessagePage | 分页数据 | ["",""] |

### PushMessagePage 对象结构

| 参数名        | 类型                | 描述        | 示例值 |
|------------|-------------------|-----------|-----|
| datas      | List<PushMessage> | 分页的消息数据列表 | -   |
| totalCount | int               | 消息总数      | -   |

### PushMessage 对象结构

| 参数名      | 类型            | 描述              | 示例值 |
|----------|---------------|-----------------|-----|
| msgId    | long          | 消息唯一id          | -   |
| type     | String        | 消息类型            | -   |
| userInfo | String        | 消息关联的用户memberId | -   |
| data     | java.util.Map | 消息内容            | -   |
| gmtBorn  | long          | 消息创建的时间戳，单位毫秒   | -   |

## JSON返回示例

```json
{
    "pushMessagePage": {
        "datas": [
            {
                "gmtBorn": 1399452484000,
                "topicGroup": "CAIGOU1",
                "data": {
                    "quotationId": 123,
                    "buyOfferId": 123,
                    "supplierMemberId": "memberId"
                },
                "msgId": 68891027,
                "type": "CAIGOU_MSG_BUYER_MARK_QUOTATION",
                "userInfo": "memberId",
                "appKey": "123456",
                "topicName": "MSG_BUYER_MARK_QUOTATION"
            }
        ],
        "totalCount": 1
    }
}
```