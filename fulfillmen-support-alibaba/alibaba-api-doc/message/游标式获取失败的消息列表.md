# 游标式获取失败的消息列表

## API 概述

获取失败的消息列表，支持游标式分页。每次请求返回的消息会自动从消息队列中删除，所以下次请求不会再获取到相同的消息。

- **接口路径**: `cn.alibaba.open:push.cursor.messageList-1`
- **请求方式**: POST
- **请求URL**: `https://gw.open.1688.com/openapi/param2/1/cn.alibaba.open/push.cursor.messageList/{APPKEY}`

## 系统级入参

| 参数名            | 类型     | 必填 | 描述     | 文档     |
|----------------|--------|----|--------|--------|
| _aop_timestamp | String | 否  | 请求时间戳  | [文档地址] |
| _aop_signature | String | 是  | 请求签名   | [文档地址] |
| access_token   | String | 否  | 用户授权令牌 | [文档地址] |

## 应用级入参

| 参数名             | 类型             | 必填 | 描述                    | 示例值                    |
|-----------------|----------------|----|-----------------------|------------------------|
| createStartTime | java.util.Date | 否  | 消息创建时间查找范围开始          | 20130417000000000+0800 |
| createEndTime   | java.util.Date | 否  | 消息创建时间查找范围结束          | 20130417000000000+0800 |
| quantity        | int            | 否  | 每次取的数据量，范围20-200，默认20 | 20                     |
| type            | String         | 否  | 消息类型                  | ORDER_BUYER_MAKER      |
| userInfo        | String         | 否  | 用户Id                  | b2b-413749517f12513    |

## 返回结果

| 参数名             | 类型                | 描述     | 示例值 |
|-----------------|-------------------|--------|-----|
| pushMessageList | List<PushMessage> | 推送消息列表 | []  |

### PushMessage 对象结构

| 参数名      | 类型            | 描述              | 示例值 |
|----------|---------------|-----------------|-----|
| msgId    | long          | 消息唯一id          | -   |
| type     | String        | 消息类型            | -   |
| userInfo | String        | 消息关联的用户memberId | -   |
| data     | java.util.Map | 消息内容            | -   |
| gmtBorn  | long          | 消息创建的时间，单位毫秒    | -   |

## JSON返回示例

```json
{
    "pushMessageList": [
        {
            "gmtBorn": 1399182274000,
            "topicGroup": "CAIGOU",
            "data": {
                "buyOfferId": 123,
                "subUserId": 123
            },
            "msgId": 123456,
            "type": "CAIGOU_MSG_BUYER_PUBLISH_BUYOFFER",
            "userInfo": "memberId",
            "appKey": "1688",
            "topicName": "MSG_BUYER_PUBLISH_BUYOFFER"
        }
    ]
}
```

## 注意事项

1. 获取的消息会自动从消息队列中删除，所以下次请求不会再获取到相同的消息
2. 请求时间范围不要太大，建议在合理范围内
3. quantity参数建议使用默认值20，避免单次获取数据量过大
4. 需要处理好消息的幂等性，避免重复处理相同消息

## 错误码

| 错误码 | 描述    | 解决方案       |
|-----|-------|------------|
| 15  | 参数不合法 | 检查参数是否符合要求 |
| 50  | 系统繁忙  | 请稍后重试      |

## 相关解决方案

- 1688国家服务站解决方案
- 跨境代买寻源比价搜索解决方案（国际化）
- 跨境超买寻源比价搜索解决方案
- 跨境营销SaaS数字化解决方案
