# 获取唤起旺旺聊天的链接

## 接口说明

获取唤起旺旺聊天的链接接口，用于获取与指定用户的旺旺聊天链接。

## 接口信息

- **接口路径**: `/param2/1/com.alibaba.account/account.wangwangUrl.get/${APPKEY}`
- **请求方式**: POST
- **接口版本**: 1.0
- **API名称**: com.alibaba.account:account.wangwangUrl.get-1

## 系统级参数

| 参数名            | 类型     | 必填 | 描述     | 示例值 |
|----------------|--------|----|--------|-----|
| _aop_timestamp | String | 否  | 请求时间戳  | -   |
| _aop_signature | String | 是  | 请求签名   | -   |
| access_token   | String | 是  | 用户授权令牌 | -   |

## 业务请求参数

| 参数名       | 类型     | 必填 | 描述          | 示例值 |
|-----------|--------|----|-------------|-----|
| toOpenUid | String | 是  | 聊天对象openUid | -   |

## 请求示例

```json
{
    "toOpenUid": "目标用户的openUid"
}
```

## 返回参数

### result 对象

| 参数名        | 类型           | 描述     | 示例值 |
|------------|--------------|--------|-----|
| code       | String       | -      | -   |
| message    | String       | -      | -   |
| result     | String       | 旺旺聊天链接 | -   |
| retCodes   | List<String> | -      | -   |
| subCode    | String       | -      | -   |
| subMessage | String       | -      | -   |
| success    | Boolean      | -      | -   |

## 返回示例

```json
{
    "result": {
        "code": "200",
        "message": "操作成功",
        "result": "https://wangwang.chat.link",
        "retCodes": [],
        "subCode": "",
        "subMessage": "",
        "success": true
    }
}
```

## 注意事项

1. 确保聊天对象的openUid正确。
2. 确保用户授权令牌有效。

## 相关链接

- [CRM客户管理解决方案]()
- [数字人直播解决方案]()
- [1688分销寻源采购解决方案（服务商版）]()
- [1688分销寻源采购解决方案（分销买家版）]()
- [供应商分销管理解决方案]()
- [跨境代采寻源比价搜索解决方案（国际化）]()
- [跨境ERP独立站SaaS数字化解决方案]()
