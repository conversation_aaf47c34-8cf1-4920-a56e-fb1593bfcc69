# 拉取商品池中商品数据

## 接口说明

通过商品池ID批量拉取池中商品数据。

## 接口信息

- **接口路径**: `/param2/1/com.alibaba.fenxiao.crossborder/pool.product.pull/${APPKEY}`
- **请求方式**: POST
- **接口版本**: 1.0
- **API名称**: com.alibaba.fenxiao.crossborder.pool.product.pull-1

## 系统级参数

| 参数名            | 类型     | 必填 | 描述     | 示例值 |
|----------------|--------|----|--------|-----|
| _aop_timestamp | String | 否  | 请求时间戳  | -   |
| _aop_signature | String | 是  | 请求签名   | -   |
| access_token   | String | 是  | 用户授权令牌 | -   |

## 业务请求参数

### offerPoolQueryParam 对象

| 参数名         | 类型      | 必填 | 描述   | 示例值             |
|-------------|---------|----|------|-----------------|
| offerPoolId | Long    | 是  | 品池ID | 111             |
| cateId      | Long    | 否  | 类目ID | 1               |
| taskId      | String  | 是  | 任务ID | -               |
| language    | String  | 否  | 语言   | en              |
| pageNo      | Integer | 是  | 页码   | 1               |
| pageSize    | Integer | 是  | 每页数量 | 10              |
| sortField   | String  | 否  | 排序字段 | order1m/buyer1m |
| sortType    | String  | 否  | 排序规则 | ASC/DESC        |

## 请求示例

```json
{
    "offerPoolQueryParam": {
        "offerPoolId": 111,
        "cateId": 1,
        "taskId": "task123",
        "language": "en",
        "pageNo": 1,
        "pageSize": 10,
        "sortField": "order1m",
        "sortType": "ASC"
    }
}
```

## 返回参数

### result 对象

| 参数名     | 类型                | 描述   | 示例值   |
|---------|-------------------|------|-------|
| success | String            | 是否成功 | true  |
| code    | String            | 错误码  | S0000 |
| message | String            | 错误描述 | 成功    |
| result  | List<ProductPool> | 结果   | 结果    |

### ProductPool 对象

| 参数名            | 类型      | 描述                | 示例值    |
|----------------|---------|-------------------|--------|
| offerId        | Long    | 商品ID              | 111111 |
| bizCategoryId  | String  | 机构的类目ID           | 111111 |
| offerPoolTotal | Integer | 商品池总数(每个offer都返回) | 122211 |

## 返回示例

```json
{
    "result": {
        "success": "true",
        "code": "S0000",
        "message": "成功",
        "result": [
            {
                "offerId": 111111,
                "bizCategoryId": "111111",
                "offerPoolTotal": 122211
            }
        ]
    }
}
```

## 注意事项

1. 确保品池ID和任务ID的正确性。
2. 排序字段和排序规则需符合接口要求。

## 相关链接

- [1688分销管理解决方案]()
- [跨境大客户寻源通解决方案]()
- [跨境超买寻源比价搜索解决方案（国际化）]()
- [跨境ERP独立站SaaS数字化解决方案]()
