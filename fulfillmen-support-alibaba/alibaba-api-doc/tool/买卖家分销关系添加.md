# 买卖家分销关系添加

## 接口说明

通过商品ID，添加买卖家分销关系。

## 接口信息

- **接口路径**: `/param2/1/com.alibaba.fenxiao/alibaba.fenxiao.relationadd/${APPKEY}`
- **请求方式**: POST
- **接口版本**: 1.0
- **API名称**: com.alibaba.fenxiao:alibaba.fenxiao.relationadd-1

## 系统级参数

| 参数名            | 类型     | 必填 | 描述     | 示例值 |
|----------------|--------|----|--------|-----|
| _aop_timestamp | String | 否  | 请求时间戳  | -   |
| _aop_signature | String | 是  | 请求签名   | -   |
| access_token   | String | 是  | 用户授权令牌 | -   |

## 业务请求参数

| 参数名     | 类型   | 必填 | 描述   | 示例值      |
|---------|------|----|------|----------|
| offerId | Long | 是  | 商品id | 98129931 |

## 请求示例

```json
{
    "offerId": 98129931
}
```

## 返回参数

### result 对象

| 参数名     | 类型      | 描述 | 示例值 |
|---------|---------|----|-----|
| success | Boolean | -  | -   |
| code    | String  | -  | -   |
| message | String  | -  | -   |
| result  | Boolean | -  | -   |

## 返回示例

```json
{
    "result": {
        "success": true,
        "code": "200",
        "message": "操作成功",
        "result": true
    }
}
```

## 注意事项

1. 确保商品ID的正确性。
2. 确保用户授权令牌有效。

## 相关链接

- [代发解决方案（分销买家版）]()
- [代发解决方案（服务商版）]()
- [分销工具解决方案]()
- [跨境超买寻源比价搜索解决方案（国际化）]()
- [跨境ERP/独立站SaaS数字化解决方案]()
- [1688国家服务站解决方案]()
