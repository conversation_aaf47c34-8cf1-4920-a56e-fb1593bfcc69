# Openuid转换解密为旺旺昵称接口（仅可使用于用户唤起旺旺）

## 接口说明

Openuid转换解密为旺旺昵称接口，用于将待解密的openUid转换为用户唤起旺旺的旺旺昵称。

## 接口信息

- **接口路径**: `/param2/1/com.alibaba.account/wangwangnick.openuid.decrypt/${APPKEY}`
- **请求方式**: POST
- **接口版本**: 1.0
- **API名称**: com.alibaba.account:wangwangnick.openuid.decrypt-1

## 系统级参数

| 参数名            | 类型     | 必填 | 描述     | 示例值 |
|----------------|--------|----|--------|-----|
| _aop_timestamp | String | 是  | 请求时间戳  | -   |
| _aop_signature | String | 是  | 请求签名   | -   |
| access_token   | String | 是  | 用户授权令牌 | -   |

## 业务请求参数

| 参数名     | 类型     | 必填 | 描述          | 示例值 |
|---------|--------|----|-------------|-----|
| openUid | String | 是  | 待解密的openUid | -   |

## 请求示例

```json
{
    "openUid": "待解密的openUid"
}
```

## 返回参数

| 参数名          | 类型     | 描述          | 示例值 |
|--------------|--------|-------------|-----|
| wangwangNick | String | 用户唤起旺旺的旺旺昵称 | -   |

## 返回示例

```json
{
    "wangwangNick": "用户的旺旺昵称"
}
```

## 注意事项

1. 该接口仅用于用户唤起旺旺，不允许用于其他用途。
2. 确保请求参数的正确性。

## 相关链接

- [订单管理解决方案（商家版）]()
- [订单管理解决方案（加密服务商新版）]()
- [绩效管理解决方案]()
- [数据分析解决方案]()
- [新版商品配管解决方案]()
- [跨境代采源头比价搜索解决方案（国际化）]()
- [1688国家服务站解决方案]()
- [跨境寻源通加工定制解决方案]()
- [跨境ERP独立站SaaS数字化解决方案]()
