# 用户loginId加密转换为Openuid接口

## 接口说明

用户loginId加密转换为Openuid接口，用于将用户登录名转换为Openuid。该接口仅允许商家手动触发。

## 接口信息

- **接口路径**: `/param2/1/com.alibaba.account/loginid.openuid.encrypt/${APPKEY}`
- **请求方式**: POST
- **接口版本**: 1.0
- **API名称**: com.alibaba.account:loginid.openuid.encrypt-1

## 系统级参数

| 参数名            | 类型     | 必填 | 描述     | 示例值 |
|----------------|--------|----|--------|-----|
| _aop_timestamp | String | 是  | 请求时间戳  | -   |
| _aop_signature | String | 是  | 请求签名   | -   |
| access_token   | String | 是  | 用户授权令牌 | -   |

## 业务请求参数

| 参数名     | 类型     | 必填 | 描述    | 示例值 |
|---------|--------|----|-------|-----|
| loginId | String | 是  | 用户登陆名 | -   |

## 请求示例

```json
{
    "loginId": "用户的登录名"
}
```

## 返回参数

| 参数名     | 类型     | 描述      | 示例值 |
|---------|--------|---------|-----|
| openUid | String | openUid | -   |

## 返回示例

```json
{
    "openUid": "转换后的openUid"
}
```

## 注意事项

1. 该接口仅允许商家手动触发。
2. 确保请求参数的正确性。

## 相关链接

- [订单管理解决方案（商家版）]()
- [订单管理解决方案（加密服务商新版）]()
- [绩效管理解决方案]()
- [数据分析解决方案]()
- [新版商品配管解决方案]()
- [供应商分销管理解决方案]()
- [跨境代采寻源比价搜索解决方案（国际化）]()
- [跨境ERP/独立站SaaS数字化解决方案]()
