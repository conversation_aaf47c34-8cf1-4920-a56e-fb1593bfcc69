# Fulfillmen Shop Alibaba Support SDK 开发指南

## 一、项目概览

### 1.1 项目简介

本项目是对阿里巴巴1688开放平台API的响应式封装，基于Spring Boot WebFlux构建，使用Project Reactor提供响应式支持。

### 1.2 核心设计原则

1. 统一异常处理
   - 所有服务实现类继承 BaseAlibabaServiceImpl
   - 使用 wrapWithErrorHandler 统一处理异常
   - 规范化错误日志格式和内容

2. 统一日志记录
   - 使用 debug 级别记录操作日志
   - 标准化日志格式和分隔符
   - 记录完整的请求响应信息

3. 参数校验
   - 在基类中统一处理参数校验
   - 使用 assertNotBlank、assertNotNull 等工具方法
   - 提供清晰的校验失败信息

4. 响应式编程
   - 避免阻塞操作
   - 正确处理响应式链
   - 合理使用操作符

### 1.3 项目结构

```
com.fulfillmen.support.alibaba
├── api/                    # API 接口定义
│   ├── ApiPaths.java      # API 路径常量
│   ├── request/           # 请求对象
│   └── response/          # 响应对象
├── service/               # 业务服务
│   ├── impl/             # 服务实现类
│   └── base/             # 基础服务类
├── common/               # 公共工具
└── autoconfigure/        # 自动配置
```

## 二、编码规范

### 2.1 服务实现类规范

1. 类结构

```java
@Slf4j
@Service
public class NewServiceImpl extends BaseAlibabaServiceImpl implements INewService {
    private static final String SERVICE_NAME = "服务名称";
    private final NewAPI newAPI;

    public NewServiceImpl(NewAPI newAPI, AlibabaProperties alibabaProperties, ObjectMapper objectMapper) {
        super(alibabaProperties, objectMapper);
        this.newAPI = newAPI;
    }

    @Override
    protected String getServiceName() {
        return SERVICE_NAME;
    }

    @Override
    public Mono<APIResponse> methodName(APIRequest request) {
        log.debug("开始处理XXX请求: {}", request);
        return wrapWithErrorHandler(
            "操作说明",
            request,
            ApiPaths.NewAPI.METHOD_PATH,
            formParams -> newAPI.methodName(appKey, formParams)
        );
    }
}
```

2. 方法实现规范
   - 使用 debug 级别日志
   - 使用 wrapWithErrorHandler 处理请求
   - 提供清晰的操作说明
   - 使用统一的参数命名

### 2.2 API接口规范

1. 类级别文档

```java
/**
 * API功能说明
 * <p>
 * 包含以下接口：
 * 1. 接口1功能说明
 * 2. 接口2功能说明
 *
 * <AUTHOR>
 * @created 2024-01-16
 * @see <a href="API文档URL">API文档</a>
 */
```

2. 方法级别文档

```java
/**
 * 方法功能说明
 * <p>
 * 详细说明方法的用途和处理逻辑
 *
 * @param appKey 应用key
 * @param params 请求参数
 * @return 响应对象
 * @see <a href="具体API文档URL">API文档</a>
 */
```

### 2.3 测试规范

1. 测试类结构

```java
@Slf4j
class NewAPIMockTest extends BaseAPITest {
    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    @Autowired
    private INewService newService;

    @MockBean
    private NewAPI newAPI;

    @Test
    void shouldSucceed() {
        // Given
        log.info("{} 开始测试XXX {}", LOG_SEPARATOR, LOG_SEPARATOR);
        
        // When
        
        // Then
        StepVerifier.create(response)
            .assertNext(result -> {
                assertThat(result).isNotNull();
                log.info("{}测试结果: {}", LOG_ITEM, result);
            })
            .verifyComplete();
    }

    @Test
    void shouldHandleValidationError() {
        // Given
        log.info("{} 开始测试参数校验失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        
        // When & Then
        StepVerifier.create(response)
            .expectErrorMatches(throwable -> {
                assertThat(throwable).isInstanceOf(AlibabaServiceValidationException.class);
                log.info("{}错误信息: {}", LOG_ITEM, throwable.getMessage());
                return true;
            })
            .verify();
    }
}
```

### 2.4 集成测试规范

1. 测试类结构

```java
/**
 * XXX API 集成测试
 *
 * <AUTHOR>
 * @created 2024-01-16
 */
@Slf4j
class NewAPIIntegrationTest extends BaseAPITest {
    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    @Autowired
    private INewService newService;

    @Test
    void shouldExecuteSuccessfully() {
        log.info("{} 开始测试XXX {}", LOG_SEPARATOR, LOG_SEPARATOR);
        
        // Given
        NewAPIRequest request = createTestRequest();
        log.info("{}请求参数: param1={}, param2={}", 
            LOG_ITEM, request.getParam1(), request.getParam2());
            
        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();
        
        // When
        var response = newService.methodName(request);
        
        // Then
        StepVerifier.create(response)
            .assertNext(result -> {
                // 基本检查
                assertThat(result).isNotNull();
                assertThat(result.getSuccess()).isTrue();
                
                // 业务检查
                assertThat(result.getResult()).isNotNull();
                assertThat(result.getResult().getField()).isNotEmpty();
                
                // 记录日志
                log.info("{}响应结果: {}", LOG_ITEM, result);
                log.info("{}业务数据: {}", LOG_ITEM, result.getResult().getField());
                
                // 记录性能指标
                recordMetrics("MethodName", startTime, result.getSuccess());
            })
            .verifyComplete();
            
        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("MethodName");
        log.info("{} 测试XXX完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldHandleErrorCase() {
        log.info("{} 开始测试错误场景 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        
        // Given
        NewAPIRequest request = createInvalidRequest();
        log.info("{}请求参数: param1={}, param2={}", 
            LOG_ITEM, request.getParam1(), request.getParam2());
            
        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();
        
        // When & Then
        StepVerifier.create(newService.methodName(request))
            .expectNextMatches(result -> {
                // 记录日志
                log.info("{}响应结果: {}", LOG_ITEM, result);
                
                // 记录性能指标
                recordMetrics("MethodNameError", startTime, false);
                
                return !result.getSuccess() && result.getCode().equals("ERROR_CODE");
            })
            .verifyComplete();
            
        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("MethodNameError");
        log.info("{} 测试错误场景完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }
}
```

2. 性能指标管理
   - 使用 resetMetrics() 重置计数器
   - 记录操作开始时间
   - 使用 recordMetrics() 记录指标
   - 使用 logMetrics() 打印指标
   - 区分成功和失败场景
   - 记录响应时间分布

3. 日志记录规范
   - 测试开始和结束标记

   ```java
   log.info("{} 开始测试XXX {}", LOG_SEPARATOR, LOG_SEPARATOR);
   log.info("{} 测试XXX完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
   ```

   - 请求参数记录

   ```java
   log.info("{}请求参数: param1={}, param2={}", 
       LOG_ITEM, request.getParam1(), request.getParam2());
   ```

   - 响应结果记录

   ```java
   log.info("{}响应结果: {}", LOG_ITEM, result);
   log.info("{}业务数据: {}", LOG_ITEM, result.getResult().getField());
   ```

   - 性能指标记录

   ```java
   log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
   logMetrics("MethodName");
   ```

4. 测试场景覆盖
   - 正常业务流程测试
     - 验证基本功能
     - 检查业务数据
     - 记录性能指标

   - 参数验证测试
     - 无效参数
     - 边界值
     - 特殊字符

   - 错误场景测试
     - API调用失败
     - 业务规则校验
     - 超时处理

   - 性能测试
     - 响应时间
     - 成功率
     - 资源使用

5. 测试数据管理
   - 使用工厂方法创建数据

   ```java
   private NewAPIRequest createTestRequest() {
       return NewAPIRequest.builder()
           .param1("value1")
           .param2("value2")
           .build();
   }
   ```

   - 数据验证方法

   ```java
   private void verifyResponse(NewAPIResponse response) {
       assertThat(response.getResult()).isNotNull();
       assertThat(response.getResult().getField()).isNotEmpty();
       // 其他业务相关的断言
   }
   ```

6. 断言最佳实践
   - 基本检查

   ```java
   assertThat(result).isNotNull();
   assertThat(result.getSuccess()).isTrue();
   ```

   - 业务检查

   ```java
   assertThat(result.getResult()).isNotNull();
   assertThat(result.getResult().getField()).isNotEmpty();
   ```

   - 错误检查

   ```java
   assertThat(!result.getSuccess() && result.getCode().equals("ERROR_CODE"));
   ```

7. 测试方法命名
   - should + 预期行为
   - 正向测试: shouldExecuteSuccessfully
   - 异常测试: shouldHandleErrorCase
   - 验证测试: shouldValidateParameters

8. 性能指标记录

   ```java
   // 重置指标
   resetMetrics();
   long startTime = System.currentTimeMillis();
   
   // 记录指标
   recordMetrics("OperationName", startTime, success);
   
   // 打印指标
   logMetrics("OperationName");
   ```

## 三、最佳实践

### 3.1 异常处理

1. 使用 wrapWithErrorHandler
   - 统一的异常包装
   - 标准的错误日志
   - 完整的上下文信息

2. 参数校验
   - 在请求对象中实现 requireParams
   - 使用断言工具方法
   - 提供明确的错误信息

### 3.2 日志记录

1. 日志级别
   - debug: 详细的操作日志
   - info: 重要的状态变更
   - error: 异常和错误信息

2. 日志格式
   - 使用统一的分隔符
   - 包含操作说明
   - 记录关键参数
   - 使用占位符而不是字符串拼接

### 3.3 性能优化

1. 响应式编程
   - 避免阻塞操作
   - 合理使用操作符
   - 正确处理背压

2. 日志优化
   - 使用合适的日志级别
   - 避免不必要的字符串拼接
   - 控制日志数量

## 四、常见问题

### 4.1 代码检查清单

1. 基础检查
   - [ ] 继承 BaseAlibabaServiceImpl
   - [ ] 实现 getServiceName
   - [ ] 使用 wrapWithErrorHandler
   - [ ] 添加完整的文档注释

2. 日志检查
   - [ ] 使用 debug 级别
   - [ ] 包含完整的上下文
   - [ ] 使用正确的格式
   - [ ] 避免敏感信息

3. 测试检查
   - [ ] 包含成功场景测试
   - [ ] 包含参数校验测试
   - [ ] 包含错误处理测试
   - [ ] 使用统一的日志格式

### 4.2 常见错误

1. 异常处理

```java
// 错误示例
return apiCall.apply(params);

// 正确示例
return wrapWithErrorHandler(
    "操作说明",
    request,
    apiPath,
    formParams -> apiCall.apply(formParams)
);
```

2. 日志记录

```java
// 错误示例
log.info("处理请求: " + request);

// 正确示例
log.debug("开始处理XXX请求: {}", request);
```

3. 参数校验

```java
// 错误示例
public void requireParams() {
    if (field == null) {
        throw new IllegalArgumentException();
    }
}

// 正确示例
public void requireParams() {
    assertNotNull(field, "field不能为空");
    assertNotBlank(value, "value不能为空");
}
```

## 五、开发流程

### 5.1 新增API流程

1. 创建API接口
   - 添加完整的文档注释
   - 定义清晰的方法签名
   - 添加API文档链接

2. 创建请求/响应对象
   - 继承基类
   - 实现参数校验
   - 添加字段注释

3. 创建服务实现
   - 继承 BaseAlibabaServiceImpl
   - 实现接口方法
   - 添加完整的日志

4. 编写测试用例
   - 测试成功场景
   - 测试参数校验
   - 测试错误处理

### 5.2 代码审查要点

1. 架构
   - 是否继承基类
   - 是否使用统一的异常处理
   - 是否符合响应式编程规范

2. 实现
   - 是否包含完整的参数校验
   - 是否有适当的日志记录
   - 是否处理了所有异常场景

3. 测试
   - 是否覆盖了主要场景
   - 是否包含参数校验测试
   - 是否有完整的日志记录

### 5.3 代码一致性原则

1. 遵循现有模式
   - 研究并理解现有代码的实现模式
   - 保持与现有代码相同的结构和风格
   - 使用相同的命名约定和代码组织方式
   - 参考相似功能的实现方式

2. 示例

```java
// 现有代码模式
public class ExistingServiceImpl extends BaseAlibabaServiceImpl {
    private static final String SERVICE_NAME = "服务名称";
    
    @Override
    public Mono<APIResponse> methodName(APIRequest request) {
        log.debug("开始处理XXX请求: {}", request);
        return wrapWithErrorHandler(
            "操作说明",
            request,
            ApiPaths.ExistingAPI.METHOD_PATH,
            formParams -> existingAPI.methodName(appKey, formParams)
        );
    }
}

// 新增代码应保持一致
public class NewServiceImpl extends BaseAlibabaServiceImpl {
    private static final String SERVICE_NAME = "新服务名称";
    
    @Override
    public Mono<APIResponse> methodName(APIRequest request) {
        log.debug("开始处理XXX请求: {}", request);
        return wrapWithErrorHandler(
            "操作说明",
            request,
            ApiPaths.NewAPI.METHOD_PATH,
            formParams -> newAPI.methodName(appKey, formParams)
        );
    }
}
```

3. 变更管理
   - 发现改进机会时，先记录问题和建议
   - 通过正式的变更请求流程提出修改
   - 等待团队评审和确认后再实施变更
   - 确保变更有充分的理由和明确的收益

4. 变更请求模板

```markdown
## 变更建议

### 当前情况
- 描述现有代码的实现方式
- 指出存在的问题或限制

### 建议修改
- 提出具体的改进建议
- 说明预期的收益
- 列出可能的影响

### 实施计划
- 变更范围
- 具体步骤
- 回滚方案

### 评审要点
- 是否符合架构原则
- 是否向后兼容
- 是否便于维护
```
