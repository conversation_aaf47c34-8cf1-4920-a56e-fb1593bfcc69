#!/bin/bash
set -e # 遇到错误立即退出

PROJECT_NAME="fulfillmen-support"
NEXUS_URL="阿里云 - 云效"

# 默认配置
AUTO_DEPLOY=true  # 默认自动部署到私有仓库

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 <版本号> [类型] [选项]"
    echo ""
    echo "参数:"
    echo "  版本号    必需，格式: X.Y.Z (例如: 1.1.4)"
    echo "  类型      可选，snapshot|release (默认: snapshot)"
    echo ""
    echo "选项:"
    echo "  --no-deploy    跳过部署到私服"
    echo ""
    echo "示例:"
    echo "  $0 1.1.4                # 发布 1.1.4-SNAPSHOT 并部署"
    echo "  $0 1.1.4 snapshot       # 发布 1.1.4-SNAPSHOT 并部署"
    echo "  $0 1.1.4 release        # 发布 1.1.4 并部署"
    echo "  $0 1.1.4 --no-deploy    # 发布 1.1.4-SNAPSHOT 但不部署"
    echo "  $0 1.1.4 release --no-deploy  # 发布 1.1.4 但不部署"
    echo ""
    echo "功能:"
    echo "  - 自动更新版本号"
    echo "  - 构建项目（跳过测试）"
    echo "  - 默认部署到私服（可通过 --no-deploy 跳过）"
    echo "  - Git 标签管理（仅正式版本）"
}

# 验证版本号格式
validate_version() {
    local version=$1
    if [[ ! $version =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        log_error "版本号格式错误，应为 X.Y.Z 格式（例如: 1.1.4）"
        exit 1
    fi
}

# 检查是否在项目根目录
check_project_root() {
    if [[ ! -f "pom.xml" ]]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 检查是否是正确的项目
    if ! grep -q "fulfillmen-support" pom.xml; then
        log_warn "当前目录似乎不是 ${PROJECT_NAME} 项目"
        read -p "是否继续？(y/n): " answer
        if [[ "$answer" != "y" ]]; then
            log_info "操作已取消"
            exit 0
        fi
    fi
    
    # 检查 fulfillmen-support-alibaba 模块是否存在
    if [[ ! -d "fulfillmen-support-alibaba" ]]; then
        log_warn "未找到 fulfillmen-support-alibaba 模块目录"
        read -p "是否继续？(y/n): " answer
        if [[ "$answer" != "y" ]]; then
            log_info "操作已取消"
            exit 0
        fi
    fi
}

# 获取当前版本
get_current_version() {
    # 从 pom.xml 中直接获取版本号，更可靠
    local version=$(grep -A 1 '<artifactId>fulfillmen-support</artifactId>' pom.xml | grep '<version>' | sed 's/.*<version>\(.*\)<\/version>.*/\1/' | head -1)
    echo "${version:-未知}"
}

# 更新版本号
update_version() {
    local new_version=$1
    log_info "使用 Maven Versions Plugin 更新版本号为: ${new_version}"

    # 使用 mvnd 代替 mvn 以提高性能
    mvnd versions:set -DnewVersion="${new_version}" -DgenerateBackupPoms=false

    # 验证更新是否成功
    local updated_version=$(get_current_version)
    if [[ "$updated_version" == "$new_version" ]]; then
        log_success "版本号更新成功: ${new_version}"
    else
        log_error "版本号更新失败，当前版本: ${updated_version}"
        exit 1
    fi
}

# 检查 Git 状态
check_git_status() {
    if [[ -n $(git status --porcelain) ]]; then
        log_warn "检测到未提交的更改:"
        git status --short
        echo ""
        read -p "是否继续？(y/n): " answer
        if [[ "$answer" != "y" ]]; then
            log_info "操作已取消"
            exit 0
        fi
    fi
}

# 检查 mvnd 是否可用
check_mvnd() {
    if ! command -v mvnd &> /dev/null; then
        log_error "mvnd 命令未找到，请安装 Maven Daemon"
        log_info "安装指南: https://github.com/apache/maven-mvnd"
        exit 1
    fi
    
    log_info "使用 Maven Daemon (mvnd) 进行构建以提高性能"
}

# 构建项目
build_project() {
    log_info "清理并构建整个项目（跳过测试）..."
    # 使用 mvnd 的并行构建功能 (-T 1C: 每个CPU核心一个线程)
    mvnd -T 1C clean install -DskipTests
    log_success "项目构建完成"
}

# 部署到私服
deploy_to_nexus() {
    log_info "部署到私服 ${NEXUS_URL}..."
    # 使用 mvnd 进行部署，同样启用并行处理
    mvnd deploy -DskipTests -T 1C
    log_success "部署成功"
}

# 创建 Git 标签
create_git_tag() {
    local version=$1
    local tag_name="v${version}"
    
    if git tag -l | grep -q "^${tag_name}$"; then
        log_warn "标签 ${tag_name} 已存在"
        read -p "是否删除现有标签并重新创建？(y/n): " answer
        if [[ "$answer" == "y" ]]; then
            git tag -d "${tag_name}"
            git push origin ":refs/tags/${tag_name}" 2>/dev/null || true
        else
            return
        fi
    fi
    
    git add .
    git commit -m "chore: bump version to ${version}" || true
    git tag -a "${tag_name}" -m "Release version ${version}"
    
    log_success "Git 标签 ${tag_name} 创建成功"
    
    read -p "是否推送标签到远程仓库？(y/n): " answer
    if [[ "$answer" == "y" ]]; then
        git push origin "${tag_name}"
        git push origin HEAD
        log_success "标签已推送到远程仓库"
    fi
}

# 主函数
main() {
    local skip_deploy=false
    local version=""
    local type="snapshot"
    
    # 检查项目根目录
    check_project_root
    
    # 检查 mvnd 可用性
    check_mvnd
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --no-deploy)
                skip_deploy=true
                shift
                ;;
            *)
                if [[ -z "$version" ]]; then
                    version="$1"
                elif [[ "$type" == "snapshot" && ("$1" == "release" || "$1" == "snapshot") ]]; then
                    type="$1"
                else
                    log_error "未知参数: $1"
                    show_help
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # 检查必需参数
    if [[ -z "$version" ]]; then
        show_help
        exit 0
    fi
    
    # 验证版本号
    validate_version "$version"
    
    # 确定最终版本号
    local final_version
    if [[ "$type" == "release" ]]; then
        final_version="$version"
    else
        final_version="${version}-SNAPSHOT"
    fi
    
    log_info "准备发布 ${PROJECT_NAME} ${type}版本: ${final_version}"
    
    # 显示当前版本
    local current_version=$(get_current_version)
    log_info "当前版本: ${current_version}"
    log_info "目标版本: ${final_version}"
    
    # 显示部署状态
    if [[ "$skip_deploy" == true ]]; then
        log_warn "将跳过部署到私服"
    else
        log_info "将自动部署到私服: ${NEXUS_URL}"
    fi
    
    # 确认操作
    echo ""
    read -p "确认要发布版本 ${final_version} 吗？(y/n): " confirm
    if [[ "$confirm" != "y" ]]; then
        log_info "操作已取消"
        exit 0
    fi
    
    # 检查 Git 状态
    check_git_status
    
    # 更新版本号
    update_version "$final_version"
    
    # 构建项目
    build_project
    
    # 自动部署到私服（除非指定跳过）
    if [[ "$skip_deploy" == false && "$AUTO_DEPLOY" == true ]]; then
        deploy_to_nexus
    elif [[ "$skip_deploy" == false ]]; then
        # 如果 AUTO_DEPLOY 为 false，则询问
        read -p "是否部署到私服 ${NEXUS_URL}？(y/n): " deploy
        if [[ "$deploy" == "y" ]]; then
            deploy_to_nexus
        fi
    else
        log_info "跳过部署到私服"
    fi
    
    # 对于正式版本，询问是否创建 Git 标签
    if [[ "$type" == "release" ]]; then
        read -p "是否创建 Git 标签？(y/n): " create_tag
        if [[ "$create_tag" == "y" ]]; then
            create_git_tag "$version"
        fi
    fi
    
    log_success "版本发布完成: ${final_version}"
    
    # 显示后续步骤提示
    echo ""
    log_info "后续步骤提示:"
    if [[ "$type" == "snapshot" ]]; then
        echo "  - 开发版本已发布，可以继续开发"
        echo "  - 准备发布正式版本时，运行: $0 $version release"
    else
        echo "  - 正式版本已发布"
        echo "  - 建议准备下一个开发版本"
        local next_patch=$((${version##*.} + 1))
        local next_version="${version%.*}.${next_patch}"
        echo "  - 下一个开发版本: $0 $next_version snapshot"
    fi
    
    log_info "项目模块:"
    echo "  - fulfillmen-support-alibaba"
}

# 执行主函数
main "$@"