#!/bin/bash
set -e # 遇到错误立即退出

PROJECT_NAME="fulfillmen-support"
NEXUS_URL="阿里云 - 云效"

if [ -z "$1" ]; then
    echo "错误：必须指定版本号参数（例如：1.0.0）"
    exit 1
fi

NEW_VERSION="${1}-SNAPSHOT"

echo "正在更新父项目版本号为 ${NEW_VERSION}"
mvnd -N versions:set -DnewVersion=${NEW_VERSION} -DgenerateBackupPoms=false

echo "统一设置所有模块版本号"
mvnd versions:set-property -Dproperty=revision -DnewVersion=${NEW_VERSION} -DgenerateBackupPoms=false

echo "构建整个项目..."
mvnd -T 1C -DskipTests clean install

echo "是否要发布到私服 ${NEXUS_URL}? (y/n)"
read answer

if [ "$answer" == "y" ]; then
    echo "清理版本备份文件..."
    mvnd deploy -DskipTests -am
    # 提交本次版本好操作
    mvnd versions:commit
    echo "发布成功！版本号：${NEW_VERSION}"
else
    # 取消发布
    exit 1
fi